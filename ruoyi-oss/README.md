# ruoyi-oss 对象存储服务模块

## 模块介绍

`ruoyi-oss` 是MallB电商平台的对象存储服务模块，提供统一的文件上传、下载、管理接口，支持多种云存储平台，用于处理商城系统中的图片、视频、文档等各类文件资源。

## 主要功能

- 文件上传：支持单文件和批量上传
- 文件下载：支持直接下载和外链访问
- 文件管理：支持文件删除、移动、复制等操作
- 图片处理：支持图片缩放、裁剪、水印等处理
- 访问控制：支持公共访问和私有访问模式
- 存储策略：支持按日期、业务类型等组织存储路径
- 文件预览：支持常见文档和图片的在线预览

## 支持的存储平台

- 阿里云OSS
- 腾讯云COS
- 七牛云Kodo
- MinIO
- FastDFS
- 本地文件系统

## 使用示例

### 文件上传

```java
@Autowired
private OssService ossService;

public String uploadFile(MultipartFile file) {
    return ossService.uploadFile(file, "product/images");
}
```

### 获取文件URL

```java
String url = ossService.getFileUrl("product/images/example.jpg");
```

### 删除文件

```java
boolean result = ossService.deleteFile("product/images/example.jpg");
```

## 配置说明

在application.yml中配置OSS参数：

```yaml
oss:
  enabled: true
  type: aliyun  # 可选：aliyun, tencent, qiniu, minio, local
  endpoint: oss-cn-beijing.aliyuncs.com
  accessKey: your-access-key
  secretKey: your-secret-key
  bucketName: your-bucket-name
  domain: https://your-bucket.oss-cn-beijing.aliyuncs.com
  baseDir: mallb
```

## 扩展新的存储实现

1. 实现OssService接口
2. 添加对应的配置项
3. 在OssFactory中注册新实现 