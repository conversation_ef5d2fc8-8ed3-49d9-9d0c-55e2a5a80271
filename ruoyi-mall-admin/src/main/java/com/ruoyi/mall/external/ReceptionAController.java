package com.ruoyi.mall.external;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OrderVirtualStatusEnum;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.mall.domain.bo.ReceiveQuantizedBo;
import com.ruoyi.mall.domain.bo.ReceiveQuantizedUserFaithBo;
import com.ruoyi.mall.domain.bo.ReceptionABo;
import com.ruoyi.mall.domain.bo.RelationChainBo;
import com.ruoyi.mall.domain.dto.BatchExchangeFundDTO;
import com.ruoyi.mall.domain.dto.ExchangeFundDTO;
import com.ruoyi.mall.domain.dto.FaithStatisticsDTO;
import com.ruoyi.mall.domain.dto.QuantificationStatisticsDTO;
import com.ruoyi.mall.domain.entity.*;
import com.ruoyi.mall.domain.vo.ConfigSettingVo;
import com.ruoyi.mall.domain.vo.ShopPromotionReqVO;
import com.ruoyi.mall.domain.vo.ShopPromotionRespVO;
import com.ruoyi.mall.domain.vo.UserDeductionDailyStatVO;
import com.ruoyi.mall.enums.VirtualTypeEnum;
import com.ruoyi.mall.service.*;
import com.ruoyi.mall.strategy.ReceptionA.ReceptionAFactory;
import com.ruoyi.mall.strategy.ReceptionA.ReceptionAStrategy;
import com.ruoyi.system.service.ISysOperLogService;
import com.ruoyi.system.service.ISysUserService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 对接A系统的接口
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/receptionA")
public class ReceptionAController {

    private final ReceptionAFactory receptionAFactory;
    private final IShopService shopService;
    private final IVirtualOrderService virtualOrderService;
    private final ISysUserService sysUserService;
    private final ITzUserService tzUserService;
    private final IQuantizationRateService quantizationRateService;
    private final IAuthorizationAreaService authorizationAreaService;
    private final IShopValuePushService shopValuePushService;
    private final IUserDeductionDailyStatService userDeductionDailyStatService;
    private final IShopValueRecordService shopValueRecordService;
    private final IOfflineDrainageRecordService offlineDrainageRecordService;
    private final IUserPromotionService userPromotionService;
    private final ISysUserDonationRecordService sysUserDonationRecordService;
    private final ISysOperLogService sysOperLogService;

    /**
     * 接收A系统传输的分量、引流数据
     */
    @Log(title = "接收A系统传输的分量、引流数据", businessType = BusinessType.OTHER)
    @SaCheckPermission("mall:receptionA:receiveData")
    @PostMapping("/receiveData")
    public R<Boolean> receiveData(@RequestBody @Validated ReceptionABo receptionABo) {
        ReceptionAStrategy receptionStrategy = receptionAFactory.getReceptionStrategy(receptionABo.getType());
        return R.ok(receptionStrategy.setValue(receptionABo));
    }

    /**
     * 接收量化值
     */
    @Log(title = "接收量化值", businessType = BusinessType.OTHER)
    @SaCheckPermission("mall:receptionA:receiveQuantizedData")
    @PostMapping("/receiveQuantizedData")
    public R<Void> receiveQuantizedData(@RequestBody @Validated ReceiveQuantizedBo receiveQuantizedBo) {
        quantizationRateService.receiveQuantizedData(receiveQuantizedBo);
        return R.ok();
    }

    /**
     * 接收地区授权数据
     */
    @Log(title = "接收地区授权数据", businessType = BusinessType.OTHER)
    @SaCheckPermission("mall:receptionA:receiveAuthorizationData")
    @PostMapping("/receiveAuthorizationData")
    public R<Void> receiveAuthorizationData(@RequestBody @Validated List<AuthorizationArea> dataList) {
        authorizationAreaService.receiveAuthorizationData(dataList);
        return R.ok();
    }

    /**
     * 批量接收外部系统推送的店铺量化值和量化
     */
    @SaCheckPermission("mall:receptionA:batchReceivePush")
    @PostMapping("/batchReceivePush")
    @Log(title = "批量接收店铺量化值和量化", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    public R<Map<String, Boolean>> batchReceivePush(@Validated @RequestBody List<ReceiveQuantizedUserFaithBo> receiveQuantizedUserFaithBos) {
        Map<String, Boolean> resultMap = shopValuePushService.handleBatchShopValuePush(receiveQuantizedUserFaithBos);
        return R.ok(resultMap);
    }

    /**
     * 接收平台兑换金
     */
    @SaCheckPermission("mall:receptionA:receiveExchangeFund")
    @Log(title = "接收平台兑换金", businessType = BusinessType.OTHER)
    @PostMapping("/receiveExchangeFund")
    public R<Boolean> receiveExchangeFund(@RequestBody @Validated ExchangeFundDTO exchangeFundDTO) {
        return R.ok(tzUserService.receiveExchangeFund(exchangeFundDTO));
    }

    /**
     * 接收admin的量化值
     */
    @Log(title = "接收admin的量化值", businessType = BusinessType.OTHER)
    @SaCheckPermission("mall:receptionA:receiveAdminDailyQuantify")
    @PostMapping("/receiveAdminDailyQuantify")
    public R<Boolean> receiveAdminDailyQuantify(@RequestBody @Validated AdminDailyQuantifyDao adminDailyQuantifyDao) {
        Double adminDailyQuantifyValue = adminDailyQuantifyDao.getAdminDailyQuantifyValue();
        if (ObjUtil.isNull(adminDailyQuantifyValue)) {
            return R.fail("数据不能为空");
        }
        // 获取原来的
        SysUser dbsysUser = sysUserService.getById(1L);
        Double quantificationValue = dbsysUser.getQuantificationValue();
        if (quantificationValue == null) {
            quantificationValue = 0D;
        }

        // 设置新的
        SysUser sysUser = new SysUser();
        sysUser.setUserId(1L);
        sysUser.setQuantificationValue(quantificationValue + adminDailyQuantifyValue);

        return R.ok(sysUserService.updateById(sysUser));
    }

    /**
     * 批量接收平台兑换金
     */
    @SaCheckPermission("mall:receptionA:batchReceiveExchangeFund")
    @PostMapping("/batchReceiveExchangeFund")
    @Log(title = "批量接收平台兑换金", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    public R<Boolean> batchReceiveExchangeFund(@RequestBody @Validated BatchExchangeFundDTO batchDTO) {
        return R.ok(tzUserService.batchReceiveExchangeFund(batchDTO));
    }

    /**
     * 获取用户昨日平台抵扣金使用统计
     */
    @SaCheckPermission("mall:receptionA:getUserDeductionStat")
    @GetMapping("/getUserDeductionStat")
    @Log(title = "获取用户昨日平台抵扣金使用统计", businessType = BusinessType.OTHER)
    public R<List<UserDeductionDailyStatVO>> getUserDeductionStat() {
        List<UserDeductionDailyStatVO> statList = userDeductionDailyStatService.getYesterdayDeductionStat();
        return R.ok(statList);
    }

    /**
     * 读取交易数据
     */
    @SaCheckPermission("mall:receptionA:getDealData")
    @GetMapping("/getDealData")
    @Log(title = "读取交易数据", businessType = BusinessType.OTHER)
    public R<List<Shop>> getDealData(@RequestParam String productTypes) {
        // types为空时报错
        if (StrUtil.isBlank(productTypes)) {
            return R.fail("参数不能为空");
        }
        List<String> stringList = Arrays.asList(productTypes.split(","));
        if (stringList.isEmpty()) {
            return R.fail("参数不能为空");
        }

        // 昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime startOfYesterday = yesterday.atStartOfDay();
        LocalDateTime endOfYesterday = yesterday.atTime(23, 59, 59);

        // 查询昨天的虚拟订单数据
        List<VirtualOrder> virtualOrders = virtualOrderService.list(
            new LambdaQueryWrapper<VirtualOrder>()
                .eq(VirtualOrder::getStatus, OrderVirtualStatusEnum.PENDING_SHIPMENT.getCode())
                // 只查询昨天一天的的数据
                .between(VirtualOrder::getPayTime, startOfYesterday, endOfYesterday)
        );

        // 查询昨天线下引流付费的记录
        List<OfflineDrainageRecord> offlineDrainageRecords = offlineDrainageRecordService.list(
            new LambdaQueryWrapper<OfflineDrainageRecord>()
                .between(OfflineDrainageRecord::getCreateTime, startOfYesterday, endOfYesterday)
        );
        // 获取对应的店铺ID
        List<Long> userIds = virtualOrders.stream().map(VirtualOrder::getUserId).collect(Collectors.toList());
        List<Long> shopIds = offlineDrainageRecords.stream().map(OfflineDrainageRecord::getShopId).collect(Collectors.toList());
        if (userIds.isEmpty() && shopIds.isEmpty()) {
            return R.ok(new ArrayList<>());
        }
        // 查询店铺
        List<Shop> shopList = shopService.list(
            new LambdaQueryWrapper<Shop>()
                .and(item -> {
                    if (shopIds.isEmpty()) {
                        item.in(Shop::getUserId, userIds);
                    } else if (userIds.isEmpty()) {
                        item.in(Shop::getId, shopIds);
                    } else {
                        item.in(Shop::getId, shopIds).or().in(Shop::getUserId, userIds);
                    }
                })
        );
        if (shopList.isEmpty()) {
            return R.ok(new ArrayList<>());
        }

        ArrayList<Shop> reslutlist = new ArrayList<>();
        // 权限支付记录
        if (stringList.contains("1")) {
            // productType = 1,2,3为权限组组 根据id进行区分，sum(count)值
            shopList.forEach(
                shop -> {
                    shop.setProductType(1);
                    // 虚拟订单的所有金额
                    BigDecimal amountCount = virtualOrders.stream().filter(
                        virtualOrder ->
                            (Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.DAIXOAO_ONE.type()) ||
                                Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.DAIXOAO_TWO.type()) ||
                                Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.DAIXOAO_THERE.type()))
                                && virtualOrder.getUserId().equals(shop.getUserId())
                    ).map(VirtualOrder::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 虚拟订单的手续费
                    BigDecimal serviceFeeCount = virtualOrders.stream().filter(
                        virtualOrder ->
                            (Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.DAIXOAO_ONE.type()) ||
                                Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.DAIXOAO_TWO.type()) ||
                                Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.DAIXOAO_THERE.type()))
                                && virtualOrder.getUserId().equals(shop.getUserId())
                    ).map(VirtualOrder::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);

                    shop.setAmount(amountCount.subtract(serviceFeeCount));
                    if (shop.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                        Shop newShop = new Shop();
                        BeanUtils.copyProperties(shop, newShop);
                        reslutlist.add(newShop);
                    }
                }
            );
        }
        // 广告记录
        if (stringList.contains("2")) {
            shopList.forEach(
                shop -> {
                    shop.setProductType(2);
                    // 虚拟订单的所有金额
                    BigDecimal amountCount = virtualOrders.stream().filter(
                        virtualOrder ->
                            Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.ADVERTISEMENT_PUTTING.type())
                                && virtualOrder.getUserId().equals(shop.getUserId())
                    ).map(VirtualOrder::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 虚拟订单的手续费
                    BigDecimal serviceFeeCount = virtualOrders.stream().filter(
                        virtualOrder
                            -> Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.ADVERTISEMENT_PUTTING.type())
                            && virtualOrder.getUserId().equals(shop.getUserId())
                    ).map(VirtualOrder::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);

                    shop.setAmount(amountCount.subtract(serviceFeeCount));
                    if (shop.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                        Shop newShop = new Shop();
                        BeanUtils.copyProperties(shop, newShop);
                        reslutlist.add(newShop);
                    }
                }
            );
        }
        // 技术引流记录
        if (stringList.contains("3")) {
            shopList.forEach(
                shop -> {
                    shop.setProductType(3);
                    // 虚拟订单的所有金额
                    BigDecimal amountCount = virtualOrders.stream().filter(
                        virtualOrder -> Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.JISHU_DRAINAGE.type())
                            && virtualOrder.getUserId().equals(shop.getUserId())
                    ).map(VirtualOrder::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 虚拟订单的手续费
                    BigDecimal serviceFeeCount = virtualOrders.stream().filter(
                        virtualOrder -> Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.JISHU_DRAINAGE.type())
                            && virtualOrder.getUserId().equals(shop.getUserId())
                    ).map(VirtualOrder::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 线下引流的费用
                    BigDecimal reduce = offlineDrainageRecords
                        .stream()
                        .filter(offlineDrainageRecord -> offlineDrainageRecord.getShopId().equals(shop.getId()))
                        .map(OfflineDrainageRecord::getCitationValueMoney)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    shop.setAmount(amountCount.subtract(serviceFeeCount).add(reduce));
                    if (shop.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                        Shop newShop = new Shop();
                        BeanUtils.copyProperties(shop, newShop);
                        reslutlist.add(newShop);
                    }
                }
            );
        }
        return R.ok(reslutlist);
    }


    /**
     * 获取关系链
     */
    @SaCheckPermission("mall:receptionA:getRelationChain")
    @GetMapping("/getRelationChain")
    @Log(title = "获取关系链", businessType = BusinessType.OTHER)
    public R<List<RelationChainBo>> getRelationChain() {
        // 每日的合格数量
        ReceptionAStrategy receptionStrategy = receptionAFactory.getReceptionStrategy(1);
        ConfigSettingVo configSettingVo = receptionStrategy.getValue();
        JSONObject configValue = configSettingVo.getConfigValue();
        int dailyThreshold = configValue.getInt("dailyThreshold");
        int dailyExtraQuantityUnit = configValue.getInt("dailyExtraQuantityUnit");
        Double dailyThresholdReward = configValue.getDouble("dailyThresholdReward");
        List<RelationChainBo> relationChain = tzUserService.getRelationChain(dailyThreshold, dailyExtraQuantityUnit + dailyThreshold, dailyThresholdReward);
        return R.ok(relationChain);
    }

    /**
     * 读取昨天的量化值进化量
     */
    @SaCheckPermission("mall:receptionA:quantification:list")
    @GetMapping("/statistics/quantification")
    @Log(title = "读取昨天的量化值进化量", businessType = BusinessType.OTHER)
    public TableDataInfo<QuantificationStatisticsDTO> statisticsQuantification() {
        // 获取昨天的日期 格式为yyyy-MM-dd
        Date date = new Date(DateUtils.getNowDate().getTime() - 24 * 60 * 60 * 1000);
        // 仅查询量化值转量化损耗记录(operationType=3)
        return shopValueRecordService.statisticsQuantificationByTimePage(
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, date), null, 3, new PageQuery());
    }

    /**
     * 读取昨天的量化进化量
     */
    @SaCheckPermission("mall:receptionA:faith:list")
    @GetMapping("/statistics/faith")
    @Log(title = "读取昨天的量化进化量", businessType = BusinessType.OTHER)
    public TableDataInfo<FaithStatisticsDTO> statisticsFaith() {
        // 获取昨天的日期 格式为yyyy-MM-dd
        Date date = new Date(DateUtils.getNowDate().getTime() - 24 * 60 * 60 * 1000);
        // 仅查询量化转量化损耗记录(operationType=4)
        return shopValueRecordService.statisticsFaithByTimePage(
            DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, date), null, 4, new PageQuery());
    }

    /**
     * 读取昨天的admin量化值转化数据
     */
    @SaCheckPermission("mall:receptionA:adminQuantification")
    @GetMapping("/statistics/adminQuantification")
    @Log(title = "读取昨天的admin量化值转化数据", businessType = BusinessType.OTHER)
    public R<QuantificationStatisticsDTO> adminQuantification() {
        // 获取昨天的日期 格式为yyyy-MM-dd
        Date date = new Date(DateUtils.getNowDate().getTime() - 24 * 60 * 60 * 1000);
        // 仅查询量化值转量化损耗记录
        return R.ok(shopValueRecordService.adminQuantification(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, date), null, 3));
    }

    /**
     * 读取昨天的admin量化转化数据
     */
    @SaCheckPermission("mall:receptionA:adminFaith")
    @GetMapping("/statistics/adminFaith")
    @Log(title = "读取昨天的admin量化转化数据", businessType = BusinessType.OTHER)
    public R<FaithStatisticsDTO> adminFaith() {
        // 获取昨天的日期 格式为yyyy-MM-dd
        Date date = new Date(DateUtils.getNowDate().getTime() - 24 * 60 * 60 * 1000);
        // 量化转平台促销金的记录
        return R.ok(shopValueRecordService.adminFaith(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, date), null, 4));
    }

    /**
     * 读取昨天的促销金明细管理
     */
    @SaCheckPermission("mall:receptionA:getListPromotionDetail")
    @GetMapping("/statistics/getListPromotionDetail")
    @Log(title = "读取昨天的促销金明细管理", businessType = BusinessType.OTHER)
    public R<List<ShopPromotionRespVO>> getListPromotionDetail() {
        // 获取昨天的日期 格式为yyyy-MM-dd
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);
        ShopPromotionReqVO shopPromotion = new ShopPromotionReqVO();

        // 构建查询开始和结束时间（昨天的00:00:00到23:59:59）
        LocalDateTime beginTime = yesterday.atStartOfDay(); // 00:00:00
        LocalDateTime endTime = yesterday.atTime(23, 59, 59); // 23:59:59

        shopPromotion.setBeginTime(beginTime);
        shopPromotion.setEndTime(endTime);


        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageNum(1);
        pageQuery.setPageSize(Integer.MAX_VALUE);
        TableDataInfo<ShopPromotionRespVO> shopPromotionRespVOTableDataInfo = userPromotionService.pageUserPromotionDetail(shopPromotion, pageQuery);
        return R.ok(shopPromotionRespVOTableDataInfo.getRows());
    }

    /**
     * 获取超级管理员手机号
     */
    @SaCheckPermission("mall:receptionA:getAdminPhone")
    @GetMapping("/getAdminPhone")
    @Log(title = "获取超级管理员手机号", businessType = BusinessType.OTHER)
    public R<String> getAdminPhone() {
        SysUser sysUser = sysUserService.getOne(
            new LambdaQueryWrapper<SysUser>()
                .select(SysUser::getPhonenumber)
                .eq(SysUser::getUserId, 1L)
        );
        return R.ok("", sysUser.getPhonenumber());
    }

    /**
     * 获取超级管理员的昨天的核销金总额
     */
    @SaCheckPermission("mall:receptionA:getAdminDailyQuantify")
    @GetMapping("/getAdminDailyQuantify")
    @Log(title = "获取超级管理员的昨天的核销金总额", businessType = BusinessType.OTHER)
    public R<Map<String, Object>> getAdminDailyQuantify() {

        SysUser sysUser = sysUserService.getById(1L);
        // 获取昨天的日期 格式为yyyy-MM-dd
        // 获取昨天的日期
        LocalDate yesterday = LocalDate.now().minusDays(1);

        // 构建查询开始和结束时间（昨天的00:00:00到23:59:59）
        LocalDateTime beginTime = yesterday.atStartOfDay(); // 00:00:00
        LocalDateTime endTime = yesterday.atTime(23, 59, 59); // 23:59:59

        BigDecimal reduce = sysUserDonationRecordService.list(
            new LambdaQueryWrapper<SysUserDonationRecord>()
                .eq(SysUserDonationRecord::getUserId, 1L)
                .gt(SysUserDonationRecord::getDonationTime, beginTime)
                .le(SysUserDonationRecord::getDonationTime, endTime)
        ).stream().map(SysUserDonationRecord::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        Map<String, Object> map = new HashMap<>();
        map.put("dailyQuantify", reduce);
        map.put("phone", sysUser.getPhonenumber());
        return R.ok(map);
    }


}

@Data
class AdminDailyQuantifyDao {

    @NotNull(message = "不能为空")
    private Double adminDailyQuantifyValue;

}
