package com.ruoyi.mall.area;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.service.IAuthorizationAreaService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 地区管理控制器
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/authorizationArea")
public class AuthorizationAreaController extends BaseController {

    private final IAuthorizationAreaService authorizationAreaService;

    /**
     * 获取所有省份列表
     */
    @GetMapping("/queryAreaAuthorize")
    public R<TableDataInfo<Map<String, Object>>> queryAreaAuthorize(@RequestParam("type") String type, @RequestParam("level") String level, PageQuery pageQuery) {
        return R.ok(TableDataInfo.build(authorizationAreaService.queryAreaAuthorize(type, level, pageQuery)));
    }
}
