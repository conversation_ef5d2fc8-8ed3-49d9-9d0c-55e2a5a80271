package com.ruoyi.mall.area;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.mall.domain.entity.Area;
import com.ruoyi.mall.service.IAreaService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 地区管理控制器
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/area")
public class AreaController extends BaseController {

    private final IAreaService areaService;

    /**
     * 获取所有省份列表
     */
    @GetMapping("/provinces")
    public R<List<Area>> getProvinces() {
        List<Area> provinces = areaService.getProvinces();
        return R.ok(provinces);
    }

    /**
     * 获取指定省份下的城市列表
     */
    @GetMapping("/cities/{provinceCode}")
    public R<List<Area>> getCitiesByProvince(@PathVariable("provinceCode") Long provinceCode) {
        List<Area> cities = areaService.getCitiesByProvince(provinceCode);
        return R.ok(cities);
    }

    /**
     * 获取指定城市下的区县列表
     */
    @GetMapping("/districts/{cityCode}")
    public R<List<Area>> getDistrictsByCity(@PathVariable("cityCode") Long cityCode) {
        List<Area> districts = areaService.getDistrictsByCity(cityCode);
        return R.ok(districts);
    }

    /**
     * 获取指定区县下的街道列表
     */
    @GetMapping("/towns/{districtCode}")
    public R<List<Area>> getTownsByDistrict(@PathVariable("districtCode") Long districtCode) {
        List<Area> towns = areaService.getTownsByDistrict(districtCode);
        return R.ok(towns);
    }

    /**
     * 根据编码获取地区信息
     */
    @GetMapping("/{code}")
    public R<Area> getAreaByCode(@PathVariable("code") Long code) {
        Area area = areaService.getAreaByCode(code);
        return R.ok(area);
    }

    /**
     * 获取完整的地区名称
     */
    @GetMapping("/fullname/{provinceCode}/{cityCode}/{districtCode}")
    public R<String> getFullAreaName(
        @PathVariable("provinceCode") Long provinceCode,
        @PathVariable("cityCode") Long cityCode,
        @PathVariable("districtCode") Long districtCode) {
        String fullName = areaService.getFullAreaName(provinceCode, cityCode, districtCode);
        return R.ok(fullName);
    }

    /**
     * 根据父ID查询下级地区列表
     */
    @GetMapping("/children/{parentId}")
    public R<List<Area>> getAreasByParentId(@PathVariable("parentId") Long parentId) {
        List<Area> areas = areaService.getAreasByParentId(parentId);
        return R.ok(areas);
    }
}
