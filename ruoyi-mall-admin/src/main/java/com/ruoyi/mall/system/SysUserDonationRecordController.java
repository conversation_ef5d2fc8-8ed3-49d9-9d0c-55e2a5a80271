package com.ruoyi.mall.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.mall.domain.entity.SysUserDonationRecord;
import com.ruoyi.mall.service.ISysUserDonationRecordService;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 管理员核销记录
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/sysUserDonationRecord")
public class SysUserDonationRecordController extends BaseController {

    private final ISysUserService sysUserService;
    private final ISysUserDonationRecordService sysUserDonationRecordService;

    /**
     * 查询管理员核销记录列表
     */
    @SaCheckPermission("mall:sysUserDonationRecord:list")
    @GetMapping("/list")
    public TableDataInfo<SysUserDonationRecord> list(SysUserDonationRecord sysUserDonationRecord, PageQuery pageQuery) {
        LambdaQueryWrapper<SysUserDonationRecord> objectLambdaQueryWrapper = new LambdaQueryWrapper<>();
        objectLambdaQueryWrapper.eq(SysUserDonationRecord::getUserId, getUserId());
        // 小于开始时间，大于结束时间
        objectLambdaQueryWrapper.ge(StrUtil.isNotBlank(sysUserDonationRecord.getBeginTime()), SysUserDonationRecord::getDonationTime, DateUtils.parseDate(sysUserDonationRecord.getBeginTime()));
        objectLambdaQueryWrapper.lt(StrUtil.isNotBlank(sysUserDonationRecord.getEndTime()), SysUserDonationRecord::getDonationTime, DateUtils.parseDate(sysUserDonationRecord.getEndTime()));
        objectLambdaQueryWrapper.orderByDesc(SysUserDonationRecord::getDonationTime);
        return TableDataInfo.build(sysUserDonationRecordService.page(pageQuery.build(), objectLambdaQueryWrapper));
    }

    /**
     * 新增管理员核销记录
     */
    @SaCheckPermission("mall:sysUserDonationRecord:add")
    @RepeatSubmit()
    @Log(title = "新增管理员核销记录", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Void> add(@Validated @RequestBody SysUserDonationRecord sysUserDonationRecord) {

        SysUser sysUser = sysUserService.getById(getUserId());
        String operationPassword = sysUser.getOperationPassword();
        if (StrUtil.isEmpty(operationPassword)) {
            return R.fail("暂未设置操作密码");
        }
        if (!BCrypt.checkpw(sysUserDonationRecord.getOperationPassword(), sysUser.getOperationPassword())) {
            return R.fail("密码错误");
        }
        // 获取原来的核销值
        BigDecimal platformPromotionGold = sysUser.getPlatformPromotionGold();
        sysUser.setPlatformPromotionGold(platformPromotionGold.subtract(sysUserDonationRecord.getAmount()));
        sysUserService.updateById(sysUser);

        sysUserDonationRecord.setUserId(getUserId());
        sysUserDonationRecord.setStatus("1");
        sysUserDonationRecord.setDonationTime(new Date());
        return toAjax(sysUserDonationRecordService.save(sysUserDonationRecord));
    }
}
