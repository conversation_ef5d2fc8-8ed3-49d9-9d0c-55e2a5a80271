package com.ruoyi.mall.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.mall.domain.bo.PlatformAgreementBo;
import com.ruoyi.mall.domain.entity.PlatformAgreement;
import com.ruoyi.mall.service.IPlatformAgreementService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 平台协议配置
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/platformAgreement")
public class PlatformAgreementController extends BaseController {

    private final IPlatformAgreementService iPlatformAgreementService;

    /**
     * 查询平台协议配置列表
     */
    @SaCheckPermission("mall:platformAgreement:list")
    @GetMapping("/list")
    public TableDataInfo<PlatformAgreement> list(PlatformAgreementBo bo, PageQuery pageQuery) {
        return iPlatformAgreementService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出平台协议配置列表
     */
    @SaCheckPermission("mall:platformAgreement:export")
    @PostMapping("/export")
    @Log(title = "导出平台协议配置列表", businessType = BusinessType.EXPORT)
    public void export(PlatformAgreementBo bo, HttpServletResponse response) {
        List<PlatformAgreement> list = iPlatformAgreementService.queryList(bo);
        ExcelUtil.exportExcel(list, "平台协议配置", PlatformAgreement.class, response);
    }

    /**
     * 获取平台协议配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mall:platformAgreement:getInfo")
    @GetMapping("/{id}")
    public R<PlatformAgreement> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable("id") Long id) {
        return R.ok(iPlatformAgreementService.queryById(id));
    }

    @SaCheckPermission("mall:platformAgreement:getInfoByTitle")
    @GetMapping("/getInfoByTitle")
    public R<PlatformAgreement> getInfoByTitle(@RequestParam String title) {
        return R.ok(iPlatformAgreementService.getOne(
            new LambdaQueryWrapper<PlatformAgreement>()
                .eq(PlatformAgreement::getName, title)
                .orderByDesc(PlatformAgreement::getCreateTime)
        ));
    }

    /**
     * 新增平台协议配置
     */
    @SaCheckPermission("mall:platformAgreement:add")
    @RepeatSubmit()
    @Log(title = "新增平台协议配置", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PlatformAgreementBo bo) {
        return toAjax(iPlatformAgreementService.insertByBo(bo));
    }

    /**
     * 修改平台协议配置
     */
    @SaCheckPermission("mall:platformAgreement:edit")
    @RepeatSubmit()
    @Log(title = "修改平台协议配置", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PlatformAgreementBo bo) {
        return toAjax(iPlatformAgreementService.updateByBo(bo));
    }

    /**
     * 删除平台协议配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("mall:platformAgreement:remove")
    @Log(title = "删除平台协议配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iPlatformAgreementService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
