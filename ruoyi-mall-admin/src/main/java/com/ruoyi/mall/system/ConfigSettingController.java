package com.ruoyi.mall.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.mall.domain.bo.ConfigSettingBo;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.vo.ConfigSettingVo;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.service.IShopService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 定制化配置管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/config")
public class ConfigSettingController extends BaseController {

    private final IConfigSettingService configSettingService;

    private final IShopService shopService;

    /**
     * 根据配置键获取配置
     */
    @GetMapping("/key/{index}")
    @SaCheckPermission("mall:config:getConfigByKey")
    public R<JSONObject> getConfigByKey(@PathVariable Integer index) {
        verificationIndex(index);
        JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(index));
        Shop shop = shopService.getById(getLoginUser().getShopId());
        // 设置权限
        if (index == 1 && shop != null) {
            // 校验是否过期时间内
            if (shop.getJurisdictionExpireTime() != null && shop.getJurisdictionExpireTime().getTime() > new Date().getTime()) {
                configValue.set("jurisdiction", shop.getJurisdiction() != null ? shop.getJurisdiction() : "0");
                configValue.set("jurisdictionExpireTime", shop.getJurisdictionExpireTime() != null && shop.getJurisdictionExpireTime().getTime() > new Date().getTime() ? shop.getJurisdictionExpireTime() : "0");
            } else {
                configValue.set("jurisdiction", "0");
            }
            // CB的代销权限
            configValue.set("consignmentPermission", shop.getConsignmentPermission() != null ? shop.getConsignmentPermission() : "0");
        }
        return R.ok(configValue);
    }

    /**
     * 配置管理
     */
    @PostMapping("/{index}")
    @SaCheckPermission("mall:config:addOrUpdate")
    @Log(title = "配置管理", businessType = BusinessType.UPDATE)
    public R<Boolean> addOrUpdate(@PathVariable Integer index, @RequestBody ConfigSettingBo configBo) {
        verificationIndex(index);
        if (configBo.getConfigValue() == null) {
            return R.fail("配置值不能为空");
        }
        String key = ConfigSettingConstants.config.get(index);
        ConfigSettingVo configSettingVo = configSettingService.getConfigByKey(key);
        configBo.setConfigKey(ConfigSettingConstants.config.get(index));
        if (configSettingVo == null) {
            configBo.setConfigName(ConfigSettingConstants.configName.get(index));
            return R.ok(configSettingService.saveConfig(configBo));
        } else {
            return R.ok(configSettingService.updateConfig(configBo));
        }
    }

    public void verificationIndex(Integer index) {
        if (index >= ConfigSettingConstants.config.size() || index < 1) {
            throw new ServiceException("未知的配置");
        }
    }
}
