package com.ruoyi.mall.system;


import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.PublicParam;
import com.ruoyi.mall.service.IPublicParamService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公共参数
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/param")
public class PublicParamController {

    private final IPublicParamService publicParamService;

    /**
     * 新增公共参数
     */
    @PostMapping("/add")
    @SaCheckPermission("mall:param:add")
    @Log(title = "新增公共参数", businessType = BusinessType.INSERT)
    public R<Boolean> addPublicParam(@RequestBody PublicParam publicParam) {
        return R.ok(publicParamService.save(publicParam));
    }

    /**
     * 根据id删除公共参数
     *
     * @param publicId 公共参数id
     */
    @DeleteMapping("/{publicId}")
    @SaCheckPermission("mall:param:delete")
    @Log(title = "删除公共参数", businessType = BusinessType.DELETE)
    public R<Boolean> removePublicParam(@PathVariable Long publicId) {
        return R.ok(publicParamService.removeById(publicId));
    }

    /**
     * 查询公共参数设设置
     */
    @GetMapping
    @SaCheckPermission("mall:param:list")
    public R<List<PublicParam>> selectPublicParamList() {
        return R.ok(publicParamService.list());
    }

    /**
     * 修改公共参数
     */
    @PutMapping("/edit")
    @SaCheckPermission("mall:param:edit")
    @Log(title = "修改公共参数", businessType = BusinessType.UPDATE)
    public R<Boolean> editPublicParam(@RequestBody PublicParam publicParam) {

        LambdaUpdateWrapper<PublicParam> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .set(StringUtils.isNotBlank(publicParam.getPublicName()), PublicParam::getPublicName, publicParam.getPublicName())
            .set(StringUtils.isNotBlank(publicParam.getPublicKey()), PublicParam::getPublicKey, publicParam.getPublicKey())
            .set(StringUtils.isNotBlank(publicParam.getPublicValue()), PublicParam::getPublicValue, publicParam.getPublicValue())
            .set(StringUtils.isNotBlank(publicParam.getStatus()), PublicParam::getStatus, publicParam.getStatus())
            .eq(PublicParam::getPublicId, publicParam.getPublicId());

        return R.ok(publicParamService.update(updateWrapper));
    }
}



