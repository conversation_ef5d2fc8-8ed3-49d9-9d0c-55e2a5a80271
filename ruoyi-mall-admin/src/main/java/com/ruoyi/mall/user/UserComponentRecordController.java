package com.ruoyi.mall.user;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.mall.domain.dto.UserComponentAdjustDTO;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.entity.UserComponentRecord;
import com.ruoyi.mall.enums.ComponentRecordTypeEnum;
import com.ruoyi.mall.service.IUserComponentRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户分量记录
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/component/record")
public class UserComponentRecordController extends BaseController {

    private final IUserComponentRecordService userComponentRecordService;

    /**
     * 查询用户分量记录列表
     */
    @SaCheckPermission("mall:component:record:list")
    @GetMapping("/list")
    public TableDataInfo<UserComponentRecord> list(UserComponentRecord userComponentRecord, PageQuery pageQuery) {
        return userComponentRecordService.selectUserComponentRecordPage(userComponentRecord, pageQuery);
    }

    /**
     * 导出用户分量记录列表
     */
    @SaCheckPermission("mall:component:record:export")
    @Log(title = "用户分量记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(UserComponentRecord userComponentRecord, HttpServletResponse response) {
        List<UserComponentRecord> list = userComponentRecordService.selectUserComponentRecordList(userComponentRecord);
        ExcelUtil.exportExcel(list, "用户分量记录", UserComponentRecord.class, response);
    }

    /**
     * 获取用户分量记录详细信息
     */
    @SaCheckPermission("mall:component:record:query")
    @GetMapping(value = "/{id}")
    public R<UserComponentRecord> getInfo(@PathVariable("id") Long id) {
        return R.ok(userComponentRecordService.getById(id));
    }

    /**
     * 管理员调整用户分量
     */
    @SaCheckPermission("mall:component:record:adjust")
    @Log(title = "调整用户分量", businessType = BusinessType.UPDATE)
    @PostMapping("/adjust")
    public R<Void> adjust(@Validated @RequestBody UserComponentAdjustDTO adjustDTO) {
        // 获取当前管理员信息
        String operator = getUsername();

        // 添加分量记录
        boolean success = userComponentRecordService.addComponentRecord(
            adjustDTO.getUserId(),
            adjustDTO.getComponentValue(),
            ComponentRecordTypeEnum.ADMIN_ADJUST.getValue(),
            null,
            null,
            adjustDTO.getRemark(),
            operator
        );

        return toAjax(success);
    }

    /**
     * 获取用户分量统计数据
     */
    @SaCheckPermission("mall:component:record:statistics")
    @GetMapping("/statistics/{userId}")
    public R<Object> getStatistics(@PathVariable("userId") Long userId) {
        return R.ok(userComponentRecordService.getUserComponentStatistics(userId));
    }

    /**
     * 获取用户每日分量统计
     */
    @SaCheckPermission("mall:component:statistics:list")
    @GetMapping("/daily")
    public TableDataInfo<TzUser> getDailyStatistics(@RequestParam String date, @RequestParam String username, @RequestParam String phone, PageQuery pageQuery) {
        return userComponentRecordService.getDailyStatistics(date, username, phone, pageQuery);
    }
}
