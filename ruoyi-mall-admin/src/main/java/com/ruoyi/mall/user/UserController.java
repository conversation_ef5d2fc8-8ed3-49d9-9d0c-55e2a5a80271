package com.ruoyi.mall.user;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.vo.UserDeductionReqVO;
import com.ruoyi.mall.domain.vo.UserDeductionRespVO;
import com.ruoyi.mall.domain.vo.UserDeductionVO;
import com.ruoyi.mall.service.ITzUserService;
import com.ruoyi.mall.service.IUserDeductionService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户管理
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/user")
public class UserController extends BaseController {

    private final ITzUserService userService;

    private final IUserDeductionService userDeductionService;


    /**
     * 查询所有用户，查询条件有手机号，姓名，注册时间进行大于，小于范围查询
     */
    @GetMapping("/list")
    public TableDataInfo<TzUser> list(TzUser user,
                                      @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
                                      @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
                                      PageQuery pageQuery) {
        if (startTime != null) {
            user.getParams().put("beginTime", startTime);
        }
        if (endTime != null) {
            user.getParams().put("endTime", endTime);
        }
        // 只查询消费者，不查询商家
        user.setUserType("C");
        return userService.selectTzUserPage(user, pageQuery);
    }

    /**
     * 获取用户详细信息
     */
    @GetMapping(value = "/{userId}")
    public R<TzUser> getInfo(@PathVariable("userId") Long userId) {
        return R.ok(userService.selectTzUserByUserId(userId));
    }

    /**
     * 修改用户状态
     */
    @PutMapping("/updateStatus")
    public R<Void> updateStatus(@RequestBody Map<String, Object> params) {
        Long userId = Long.valueOf(params.get("userId").toString());
        String status = params.get("status").toString();

        TzUser user = new TzUser();
        user.setUserId(userId);
        user.setStatus(status);
        if (status.equals("0")) {
            user.setLoginTime(new Date());
        }

        return toAjax(userService.updateTzUser(user));
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/resetPwd/{userId}")
    public R<Void> resetPwd(@PathVariable("userId") Long userId) {
        // 创建用户对象
        TzUser user = new TzUser();
        user.setUserId(userId);
        // 默认密码为123456
        user.setPassword("123456");

        // 调用修改方法
        int rows = userService.resetUserPassword(user);
        return toAjax(rows);
    }

    /**
     * 查询用户平台抵扣金列表
     */
    @GetMapping("/deduction/list")
    public TableDataInfo<UserDeductionVO> list(UserDeductionVO userDeduction, PageQuery pageQuery) {
        return userDeductionService.queryUserDeductionList(userDeduction, pageQuery);
    }


    /**
     * 查询用户每条平台抵扣金明细列表
     */
    @GetMapping("/deduction/listDeductionDetail")
    public TableDataInfo<UserDeductionRespVO> listDeductionDetail(UserDeductionReqVO userDeduction, PageQuery pageQuery) {
        return userDeductionService.pageUserDeductionDetail(userDeduction, pageQuery);
    }

    /**
     * 导出用户抵扣金明细列表
     */
    @Log(title = "导出用户抵扣金明细列表", businessType = BusinessType.EXPORT)
    @PostMapping("/deduction/exportDeductionDetail")
    public void exportUserDeductionDetail(UserDeductionReqVO userDeduction, HttpServletResponse response) {
        List<UserDeductionRespVO> list = userDeductionService.listUserDeductionDetail(userDeduction);
        ExcelUtil.exportExcel(list, "用户抵扣金明细", UserDeductionRespVO.class, response);
    }

    /**
     * 导出用户平台抵扣金列表
     */
    @Log(title = "导出用户平台抵扣金", businessType = BusinessType.EXPORT)
    @PostMapping("/deduction/export")
    public void export(UserDeductionVO userDeduction, HttpServletResponse response) {
        List<UserDeductionVO> list = userDeductionService.queryUserDeductionList(userDeduction);
        ExcelUtil.exportExcel(list, "用户平台抵扣金", UserDeductionVO.class, response);
    }


}
