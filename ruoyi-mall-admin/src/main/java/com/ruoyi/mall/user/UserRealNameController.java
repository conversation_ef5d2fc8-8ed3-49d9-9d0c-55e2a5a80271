package com.ruoyi.mall.user;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.mall.domain.dto.UserRealNameAuditDTO;
import com.ruoyi.mall.domain.entity.TzUserRealName;
import com.ruoyi.mall.domain.vo.UserRealNameVO;
import com.ruoyi.mall.service.ITzUserRealNameService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户实名认证审核管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/userRealName")
public class UserRealNameController extends BaseController {

    private final ITzUserRealNameService userRealNameService;

    /**
     * 查询用户实名认证申请列表
     */
    @SaCheckPermission("mall:user:realName:list")
    @GetMapping("/list")
    public TableDataInfo<UserRealNameVO> list(TzUserRealName tzUserRealName, PageQuery pageQuery) {
        return userRealNameService.getAdminUserRealNameList(tzUserRealName, pageQuery);
    }

    /**
     * 获取用户实名认证详细信息
     */
    @SaCheckPermission("mall:user:realName:query")
    @GetMapping(value = "/{id}")
    public R<UserRealNameVO> getInfo(@PathVariable("id") Long id) {
        return R.ok(userRealNameService.getUserRealNameDetail(id));
    }

    /**
     * 审核用户实名认证申请
     */
    @SaCheckPermission("mall:user:realName:audit")
    @Log(title = "审核用户实名认证", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/audit")
    public R<Void> audit(@Validated @RequestBody UserRealNameAuditDTO auditDTO) {
        userRealNameService.adminAuditUserRealName(auditDTO);
        return R.ok();
    }

    /**
     * 删除用户实名认证记录
     */
    @SaCheckPermission("mall:user:realName:remove")
    @Log(title = "删除用户实名认证记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Long[] ids) {
        return toAjax(userRealNameService.removeByIds(java.util.Arrays.asList(ids)));
    }
}
