package com.ruoyi.mall.user;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.RelationChainQueryParam;
import com.ruoyi.mall.domain.vo.RelationChainVO;
import com.ruoyi.mall.domain.vo.UserStatusJurisdictionVO;
import com.ruoyi.mall.service.IRelationChainService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 用户的关系链管理
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/mall/relationChain")
public class RelationChainController extends BaseController {

    private final IRelationChainService relationChainService;

    /**
     * 获取关系链列表
     */
    @GetMapping("/list")
    public TableDataInfo<RelationChainVO> list(PageQuery pageQuery, RelationChainQueryParam queryParam) {
        return relationChainService.getRelationChainList(pageQuery, queryParam);
    }

    /**
     * 修改用户状态
     */
    @PostMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody @Validated ChangeStatusDao changeStatusDao) {
        int rows = relationChainService.changeUserStatus(changeStatusDao.getUserId(), changeStatusDao.getStatus());
        return toAjax(rows);
    }


    /**
     * 获取用户状态和权限信息
     * /mall/relationChain/statusAndJurisdiction
     */

    @GetMapping("/statusAndJurisdiction")
    public R<UserStatusJurisdictionVO> getStatusAndJurisdiction() {
        return R.ok(relationChainService.getStatusAndJurisdiction());
    }


}

@Data
class ChangeStatusDao {

    @NotNull(message = "ID不能为空")
    private Long userId;

    @NotNull(message = "状态不能为空")
    private Integer status;
}
