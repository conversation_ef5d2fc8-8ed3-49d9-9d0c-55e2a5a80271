package com.ruoyi.mall.user;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.mall.domain.query.ShopOrderDetailDTO;
import com.ruoyi.mall.domain.vo.ShopOrderDetailVO;
import com.ruoyi.mall.service.IShopOrderDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商家订单-货款明细管理 控制器
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/user/shopOrder")
public class ShopOrderDetailController {

    @Autowired
    private IShopOrderDetailService shopOrderDetailService;

    @GetMapping("/list")
    public TableDataInfo<ShopOrderDetailVO> getMallShopOrderDetails(ShopOrderDetailDTO query, PageQuery pageQuery) {
        return shopOrderDetailService.getMallShopOrderDetails(query, pageQuery);

    }


    @PostMapping("/export")
    public void export(HttpServletResponse response, ShopOrderDetailDTO dto) {
        List<ShopOrderDetailVO> list = shopOrderDetailService.exportFundDetails(dto);
        ExcelUtil.exportExcel(list, "商家货款明细数据", ShopOrderDetailVO.class, response);

    }


}
