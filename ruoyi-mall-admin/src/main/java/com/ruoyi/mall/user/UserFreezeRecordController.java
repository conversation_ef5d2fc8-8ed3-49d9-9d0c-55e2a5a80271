package com.ruoyi.mall.user;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.mall.domain.entity.UserFreezeRecord;
import com.ruoyi.mall.service.IUserFreezeRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 店铺支付管理控制器
 */
@RestController
@RequestMapping("/mall/userFreezeRecord")
@RequiredArgsConstructor
public class UserFreezeRecordController extends BaseController {

    private final IUserFreezeRecordService userFreezeRecordService;

    /**
     * 根据电话号码查询店铺信息
     */
    @GetMapping("/getUserByPhone")
    @SaCheckPermission("mall:userFreezeRecord:getUserByPhone")
    public R<Map<String, Object>> getUserByPhone(@RequestParam("phone") String phone, @RequestParam("type") Integer type) {
        return R.ok(userFreezeRecordService.getUserByPhone(phone, type));
    }

    /**
     * 获取店铺货款冻结记录
     */
    @GetMapping("/getFreezeRecords")
    @SaCheckPermission("mall:userFreezeRecord:getFreezeRecords")
    public TableDataInfo<UserFreezeRecord> getFreezeRecords(PageQuery pageQuery,
                                                            @RequestParam(required = false) String shopName,
                                                            @RequestParam(required = false) String shopPhone) {
        return userFreezeRecordService.getFreezeRecords(pageQuery, shopName, shopPhone);
    }

    /**
     * 冻结店铺货款
     */
    @PostMapping("/freezeAmount")
    @SaCheckPermission("mall:userFreezeRecord:freezeAmount")
    @RepeatSubmit
    @Log(title = "店铺货款冻结", businessType = BusinessType.UPDATE)
    public R<Void> freezeAmount(@RequestBody Map<String, Object> params) {
        Long userId = Long.valueOf(params.get("userId").toString());
        BigDecimal amount = new BigDecimal(params.get("amount").toString());
        String reason = params.get("reason").toString();
        Integer type = Integer.valueOf(params.get("type").toString());
        return toAjax(userFreezeRecordService.freezeAmount(userId, amount, reason, type));
    }

}
