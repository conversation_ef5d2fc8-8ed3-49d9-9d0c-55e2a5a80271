package com.ruoyi.mall.user;

import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.dto.AccountAuditDTO;
import com.ruoyi.mall.domain.dto.AccountStatusDTO;
import com.ruoyi.mall.domain.entity.UserAccountAudit;
import com.ruoyi.mall.domain.vo.AccountStatusVO;
import com.ruoyi.mall.mapper.AccountAuditMapper;
import com.ruoyi.mall.mapper.ShopMapper;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.mall.service.IAccountAuditService;
import com.ruoyi.mall.service.IAccountService;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.sms.enums.SmsType;
import com.ruoyi.sms.service.SmsLogService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static cn.dev33.satoken.SaManager.log;

/**
 * 账户注销 前端控制器
 *
 * <AUTHOR>
 */

@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/user/account")
public class AccountController extends BaseController {


    private final IAccountAuditService accountAuditService;
    private final AccountAuditMapper accountAuditMapper;
    private final SmsLogService smsLogService;
    private final IConfigSettingService configSettingService;
    private final SysUserMapper sysUserMapper;
    private final ISysUserService userService;
    @Resource
    private SysUserMapper userMapper;
    @Resource
    private TzUserMapper tzUserMapper;
    @Resource
    private ShopMapper shopMapper;
    @Resource
    private IAccountService accountService;

    /**
     * 查询账户注销审核记录列表
     *
     * @param userAccountAudit
     * @param pageQuery
     * @return
     */
    @GetMapping("/listRecord")
    public TableDataInfo<UserAccountAudit> listRecord(UserAccountAudit userAccountAudit, PageQuery pageQuery) {
        return accountAuditService.selectAccountRecordPage(userAccountAudit, pageQuery);
    }


    /**
     * 查询账户注销审核记录列表
     *
     * @param userAccountAudit
     * @param pageQuery
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo<UserAccountAudit> list(UserAccountAudit userAccountAudit, PageQuery pageQuery) {
        return accountAuditService.selectAccountAuditPage(userAccountAudit, pageQuery);
    }

    /**
     * 审核账户注销申请
     */
    @Log(title = "审核账户注销申请", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/audit")
    public R<Void> audit(@Validated @RequestBody AccountAuditDTO accountAuditDTO) {
        return toAjax(accountAuditService.auditAccount(accountAuditDTO));
    }


    @GetMapping("/status")
    public R<AccountStatusVO> getAccountStatus() {
        Long userId = LoginHelper.getUserId();
//        return R.ok(accountService.getAccountStatus(userId));
        return R.ok();
    }

    @PostMapping("/status")
    public R<Void> saveAccountStatus(@Validated @RequestBody AccountStatusDTO dto) {
        Long userId = LoginHelper.getUserId();
        dto.setUserId(userId);
//        accountService.saveAccountStatus(dto);
        return R.ok();
    }

    // 正确逻辑思路
    // 1:发送验证码
    // 2:校验验证码，校验申请条件，创建注销审核记录
    // 3:平台进行审核


    // 错误逻辑
    // 发送验证码并且申请注销审核
    // 平台审核注销
    // 校验验证码，校验密码，注销账号


    /**
     * 发送账号注销短信验证码
     */
    @GetMapping("/sendDeleteAccountSmsCode")
    public R<Void> sendDeleteAccountSmsCode() {

/*


        // 1 账户注销申请逻辑处理
        // 1.1 创建注销审核记录
        Long userId = LoginHelper.getUserId();

        // 1 账户不能注销的业务逻辑处理
        if (!accountService.checkAccountCanBeDeleted(userId)) {
            return R.fail("您的账户存在异常状态，暂时无法注销，请处理以下事项后再申请：" +
                "\n1. 完成所有待付款/待发货/待收货订单" +
                "\n2. 所有 CB 订单需完成结算并满 30 天" +
                "\n3. 取消所有代销商品");
        }

        SysUser sysUser = sysUserMapper.selectById(userId);
        LambdaQueryWrapper<UserAccountAudit> lqwAudit = new LambdaQueryWrapper<>();
        lqwAudit.eq(UserAccountAudit::getUserId, userId)
            .eq(UserAccountAudit::getDelFlag, "0");
        lqwAudit.last("limit 1");
        UserAccountAudit userAccountAudit = accountAuditMapper.selectOne(lqwAudit);
        // TODO 确定审核状态
        if (userAccountAudit != null && !userAccountAudit.getAuditStatus().equals("1")){
            return R.fail("已经申请了注销或正在审核中");
        }

        // 1:商家，2:代销，3:消费者
        String userType = getLoginUser().getUserType();
        userAccountAudit = new UserAccountAudit();
        userAccountAudit.setAuditStatus("0");
        userAccountAudit.setCreateTime(new Date());
        userAccountAudit.setUserId(userId);
        userAccountAudit.setUsername(sysUser.getUserName());
        userAccountAudit.setNickname(sysUser.getNickName());
        userAccountAudit.setPhone(sysUser.getPhonenumber());
        userAccountAudit.setUserType(userType);
        int insert = accountAuditMapper.insert(userAccountAudit);
        if (insert<1) {
            return R.fail("创建注销审核记录失败");
        }


    */

        // 1 发送验证码
        Long userId = LoginHelper.getUserId();
        SysUser sysUser = sysUserMapper.selectUserById(userId);

        String phone = sysUser.getPhonenumber();
        if (StringUtils.isEmpty(phone)) {
            return R.fail("您尚未绑定手机号");
        }

        try {
            smsLogService.sendSms(SmsType.LOGOUT_ACCOUNT,
                userId.toString(),
                phone, new HashMap<>());
            return R.ok();
        } catch (Exception e) {
            log.error("发送短信验证码失败", e);
            return R.fail("发送验证码失败：" + e.getMessage());
        }
    }

    /**
     * 注销账号
     */
    @Log(title = "账号注销", businessType = BusinessType.DELETE)
    @PostMapping("/deleteAccount")
    @Transactional(rollbackFor = Exception.class)
    public R<Void> deleteAccount(@RequestBody Map<String, String> params) {
        Long userId = LoginHelper.getUserId();
        SysUser sysUser = sysUserMapper.selectById(userId);
        if (sysUser == null) {
            return R.fail("用户不存在");
        }

        String isShop = sysUser.getIsShop();
        String isConsignment = sysUser.getIsConsignment();


        if (!"1".equals(isConsignment) && !"1".equals(isShop)) {
            return R.fail("非代销商或商户不能注销");
        }


        // 2:校验验证码和密码，校验申请条件，创建注销审核记录
        // 2.1 验证码和密码验证逻辑处理
        String phone = sysUser.getPhonenumber();
        String code = params.get("code");
        String password = params.get("password");

        if (StringUtils.isEmpty(code) || StringUtils.isEmpty(password)) {
            return R.fail("参数不完整");
        }

        // 2.1.1 验证短信验证码
        boolean validCode = smsLogService.checkValidCode(phone, code, getUserId().toString(), SmsType.LOGOUT_ACCOUNT);
        if (!validCode) {
            return R.fail("验证码验证失败，请重新输入");
        }

        // 2.1.2 验证密码
        if (!BCrypt.checkpw(password, sysUser.getPassword())) {
            return R.fail("密码错误");
        }


        // 2.2 校验申请条件
        // 2.2.1 账户不能注销的业务逻辑处理
        /*if (!accountService.checkAccountCanBeDeleted(userId)) {
            return R.fail("您的账户存在异常状态，暂时无法注销，请处理以下事项后再申请：" +
                "\n1. 完成所有待付款/待发货/待收货订单" +
                "\n2. 所有 CB 订单需完成结算并满 30 天" +
                "\n3. 取消所有代销商品");
        }*/
        boolean canBeDeleted = accountService.checkAccountCanBeDeleted(userId);
        System.out.println("=============>>>>>>>>>>>>> 业务逻辑是否符合注销：" + canBeDeleted);


        // 3 创建注销审核记录
        // 3.1 创建注销审核记录
        //SysUser sysUser = sysUserMapper.selectById(userId);
        LambdaQueryWrapper<UserAccountAudit> lqwAudit = new LambdaQueryWrapper<>();
        lqwAudit.eq(UserAccountAudit::getUserId, userId);
        lqwAudit.last("limit 1");
        UserAccountAudit userAccountAudit = accountAuditMapper.selectOne(lqwAudit);
        // TODO 确定审核状态
        /*if (userAccountAudit != null && !userAccountAudit.getAuditStatus().equals("1")) {
            return R.fail("已经申请了注销或正在审核中");
        }*/
        if (userAccountAudit != null && userAccountAudit.getAuditStatus().equals("0")) {
            return R.fail("已经申请了注销，正在审核中！");
        }
        if (userAccountAudit != null && !userAccountAudit.getAuditStatus().equals("0")) {
            // 拒绝后商注销用户再次发起请求等待审核
            LambdaQueryWrapper<UserAccountAudit> lqw = new LambdaQueryWrapper<>();
            lqw.eq(UserAccountAudit::getUserId, userId);

            UserAccountAudit accountAudit = new UserAccountAudit();

            BeanUtils.copyProperties(userAccountAudit, accountAudit);

            accountAudit.setAuditTime(new Date());
            accountAudit.setAuditStatus("0");
            accountAudit.setAuditRemark("");
            accountAudit.setCreateTime(new Date());
            accountAudit.setUpdateTime(new Date());

            int update = accountAuditMapper.update(accountAudit, lqw);
            if (update < 1) {
                return R.fail("再次申请注销失败！");
            }


            /*boolean update = update(userAccountAudit, lqw);
            if (!update) {
                return R.fail("再次申请注销失败！");
            }*/

            return R.ok("已经申请了注销，正在审核中！");
        }

        String userType = getLoginUser().getUserType();
        if (userAccountAudit == null) {
            // 1:商家，2:代销，3:消费者
            userAccountAudit = new UserAccountAudit();
            userAccountAudit.setAuditStatus("0");
            userAccountAudit.setCreateTime(new Date());
            userAccountAudit.setUserId(userId);
            userAccountAudit.setUsername(sysUser.getUserName());
            userAccountAudit.setNickname(sysUser.getNickName());
            userAccountAudit.setPhone(sysUser.getPhonenumber());
            userAccountAudit.setUserType(userType);
            int insert = accountAuditMapper.insert(userAccountAudit);
            if (insert < 1) {
                return R.fail("创建注销审核记录失败");
            }

            // 为空时再次查询确认
            /*userAccountAudit = accountAuditMapper.selectOne(lqwAudit);
            // TODO 确定审核状态
            if (userAccountAudit != null && !userAccountAudit.getAuditStatus().equals("1")) {
                return R.fail("已经申请了注销或正在审核中");
            }*/

        }

        return R.ok("注销申请正在审核中！");

//        return R.fail("已经申请了注销或正在审核中");

        // 4 注销账户逻辑处理
       /* try {


            Long tzUserId = sysUser.getTzUserId();

            TzUser tzUser;

            // 4.1 商家 B
            if (*//*"1".equals(userType) && *//*"1".equals(sysUser.getIsShop())) {
                // 4.1 处理B 要删除 tz_user、mall_shop和sys_user
                // 4.1.1 删除用户表tz_user
                LambdaQueryWrapper<TzUser> tzUserQW = new LambdaQueryWrapper<>();
                tzUserQW.eq(TzUser::getUserId, tzUserId)
                    *//*.eq(TzUser::getUserType, "B")*//*;
                tzUser = tzUserMapper.selectOne(tzUserQW);
                if (tzUser != null) {
                    int updateTzUser = tzUserMapper.updateTzUserDelFagByUserId(tzUserId, "2");
                    log.error("===============tz_user===========>>>>>>>>>>>>>>>>", updateTzUser);
                    System.out.println("===============tz_user===========>>>>>>>>>>>>>>>>" + updateTzUser);
                }
                // 4.1.2 删除商家信息
                LambdaQueryWrapper<Shop> shopQW = new LambdaQueryWrapper<>();
                shopQW.eq(Shop::getUserId, userId);
                Shop shop = shopMapper.selectOne(shopQW);
                if (shop != null) {
                    int updateShop = shopMapper.updateShopDelFagByUserId(userId, "2");
                    log.error("=============mall_shop=============>>>>>>>>>>>>>>>>", updateShop);
                    System.out.println("=============mall_shop=============>>>>>>>>>>>>>>>>" + updateShop);
                }
                // 4.1.3 删除用户表sys_user
                LambdaQueryWrapper<SysUser> sysUserQW = new LambdaQueryWrapper<>();
                sysUserQW.eq(SysUser::getUserId, userId)
                   *//* .eq(SysUser::getUserType, "1")*//*
                    .eq(SysUser::getIsShop, "1");
                sysUser = sysUserMapper.selectOne(sysUserQW);
                if (sysUser != null) {
                    int updateSysUser = sysUserMapper.updateSysUserDelFagByUserId(userId, "2");
                    log.error("=============sys_user=============>>>>>>>>>>>>>>>>", updateSysUser);
                    System.out.println("=============sys_user=============>>>>>>>>>>>>>>>>" + updateSysUser);
                }

                return R.ok("账号注销成功");
            }

            // 4.2 代销商 CB
            if (*//*"2".equals(userType) &&*//* "1".equals(sysUser.getIsConsignment())) {
                // 4.1 处理B 要删除 tz_user、mall_shop和sys_user
                // 4.1.1 删除用户表tz_user
                LambdaQueryWrapper<TzUser> tzUserQW = new LambdaQueryWrapper<>();
                tzUserQW.eq(TzUser::getUserId, tzUserId)
                    *//*.eq(TzUser::getUserType, "CB")*//*;
                tzUser = tzUserMapper.selectOne(tzUserQW);
                if (tzUser != null) {
                    int updateTzUser = tzUserMapper.updateTzUserDelFagByUserId(tzUserId, "2");
                    log.error("===============tz_user===========>>>>>>>>>>>>>>>>", updateTzUser);
                    System.out.println("===============tz_user===========>>>>>>>>>>>>>>>>" + updateTzUser);
                }
                // 4.1.2 删除商家信息
                LambdaQueryWrapper<Shop> shopQW = new LambdaQueryWrapper<>();
                shopQW.eq(Shop::getUserId, userId);
                Shop shop = shopMapper.selectOne(shopQW);
                if (shop != null) {
                    int updateShop = shopMapper.updateShopDelFagByUserId(userId, "2");
                    log.error("=============mall_shop=============>>>>>>>>>>>>>>>>", updateShop);
                    System.out.println("=============mall_shop=============>>>>>>>>>>>>>>>>" + updateShop);
                }
                // 4.1.3 删除用户表sys_user
                LambdaQueryWrapper<SysUser> sysUserQW = new LambdaQueryWrapper<>();
                sysUserQW.eq(SysUser::getUserId, userId)
                    *//*.eq(SysUser::getUserType, "2")*//*
                    .eq(SysUser::getIsConsignment, "1");
                sysUser = sysUserMapper.selectOne(sysUserQW);
                if (sysUser != null) {
                    int updateSysUser = sysUserMapper.updateSysUserDelFagByUserId(userId, "2");
                    log.error("=============sys_user=============>>>>>>>>>>>>>>>>", updateSysUser);
                    System.out.println("=============sys_user=============>>>>>>>>>>>>>>>>"+ updateSysUser);
                }

                return R.ok("账号注销成功");
            }


            return R.fail("账号注销失败");

        } catch (Exception e) {
            log.error("账号注销失败", e);
            throw new ServiceException("账号注销失败：" + e.getMessage());
        }*/
    }


}
