package com.ruoyi.mall.user;


import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.mall.domain.vo.ShopPromotionReqVO;
import com.ruoyi.mall.domain.vo.ShopPromotionRespVO;
import com.ruoyi.mall.domain.vo.ShopPromotionVO;
import com.ruoyi.mall.service.IUserPromotionService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商家平台促销金管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/user/promotion")
public class UserPromotionController extends BaseController {

    private final IUserPromotionService userPromotionService;


    /**
     * 查询商家平台促销金列表
     */
    @GetMapping("/list")
    public TableDataInfo<ShopPromotionVO> list(ShopPromotionVO shopPromotion, PageQuery pageQuery) {
        return userPromotionService.queryUserPromotionList(shopPromotion, pageQuery);
    }

    /**
     * 查询每个商家平台促销金明细列表
     */
    @GetMapping("/listPromotionDetail")
    public TableDataInfo<ShopPromotionRespVO> listPromotionDetail(ShopPromotionReqVO shopPromotion, PageQuery pageQuery) {
        return userPromotionService.pageUserPromotionDetail(shopPromotion, pageQuery);
    }


    /**
     * 导出商家平台促销金明细列表
     */
    @PostMapping("/exportPromotionDetail")
    public void exportPromotionDetail(ShopPromotionReqVO shopPromotion, HttpServletResponse response) {
        List<ShopPromotionRespVO> list = userPromotionService.listUserPromotionDetail(shopPromotion);
        ExcelUtil.exportExcel(list, "商家促销金明细", ShopPromotionRespVO.class, response);
    }


}
