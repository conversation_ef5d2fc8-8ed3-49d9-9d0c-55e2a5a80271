package com.ruoyi.mall.shop;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.mall.domain.vo.ShopDonationRecordVo;
import com.ruoyi.mall.service.IShopDonationService;
import com.ruoyi.mall.service.IShopService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商家促销金赠送控制器
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/shopDonation")
public class ShopDonationController extends BaseController {

    private final IShopDonationService shopDonationService;
    private final IShopService shopService;

    /**
     * 商家赠送促销金给用户
     */
    @Log(title = "商家赠送促销金", businessType = BusinessType.INSERT)
    @PostMapping("/donate")
    public R<Void> donatePromotionGold(@RequestBody @Validated ShopDonationRequest request) {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 获取商家ID
        Long shopId = shopService.getShopIdByUserId(userId);
        if (shopId == null) {
            return R.fail("未找到商家信息");
        }

        return toAjax(shopDonationService.donatePromotionGold(
            shopId, request.getPhone(), request.getAmount(), request.getPassword(), request.getRemark()));
    }

    /**
     * 代销赠送促销金给用户
     */
    @Log(title = "代销赠送促销金", businessType = BusinessType.INSERT)
    @PostMapping("/donateByDistributor")
    public R<Void> donatePromotionGoldByDistributor(@RequestBody @Validated ShopDonationRequest request) {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 获取商家ID
        Long shopId = shopService.getShopIdByUserId(userId);
        if (shopId == null) {
            return R.fail("未找到商家信息");
        }

        return toAjax(shopDonationService.donatePromotionGoldByDistributor(
            shopId, request.getPhone(), request.getAmount(), request.getPassword(), request.getRemark()));
    }

    /**
     * 查询赠送记录列表
     */
    @GetMapping("/list")
    public TableDataInfo<ShopDonationRecordVo> list(String beginTime, String endTime, String phone, PageQuery pageQuery) {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 获取商家ID
        Long shopId = shopService.getShopIdByUserId(userId);
        if (shopId == null) {
            return TableDataInfo.build();
        }

        return shopDonationService.getDonationRecords(DateUtils.parseDate(beginTime), DateUtils.parseDate(endTime), shopId, phone, getLoginUser().getShopType(), pageQuery);
    }

    /**
     * 导出赠送记录列表
     */
    @Log(title = "导出促销金赠送记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(String beginTime, String endTime, String phone, HttpServletResponse response) {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 获取商家ID
        Long shopId = shopService.getShopIdByUserId(userId);
        if (shopId == null) {
            return;
        }

        List<ShopDonationRecordVo> list = shopDonationService.getDonationRecordsForExport(DateUtils.parseDate(beginTime), DateUtils.parseDate(endTime), shopId, phone, getLoginUser().getShopType());
        ExcelUtil.exportExcel(list, "赠送记录数据", ShopDonationRecordVo.class, response);
    }

    /**
     * 促销金赠送请求对象
     */
    @Data
    public static class ShopDonationRequest {

        /**
         * 开始时间
         */
        private String beginTime;

        /**
         * 结束时间
         */
        private String endTime;

        /**
         * 接收用户手机号
         */
        @NotBlank(message = "用户手机号不能为空")
        private String phone;

        /**
         * 赠送金
         */
        @NotNull(message = "赠送金不能为空")
        @DecimalMin(value = "0.01", message = "赠送金必须大于0")
        private BigDecimal amount;

        /**
         * 操作密码
         */
        @NotBlank(message = "操作密码不能为空")
        private String password;

        /**
         * 备注
         */
        private String remark;
    }
}
