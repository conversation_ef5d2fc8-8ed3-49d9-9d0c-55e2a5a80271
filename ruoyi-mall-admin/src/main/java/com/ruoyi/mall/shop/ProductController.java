package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.mall.domain.dto.AuditProductDto;
import com.ruoyi.mall.domain.dto.ProductDTO;
import com.ruoyi.mall.domain.dto.ProductDaixiaoDTO;
import com.ruoyi.mall.domain.entity.Category;
import com.ruoyi.mall.domain.entity.Product;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.Sku;
import com.ruoyi.mall.domain.query.QueryConsignmentProductDTO;
import com.ruoyi.mall.domain.vo.DaiXiaoInfoVo;
import com.ruoyi.mall.domain.vo.ProductExportVo;
import com.ruoyi.mall.service.*;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品管理
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/mall/product")
public class ProductController extends BaseController {

    private final IProductService productService;
    private final ICategoryService categoryService;
    private final ISkuService skuService;
    private final IShopService shopService;
    private final ISysUserService sysUserService;
    private final IVirtualOrderService virtualOrderService;

    /**
     * 查询商品列表
     */
    @SaCheckPermission("mall:product:list")
    @GetMapping("/list")
    public TableDataInfo<Product> list(Product product, PageQuery pageQuery) {
        product.setShopId(getLoginUser().getShopId());
        return productService.queryPageList(product, pageQuery);
    }

    /**
     * 获取商品详细信息
     */
    @SaCheckPermission("mall:product:query")
    @GetMapping("/{productId}")
    public R<Product> getInfo(@PathVariable Long productId) {
        // 获取商品基本信息
        Product product = productService.getById(productId);
        if (product == null) {
            return R.fail("商品不存在");
        }

        // 获取SKU列表
        List<Sku> skuList = skuService.lambdaQuery().eq(Sku::getProductId, productId).list();
        product.setSkuList(skuList);

        return R.ok(product);
    }

    /**
     * 新增商品
     */
    @SaCheckPermission("mall:product:add")
    @Log(title = "新增商品", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit()
    public R<Long> add(@Validated @RequestBody ProductDTO productDto) {
        Product product = new Product();
        BeanUtils.copyProperties(productDto, product);
        // 设置店铺ID
        product.setShopId(getLoginUser().getShopId());
        // 校验商品名称是否唯一
        if (!productService.checkProductNameUnique(product)) {
            return R.fail("新增商品'" + product.getName() + "'失败，商品名称已存在");
        }

        // 设置商品默认上架状态为下架
        product.setStatus("0");
        // 新上架的商品待审核
        product.setAuditStatus("0");

        return R.ok(productService.saveProductWithSkus(product));
    }

    /**
     * 修改商品
     */
    @SaCheckPermission("mall:product:edit")
    @PutMapping
    @Log(title = "修改商品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<Void> edit(@Validated @RequestBody ProductDTO productDto) {
        Product product = new Product();
        BeanUtils.copyProperties(productDto, product);
        // 设置店铺ID
        product.setShopId(getLoginUser().getShopId());
        // 校验商品名称是否唯一
        if (!productService.checkProductNameUnique(product)) {
            return R.fail("修改商品'" + product.getName() + "'失败，商品名称已存在");
        }

        return toAjax(productService.updateProductWithSkus(product));
    }

    /**
     * 保存商品代销信息
     */
    @SaCheckPermission("mall:product:editDaixiao")
    @PutMapping("/editDaixiao")
    @Log(title = "保存商品代销信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<Void> editDaixiao(@RequestBody ProductDaixiaoDTO productDaixiaoDTO) {
        SysUser sysUser = sysUserService.getById(getUserId());
        if (sysUser == null) {
            return R.fail("用户不存在");
        }
        String isShop = sysUser.getIsShop();
        if (!isShop.equals("1")) {
            return R.fail("用户并非是商家");
        }
        Product product = new Product();
        BeanUtils.copyProperties(productDaixiaoDTO, product);
        // 取消代销，直接成功
        if (productDaixiaoDTO.getIsDaixiao().equals(0)) {
            return toAjax(productService.updateById(product));
        }

        Long shopId = getLoginUser().getShopId();
        // 可以代销的数量
        Long count = virtualOrderService.getConsignmentCount(sysUser.getUserId(), shopId);
        if (count == null) {
            return R.fail("还没有开通代销权限");
        }

        // 判断当前商品在代销
        Long id = productDaixiaoDTO.getId();
        Product dbProduct = productService.getById(id);
        if (dbProduct == null) {
            return R.fail("商品不存在");
        }
        Integer isDaixiao = dbProduct.getIsDaixiao();
        // 在代销
        if (isDaixiao == null || isDaixiao == 0) {
            if (count - 1 < 0) {
                return R.fail("代销数量不足");
            }
        } else {
            if (count < 0) {
                return R.fail("代销数量不足");
            }
        }
        return toAjax(productService.updateById(product));
    }

    /**
     * 审核商品
     */
    @SaCheckPermission("mall:product:audit")
    @RepeatSubmit()
    @Log(title = "审核商品", businessType = BusinessType.UPDATE)
    @PostMapping("/audit")
    public R<Void> audit(@Validated @RequestBody AuditProductDto auditProductDto) {
        Product product = productService.getById(auditProductDto.getId());
        if (product == null) {
            return R.fail("商品不存在");
        }
        if ("1".equals(product.getAuditStatus()) || "2".equals(product.getAuditStatus())) {
            return R.fail("商品已审核");
        }
        product.setAuditStatus(auditProductDto.getAuditStatus());
        // 审核通过，直接上线
        if (auditProductDto.getAuditStatus().equals("1")) {
            product.setStatus("1");
        }
        product.setAuditRemake(auditProductDto.getAuditRemake());
        return toAjax(productService.updateById(product));
    }

    /**
     * 批量审核商品
     */
    @SaCheckPermission("mall:product:batchAudit")
    @RepeatSubmit()
    @Log(title = "批量审核商品", businessType = BusinessType.UPDATE)
    @PostMapping("/batchAudit")
    public R<Void> batchAudit(@Validated @RequestBody AuditProductDto auditProductDto) {
        Long[] ids = auditProductDto.getIds();
        if (ObjectUtil.isNull(ids)) {
            return R.fail("未勾选商品");
        }
        if (auditProductDto.getAuditStatus() == null) {
            auditProductDto.setAuditStatus("1");
        }
        List<Product> products = productService.listByIds(Arrays.asList(ids));
        List<Long> collect = products.stream()
            .filter((product) -> product.getAuditStatus().equals("0"))
            .map(Product::getId).collect(Collectors.toList());

        LambdaUpdateWrapper<Product> qw = new LambdaUpdateWrapper<>();
        qw.in(Product::getId, collect)
            .set(Product::getAuditStatus, auditProductDto.getAuditStatus())
            .set(Product::getAuditRemake, auditProductDto.getAuditRemake())
            .set(auditProductDto.getAuditStatus().equals("1"), Product::getStatus, "1");

        return toAjax(productService.update(qw));
    }

    /**
     * 商品上架
     */
    @RepeatSubmit()
    @SaCheckPermission("mall:product:publish")
    @Log(title = "商品上架", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{productId}")
    public R<Void> publish(@PathVariable Long productId) {
        Product product = productService.getById(productId);
        if (product == null) {
            return R.fail("商品不存在");
        }

        // 检查商品库存
        if (product.getStock() <= 0) {
            return R.fail("商品库存不足，无法上架");
        }

        // 检查商品是否已通过审核
        if ("0".equals(product.getAuditStatus()) || "2".equals(product.getAuditStatus())) {
            return R.fail("商品未通过审核，无法上架");
        }

        product.setStatus("1"); // 使用status作为上架状态
        return toAjax(productService.updateById(product));
    }

    /**
     * 商品下架
     */
    @RepeatSubmit()
    @SaCheckPermission("mall:product:unpublish")
    @Log(title = "商品下架", businessType = BusinessType.UPDATE)
    @PutMapping("/unpublish/{productId}")
    public R<Void> unpublish(@PathVariable Long productId) {
        Product product = productService.getById(productId);
        if (product == null) {
            return R.fail("商品不存在");
        }

        product.setStatus("0"); // 使用status作为下架状态
        return toAjax(productService.updateById(product));
    }

    /**
     * 批量上架商品
     */
    @RepeatSubmit()
    @SaCheckPermission("mall:product:batchPublish")
    @Log(title = "批量上架商品", businessType = BusinessType.UPDATE)
    @PutMapping("/batchPublish")
    public R<Void> batchPublish(@RequestBody List<Long> productIds) {
        if (productIds == null || productIds.isEmpty()) {
            return R.fail("请选择要上架的商品");
        }

        // 获取所有选中的商品
        List<Product> products = productService.listByIds(productIds);
        if (products.isEmpty()) {
            return R.fail("未找到有效的商品");
        }

        // 筛选出已审核通过的商品ID
        List<Long> approvedProductIds = products.stream()
            .filter(p -> "1".equals(p.getAuditStatus()) && p.getStock() > 0)
            .map(Product::getId)
            .collect(Collectors.toList());

        if (approvedProductIds.isEmpty()) {
            return R.fail("选中的商品中没有符合上架条件的商品");
        }

        // 如果有些商品未被筛选，说明它们不符合上架条件
        if (approvedProductIds.size() < productIds.size()) {
            // 只上架符合条件的商品
            boolean result = productService.batchUpdatePublishStatus(approvedProductIds, "1");
            return result ? R.ok("部分商品上架成功，未审核或库存不足的商品已跳过") : R.fail("批量上架失败");
        } else {
            // 所有商品都符合条件
            return toAjax(productService.batchUpdatePublishStatus(approvedProductIds, "1"));
        }
    }

    /**
     * 批量下架商品
     */
    @RepeatSubmit()
    @SaCheckPermission("mall:product:batchUnpublish")
    @Log(title = "批量下架商品", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUnpublish")
    public R<Void> batchUnpublish(@RequestBody List<Long> productIds) {
        if (productIds == null || productIds.isEmpty()) {
            return R.fail("请选择要下架的商品");
        }

        return toAjax(productService.batchUpdatePublishStatus(productIds, "0"));
    }

    /**
     * 设置商品推荐状态
     */
    @SaCheckPermission("mall:product:recommend")
    @Log(title = "设置商品推荐状态", businessType = BusinessType.UPDATE)
    @PutMapping("/recommend/{productId}/{status}")
    public R<Void> setRecommendStatus(@PathVariable Long productId, @PathVariable String status) {
        Product product = new Product();
        product.setId(productId);
        product.setIsRecommend(status);
        return toAjax(productService.updateById(product));
    }

    /**
     * 设置商品新品状态
     */
    @SaCheckPermission("mall:product:setNewStatus")
    @Log(title = "设置商品新品状态", businessType = BusinessType.UPDATE)
    @PutMapping("/new/{productId}/{status}")
    public R<Void> setNewStatus(@PathVariable Long productId, @PathVariable String status) {
        Product product = new Product();
        product.setId(productId);
        product.setIsNew(status);
        return toAjax(productService.updateById(product));
    }

    /**
     * 删除商品
     */
    @SaCheckPermission("mall:product:remove")
    @Log(title = "删除商品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{productIds}")
    public R<Void> remove(@PathVariable Long[] productIds) {
        return toAjax(productService.deleteProductByIds(productIds));
    }

    /**
     * 校验商品名称是否唯一
     */
    @GetMapping("/checkProductNameUnique")
    public R<Boolean> checkProductNameUnique(Product product) {
        product.setShopId(getLoginUser().getShopId());
        return R.ok(productService.checkProductNameUnique(product));
    }

    /**
     * 查询店铺可以代销的数量
     */
    @GetMapping("/getConsignmentCount")
    public R<Long> getConsignmentCount() {
        SysUser sysUser = sysUserService.getById(getUserId());
        if (sysUser == null) {
            return R.fail("用户不存在");
        }
        String isShop = sysUser.getIsShop();
        if (!StrUtil.equals(isShop, "1")) {
            return R.fail("用户并非是商家");
        }
        Long shopId = getLoginUser().getShopId();
        // 并且返回可以代销的数量
        Long count = virtualOrderService.getConsignmentCount(sysUser.getUserId(), shopId);
        return R.ok(count);
    }

    /**
     * 获取店铺的分类信息
     */
    @GetMapping("/getShopCategory")
    public R<Map<String, Object>> getShopCategory() {
        Long shopId = getLoginUser().getShopId();
        // 获取店铺信息
        Shop shop = shopService.getById(shopId);
        if (shop == null) {
            return R.fail("店铺不存在");
        }

        Map<String, Object> result = new HashMap<>();

        // 如果店铺还未确定分类
        if (shop.getShopCategoryId() == null) {
            result.put("hasCategory", false);
            result.put("message", "该店铺尚未确定商品分类");
            return R.ok(result);
        }

        // 获取店铺已确定的分类
        Category shopCategory = categoryService.getById(shop.getShopCategoryId());
        if (shopCategory == null) {
            return R.fail("店铺分类信息不存在");
        }

        // 获取同级分类列表
        List<Category> siblingCategories = categoryService.list(
            new LambdaQueryWrapper<Category>()
                .eq(Category::getParentId, shopCategory.getParentId())
                .eq(Category::getDelFlag, "0")
                .orderByAsc(Category::getSort)
        );

        // 构建返回结果
        result.put("hasCategory", true);
        result.put("shopCategory", shopCategory);
        result.put("siblingCategories", siblingCategories);

        return R.ok(result);
    }

    /**
     * 当前代销用户获取能代销的产品
     *
     * @param pageQuery 分页参数
     * @return
     */
    @GetMapping("/getDaiXiaoProductList")
    public TableDataInfo<DaiXiaoInfoVo> getDaiXiaoProductList(QueryConsignmentProductDTO query, PageQuery pageQuery) {
        Long shopId = getLoginUser().getShopId();
        if (shopId == null) {
            throw new ServiceException("当前账号并非商家");
        }
        return productService.getDaiXiaoProductList(getUserId(), query, pageQuery);
    }

    /**
     * 根据店铺ID获取店铺下的代销产品
     *
     * @param pageQuery 分页参数
     * @return
     */
    @GetMapping("/getShopDaiXiaoProductList")
    public TableDataInfo<DaiXiaoInfoVo> getShopDaiXiaoProductList(QueryConsignmentProductDTO query, PageQuery pageQuery) {
        Long shopId = getLoginUser().getShopId();
        if (shopId == null) {
            throw new ServiceException("当前账号并非商家");
        }
        return productService.getShopDaiXiaoProductList(shopId, query, pageQuery);
    }

    /**
     * 修改代销产品链接
     *
     * @param productId 代销产品ID
     */
    @PutMapping("/editDaiXiaoProductUrl/{productId}")
    public R<Void> editDaiXiaoProductUrl(@PathVariable Long productId) {
        return toAjax(productService.updateDaiXiaoProductUrl(productId));
    }


    /**
     * 商家取消代销产品
     *
     * @param productId 产品ID
     * @return
     */
    @PutMapping("/cancel/consignmentProduct")
    public R<Void> cancelConsignment(@RequestParam Long productId) {
        productService.cancelConsignmentProduct(productId);
        return R.ok();
    }

    /**
     * 导出商品列表
     */
    @SaCheckPermission("mall:product:export")
    @Log(title = "导出商品", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(Product product, HttpServletResponse response) {
        product.setShopId(getLoginUser().getShopId());
        List<Product> list = productService.selectProductList(product);

        // 转换为导出VO
        List<ProductExportVo> exportList = new ArrayList<>();
        for (Product p : list) {
            ProductExportVo vo = new ProductExportVo();
            vo.setId(p.getId());
            vo.setName(p.getName());
            vo.setCategoryName(p.getCategoryName());
            vo.setPrice(p.getPrice());
            vo.setStock(p.getStock());
            vo.setSales(p.getSales());
            vo.setStatus("1".equals(p.getStatus()) ? "上架中" : "已下架");
            vo.setIsRecommend("1".equals(p.getIsRecommend()) ? "是" : "否");
            vo.setIsNew("1".equals(p.getIsNew()) ? "是" : "否");
            vo.setIsHot("1".equals(p.getIsHot()) ? "是" : "否");
            vo.setCreateTime(p.getCreateTime());
            exportList.add(vo);
        }
        ExcelUtil.exportExcel(exportList, "商品数据", ProductExportVo.class, response);
    }
}
