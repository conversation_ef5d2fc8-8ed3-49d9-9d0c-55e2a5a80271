package com.ruoyi.mall.shop;


import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.mall.domain.entity.ShopAdvertisement;
import com.ruoyi.mall.service.IShopAdvertisementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 商铺广告
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/mall/shopAd")
@Validated
public class ShopAdvertisementController extends BaseController {


    private final IShopAdvertisementService shopAdvertisementService;

    /**
     * 获取当前店铺广告列表
     *
     * @param shopAdv   搜索条件
     * @param pageQuery 分页参数
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo<ShopAdvertisement> getShopAdvertisementList(ShopAdvertisement shopAdv, PageQuery pageQuery) {
        LoginUser loginUser = getLoginUser();
        shopAdv.setShopId(loginUser.getShopId());
        return shopAdvertisementService.selectShopAdvertisementList(shopAdv, pageQuery);
    }

    /**
     * 获取全部店铺广告列表
     *
     * @param shopAdv   搜索条件
     * @param pageQuery 分页参数
     * @return
     */
    @GetMapping("/shopAdvList")
    public TableDataInfo<ShopAdvertisement> getAdvertisementList(ShopAdvertisement shopAdv, PageQuery pageQuery) {
        return shopAdvertisementService.selectAdvertisementList(shopAdv, pageQuery);
    }

    /**
     * 新增店铺广告
     *
     * @param shopAdvertisement 广告对象
     * @return
     */
    @PostMapping("/add")
    @Log(title = "新增广告", businessType = BusinessType.INSERT)
    public R<Void> addShopAdvertisement(@RequestBody @Validated ShopAdvertisement shopAdvertisement) {
        return toAjax(shopAdvertisementService.addShopAdvertisement(shopAdvertisement));
    }

    /**
     * 修改广告
     *
     * @param shopAdvertisement 广告对象
     * @return
     */
    @PutMapping("/edit")
    @Log(title = "修改广告", businessType = BusinessType.UPDATE)
    public R<Void> updateShopAdvertisement(@RequestBody @Validated ShopAdvertisement shopAdvertisement) {
        if (shopAdvertisement.getId() == null) {
            return R.fail("广告ID不能为空");
        }

        return toAjax(shopAdvertisementService.updateShopAdvertisement(shopAdvertisement));
    }

    /**
     * 根据ID删除广告
     *
     * @param id 广告ID
     * @return
     */
    @DeleteMapping("/{id}")
    @Log(title = "删除广告", businessType = BusinessType.DELETE)
    public R<Void> deleteShopAdvertisement(@PathVariable Long id) {
        return toAjax(shopAdvertisementService.deleteShopAdvertisement(id));
    }

    /**
     * 批量删除广告ID
     *
     * @param ids 广告ID集合
     * @return
     */
    @DeleteMapping("/batch/{ids}")
    @Log(title = "批量删除广告", businessType = BusinessType.DELETE)
    public R<Void> deleteBatchShopAdvertisement(@PathVariable Long[] ids) {
        return toAjax(shopAdvertisementService.deleteBatchShopAdvertisement(ids));
    }

    /**
     * 根据ID查询广告详情
     *
     * @param id 广告ID
     * @return
     */
    @GetMapping("/{id}")
    public R<ShopAdvertisement> getShopAdvertisementById(@PathVariable Long id) {
        if (id == null) {
            return R.fail("广告ID不能为空");
        }

        ShopAdvertisement advertisement = shopAdvertisementService.selectShopAdvertisementById(id);
        if (advertisement == null) {
            return R.fail("广告不存在");
        }

        return R.ok(advertisement);
    }

    /**
     * 审核广告
     *
     * @param id        广告ID
     * @param auditData 审核数据，包含status和remark
     * @return 操作结果
     */
    @PostMapping("/audit/{id}")
    @Log(title = "审核广告", businessType = BusinessType.UPDATE)
    public R<Void> auditAdvertisement(@PathVariable Long id, @RequestBody Map<String, String> auditData) {
        if (id == null) {
            return R.fail("广告ID不能为空");
        }

        String status = auditData.get("status");
        String remark = auditData.get("remark");

        if (status == null) {
            return R.fail("审核状态不能为空");
        }

        try {
            return toAjax(shopAdvertisementService.auditAdvertisement(id, status, remark));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 批量审核广告
     *
     * @param ids       广告ID数组
     * @param auditData 审核数据，包含status和remark
     * @return 操作结果
     */
    @PostMapping("/batchAudit")
    @Log(title = "批量审核广告", businessType = BusinessType.UPDATE)
    public R<Void> batchAuditAdvertisement(@RequestBody Map<String, Object> auditData) {
        if (auditData.get("ids") == null) {
            return R.fail("广告ID不能为空");
        }

        Long[] ids = (Long[]) auditData.get("ids");
        String status = (String) auditData.get("status");
        String remark = (String) auditData.get("remark");

        if (status == null) {
            return R.fail("审核状态不能为空");
        }

        try {
            return toAjax(shopAdvertisementService.batchAuditAdvertisement(ids, status, remark));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
