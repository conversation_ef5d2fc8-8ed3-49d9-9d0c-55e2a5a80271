package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.common.constant.ConsignmentProductLimitConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OrderStatusEnum;
import com.ruoyi.common.enums.OrderVirtualStatusEnum;
import com.ruoyi.mall.domain.bo.VirtualOrderBo;
import com.ruoyi.mall.domain.entity.ConsignmentProduct;
import com.ruoyi.mall.domain.entity.OfflineDrainageRecord;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.domain.vo.VirtualOrderVo;
import com.ruoyi.mall.enums.VirtualTypeEnum;
import com.ruoyi.mall.service.*;
import com.ruoyi.mall.strategy.VirtualProduct.VirtualFactory;
import com.ruoyi.mall.strategy.VirtualProduct.VirtualStrategy;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.system.service.ISysUserService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;


/**
 * 虚拟商品订单管理与支付
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/virtual")
public class VirtualController extends BaseController {

    private final IVirtualOrderService virtualOrderService;
    private final VirtualFactory virtualFactory;
    private final ISysUserService sysUserService;
    private final IConfigSettingService configSettingService;
    private final IShopService shopService;
    private final IOfflineDrainageRecordService offlineDrainageRecordService;
    private final IConsignmentProductService consignmentProductService;

    /**
     * 查询虚拟商品订单列表
     */
    @GetMapping("/getLoginVirtualOrder")
    public R<TableDataInfo<VirtualOrder>> getLoginVirtualOrder(VirtualOrder virtualOrder, PageQuery pageQuery) {
        Long shopId = getLoginUser().getShopId();
        if (ObjUtil.isNotNull(shopId)) {
            virtualOrder.setUserId(getUserId());
        }
        TableDataInfo<VirtualOrder> order = virtualOrderService.getLoginVirtualOrder(virtualOrder, pageQuery);
        return R.ok(order);
    }

    /**
     * 创建虚拟商品订单
     */
    @PostMapping("/create/{type}")
    @SaCheckPermission("mall:virtual:create")
    @Log(title = "创建虚拟商品订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    public R<String> createVirtualOrder(@PathVariable Integer type, @RequestBody(required = false) Map<String, Object> map) {
        VirtualTypeEnum instance = VirtualTypeEnum.instance(type);
        if (null == instance) {
            return R.fail("未知的商品");
        }
        // 如果是权限功能类型，检查是否开启了权限功能
        if (VirtualTypeEnum.DAIXOAO_ONE.type().equals(type) ||
            VirtualTypeEnum.DAIXOAO_TWO.type().equals(type) ||
            VirtualTypeEnum.DAIXOAO_THERE.type().equals(type)) {
            JSONObject configValue = configSettingService.getConfigValue("configOne");
            if (configValue == null || "close".equals(configValue.getStr("permissionOpen"))) {
                return R.fail("权限功能未开启，无法开通");
            }
        }

        // 如果是广告功能类型，检查是否开启了技术引流功能
        if (VirtualTypeEnum.ADVERTISEMENT_PUTTING.type().equals(type)) {
            JSONObject configValue = configSettingService.getConfigValue("configSix");
            if (configValue == null || "close".equals(configValue.getStr("permissionOpen"))) {
                return R.fail("广告功能未开启，无法支付");
            }
        }

        // 如果是技术引流类型，检查是否开启了技术引流功能
        if (VirtualTypeEnum.JISHU_DRAINAGE.type().equals(type)) {
            JSONObject configValue = configSettingService.getConfigValue("configFive");
            if (configValue == null || "1".equals(configValue.getStr("isEnabled"))) {
                return R.fail("技术引流功能未开启，无法开通");
            }
        }

        // 如果是代销权限二类型，检查是否启用了代销权限二功能
        if (VirtualTypeEnum.CONSIGNMENT_LEVEL_TWO.type().equals(type)) {
            JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(12));
            if (configValue != null && Boolean.FALSE.equals(configValue.getBool("enabled"))) {
                return R.fail("代销权限二功能已禁用，暂时无法购买");
            }
        }
        Shop shop = shopService.getOne(new LambdaQueryWrapper<Shop>().eq(Shop::getUserId, getUserId()));
        if (shop == null) {
            return R.fail("当前账号并非商家");
        }
        VirtualOrderBo virtualOrderBo = new VirtualOrderBo();
        virtualOrderBo.setUserId(getUserId());
        virtualOrderBo.setPhone(shop.getPhone());
        virtualOrderBo.setUserName(shop.getBusinessName());
        virtualOrderBo.setProductName(instance.productName());
        if (VirtualTypeEnum.ADVERTISEMENT_PUTTING.type().equals(type)) {
            virtualOrderBo.setValidDays(7);
        } else {
            // 默认的都是30天
            virtualOrderBo.setValidDays(30);
        }
        VirtualStrategy paymentStrategy = virtualFactory.getPaymentStrategy(type);
        virtualOrderBo.setAmount(paymentStrategy.getAmount());

        // 处理不同类型订单的特殊参数
        if (map != null && !map.isEmpty()) {
            log.info("接收到订单创建额外参数: {}", map);

            // 如果是广告投放类型
            if (VirtualTypeEnum.ADVERTISEMENT_PUTTING.type().equals(type)) {
                // 将广告ID放入备注字段
                virtualOrderBo.setOrderId(Long.valueOf(map.get("adId").toString()));
                virtualOrderBo.setValidDays(7);
            }
            // 如果是技术引流类型
            if (VirtualTypeEnum.JISHU_DRAINAGE.type().equals(type)) {
                Object o = map.get("drainageCount");
                if (ObjUtil.isNull(o)) {
                    return R.fail("引流次数不能为空");
                }
                // 技术引流的次数
                int drainageCount = Integer.parseInt(o.toString());
                // 获取引流一次多少钱
                virtualOrderBo.setQuantity(drainageCount);
                // 设置金额
                virtualOrderBo.setAmount(paymentStrategy.getAmount().multiply(new BigDecimal(drainageCount)));
                virtualOrderBo.setProductName(VirtualTypeEnum.JISHU_DRAINAGE.productName());
            }
        }

        return R.ok("", virtualOrderService.createVirtualOrder(virtualOrderBo, instance));
    }

    /**
     * 根据订单编号查询虚拟订单详情
     */
    @GetMapping("/getVirtualOrderDetail/{orderNo}")
    @SaCheckPermission("mall:virtual:getVirtualOrderDetail")
    public R<VirtualOrderVo> getVirtualOrderDetail(@PathVariable String orderNo) {
        VirtualOrderVo order = virtualOrderService.getVirtualOrderDetail(orderNo);
        return R.ok(order);
    }

    /**
     * 获取代销权限信息
     */
    @GetMapping("/consignmentPermissionInfo")
    @SaCheckPermission("mall:virtual:info")
    public R<Map<String, Object>> getConsignmentPermissionInfo() {
        Shop shop = shopService.getOne(new LambdaQueryWrapper<Shop>().eq(Shop::getUserId, getUserId()));
        if (shop == null) {
            return R.fail("商家信息不存在");
        }

        Map<String, Object> result = new HashMap<>();

        // 获取代销权限配置
        Map<String, Object> consignmentLevelConfig0 = getConsignmentLevelZero();
        Map<String, Object> consignmentLevelConfig1 = getConsignmentLevelConfig(1);
        Map<String, Object> consignmentLevelConfig2 = getConsignmentLevelConfig(2);
        // 基础代销权限状态
        result.put("hasBasicConsignment", "1".equals(shop.getConsignmentPermission()));
        result.put("basicExpireTime", shop.getConsignmentExpireTime());

        // 代销等级信息
        String consignmentLevel = shop.getConsignmentLevel();
        if (consignmentLevel != null) {
            // 如果等级权限已过期
            if (shop.getConsignmentLevelExpireTime().before(new Date())) {
                consignmentLevel = null;
            }
        }
        result.put("consignmentLevel", consignmentLevel);
        result.put("levelExpireTime", shop.getConsignmentLevelExpireTime());

        // 根据等级获取可代销商品数量
        Long maxProducts = getMaxProductsByLevel(consignmentLevelConfig1, consignmentLevelConfig2, consignmentLevel);
        result.put("maxProducts", maxProducts);

        // 获取已使用的代销商品数量
        Long usedProducts = consignmentProductService.count(
            new LambdaQueryWrapper<ConsignmentProduct>()
                .eq(ConsignmentProduct::getConsignmentShop, shop.getId())
                .eq(ConsignmentProduct::getStatus, "0")
        );
        result.put("usedProducts", usedProducts);

        // 获取权限价格配置
        result.put("level0Config", consignmentLevelConfig0);
        result.put("level1Config", consignmentLevelConfig1);
        result.put("level2Config", consignmentLevelConfig2);

        return R.ok(result);
    }

    /**
     * 为虚拟订单创建支付
     *
     * @param paymentData 支付数据
     * @return 支付参数
     */
    @PostMapping("/payment")
    @RepeatSubmit()
    @SaCheckPermission("mall:virtual:createVirtualPayment")
    @Log(title = "为虚拟订单创建支付", businessType = BusinessType.INSERT)
    public R<Map<String, String>> createVirtualPayment(@RequestBody @Validated PaymentData paymentData) {
        try {
            // 获取虚拟订单信息
            VirtualOrderVo order = virtualOrderService.getVirtualOrderDetail(paymentData.getOrderNo());
            if (order == null) {
                return R.fail("订单不存在");
            }

            if (!order.getUserId().equals(getUserId())) {
                return R.fail("无权操作此订单");
            }

            if (!OrderStatusEnum.PENDING_PAYMENT.getCode().equals(order.getStatus())) {
                return R.fail("订单状态不允许支付");
            }

            // 特殊处理商家平台促销金支付
            if (PayOrder.PAY_TYPE_SHOP_DEDUCTION.equals(paymentData.getPayType())) {
                if (StrUtil.isEmpty(paymentData.getPassword())) {
                    return R.fail("确认密码为空");
                }
                // 查询店铺的确认密码与password进行比较
                SysUser sysUser = sysUserService.getById(getUserId());
                if (StrUtil.isEmpty(sysUser.getOperationPassword())) {
                    return R.fail("暂未设置操作密码");
                }

                if (!BCrypt.checkpw(paymentData.getPassword(), sysUser.getOperationPassword())) {
                    return R.fail("密码错误");
                }

                // 调用虚拟订单服务中的商家平台促销金支付方法
                Map<String, String> result = virtualOrderService.payWithShopDeduction(
                    order.getId(),
                    order.getOrderNo(),
                    getUserId(),
                    order.getAmount()
                );

                return R.ok(result);
            }

            // 创建支付订单
            Map<String, String> payment = virtualOrderService.createPayment(
                order.getId(),
                order.getOrderNo(),
                getUserId(),
                order.getAmount(),
                paymentData.getPayType()
            );

            return R.ok(payment);
        } catch (Exception e) {
            log.error("创建虚拟商品支付订单失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 取消虚拟订单
     *
     * @param orderNo 订单编号
     * @return 操作结果
     */
    @PostMapping("/cancel/{orderNo}")
    @RepeatSubmit()
    @SaCheckPermission("mall:virtual:cancel")
    @Log(title = "取消虚拟订单", businessType = BusinessType.CLEAN)
    public R<Boolean> cancelVirtualOrder(@PathVariable String orderNo) {
        boolean result = virtualOrderService.cancelVirtualOrder(orderNo, getUserId());
        return R.ok(result);
    }

    /**
     * 删除虚拟订单
     *
     * @param orderNo 订单编号
     * @return 操作结果
     */
    @DeleteMapping("/{orderNo}")
    @RepeatSubmit()
    @SaCheckPermission("mall:virtual:delete")
    @Log(title = "删除虚拟订单", businessType = BusinessType.DELETE)
    public R<Boolean> deleteVirtualOrder(@PathVariable String orderNo) {
        boolean result = virtualOrderService.deleteVirtualOrder(orderNo, getUserId());
        return R.ok(result);
    }

    /**
     * 统计昨天的虚拟订单金额
     */
    @SaCheckPermission("mall:virtual:yesterdayAmount")
    @GetMapping("/yesterdayAmount")
    public R<Map<String, BigDecimal>> yesterdayAmount(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") String date) {
        LocalDate yesterday = someMethod(date);
        if (StrUtil.isEmpty(date)) {
            // 昨天的日期
            yesterday = LocalDate.now().minusDays(1);
        }

        LocalDateTime startOfYesterday = yesterday.atStartOfDay();
        LocalDateTime endOfYesterday = yesterday.atTime(23, 59, 59);

        // 查询昨天的虚拟订单数据
        List<VirtualOrder> virtualOrders = virtualOrderService.list(
            new LambdaQueryWrapper<VirtualOrder>()
                .eq(VirtualOrder::getStatus, OrderVirtualStatusEnum.PENDING_SHIPMENT.getCode())
                // 只查询昨天一天的的数据
                .between(VirtualOrder::getPayTime, startOfYesterday, endOfYesterday)
        );

        // 查询昨天线下引流付费的记录
        List<OfflineDrainageRecord> offlineDrainageRecords = offlineDrainageRecordService.list(
            new LambdaQueryWrapper<OfflineDrainageRecord>()
                .between(OfflineDrainageRecord::getCreateTime, startOfYesterday, endOfYesterday)
        );

        Map<String, BigDecimal> result = new HashMap<>();
        // 虚拟订单的所有金额
        BigDecimal amountCount = virtualOrders.stream().filter(
            virtualOrder ->
                (Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.DAIXOAO_ONE.type()) ||
                    Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.DAIXOAO_TWO.type()) ||
                    Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.DAIXOAO_THERE.type()))
        ).map(VirtualOrder::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 虚拟订单的手续费
        BigDecimal serviceFeeCount = virtualOrders.stream().filter(
            virtualOrder ->
                (Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.DAIXOAO_ONE.type()) ||
                    Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.DAIXOAO_TWO.type()) ||
                    Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.DAIXOAO_THERE.type()))
        ).map(VirtualOrder::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);

        result.put("权限", amountCount.subtract(serviceFeeCount));


        // 虚拟订单的所有金额
        BigDecimal amountCount1 = virtualOrders.stream().filter(
            virtualOrder ->
                Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.ADVERTISEMENT_PUTTING.type())
        ).map(VirtualOrder::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 虚拟订单的手续费
        BigDecimal serviceFeeCount1 = virtualOrders.stream().filter(
            virtualOrder
                -> Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.ADVERTISEMENT_PUTTING.type())
        ).map(VirtualOrder::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);

        result.put("广告", amountCount1.subtract(serviceFeeCount1));

        // 虚拟订单的所有金额
        BigDecimal amountCount2 = virtualOrders.stream().filter(
            virtualOrder -> Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.JISHU_DRAINAGE.type())
        ).map(VirtualOrder::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 虚拟订单的手续费
        BigDecimal serviceFeeCount2 = virtualOrders.stream().filter(
            virtualOrder -> Objects.equals(virtualOrder.getProductType(), VirtualTypeEnum.JISHU_DRAINAGE.type())
        ).map(VirtualOrder::getServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 线下引流的费用
        BigDecimal reduce = offlineDrainageRecords
            .stream()
            .map(OfflineDrainageRecord::getCitationValueMoney)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        result.put("技术引流", amountCount2.subtract(serviceFeeCount2).add(reduce));
        return R.ok(result);
    }

    public LocalDate someMethod(String date) {
        if (StrUtil.isEmpty(date)) {
            return null;
        }
        LocalDate localDate;
        try {
            localDate = LocalDate.parse(date);
        } catch (Exception e) {
            // 处理解析异常
            return null;
        }
        return localDate;
    }

    /**
     * 根据等级获取最大商品数量
     */
    private Long getMaxProductsByLevel(Map<String, Object> consignmentLevelConfig, Map<String, Object> consignmentLevelConfig1, String level) {
        if (level == null) {
            return 0L;
        }
        switch (level) {
            case "1":
                return (Long) consignmentLevelConfig.get("maxProducts");
            case "2":
                return (Long) consignmentLevelConfig1.get("maxProducts");
            default:
                return ConsignmentProductLimitConstants.ConsignmentProductLimitZero;
        }
    }

    /**
     * 获取代销权限等级配置
     */
    private Map<String, Object> getConsignmentLevelZero() {
        VirtualStrategy strategy = virtualFactory.getPaymentStrategy(4);
        Map<String, Object> config = new HashMap<>();
        config.put("discountPrice", strategy.getAmount());
        config.put("maxProducts", strategy.getCount());

        // 获取完整配置信息
        int configIndex = 2;
        JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(configIndex));
        if (configValue != null) {
            config.put("originalPrice", configValue.getStr("originalPrice"));
            config.put("name", "基础代销");
            config.put("description", configValue.getStr("discountPrice"));
            // 添加开关状态，默认为启用
            config.put("enabled", configValue.getBool("enabled", true));
        } else {
            // 如果配置不存在，默认启用
            config.put("enabled", true);
        }
        return config;
    }

    /**
     * 获取代销权限等级配置
     */
    private Map<String, Object> getConsignmentLevelConfig(Integer level) {
        try {
            VirtualStrategy strategy = virtualFactory.getPaymentStrategy(level + 6); // 7或8
            Map<String, Object> config = new HashMap<>();
            config.put("discountPrice", strategy.getAmount());
            config.put("maxProducts", strategy.getCount());

            // 获取完整配置信息
            int configIndex = level == 1 ? 11 : 12;
            JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(configIndex));
            if (configValue != null) {
                config.put("originalPrice", configValue.getStr("originalPrice"));
                config.put("name", configValue.getStr("name"));
                config.put("description", configValue.getStr("description"));
                // 添加开关状态，默认为启用
                config.put("enabled", configValue.getBool("enabled", true));
            } else {
                // 如果配置不存在，默认启用
                config.put("enabled", true);
            }

            return config;
        } catch (Exception e) {
            log.warn("获取代销权限等级{}配置失败", level, e);
            return new HashMap<>();
        }
    }
}

@Data
class PaymentData {

    @NotEmpty(message = "订单编号不能为空")
    private String orderNo;

    @NotEmpty(message = "支付方式不能为空")
    private String payType;

    private String password;
}
