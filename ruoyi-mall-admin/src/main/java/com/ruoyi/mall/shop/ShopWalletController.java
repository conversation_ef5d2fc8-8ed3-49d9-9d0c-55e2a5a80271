package com.ruoyi.mall.shop;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.mall.domain.entity.ShopWallet;
import com.ruoyi.mall.service.IShopWalletService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家钱包管理控制器
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/mall/shop/wallet")
public class ShopWalletController extends BaseController {

    private final IShopWalletService shopWalletService;

    /**
     * 获取当前商家钱包信息
     */
    @GetMapping("/info")
    public R<ShopWallet> getCurrentShopWallet() {
        Long shopId = getLoginUser().getShopId();
        if (shopId == null) {
            return R.fail("未获取到商家信息");
        }
        ShopWallet wallet = shopWalletService.getByShopId(shopId);
        if (wallet == null) {
            wallet = shopWalletService.createWallet(shopId);
        }
        return R.ok(wallet);
    }

//    /**
//     * 商家钱包入账
//     */
//    @SaCheckPermission("mall:shop:wallet:income")
//    @PostMapping("/income")
//    @RepeatSubmit()
//    public R<Void> income(@RequestBody Map<String, Object> params) {
//        try {
//            BigDecimal amount = new BigDecimal(params.get("amount").toString());
//            Integer tradeMethod = Integer.valueOf(params.get("tradeMethod").toString());
//            String orderNo = params.get("orderNo") != null ? params.get("orderNo").toString() : null;
//            String remark = params.get("remark") != null ? params.get("remark").toString() : null;
//
//            boolean result = shopWalletService.income(getLoginUser().getShopId(), amount, tradeMethod, orderNo, remark);
//            return toAjax(result);
//        } catch (Exception e) {
//            return R.fail("入账操作失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 商家钱包出账
//     */
//    @SaCheckPermission("mall:shop:wallet:expense")
//    @PostMapping("/expense")
//    @RepeatSubmit()
//    public R<Void> expense(@RequestBody Map<String, Object> params) {
//        try {
//            BigDecimal amount = new BigDecimal(params.get("amount").toString());
//            Integer tradeMethod = Integer.valueOf(params.get("tradeMethod").toString());
//            String orderNo = params.get("orderNo") != null ? params.get("orderNo").toString() : null;
//            String remark = params.get("remark") != null ? params.get("remark").toString() : null;
//
//            boolean result = shopWalletService.expense(getLoginUser().getShopId(), amount, tradeMethod, orderNo, remark);
//            return toAjax(result);
//        } catch (Exception e) {
//            return R.fail("出账操作失败：" + e.getMessage());
//        }
//    }
}
