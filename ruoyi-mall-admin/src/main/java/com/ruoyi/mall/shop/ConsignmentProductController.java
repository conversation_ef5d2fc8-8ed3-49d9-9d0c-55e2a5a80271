package com.ruoyi.mall.shop;


import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.ConsignmentProduct;
import com.ruoyi.mall.service.IConsignmentProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;


/**
 * 代销产品管理
 */

@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/consignment/product")
public class ConsignmentProductController extends BaseController {


    private final IConsignmentProductService consignmentProductService;

    /**
     * 查询代销商家已经代销的产品
     *
     * @param pageQuery 分页参数
     * @return
     */
    @GetMapping("/list")
    public TableDataInfo<ConsignmentProduct> getUserConsignmentProductList(PageQuery pageQuery) {
        return consignmentProductService.selectConsignmentProductList(getUserId(), pageQuery);
    }

    /**
     * 代销商家复制链接代销产品
     *
     * @param url    产品url
     * @param remark 备注
     * @return
     */
    @PostMapping("/add")
    public R<Void> addConsignmentProduct(@RequestParam String url, @RequestParam String remark) {
        return toAjax(consignmentProductService.addConsignmentProductByUrl(getUserId(), url, remark));
    }

    /**
     * 代销商家代销产品
     *
     * @param productId 产品ID
     * @return
     */
    @PostMapping("/insert")
    public R<Void> insertConsignmentProduct(@RequestParam Long productId) {
        return toAjax(consignmentProductService.addConsignmentProduct(getUserId(), productId));
    }

    /**
     * 删除正在代销的产品
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public R<Void> deleteConsignmentProduct(@PathVariable Long id) {
        int count = consignmentProductService.deleteConsignmentProduct(id);
        return toAjax(count);
    }

    /**
     * 代销商家主动取消代销产品
     *
     * @param id 代销ID
     * @return
     */
    @PutMapping("/cancel")
    public R<Void> cancelConsignmentProduct(@RequestParam Long productId) {
        Long shopId = getLoginUser().getShopId();
        return toAjax(consignmentProductService.cancelConsignmentProduct(shopId, productId));
    }


}
