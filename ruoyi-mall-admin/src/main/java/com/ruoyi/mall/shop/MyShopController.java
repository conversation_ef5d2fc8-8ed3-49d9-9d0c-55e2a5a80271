package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.service.IShopService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 当前商家操作
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/myShop")
public class MyShopController extends BaseController {

    private final IShopService shopService;


    /**
     * 获取当前登录商家信息详细信息
     */
    @GetMapping(value = "/getLoginShopInfo")
    public R<Shop> getLoginShopInfo() {
        return R.ok(shopService.getById(getLoginUser().getShopId()));
    }

    /**
     * 修改商家信息
     */
    @SaCheckPermission("mall:shop:edit")
    @Log(title = "修改商家信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated @RequestBody Shop shop) {
        shop.setId(getLoginUser().getShopId());
        return toAjax(shopService.updateShop(shop));
    }

}
