package com.ruoyi.mall.shop;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.mall.domain.entity.QuantizationRate;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.service.IQuantizationRateService;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 量化比例服务
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/QuantizationRate")
public class QuantizationRateServiceController extends BaseController {
    private final IQuantizationRateService quantizationRateService;
    private final ISysUserService sysUserService;
    private final IShopService shopService;

    /**
     * 日程量化率
     *
     * @param searchMonth
     * @return
     */
    @GetMapping("/getQuantizationRate")
    public R<List<QuantizationRate>> getQuantizationRate(@RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM") Date searchMonth) {
        if (searchMonth == null) {
            searchMonth = new Date();
        }
        return R.ok(quantizationRateService.getQuantizationRate(searchMonth));
    }

    /**
     * 获取当前用户的量化值与量化
     *
     * @return
     */
    @GetMapping("/getUserQuantizationValue")
    public R<Map<String, Object>> getUserQuantizationValue() {
        Map<String, Object> objectObjectHashMap = new HashMap<>();
        // 如果是超级管理员，走特殊逻辑
        SysUser sysUser = sysUserService.getById(getUserId());
        if (sysUser.getUserId().equals(1L)) {
            objectObjectHashMap.put("quantificationValue", Optional.ofNullable(sysUser.getQuantificationValue()).orElse(0D));
            objectObjectHashMap.put("userFaith", Optional.ofNullable(sysUser.getUserFaith()).orElse(0D));
        } else {
            Long shopId = getLoginUser().getShopId();
            Shop shop = shopService.getById(shopId);
            objectObjectHashMap.put("quantificationValue", Optional.ofNullable(shop).map(Shop::getQuantificationValue).orElse(0D));
            objectObjectHashMap.put("userFaith", Optional.ofNullable(shop).map(Shop::getUserFaith).orElse(0D));
        }
        return R.ok(objectObjectHashMap);
    }

}
