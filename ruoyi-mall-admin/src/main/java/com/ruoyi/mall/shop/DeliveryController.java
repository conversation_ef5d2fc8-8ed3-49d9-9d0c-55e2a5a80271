package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.mall.domain.entity.Delivery;
import com.ruoyi.mall.service.IDeliveryService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 快递公司管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/delivery")
public class DeliveryController extends BaseController {

    private final IDeliveryService deliveryService;

    /**
     * 查询快递公司列表
     */
    @SaCheckPermission("mall:delivery:list")
    @GetMapping("/list")
    public TableDataInfo<Delivery> list(Delivery delivery, PageQuery pageQuery) {
        Long shopId = getLoginUser().getShopId();
        if (shopId == null) {
            throw new ServiceException("当前账号并非商家");
        }
        delivery.setShopId(shopId);
        return deliveryService.selectDeliveryPage(delivery, pageQuery);
    }

    /**
     * 查询所有快递公司
     */
    @GetMapping("/all")
    public List<Delivery> all(Delivery delivery) {
        Long shopId = getLoginUser().getShopId();
        if (shopId == null) {
            throw new ServiceException("当前账号并非商家");
        }
        delivery.setShopId(shopId);
        return deliveryService.selectDeliveryAll(delivery);
    }

    /**
     * 获取快递公司详细信息
     */
    @SaCheckPermission("mall:delivery:getInfo")
    @GetMapping(value = "/{id}")
    public R<Delivery> getInfo(@PathVariable("id") Long id) {
        return R.ok(deliveryService.getById(id));
    }

    /**
     * 新增快递公司
     */
    @SaCheckPermission("mall:delivery:add")
    @Log(title = "新增快递公司", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody Delivery delivery) {
        Long shopId = getLoginUser().getShopId();
        if (shopId == null) {
            throw new ServiceException("当前账号并非商家");
        }
        delivery.setShopId(getLoginUser().getShopId());
        return toAjax(deliveryService.save(delivery));
    }

    /**
     * 修改快递公司
     */
    @SaCheckPermission("mall:delivery:edit")
    @Log(title = "修改快递公司", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody Delivery delivery) {
        return toAjax(deliveryService.updateById(delivery));
    }

    /**
     * 删除快递公司
     */
    @SaCheckPermission("mall:delivery:remove")
    @Log(title = "删除快递公司", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Long[] ids) {
        return toAjax(deliveryService.removeByIds(Arrays.asList(ids)));
    }
}
