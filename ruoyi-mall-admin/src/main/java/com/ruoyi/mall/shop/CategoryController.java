package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.mall.domain.entity.Category;
import com.ruoyi.mall.service.ICategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品分类
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/category")
public class CategoryController extends BaseController {
    private final ICategoryService categoryService;


    /**
     * 根据父ID获取子分类
     */
    @GetMapping(value = "/getCategoryByParentId/{parentId}")
    public R<List<Category>> getCategoryByParentId(@PathVariable("parentId") Long parentId) {
        if (parentId == null) {
            throw new SecurityException("ID不能为空");
        }
        List<Category> list = categoryService.list(
            new LambdaQueryWrapper<Category>()
                .eq(Category::getParentId, parentId).
                eq(Category::getDelFlag, "0")
        );
        return R.ok(list);
    }

    /**
     * 查询商品分类列表
     */
    @SaCheckPermission("mall:category:list")
    @GetMapping("/list")
    public TableDataInfo<Category> list(Category category, PageQuery pageQuery) {
        category.setShopId(getLoginUser().getShopId());
        return categoryService.selectCategoryPage(category, pageQuery);
    }

    /**
     * 查询商品分类树结构
     */
    @SaCheckPermission("mall:category:tree")
    @GetMapping("/tree")
    public R<List<Category>> tree(Category category) {
        category.setShopId(getLoginUser().getShopId());
        List<Category> list = categoryService.selectCategoryTree(category);
        return R.ok(list);
    }

    /**
     * 获取商品分类详细信息
     */
    @SaCheckPermission("mall:category:query")
    @GetMapping(value = "/{id}")
    public R<Category> getInfo(@PathVariable("id") Long id) {
        return R.ok(categoryService.selectCategoryById(id));
    }

    /**
     * 新增商品分类
     */
    @SaCheckPermission("mall:category:add")
    @Log(title = "新增商品分类", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> add(@Validated @RequestBody Category category) {
        category.setShopId(getLoginUser().getShopId());
        // 店铺不能添加一级二级分类
        if (ObjUtil.isNotEmpty(getLoginUser().getShopId())) {
            // 如果添加的是一级分类
            if (category.getParentId() == 0) {
                return R.fail("店铺不能添加一二级分类");
            } else {
                Category twoCategory = categoryService.getById(category.getParentId());
                if (twoCategory.getParentId() == 0) {
                    return R.fail("店铺不能添加一二级分类");
                }
            }
        }
        return toAjax(categoryService.insertCategory(category));
    }

    /**
     * 修改商品分类
     */
    @SaCheckPermission("mall:category:edit")
    @Log(title = "修改商品分类", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated @RequestBody Category category) {
        // 店铺不能添加一级二级分类
        if (ObjUtil.isNotEmpty(getLoginUser().getShopId())) {
            // 如果添加的是一级分类
            if (category.getParentId() == 0) {
                return R.fail("店铺不能修改一二级分类");
            } else {
                Category twoCategory = categoryService.getById(category.getParentId());
                if (twoCategory.getParentId() == 0) {
                    return R.fail("店铺不能修改一二级分类");
                }
            }
        }
        return toAjax(categoryService.updateCategory(category));
    }

    /**
     * 删除商品分类
     */
    @SaCheckPermission("mall:category:remove")
    @Log(title = "删除商品分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Long[] ids) {

        // 店铺不能添加一级二级分类
        if (ObjUtil.isNotEmpty(getLoginUser().getShopId())) {
            for (Long id : ids) {
                Category category = categoryService.getById(id);
                // 如果添加的是一级分类
                if (category.getParentId() == 0) {
                    return R.fail("店铺不能删除一二级分类");
                } else {
                    Category twoCategory = categoryService.getById(category.getParentId());
                    if (twoCategory.getParentId() == 0) {
                        return R.fail("店铺不能删除一二级分类");
                    }
                }
            }
        }
        categoryService.deleteCategoryByIds(ids);
        return R.ok();
    }
}
