package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.mall.domain.entity.OfflineDrainageRecord;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.service.IOfflineDrainageRecordService;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.system.service.ISysUserService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 线下客户充值管理
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/offlineCustomer")
public class OfflineCustomerController extends BaseController {

    private final IShopService shopService;
    private final ISysUserService sysUserService;
    private final IOfflineDrainageRecordService offlineDrainageRecordService;
    private final IConfigSettingService configSettingService;

    /**
     * 线下充值客户技术引流次数
     */
    @PostMapping("/rechargeDrainage")
    @SaCheckPermission("mall:offline:rechargeDrainage")
    @Log(title = "线下充值技术引流次数", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    public R<Boolean> rechargeDrainage(@RequestBody @Validated RechargeDto rechargeDto) {
        // 校验是否开启了线下充值功能
        JSONObject configValue = configSettingService.getConfigValue("configFive");
        if (configValue == null || "1".equals(configValue.getStr("isEnabled"))) {
            return R.fail("技术引流功能未开启，无法进行线下充值");
        }

        // 1. 验证操作密码
        SysUser admin = sysUserService.getById(getUserId());
        if (StrUtil.isEmpty(admin.getOperationPassword())) {
            return R.fail("管理员未设置操作密码");
        }

        if (!BCrypt.checkpw(rechargeDto.getOperationPassword(), admin.getOperationPassword())) {
            return R.fail("操作密码错误");
        }

        // 2. 验证商家ID是否存在
        if (!shopService.checkShopExists(rechargeDto.getShopId())) {
            return R.fail("商家不存在");
        }

        try {
            // 3. 调用服务执行充值操作，同时记录充值历史
            boolean result = offlineDrainageRecordService.rechargeOfflineDrainage(
                rechargeDto.getShopId(),
                rechargeDto.getCount(),
                getUserId(),
                getUsername(),
                rechargeDto.getRemark()
            );

            if (result) {
                return R.ok("操作成功");
            } else {
                return R.fail("操作失败，请重试");
            }
        } catch (Exception e) {
            log.error("线下充值技术引流次数失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询线下充值技术引流次数记录
     */
    @GetMapping("/drainageRecords")
    @SaCheckPermission("mall:offline:drainageRecords")
    public TableDataInfo<OfflineDrainageRecord> getDrainageRecords(OfflineDrainageRecord record, PageQuery pageQuery) {
        return offlineDrainageRecordService.selectOfflineDrainageRecordPage(record, pageQuery);
    }
}

/**
 * 充值请求数据
 */
@Data
class RechargeDto {

    /**
     * 商家ID
     */
    @NotNull(message = "商家ID不能为空")
    private Long shopId;

    /**
     * 充值数量
     */
    @NotNull(message = "充值数量不能为空")
    @Min(value = 1, message = "充值数量必须大于0")
    private Integer count;

    /**
     * 操作密码
     */
    @NotEmpty(message = "操作密码不能为空")
    private String operationPassword;

    /**
     * 备注
     */
    private String remark;
}
