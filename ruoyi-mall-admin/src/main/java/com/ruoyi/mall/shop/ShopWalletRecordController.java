package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.ShopWalletRecord;
import com.ruoyi.mall.service.IShopWalletRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家钱包交易记录控制器
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/mall/shop/wallet/record")
public class ShopWalletRecordController extends BaseController {

    private final IShopWalletRecordService shopWalletRecordService;

    /**
     * 查询商家钱包交易记录列表
     */
    @SaCheckPermission("mall:shop:wallet:record:list")
    @GetMapping("/list")
    public TableDataInfo<ShopWalletRecord> list(ShopWalletRecord shopWalletRecord, PageQuery pageQuery) {
        return shopWalletRecordService.queryPageList(shopWalletRecord, pageQuery);
    }

    /**
     * 查询当前商家钱包交易记录列表
     */
    @GetMapping("/myList")
    public TableDataInfo<ShopWalletRecord> myList(ShopWalletRecord shopWalletRecord, PageQuery pageQuery) {
        shopWalletRecord.setShopId(getLoginUser().getShopId());
        return shopWalletRecordService.queryPageList(shopWalletRecord, pageQuery);
    }

    /**
     * 获取商家钱包交易记录详细信息
     */
    @SaCheckPermission("mall:shop:wallet:record:query")
    @GetMapping("/{id}")
    public R<ShopWalletRecord> getInfo(@PathVariable Long id) {
        return R.ok(shopWalletRecordService.getById(id));
    }

//    /**
//     * 新增商家钱包交易记录
//     */
//    @SaCheckPermission("mall:shop:wallet:record:add")
//    @PostMapping
//    public R<Void> add(@Validated @RequestBody ShopWalletRecord shopWalletRecord) {
//        return toAjax(shopWalletRecordService.save(shopWalletRecord));
//    }
//
//    /**
//     * 删除商家钱包交易记录
//     */
//    @SaCheckPermission("mall:shop:wallet:record:remove")
//    @DeleteMapping("/{ids}")
//    public R<Void> remove(@PathVariable Long[] ids) {
//        for (Long id : ids) {
//            shopWalletRecordService.removeById(id);
//        }
//        return R.ok();
//    }
}
