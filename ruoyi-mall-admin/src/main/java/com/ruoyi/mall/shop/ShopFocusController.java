package com.ruoyi.mall.shop;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.TZUserFocus;
import com.ruoyi.mall.service.ITZUserFocusService;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 代销关注
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/shopFocus")
public class ShopFocusController extends BaseController {

    private final ITZUserFocusService itzUserFocusService;
    private final ISysUserService sysUserService;

    /**
     * 获取用户关注列表
     */
    @GetMapping("/getUserFocusPage")
    public TableDataInfo<TZUserFocus> getUserFocusPage(TZUserFocus focus, PageQuery pageQuery) {
        Long userId = getUserId();
        SysUser sysUser = sysUserService.getById(userId);
        focus.setUserId(sysUser.getTzUserId());
        return itzUserFocusService.getUserFocusPage(focus, pageQuery);
    }

    /**
     * 代销商家取消用户关注
     *
     * @param focusUserId 关注用户的TZ_USER ID
     * @return 操作结果
     */
    @PostMapping("/cancelFocus/{focusId}")
    public R<Void> cancelUserFocus(@PathVariable @NotNull Long focusId) {
        boolean result = itzUserFocusService.cancelUserFocus(focusId);
        return result ? R.ok() : R.fail("取消关注失败");
    }

}
