package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.mall.domain.dto.ShopAuditDTO;
import com.ruoyi.mall.domain.entity.ShopAudit;
import com.ruoyi.mall.service.IShopAuditService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 商家审核管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/shopAudit")
public class ShopAuditController extends BaseController {

    private final IShopAuditService shopAuditService;

    /**
     * 查询商家审核记录列表
     */
    @SaCheckPermission("mall:shop:audit:list")
    @GetMapping("/list")
    public TableDataInfo<ShopAudit> list(ShopAudit shopAudit, PageQuery pageQuery) {
        return shopAuditService.selectShopAuditPage(shopAudit, pageQuery);
    }

    /**
     * 查询商家审核记录列表
     */
    @SaCheckPermission("mall:shop:audit:recordList")
    @GetMapping("/recordList")
    public TableDataInfo<ShopAudit> recordList(ShopAudit shopAudit, PageQuery pageQuery) {
        return shopAuditService.selectShopAuditRecordPage(shopAudit, pageQuery);
    }


    /**
     * 获取商家审核记录详细信息
     */
    @SaCheckPermission("mall:shop:audit:query")
    @GetMapping(value = "/{id}")
    public R<ShopAudit> getInfo(@PathVariable("id") Long id) {
        return R.ok(shopAuditService.getById(id));
    }

    /**
     * 审核商家申请
     */
    @SaCheckPermission("mall:shop:audit:edit")
    @Log(title = "审核商家申请", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/audit")
    public R<Void> audit(@Validated @RequestBody ShopAuditDTO shopAuditDTO) {
        return toAjax(shopAuditService.auditShop(shopAuditDTO));
    }
}
