package com.ruoyi.mall.shop;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.toolkit.JoinWrappers;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.mall.domain.entity.Order;
import com.ruoyi.mall.domain.entity.ProductEvaluate;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.service.IProductEvaluateService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 商品评价管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/productEvaluate")
public class ProductEvaluateController extends BaseController {

    private final IProductEvaluateService productEvaluateService;

    /**
     * 查询商品评价列表
     */
    @GetMapping("/list")
    public TableDataInfo<ProductEvaluate> list(ProductEvaluate productEvaluate, PageQuery pageQuery) {

        Long shopId = getLoginUser().getShopId();
        if (shopId == null) {
            throw new ServiceException("当前账号并非商家");
        }

        Page<ProductEvaluate> page = JoinWrappers.lambda(ProductEvaluate.class)
            .leftJoin(Order.class, Order::getId, ProductEvaluate::getOrderId)
            .leftJoin(TzUser.class, TzUser::getUserId, ProductEvaluate::getUserId)

            .selectAll(ProductEvaluate.class)
            .selectAs(Order::getOrderName, ProductEvaluate::getProductName)
            .selectAs(TzUser::getNickname, ProductEvaluate::getUserName)

            .eq(Order::getShopId, shopId)
            .like(StrUtil.isNotBlank(productEvaluate.getProductName()), Order::getOrderName, productEvaluate.getProductName())
            .eq(productEvaluate.getProductId() != null, ProductEvaluate::getProductId, productEvaluate.getProductId())
            .eq(productEvaluate.getStatus() != null && !productEvaluate.getStatus().isEmpty(),
                ProductEvaluate::getStatus, productEvaluate.getStatus())
            .orderByDesc(ProductEvaluate::getCreateTime)
            .page(pageQuery.build());
        return TableDataInfo.build(page);
    }

    /**
     * 获取商品评价详细信息
     */
    @GetMapping("/{id}")
    public R<ProductEvaluate> getInfo(@PathVariable Long id) {
        return R.ok(productEvaluateService.getProductEvaluateById(id));
    }

    /**
     * 审核商品评价
     */
    @PutMapping("/examine/{id}/{status}")
    public R<Integer> examine(@PathVariable Long id, @PathVariable String status) {
        return R.ok(productEvaluateService.ExamineEvaluate(id, status));
    }

    /**
     * 删除商品评价
     */
    @DeleteMapping("/{ids}")
    public R<Integer> remove(@PathVariable Long[] ids) {
        return R.ok(productEvaluateService.deleteProductEvaluateIds(ids));
    }

    /**
     * 商家回复评价
     */
    @PutMapping("/reply/{id}")
    public R<Integer> reply(@PathVariable Long id, @RequestBody Map<String, String> params) {
        String reply = params.get("reply");
        if (reply == null || reply.isEmpty()) {
            return R.fail("回复内容不能为空");
        }
        return R.ok(productEvaluateService.replyEvaluate(id, reply));
    }
}
