package com.ruoyi.mall.shop;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.mall.domain.bo.ShopReturnAddressBo;
import com.ruoyi.mall.domain.entity.ShopReturnAddress;
import com.ruoyi.mall.service.IShopReturnAddressService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 商家退货地址管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/shopReturnAddress")
public class ShopReturnAddressController extends BaseController {

    private final IShopReturnAddressService shopReturnAddressService;

    /**
     * 查询退货地址列表
     */
    @GetMapping("/list")
    public TableDataInfo<ShopReturnAddress> list(ShopReturnAddressBo bo, PageQuery pageQuery) {
        Long shopId = getLoginUser().getShopId();
        if (shopId == null) {
            throw new ServiceException("当前账号并非商家");
        }
        // 设置当前商家ID
        bo.setShopId(shopId);
        return shopReturnAddressService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取退货地址详细信息
     */
    @GetMapping("/{id}")
    public R<ShopReturnAddress> getInfo(@PathVariable Long id) {
        return R.ok(shopReturnAddressService.queryById(id));
    }

    /**
     * 新增退货地址
     */
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ShopReturnAddressBo bo) {
        Long shopId = getLoginUser().getShopId();
        if (shopId == null) {
            throw new ServiceException("当前账号并非商家");
        }
        // 设置当前商家ID
        bo.setShopId(shopId);
        return toAjax(shopReturnAddressService.insertByBo(bo));
    }

    /**
     * 修改退货地址
     */
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ShopReturnAddressBo bo) {
        Long shopId = getLoginUser().getShopId();
        if (shopId == null) {
            throw new ServiceException("当前账号并非商家");
        }
        // 设置当前商家ID
        bo.setShopId(shopId);
        return toAjax(shopReturnAddressService.updateByBo(bo));
    }

    /**
     * 删除退货地址
     */
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Long[] ids) {
        return toAjax(shopReturnAddressService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 设置默认退货地址
     */
    @PutMapping("/default/{id}")
    public R<Void> setDefault(@PathVariable Long id) {
        Long shopId = getLoginUser().getShopId();
        if (shopId == null) {
            throw new ServiceException("当前账号并非商家");
        }
        return toAjax(shopReturnAddressService.setDefault(id, shopId));
    }
}
