package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.json.JSONObject;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.mall.domain.dto.FaithStatisticsDTO;
import com.ruoyi.mall.domain.dto.QuantificationStatisticsDTO;
import com.ruoyi.mall.domain.entity.ShopValueRecord;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.service.IShopValueRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 商家量化值转换管理
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/shopValue")
public class ShopValueController extends BaseController {

    private final IShopValueRecordService shopValueRecordService;
    private final IConfigSettingService configSettingService;

    /**
     * 量化值转换为量化
     */
    @SaCheckPermission("mall:shop:value:convert")
    @PostMapping("/convertToFaith")
    @Log(title = "量化值转量化", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    public R<ShopValueRecord> convertToFaith(@RequestParam Double amount) {
        Long shopId = getLoginUser().getShopId();
        JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(8));
        if (configValue == null) {
            return R.fail("请先配置积分转换比例");
        }
        Double rate = configValue.getDouble("quantifyToCredit");
        if (rate == null) {
            return R.fail("请先配置积分转换比例");
        }
        if (getUserId().equals(1L)) {
            return R.ok(shopValueRecordService.convertQuantificationToFaith(amount, rate / 100));
        }
        return R.ok(shopValueRecordService.convertQuantificationToFaith(shopId, amount, rate / 100));
    }

    /**
     * 量化转换为平台促销金
     */
    @SaCheckPermission("mall:shop:value:convert")
    @PostMapping("/convertToGold")
    @Log(title = "量化转平台促销金", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    public R<ShopValueRecord> convertToGold(@RequestParam Double amount) {
        Long shopId = getLoginUser().getShopId();
        JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(8));
        if (configValue == null) {
            return R.fail("请先配置积分转换比例");
        }
        Double rate = configValue.getDouble("creditToCoupon");
        if (rate == null) {
            return R.fail("请先配置积分转换比例");
        }
        if (getUserId().equals(1L)) {
            return R.ok(shopValueRecordService.convertFaithToPromotionGold(amount, rate / 100));
        }
        return R.ok(shopValueRecordService.convertFaithToPromotionGold(shopId, amount, rate / 100));
    }

    /**
     * 按时间统计量化值转化数据（分页版本）
     */
    @SaCheckPermission("mall:shop:value:list")
    @GetMapping("/statistics/quantification/page")
    public TableDataInfo<QuantificationStatisticsDTO> statisticsQuantificationPage(
        @RequestParam(required = false) String date,
        @RequestParam(required = false) String phone,
        PageQuery pageQuery) {
        // 仅查询量化值转量化损耗记录(operationType=3)
        return shopValueRecordService.statisticsQuantificationByTimePage(
            date, phone, 3, pageQuery);
    }

    /**
     * 导出量化值统计数据
     */
    @SaCheckPermission("mall:shop:value:export")
    @Log(title = "导出量化值统计", businessType = BusinessType.EXPORT)
    @GetMapping("/export/quantification")
    public void exportQuantification(
        @RequestParam(required = false) String date,
        @RequestParam(required = false) String phone,
        HttpServletResponse response) {
        // 仅导出量化值转量化损耗记录(operationType=3)
        List<QuantificationStatisticsDTO> list = shopValueRecordService.exportQuantificationStatistics(
            date, phone, 3);
        ExcelUtil.exportExcel(list, "量化值转换统计", QuantificationStatisticsDTO.class, response);
    }

    /**
     * 按时间统计量化转化数据（分页版本）
     */
    @SaCheckPermission("mall:shop:value:list")
    @GetMapping("/statistics/faith/page")
    public TableDataInfo<FaithStatisticsDTO> statisticsFaithPage(
        @RequestParam(required = false) String date,
        @RequestParam(required = false) String phone,
        PageQuery pageQuery) {
        // 量化转平台促销金的记录
        return shopValueRecordService.statisticsFaithByTimePage(
            date, phone, 4, pageQuery);
    }

    /**
     * 导出量化统计数据
     */
    @SaCheckPermission("mall:shop:value:export")
    @Log(title = "导出量化统计", businessType = BusinessType.EXPORT)
    @GetMapping("/export/faith")
    public void exportFaith(
        @RequestParam(required = false) String date,
        @RequestParam(required = false) String phone,
        HttpServletResponse response) {
        // 量化转平台促销金的记录
        List<FaithStatisticsDTO> list = shopValueRecordService.exportFaithStatistics(
            date, phone, 4);
        ExcelUtil.exportExcel(list, "量化转换统计", FaithStatisticsDTO.class, response);
    }


    /**
     * 按时间统计admin量化值转化数据
     */
    @SaCheckPermission("mall:shop:value:list")
    @GetMapping("/statistics/adminQuantification")
    public R<QuantificationStatisticsDTO> adminQuantification(
        @RequestParam(required = false) String date,
        @RequestParam(required = false) String phone
    ) {
        // 仅查询量化值转量化损耗记录
        return R.ok(shopValueRecordService.adminQuantification(date, phone, 3));
    }

    /**
     * 按时间统计admin量化转化数据
     */
    @SaCheckPermission("mall:shop:value:list")
    @GetMapping("/statistics/adminFaith")
    public R<FaithStatisticsDTO> adminFaith(
        @RequestParam(required = false) String date,
        @RequestParam(required = false) String phone
    ) {
        // 量化转平台促销金的记录
        return R.ok(shopValueRecordService.adminFaith(date, phone, 4));
    }

    /**
     * 获取指定日期量化值进化统计
     */
    @SaCheckPermission("mall:shop:value:date:quantification")
    @GetMapping("/statistics/date/quantification")
    public R<Map<String, Object>> getDateQuantificationStats(@RequestParam(required = false) String date) {
        try {
            Map<String, Object> stats = shopValueRecordService.getDateQuantificationStats(date);
            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取量化值进化统计失败", e);
            return R.fail("获取统计数据失败");
        }
    }

    /**
     * 获取指定日期量化进化统计
     */
    @SaCheckPermission("mall:shop:value:date:faith")
    @GetMapping("/statistics/date/faith")
    public R<Map<String, Object>> getDateFaithStats(@RequestParam(required = false) String date) {
        try {
            Map<String, Object> stats = shopValueRecordService.getDateFaithStats(date);
            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取量化值进化统计失败", e);
            return R.fail("获取统计数据失败");
        }
    }
}
