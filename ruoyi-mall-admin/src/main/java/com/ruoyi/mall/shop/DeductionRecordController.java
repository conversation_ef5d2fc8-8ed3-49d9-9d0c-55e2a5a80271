package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.mall.domain.entity.DeductionRecord;
import com.ruoyi.mall.service.IDeductionRecordService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 抵扣金赠送记录
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/mall/deduction")
public class DeductionRecordController extends BaseController {

    private final IDeductionRecordService deductionRecordService;

    /**
     * 查询抵扣金赠送记录列表
     */
    @SaCheckPermission("mall:deduction:list")
    @GetMapping("/list")
    public TableDataInfo<DeductionRecord> list(DeductionRecord deductionRecord, PageQuery pageQuery) {
        return deductionRecordService.queryPageList(deductionRecord, pageQuery);
    }

    /**
     * 获取抵扣金赠送记录详细信息
     */
    @SaCheckPermission("mall:deduction:query")
    @GetMapping("/{id}")
    public R<DeductionRecord> getInfo(@PathVariable Long id) {
        return R.ok(deductionRecordService.getById(id));
    }

    /**
     * 商家赠送抵扣金给用户
     */
    @SaCheckPermission("mall:deduction:give")
    @Log(title = "商家赠送抵扣金给用户", businessType = BusinessType.INSERT)
    @PostMapping("/give")
    @RepeatSubmit()
    public R<Void> giveDeduction(@RequestBody @Validated GiveDeductionRequest request) {
        boolean result = deductionRecordService.giveDeductionMoney(
            request.getShopId(),
            request.getPhone(),
            request.getAmount(),
            request.getReason()
        );
        return toAjax(result);
    }

    /**
     * 删除抵扣金赠送记录
     */
    @SaCheckPermission("mall:deduction:remove")
    @RepeatSubmit()
    @Log(title = "删除抵扣金赠送记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Long[] ids) {
        return toAjax(deductionRecordService.removeByIds(java.util.Arrays.asList(ids)));
    }

    /**
     * 抵扣金赠送请求对象
     */
    @Data
    @Validated
    static class GiveDeductionRequest {

        @NotNull(message = "商家ID不能为空")
        private Long shopId;

        @NotBlank(message = "用户手机号不能为空")
        private String phone;

        @NotNull(message = "赠送金不能为空")
        @DecimalMin(value = "0.01", message = "赠送金必须大于0")
        private BigDecimal amount;

        private String reason;
    }
}
