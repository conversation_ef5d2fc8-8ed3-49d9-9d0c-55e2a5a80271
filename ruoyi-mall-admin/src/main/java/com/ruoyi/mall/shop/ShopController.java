package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.ShopBankAccount;
import com.ruoyi.mall.domain.vo.ShopHomeInfoVo;
import com.ruoyi.mall.service.IShopBankAccountService;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.system.service.ISysUserService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 商家信息
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/shop")
public class ShopController extends BaseController {

    private final IShopService shopService;
    private final IShopBankAccountService shopBankAccountService;
    private final ISysUserService sysUserService;

    /**
     * 查询商家信息列表
     */
    @SaCheckPermission("mall:shop:list")
    @GetMapping("/list")
    public TableDataInfo<Shop> list(Shop shop, PageQuery pageQuery) {
        return shopService.selectShopPage(shop, pageQuery);
    }

    /**
     * 获取商家信息详细信息
     */
    @SaCheckPermission("mall:shop:query")
    @GetMapping(value = "/{id}")
    public R<Shop> getInfo(@PathVariable("id") Long id) {
        return R.ok(shopService.getById(id));
    }

    /**
     * 获取商家信息详细信息
     */
    @SaCheckPermission("mall:shop:query")
    @GetMapping()
    public R<Shop> getLoginInfo() {
        Shop shop = shopService.getById(getLoginUser().getShopId());
        if (shop == null) {
            return R.fail("店铺不存在");
        }
        ShopBankAccount shopBankAccount = shopBankAccountService.getOne(
            new LambdaQueryWrapper<ShopBankAccount>()
                .eq(ShopBankAccount::getShopId, shop.getId())
                .orderByDesc(ShopBankAccount::getCreateTime)
                .last(" limit 1")
        );
        // 校验的电话号码为sys_user的电话号码
        Long userId = shop.getUserId();
        SysUser sysUser = sysUserService.getById(userId);
        shop.setPhone(sysUser.getPhonenumber());
        shop.setShopBankAccount(shopBankAccount);
        return R.ok(shop);
    }

    /**
     * 获取商家默认分类
     *
     * @return
     */
    @SaCheckPermission("mall:shop:getShopDefaultCategory")
    @GetMapping("/getShopDefaultCategory")
    public R<Long> getShopDefaultCategory() {
        LoginUser loginUser = getLoginUser();
        Long shopId = loginUser.getShopId();
        Shop shop = shopService.getById(shopId);
        if (shop == null) {
            return R.fail("店铺不存在");
        }
        return R.ok(shop.getShopCategoryId());
    }


    /**
     * 修改商家信息
     */
    @SaCheckPermission("mall:shop:edit")
    @Log(title = "修改商家信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated @RequestBody Shop shop) {
        return toAjax(shopService.updateShop(shop));
    }

    /**
     * 删除商家信息
     */
    @SaCheckPermission("mall:shop:remove")
    @Log(title = "删除商家信息", businessType = BusinessType.DELETE)
    @RepeatSubmit()
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Long[] ids) {
        return toAjax(shopService.deleteShopByIds(ids));
    }

    /**
     * 修改商家状态
     */
    @SaCheckPermission("mall:shop:editStatus")
    @Log(title = "修改商品状态", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/editStatus")
    public R<Void> editStatus(@RequestBody @Validated ShopStatusDTO shopStatusDTO) {
        shopService.editStatus(shopStatusDTO.getId(), shopStatusDTO.getStatus());
        return R.ok();
    }

    /**
     * 获取商家的技术引流次数
     */
    @SaCheckPermission("mall:shop:getDrainageCount")
    @GetMapping("/getDrainageCount")
    public R<Integer> getDrainageCount() {
        return R.ok(shopService.getDrainageCount(getLoginUser().getShopId()));
    }

    /**
     * 获取店铺首页信息
     *
     * @return 店铺首页信息
     */
    @SaCheckPermission("mall:shop:homeInfo")
    @GetMapping("/homeInfo")
    public R<ShopHomeInfoVo> getShopHomeInfo() {
        // 使用Service方法获取店铺首页信息
        ShopHomeInfoVo shopHomeInfoVo = shopService.getShopHomeInfo(getLoginUser().getShopType(), getLoginUser().getShopId());
        if (shopHomeInfoVo == null) {
            return R.fail("获取店铺首页信息失败");
        }

        return R.ok(shopHomeInfoVo);
    }

    /**
     * 获取商家平台促销金余额
     */
    @SaCheckPermission("mall:shop:getPromotionBalance")
    @GetMapping("/getPromotionBalance")
    public R<BigDecimal> getShopPromotionBalance() {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 获取商家ID
        Long shopId = shopService.getShopIdByUserId(userId);
        if (shopId == null) {
            return R.fail("未找到商家信息");
        }

        // 获取平台促销金余额
        BigDecimal balance = shopService.getShopPromotionBalance(shopId);
        return R.ok(balance);
    }

    /**
     * 商家与代销获取验证码
     */
    @SaIgnore
    @GetMapping("/sendForgotPasswordCode")
    public R<String> sendForgotPasswordCode(@RequestParam String phone) {
        SysUser one = sysUserService.getOne(
            new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getPhonenumber, phone)
                .and(lqw -> {
                    lqw.eq(SysUser::getIsShop, "1")
                        .or()
                        .eq(SysUser::getIsConsignment, "1");
                })
        );
        if (one == null) {
            return R.fail("未找到商家信息");
        }
        return R.ok("", shopService.sendForgotPasswordCode(phone));
    }

    /**
     * 商家与代销重置密码
     */
    @SaIgnore
    @PostMapping("/resetPassword")
    public R<Void> resetPassword(@RequestBody ResetPasswordDTO resetPasswordDTO) {
        shopService.resetPassword(resetPasswordDTO.getPhone(), resetPasswordDTO.getCode(), resetPasswordDTO.getPassword(), resetPasswordDTO.getUuid());
        return R.ok();
    }
}

@Data
class ShopStatusDTO {
    /**
     * 商家ID
     */
    @NotNull(message = "商家ID不能为空")
    private Long id;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    private String status;

}

@Data
class ResetPasswordDTO {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String code;

    /**
     * 新密码
     */
    @NotBlank(message = "新密码不能为空")
    private String password;

    /**
     * 唯一标识
     */
    @NotBlank(message = "唯一标识不能为空")
    private String uuid;

}
