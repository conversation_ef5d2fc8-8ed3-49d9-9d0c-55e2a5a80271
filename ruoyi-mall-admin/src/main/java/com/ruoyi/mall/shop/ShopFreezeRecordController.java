package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.mall.domain.entity.ShopFreezeRecord;
import com.ruoyi.mall.service.IShopPaymentService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 店铺支付管理控制器
 */
@RestController
@RequestMapping("/mall/shopFreezeRecord")
@RequiredArgsConstructor
public class ShopFreezeRecordController extends BaseController {

    private final IShopPaymentService shopPaymentService;

    /**
     * 根据电话号码查询店铺信息
     */
    @GetMapping("/getByPhone/{phone}")
    @SaCheckPermission("mall:shopFreezeRecord:getByPhone")
    public R<Map<String, Object>> getShopByPhone(@PathVariable String phone) {
        return R.ok(shopPaymentService.getShopByPhone(phone));
    }

    /**
     * 获取店铺货款冻结记录
     */
    @GetMapping("/getShopFreezeRecords")
    @SaCheckPermission("mall:shopFreezeRecord:getShopFreezeRecords")
    public TableDataInfo<ShopFreezeRecord> getShopFreezeRecords(PageQuery pageQuery,
                                                                @RequestParam(required = false) Long shopId,
                                                                @RequestParam(required = false) String shopName,
                                                                @RequestParam(required = false) String shopPhone,
                                                                @RequestParam(required = false) String status
    ) {
        return shopPaymentService.getShopFreezeRecords(pageQuery, shopId, shopName, shopPhone, status);
    }

    /**
     * 冻结店铺货款
     */
    @PostMapping("/freezeAmount")
    @SaCheckPermission("mall:shopFreezeRecord:freezeAmount")
    @RepeatSubmit
    @Log(title = "店铺货款冻结", businessType = BusinessType.UPDATE)
    public R<Void> freezeShopAmount(@RequestBody Map<String, Object> params) {
        Long shopId = Long.valueOf(params.get("shopId").toString());
        BigDecimal amount = new BigDecimal(params.get("amount").toString());
        String reason = params.get("reason").toString();
        return toAjax(shopPaymentService.freezeShopAmount(shopId, amount, reason));
    }

    /**
     * 解冻店铺货款
     */
    @PostMapping("/unfreezeAmount/{freezeId}")
    @SaCheckPermission("mall:shopFreezeRecord:unfreezeAmount")
    @RepeatSubmit
    @Log(title = "店铺货款解冻", businessType = BusinessType.UPDATE)
    public R<Void> unfreezeShopAmount(@PathVariable Long freezeId) {
        return toAjax(shopPaymentService.unfreezeShopAmount(freezeId));
    }
}
