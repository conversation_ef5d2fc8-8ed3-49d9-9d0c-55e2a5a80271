package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.mall.domain.dto.ShopBankAccountDTO;
import com.ruoyi.mall.domain.entity.ShopBankAccount;
import com.ruoyi.mall.service.IShopBankAccountService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商家银行账户
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/shopBank")
public class ShopBankAccountController extends BaseController {

    private final IShopBankAccountService shopBankAccountService;

    /**
     * 查询商家银行账户列表
     */
    @SaCheckPermission("mall:shop:bank:list")
    @GetMapping("/list")
    public TableDataInfo<ShopBankAccount> list(ShopBankAccount shopBankAccount, PageQuery pageQuery) {
        return shopBankAccountService.selectShopBankAccountPage(shopBankAccount, pageQuery);
    }

    /**
     * 导出商家银行账户列表
     */
    @SaCheckPermission("mall:shop:bank:export")
    @Log(title = "商家银行账户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ShopBankAccount shopBankAccount, HttpServletResponse response) {
        List<ShopBankAccount> list = shopBankAccountService.selectShopBankAccountList(shopBankAccount);
        ExcelUtil.exportExcel(list, "商家银行账户", ShopBankAccount.class, response);
    }

    /**
     * 获取商家银行账户详细信息
     */
    @SaCheckPermission("mall:shop:bank:query")
    @GetMapping(value = "/{id}")
    public R<ShopBankAccount> getInfo(@PathVariable("id") Long id) {
        return R.ok(shopBankAccountService.getById(id));
    }

    /**
     * 新增商家银行账户
     */
    @SaCheckPermission("mall:shop:bank:add")
    @Log(title = "商家银行账户", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated @RequestBody ShopBankAccountDTO bankAccountDTO) {
        // 获取当前商家ID
        Long shopId = getShopId();
        return toAjax(shopBankAccountService.addShopBankAccount(bankAccountDTO, shopId));
    }

    /**
     * 修改商家银行账户
     */
    @SaCheckPermission("mall:shop:bank:edit")
    @Log(title = "商家银行账户", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated @RequestBody ShopBankAccountDTO bankAccountDTO) {
        return R.fail("暂不支付修改银行卡信息");
//        // 获取当前商家ID
//        Long shopId = getShopId();
//        return toAjax(shopBankAccountService.updateShopBankAccount(bankAccountDTO, shopId));
    }

    /**
     * 删除商家银行账户
     */
    @SaCheckPermission("mall:shop:bank:remove")
    @Log(title = "商家银行账户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> remove(@PathVariable Long id) {
        // 获取当前商家ID
        Long shopId = getShopId();
        return toAjax(shopBankAccountService.deleteShopBankAccount(id, shopId));
    }

    /**
     * 设置默认银行账户
     */
    @SaCheckPermission("mall:shop:bank:edit")
    @Log(title = "设置默认银行账户", businessType = BusinessType.UPDATE)
    @PutMapping("/default/{id}")
    public R<Void> setDefault(@PathVariable Long id) {
        // 获取当前商家ID
        Long shopId = getShopId();
        return toAjax(shopBankAccountService.setDefaultAccount(id, shopId));
    }

    /**
     * 获取当前商家ID
     */
    private Long getShopId() {
        return getLoginUser().getShopId();
    }
}
