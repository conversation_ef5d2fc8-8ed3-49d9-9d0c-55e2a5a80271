package com.ruoyi.mall.shop;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.redis.RedisUtils;
import com.ruoyi.mall.domain.dto.SettlementRecordDTO;
import com.ruoyi.mall.domain.dto.SettlementStatsDTO;
import com.ruoyi.mall.domain.entity.ShopSettlement;
import com.ruoyi.mall.domain.vo.*;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.service.IShopSettlementService;
import com.ruoyi.sms.enums.SmsType;
import com.ruoyi.sms.service.SmsLogService;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * 商家结算管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/shopSettlement")
public class ShopSettlementController extends BaseController {

    private final IShopSettlementService shopSettlementService;
    private final SmsLogService smsLogService;
    private final IConfigSettingService configSettingService;
    private final ISysUserService sysUserService;

    /**
     * 查询商家结算单列表
     */
    @SaCheckPermission("mall:shopSettlement:list")
    @GetMapping("/list")
    public TableDataInfo<ShopSettlement> list(ShopSettlement shopSettlement, PageQuery pageQuery) {
        shopSettlement.setShopId(getLoginUser().getShopId());
        return shopSettlementService.queryPageList(shopSettlement, pageQuery);
    }

    /**
     * 获取商家结算单详细信息
     */
    @SaCheckPermission("mall:shopSettlement:query")
    @GetMapping(value = "/{id}")
    public R<ShopSettlement> getInfo(@PathVariable("id") Long id) {
        return R.ok(shopSettlementService.getById(id));
    }

    /**
     * 发送结算验证码
     */
    @SaCheckPermission("mall:shopReturnAddress:apply")
    @PostMapping("/sendSmsCode")
    @Log(title = "发送结算验证码", businessType = BusinessType.OTHER)
    public R<Void> sendSmsCode(@Validated @RequestBody SettlementSendSmsVo smsVo) {
        Long userId = getUserId();
        Map<String, String> params = Maps.newHashMap();
        smsLogService.sendSms(SmsType.SETTLE, userId.toString(), smsVo.getContactPhone(), params);
        return R.ok();
    }

    /**
     * 验证结算验证码并申请结算（合并操作）
     */
    @SaCheckPermission("mall:shopReturnAddress:apply")
    @PostMapping("/verifyAndApply")
    @Log(title = "验证结算验证码并申请结算", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<ShopSettlement> verifyAndApply(@Validated @RequestBody SettlementVerifySmsVo verifyVo) {
        // 先验证验证码
//        boolean verified = smsLogService.checkValidCode(verifyVo.getContactPhone(),verifyVo.getCode(), getUserId().toString(), SmsType.SETTLE);
//        if (!verified) {
//            return R.fail("验证码验证失败，请重新输入");
//        }
        // 先验证验证码
        String key = CacheConstants.DEFAULT_CODE_KEY + verifyVo.getRandomStr();
        Object codeObj = RedisUtils.get(key);
        RedisUtils.del(key);
        if (codeObj == null) {
            return R.fail("验证码不合法");
        }
        String saveCode = codeObj.toString();
        if (StrUtil.isBlank(saveCode)) {
            return R.fail("验证码不合法");
        }
        if (!StrUtil.equals(saveCode, verifyVo.getRandomCode())) {
            return R.fail("验证码不合法");
        }
        // 1. 验证操作密码
        SysUser admin = sysUserService.getById(getUserId());
        if (StrUtil.isEmpty(admin.getOperationPassword())) {
            return R.fail("管理员未设置操作密码");
        }

        if (!BCrypt.checkpw(verifyVo.getOperationPassword(), admin.getOperationPassword())) {
            return R.fail("操作密码错误");
        }
        // 判断结算金额是否超过上限
        JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(7));
        if (verifyVo.getAmount().compareTo(configValue.getBigDecimal("bankMaxAmount")) > 0) {
            return R.fail("结算金额超过上限，请重新输入");
        }
        // 获取今天的开始和结束时间
        LocalDateTime startOfDay = LocalDateTime.now().with(LocalTime.MIN);
        LocalDateTime endOfDay = LocalDateTime.now().with(LocalTime.MAX);
        // 判断今天的结算金额是否到上限了
        List<ShopSettlement> list = shopSettlementService.list(
            new LambdaQueryWrapper<ShopSettlement>()
                .select(ShopSettlement::getAmount)
                .eq(ShopSettlement::getShopId, getLoginUser().getShopId())
                .in(ShopSettlement::getStatus, "0", "1", "3", "4")
                // 今天的结算
                .between(ShopSettlement::getCreateTime, startOfDay, endOfDay)
        );
        if (!list.isEmpty()) {
            // 判断是否超过次数限制
            int bankDailyLimit = configValue.getInt("bankDailyLimit");
            if (list.size() + 1 > bankDailyLimit) {
                return R.fail("今日申请结算次数已超过限制");
            }
            BigDecimal totalAmount = list.stream().map(ShopSettlement::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 将当前申请的结算金额累加到今日已有的结算金额总额中，用于判断是否超过每日结算金额上限。
            totalAmount = totalAmount.add(verifyVo.getAmount());
            if (totalAmount.compareTo(configValue.getBigDecimal("settlementAmountLimit")) > 0) {
                return R.fail("今日的结算金额超过上限，请重新输入");
            }
        }

        // 验证通过后，直接申请结算
        ShopSettlement settlement = shopSettlementService.applySettlement(
            getLoginUser().getShopId(),
            verifyVo.getAmount(),
            verifyVo.getAccountName(),
            verifyVo.getAccountNo(),
            verifyVo.getBankName(),
            verifyVo.getBankBranchName(),
            verifyVo.getContactPhone()
        );
        return R.ok(settlement);
    }

    /**
     * 审核结算单
     */
    @SaCheckPermission("mall:shopReturnAddress:audit")
    @PostMapping("/audit")
    @Log(title = "审核结算单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<Void> audit(@Validated @RequestBody SettlementAuditVo auditVo) {
        return toAjax(shopSettlementService.auditSettlement(
            auditVo.getId(),
            auditVo.getStatus(),
            getUserId(),
            getUsername(),
            auditVo.getAuditRemark()
        ));
    }

    /**
     * 确认打款
     */
    @SaCheckPermission("mall:shopReturnAddress:payment")
    @PostMapping("/payment")
    @Log(title = "确认打款", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<Void> payment(@Validated @RequestBody SettlementPaymentVo paymentVo) {
        return toAjax(shopSettlementService.confirmPayment(
            paymentVo.getId(),
            getUserId(),
            getUsername(),
            paymentVo.getPayProof(),
            paymentVo.getPayRemark()
        ));
    }

    /**
     * 完成结算
     */
    @SaCheckPermission("mall:shopReturnAddress:complete")
    @PostMapping("/complete/{id}")
    @Log(title = "完成结算", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<Void> complete(@PathVariable Long id) {
        return toAjax(shopSettlementService.completeSettlement(id));
    }

    /**
     * 取消结算单
     */
    @SaCheckPermission("mall:shopReturnAddress:cancel")
    @PostMapping("/cancel/{id}")
    @Log(title = "取消结算单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<Void> cancel(@PathVariable Long id, @RequestParam String remark) {
        return toAjax(shopSettlementService.cancelSettlement(id, remark));
    }

    /**---------------------------------------------- 分割线 -----------------------------------------------------------*/


    /**
     * 结算统计
     *
     * @param dto       请求参数 DTO
     * @param pageQuery 分页对象
     * @return TableDataInfo<SettlementStatsVO>
     * <AUTHOR>
     */
    @GetMapping("/pageSettlementStats")
    public TableDataInfo<SettlementStatsVO> pageSettlementStats(SettlementStatsDTO dto, PageQuery pageQuery) {
        return shopSettlementService.pageSettlementStats(dto, pageQuery);
    }


    /**
     * 导出结算统计
     *
     * @param dto      统计参数
     * @param response 响应对象
     * @return void
     * <AUTHOR>
     */
    @PostMapping("/exportSettlementStats")
    public void exportSettlementStats(SettlementStatsDTO dto, HttpServletResponse response) {
        List<SettlementStatsVO> list = shopSettlementService.listSettlementStats(dto);
        ExcelUtil.exportExcel(list, "结算统计数据", SettlementStatsVO.class, response);
    }


    /**
     * 结算记录  （分页）
     *
     * @param dto
     * @param pageQuery
     * @return TableDataInfo<SettlementRecordVO>
     * <AUTHOR>
     */
    @GetMapping("/pageSettlementRecord")
    public TableDataInfo<SettlementRecordVO> pageSettlementRecord(SettlementRecordDTO dto, PageQuery pageQuery) {
        return shopSettlementService.pageSettlementRecord(dto, pageQuery);
    }


    /**
     * 导出结算记录
     *
     * @param dto
     * @param response
     * @return void
     * <AUTHOR>
     */
    @PostMapping("/exportSettlementRecord")
    public void exportSettlementRecord(SettlementRecordDTO dto, HttpServletResponse response) {
        List<SettlementRecordVO> list = shopSettlementService.listSettlementRecord(dto);
        ExcelUtil.exportExcel(list, "商家结算记录", SettlementRecordVO.class, response);
    }


}
