package com.ruoyi.mall.order;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.mall.domain.entity.Order;
import com.ruoyi.mall.domain.entity.OrderRefund;
import com.ruoyi.mall.enums.ReturnMoneyStsEnum;
import com.ruoyi.mall.service.IOrderRefundService;
import com.ruoyi.mall.service.IOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * 订单退款管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/orderRefund")
public class OrderRefundController extends BaseController {

    private final IOrderRefundService orderRefundService;
    private final IOrderService orderService;

    /**
     * 查询退款列表
     */
    @SaCheckPermission("mall:orderRefund:list")
    @GetMapping("/list")
    public TableDataInfo<OrderRefund> list(OrderRefund orderRefund, PageQuery pageQuery) {
        // 设置为商家查询模式 - 通过只设置shopId但不设置userId来标识商家模式
        orderRefund.setShopId(getLoginUser().getShopId());
        if (orderRefund.getShopId() != null && orderRefund.getUserId() != null) {
            orderRefund.setUserId(null);
        }

        return orderRefundService.selectRefundPage(orderRefund, pageQuery);
    }

    /**
     * 获取退款详情
     */
    @SaCheckPermission("mall:orderRefund:getInfo")
    @GetMapping(value = "/{refundId}")
    public R<OrderRefund> getInfo(@PathVariable("refundId") Long refundId) {
        // 使用商家模式，加载订单详情
        OrderRefund refund = orderRefundService.getRefundDetail(refundId, true);
        return R.ok(refund);
    }

    /**
     * 同意退款
     */
    @SaCheckPermission("mall:orderRefund:processRefund")
    @Log(title = "同意退款", businessType = BusinessType.UPDATE)
    @PostMapping("/process/{refundId}")
    public R<Void> processRefund(@PathVariable("refundId") Long refundId) {
        return toAjax(orderRefundService.processRefund(refundId));
    }

    /**
     * 拒绝退款
     */
    @SaCheckPermission("mall:orderRefund:rejectRefund")
    @Log(title = "拒绝退款", businessType = BusinessType.UPDATE)
    @PostMapping("/reject/{refundId}")
    public R<Void> rejectRefund(
        @PathVariable("refundId") Long refundId,
        @RequestParam("rejectMessage") String rejectMessage) {
        if (rejectMessage == null || rejectMessage.isEmpty()) {
            return R.fail("拒绝原因不能为空");
        }
        return toAjax(orderRefundService.rejectRefund(refundId, rejectMessage));
    }

    /**
     * 修改退款金额
     */
    @SaCheckPermission("mall:orderRefund:updateRefundAmount")
    @Log(title = "修改退款金额", businessType = BusinessType.UPDATE)
    @PostMapping("/updateAmount/{refundId}")
    public R<Void> updateRefundAmount(
        @PathVariable("refundId") Long refundId,
        @RequestParam("refundAmount") BigDecimal refundAmount) {

        if (refundAmount == null || refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return R.fail("退款金额必须大于0");
        }

        OrderRefund orderRefund = orderRefundService.getById(refundId);
        if (orderRefund == null) {
            return R.fail("退款记录不存在");
        }

        // 验证退款状态
        if (!ReturnMoneyStsEnum.APPLY.value().equals(orderRefund.getReturnMoneySts())) {
            return R.fail("当前退款状态不允许修改金额");
        }

        // 获取订单信息
        Order order = orderService.getById(orderRefund.getOrderId());
        if (order == null) {
            return R.fail("订单不存在");
        }

        // 验证退款金额
        if (refundAmount.compareTo(order.getPayAmount()) > 0) {
            return R.fail("退款金额不能大于订单实付金额");
        }

        // 更新退款金额
        orderRefund.setRefundAmount(refundAmount);
        return toAjax(orderRefundService.updateById(orderRefund));
    }
}
