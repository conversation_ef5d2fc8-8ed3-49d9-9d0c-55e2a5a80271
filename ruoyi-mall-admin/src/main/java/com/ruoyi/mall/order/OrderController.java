package com.ruoyi.mall.order;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OrderStatusEnum;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.mall.domain.dto.ShipmentOderDto;
import com.ruoyi.mall.domain.dto.UpdateOrderAddressDTO;
import com.ruoyi.mall.domain.dto.UpdateOrderPriceDTO;
import com.ruoyi.mall.domain.entity.Order;
import com.ruoyi.mall.domain.vo.OrderExportVo;
import com.ruoyi.mall.service.IOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订单
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mall/order")
public class OrderController extends BaseController {

    private final IOrderService orderService;

    /**
     * 查询订单列表
     */
    @SaCheckPermission("mall:order:list")
    @GetMapping("/list")
    public TableDataInfo<Order> list(Order order, PageQuery pageQuery,
                                     @RequestParam(required = false) String startTime,
                                     @RequestParam(required = false) String endTime) {
        if (ObjUtil.equals(1, getLoginUser().getShopType())) {
            order.setShopId(getLoginUser().getShopId());
        }
        if (ObjUtil.equals(2, getLoginUser().getShopType())) {
            order.setConsignmentId(getLoginUser().getShopId());
        }

        if (StrUtil.isNotEmpty(startTime)) {
            order.getParams().put("startTime", startTime);
        }
        if (StrUtil.isNotEmpty(endTime)) {
            order.getParams().put("endTime", endTime);
        }
        return orderService.selectOrderPage(order, pageQuery);
    }

    /**
     * 导出订单列表
     */
    @SaCheckPermission("mall:order:export")
    @PostMapping("/export")
    @Log(title = "订单导出", businessType = BusinessType.EXPORT)
    public void export(Order order, HttpServletResponse response,
                       @RequestParam(required = false) String startTime,
                       @RequestParam(required = false) String endTime) {
        order.setShopId(getLoginUser().getShopId());
        if (StrUtil.isNotEmpty(startTime)) {
            order.getParams().put("startTime", startTime);
        }
        if (StrUtil.isNotEmpty(endTime)) {
            order.getParams().put("endTime", endTime);
        }
        List<OrderExportVo> list = orderService.selectOrderExportList(order);
        ExcelUtil.exportExcel(list, "订单数据", OrderExportVo.class, response);
    }

    /**
     * 获取订单详细信息
     */
    @SaCheckPermission("mall:order:getInfo")
    @GetMapping(value = "/{orderNo}")
    public R<Order> getInfo(@PathVariable("orderNo") String orderNo) {
        return R.ok(orderService.selectOrderByOrderNo(orderNo));
    }


    /**
     * 订单发货
     */
    @SaCheckPermission("mall:order:shipmentOder")
    @PostMapping(value = "/shipmentOder")
    @Log(title = "订单发货", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<Boolean> shipmentOder(@Validated @RequestBody ShipmentOderDto shipmentOderDto) {
        String receiverType = shipmentOderDto.getReceiverType();
        if (ObjUtil.equals(receiverType, "1")) {
            if (ObjUtil.isEmpty(shipmentOderDto.getDeliveryId())) {
                return R.fail("快递公司不能为空");
            }
            if (ObjUtil.isEmpty(shipmentOderDto.getDeliveryNumber())) {
                return R.fail("快递单号不能为空");
            }
        }
        Order order = orderService.getOrderByOrderNo(shipmentOderDto.getOrderNo());
        if (null == order) {
            return R.fail("订单不存在");
        }
        if (!OrderStatusEnum.PENDING_SHIPMENT.getCode().equals(order.getStatus())) {
            return R.fail("订单并非待发货状态");
        }
        return R.ok(orderService.shipmentOder(shipmentOderDto));
    }

//    /**
//     * 创建订单
//     */
//    @SaCheckPermission("mall:order:add")
//    @PostMapping
//    @RepeatSubmit()
//    public R<String> add(@Validated @RequestBody OrderCreateDTO orderCreate) {
//        return R.ok(orderService.createOrder(orderCreate));
//    }

    /**
     * 取消订单
     */
    @SaCheckPermission("mall:order:cancel")
    @PutMapping("/cancel/{orderNo}")
    @Log(title = "取消订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<Boolean> cancel(@PathVariable("orderNo") String orderNo) {
        return R.ok(orderService.cancelOrder(orderNo));
    }

    /**
     * 删除订单
     */
    @SaCheckPermission("mall:order:remove")
    @DeleteMapping("/{orderNo}")
    @Log(title = "删除订单", businessType = BusinessType.DELETE)
    @RepeatSubmit()
    public R<Boolean> remove(@PathVariable("orderNo") String orderNo) {
        return R.ok(orderService.deleteOrderById(orderNo));
    }

    /**
     * 修改订单收货地址
     */
    @SaCheckPermission("mall:order:updateAddress")
    @PutMapping("/updateAddress")
    @Log(title = "修改订单收货地址", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<Boolean> updateAddress(@Validated @RequestBody UpdateOrderAddressDTO addressDTO) {
        return R.ok(orderService.updateOrderAddress(addressDTO));
    }

    /**
     * 修改订单价格
     */
    @SaCheckPermission("mall:order:updatePrice")
    @PutMapping("/updatePrice")
    @Log(title = "修改订单价格", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    public R<Boolean> updatePrice(@Validated @RequestBody UpdateOrderPriceDTO updateOrderPriceDTO) {
        return R.ok(orderService.updateOrderPrice(updateOrderPriceDTO));
    }

}
