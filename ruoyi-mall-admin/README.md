# ruoyi-mall-admin 商城管理后台模块

## 模块介绍

`ruoyi-mall-admin` 是MallB电商平台的商城管理后台服务模块，提供商城管理员操作的所有API接口和业务功能，负责整个商城的运营管理、商品管理、订单管理等后台功能实现。

## 主要功能

- 商品管理
    - 商品发布与编辑
    - 商品审核流程
    - 商品上下架管理
    - 商品分类管理
    - 商品标签管理
- 订单管理
    - 订单查询与处理
    - 订单状态变更
    - 发货管理
    - 退款处理
    - 售后管理
- 用户管理
    - 用户信息管理
    - 会员等级设置
    - 用户积分管理
- 商家管理
    - 商家审核
    - 商家评级
    - 结算管理
    - 佣金设置
- 营销管理
    - 优惠券管理
    - 限时活动
    - 满减活动
    - 秒杀活动
- 财务管理
    - 交易记录
    - 退款处理
    - 结算报表
- 内容管理
    - 广告位管理
    - 首页推荐
    - 平台公告

## 技术实现

- RESTful API设计
- 权限控制：基于RBAC模型
- 数据校验：参数验证和业务规则校验
- 事务管理：确保数据一致性
- 日志记录：操作审计和异常记录

## 与其他模块交互

- 调用ruoyi-system模块：获取基础系统功能
- 调用ruoyi-mall-server模块：复用核心商城业务逻辑
- 被前端管理界面调用：提供数据交互接口

## 开发指南

1. 接口遵循RESTful规范
2. 操作需记录日志，特别是敏感操作
3. 涉及资金的操作需严格校验权限
4. 大批量数据操作需考虑性能优化 