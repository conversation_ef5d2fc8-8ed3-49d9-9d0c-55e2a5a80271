package ${packageName}.domain.bo;

import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import java.util.Date;

#foreach ($import in $importList)
import ${import};
#end
#if($table.crud || $table.sub)
import com.ruoyi.common.core.domain.BaseEntity;
#elseif($table.tree)
import com.ruoyi.common.core.domain.TreeEntity;
#end

/**
 * ${functionName}业务对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
    #set($Entity="BaseEntity")
#elseif($table.tree)
    #set($Entity="TreeEntity<${ClassName}Bo>")
#end

@Data
@EqualsAndHashCode(callSuper = true)

public class ${ClassName}Bo extends ${Entity} {

    #foreach ($column in $columns)
        #if(!$table.isSuperColumn($column.javaField) && ($column.query || $column.insert || $column.edit))
            /**
             * $column.columnComment
             */
            #if($column.insert && $column.edit)
                #set($Group="AddGroup.class, EditGroup.class")
            #elseif($column.insert)
                #set($Group="AddGroup.class")
            #elseif($column.edit)
                #set($Group="EditGroup.class")
            #end
            #if($column.required)
                #if($column.javaType == 'String')
                @NotBlank(message = "$column.columnComment不能为空", groups = { $Group })
                #else
                @NotNull(message = "$column.columnComment不能为空", groups = { $Group })
                #end
            #end
        private $column.javaType $column.javaField;

        #end
    #end

}
