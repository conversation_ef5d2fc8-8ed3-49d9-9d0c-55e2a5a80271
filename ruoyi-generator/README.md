# ruoyi-generator 代码生成模块

## 模块介绍

`ruoyi-generator` 是MallB电商平台的代码生成工具，通过读取数据库表结构信息，自动生成对应的实体类、Mapper、Service、Controller等相关代码文件，提高开发效率，保证代码规范一致性。

## 主要功能

- 数据库表结构读取
- 实体类代码生成
- MyBatis映射文件生成
- Service层代码生成
- Controller层代码生成
- 前端Vue页面代码生成
- JavaScript交互代码生成
- SQL脚本生成
- 表单验证代码生成
- 多模板支持

## 支持的数据库

- MySQL
- Oracle
- SQL Server
- PostgreSQL

## 使用方式

1. 通过Web界面操作：
    - 访问系统的代码生成菜单
    - 选择数据表
    - 配置生成选项
    - 点击生成代码

2. 通过API调用：
   ```java
   GenUtils.generatorCode(tableInfo, columnInfos, zipOutputStream);
   ```

## 自定义配置

- 模板路径：`resources/vm/`
- 包名前缀：在配置中指定
- 模块名称：根据实际模块划分指定
- 表前缀：可配置需要忽略的表前缀
- 字段映射：可配置字段类型与Java类型映射关系

## 扩展开发

1. 自定义模板：在templates目录下修改或新增模板文件
2. 自定义类型映射：修改类型映射配置
3. 自定义生成规则：扩展GenUtils类 