# ruoyi-sms 短信服务模块

## 模块介绍

`ruoyi-sms` 是MallB电商平台的短信服务模块，提供统一的短信发送接口，支持多种短信服务供应商，用于系统通知、验证码发送、营销活动等场景的短信服务。

## 主要功能

- 短信发送
    - 验证码短信
    - 通知类短信
    - 营销类短信
    - 国际短信支持
- 短信模板管理
    - 模板创建与管理
    - 模板变量替换
    - 模板审核状态管理
- 短信记录管理
    - 发送记录查询
    - 发送状态追踪
    - 发送统计分析
- 短信配置管理
    - 多渠道配置
    - 渠道优先级设置
    - 渠道故障转移

## 支持的短信服务商

- 阿里云短信
- 腾讯云短信
- 华为云短信
- 七牛云短信
- 网易云信
- 云片短信

## 配置说明

短信配置已经从`ruoyi-admin`模块的`application-xxx.yml`文件中抽取出来，放置在`ruoyi-sms`模块的`sms.yml`配置文件中统一管理。

配置文件内容示例：

```yaml
# sms 短信支持 阿里云 腾讯云 云片 等各式各样的短信服务商
# https://wind.kim/doc/start 文档地址 各个厂商可同时使用
sms:
  # 阿里云 dysmsapi.aliyuncs.com
  alibaba:
    #请求地址 默认为 dysmsapi.aliyuncs.com 如无特殊改变可以不用设置
    requestUrl: dysmsapi.aliyuncs.com
    #阿里云的accessKey
    accessKeyId: xxxxxxx
    #阿里云的accessKeySecret
    accessKeySecret: xxxxxxx
    #短信签名
    signature: 测试
  tencent:
    #请求地址默认为 sms.tencentcloudapi.com 如无特殊改变可不用设置
    requestUrl: sms.tencentcloudapi.com
    #腾讯云的accessKey
    accessKeyId: AKIDFZktto4IJtQoyuhXZSCk6MVXQA0slZPs
    #腾讯云的accessKeySecret
    accessKeySecret: PbcPyKVSBh05xb1MUP7f082jCf2EKGzY
    #短信签名
    signature: 广州中业科技
    #短信sdkAppId
    sdkAppId: 1400987731
    #地域信息默认为 ap-guangzhou 如无特殊改变可不用设置
    territory: ap-guangzhou
```

## 使用示例

### 发送验证码

```java
@Autowired
private SmsService smsService;

public void sendVerificationCode(String phoneNumber) {
    String code = generateRandomCode();
    Map<String, String> params = new HashMap<>();
    params.put("code", code);

    smsService.send(phoneNumber, "SMS_VERIFY_CODE", params);

    // 缓存验证码用于后续验证
    cacheService.set("sms:verify:" + phoneNumber, code, 5, TimeUnit.MINUTES);
}
```

### 发送订单通知

```java
public void sendOrderNotification(String phoneNumber, String orderNo, String amount) {
    Map<String, String> params = new HashMap<>();
    params.put("orderNo", orderNo);
    params.put("amount", amount);

    smsService.send(phoneNumber, "SMS_ORDER_PAID", params);
}
```

## 模板管理

系统预设了常用的短信模板：

- SMS_VERIFY_CODE: 验证码模板
- SMS_ORDER_PAID: 订单支付成功通知
- SMS_ORDER_SHIPPED: 订单发货通知
- SMS_PASSWORD_RESET: 密码重置通知

## 注意事项

- 短信发送频率需要控制，防止恶意攻击
- 敏感操作应使用更安全的验证方式
- 营销短信需遵守相关法规，尊重用户隐私 