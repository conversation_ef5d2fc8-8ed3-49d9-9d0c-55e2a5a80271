# sms 短信支持 阿里云 腾讯云 云片 等各式各样的短信服务商
# https://wind.kim/doc/start 文档地址 各个厂商可同时使用
sms:
  # 阿里云 dysmsapi.aliyuncs.com
  alibaba:
    #请求地址 默认为 dysmsapi.aliyuncs.com 如无特殊改变可以不用设置
    requestUrl: dysmsapi.aliyuncs.com
    #阿里云的accessKey
    accessKeyId: xxxxxxx
    #阿里云的accessKeySecret
    accessKeySecret: xxxxxxx
    #短信签名
    signature: 测试
  tencent:
    #请求地址默认为 sms.tencentcloudapi.com 如无特殊改变可不用设置
    requestUrl: sms.tencentcloudapi.com
    #腾讯云的accessKey
    accessKeyId: AKIDFZktto4IJtQoyuhXZSCk6MVXQA0slZPs
    #腾讯云的accessKeySecret
    accessKeySecret: PbcPyKVSBh05xb1MUP7f082jCf2EKGzY
    #短信签名
    signature: 中南惠
    #短信sdkAppId
    sdkAppId: 1400987731
    #地域信息默认为 ap-guangzhou 如无特殊改变可不用设置
    territory: ap-guangzhou
