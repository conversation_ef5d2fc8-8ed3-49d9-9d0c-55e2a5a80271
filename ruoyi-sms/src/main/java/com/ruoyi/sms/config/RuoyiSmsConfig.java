package com.ruoyi.sms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 短信配置类
 */
@Data
@Configuration("ruoyiSmsConfig")
@ConfigurationProperties(prefix = "sms")
public class RuoyiSmsConfig {
    
    /**
     * 阿里云短信配置
     */
    private AlibabaSms alibaba = new AlibabaSms();
    
    /**
     * 腾讯云短信配置
     */
    private TencentSms tencent = new TencentSms();
    
    /**
     * 阿里云短信配置类
     */
    @Data
    public static class AlibabaSms {
        /**
         * 请求地址
         */
        private String requestUrl = "dysmsapi.aliyuncs.com";
        
        /**
         * AccessKey ID
         */
        private String accessKeyId;
        
        /**
         * AccessKey Secret
         */
        private String accessKeySecret;
        
        /**
         * 短信签名
         */
        private String signature;
    }
    
    /**
     * 腾讯云短信配置类
     */
    @Data
    public static class TencentSms {
        /**
         * 请求地址
         */
        private String requestUrl = "sms.tencentcloudapi.com";
        
        /**
         * AccessKey ID
         */
        private String accessKeyId;
        
        /**
         * AccessKey Secret
         */
        private String accessKeySecret;
        
        /**
         * 短信签名
         */
        private String signature;
        
        /**
         * SDK AppID
         */
        private String sdkAppId;
        
        /**
         * 地域信息
         */
        private String territory = "ap-guangzhou";
    }
} 