package com.ruoyi.sms.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * 短信模块自动配置
 * 加载短信配置文件
 */
@Configuration
@PropertySource(value = "classpath:sms.yml", factory = YamlPropertySourceFactory.class)
public class SmsAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(SmsAutoConfiguration.class);

    public SmsAutoConfiguration() {
        log.info("加载短信模块配置: sms.yml");
    }
} 