/*
 * Copyright (c) 2018-2999 广州亚米信息科技有限公司 All rights reserved.
 *
 * https://www.gz-yami.com/
 *
 * 未经允许，不可做商业用途！
 *
 * 版权所有，侵权必究！
 */

package com.ruoyi.sms.enums;

/**
 * 短信类型
 *
 * <AUTHOR>
 */
public enum SmsType {


    /**
     * APP登录
     */
    LOGIN(0, "2504476", "验证码为：{code}，您正在登录，若非本人操作，请勿泄露（15分钟内验证码有效)。"),

    /**
     * 用户注册验证码
     */
    REGISTER(1, "2504471", "您的注册验证码：${code}，如非本人操作，请忽略本短信（15分钟内验证码有效)！"),

    /**
     * 商家结算货款
     */
    SETTLE(5, "2504470", "尊敬的客户，你正在进行结算，您的验证码为：${code}，请勿泄漏于他人（15分钟内验证码有效)！"),

    /**
     * 操作密码重置
     */
    OPERATION_PWD_RESET(6, "2504469", "您的操作密码重置验证码：${code}，如非本人操作，请忽略本短信（15分钟内验证码有效)！"),

    /**
     * 登录密码重置
     */
    LOGIN_PWD_RESET(7, "2504468", "您的登录密码重置验证码：${code}，如非本人操作，请忽略本短信（15分钟内验证码有效)！"),

    /**
     * 注销账号
     */
    LOGOUT_ACCOUNT(8,"2504565","注销账户验证码：${code}，5分钟内有效。完成注销后，所有数据将无法恢复，请谨慎操作（15分钟内验证码有效)！"),

    /**
     * 商家重置登录密码
     */
    SHOP_LOGIN_PWD_RESET(9,"2504464","企业后台账户登录密码重置验证码：${code}，如非本人操作，请忽略本短信（15分钟内验证码有效)！"),

    /**
     * 通知商家入住成功
     */
    SHOP_CHECK(11, "2428333", "尊敬的客户，欢迎您成为中南惠共享平台的商家，官方后台网址：http://localhost:2025/#/login  帐号：您的手机号 ，初始密码：123456（登录后请及时修改）。如有疑问，请联系在线客服");

    private Integer num;

    private String templateCode;

    private String content;

    SmsType(Integer num, String templateCode, String content) {
        this.num = num;
        this.templateCode = templateCode;
        this.content = content;
    }

    public static SmsType instance(Integer value) {
        SmsType[] enums = values();
        for (SmsType statusEnum : enums) {
            if (statusEnum.value().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

    public Integer value() {
        return num;
    }

    public String getTemplateCode() {
        return this.templateCode;
    }

    public String getContent() {
        return this.content;
    }
}
