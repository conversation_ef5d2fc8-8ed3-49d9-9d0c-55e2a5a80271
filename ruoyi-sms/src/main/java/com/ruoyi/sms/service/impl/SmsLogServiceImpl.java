package com.ruoyi.sms.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.redis.RedisUtils;
import com.ruoyi.sms.domain.SmsLog;
import com.ruoyi.sms.domain.TempLoginUser;
import com.ruoyi.sms.enums.SmsType;
import com.ruoyi.sms.mapper.SmsLogMapper;
import com.ruoyi.sms.service.SmsLogService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.api.entity.SmsResponse;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.dromara.sms4j.provider.enumerate.SupplierType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.*;
import java.util.Map.Entry;

@Service
@Slf4j
@AllArgsConstructor
public class SmsLogServiceImpl extends ServiceImpl<SmsLogMapper, SmsLog> implements SmsLogService {

    /**
     * 短信验证码的前缀
     */
    public static final String CHECK_VALID_CODE_NUM_PREFIX = "checkValidCodeNum_";
    public static final String TEMP_LOGIN_USER_PREFIX = "tempLogin_";
    /**
     * 当天最大验证码短信发送量
     */
    private static final int TODAY_MAX_SEND_VALID_SMS_NUMBER = 50;
    /**
     * 一段时间内短信验证码的最大验证次数
     */
    private static final int TIMES_CHECK_VALID_CODE_NUM = 10;

    /**
     * 验证码有效期 15分钟
     */
    private static final int EXPIRE_TIME = 15;

    /**
     * 短信列表的大小/列表的索引
     */
    private static final Integer INDEX = 0;
    private SmsLogMapper smsLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void sendSms(SmsType smsType, String userId, String mobile, Map<String, String> params) {

        SmsLog smsLog = new SmsLog();
        boolean isTrue =
            Objects.equals(SmsType.LOGIN, smsType) ||
                Objects.equals(SmsType.REGISTER, smsType) ||
                Objects.equals(SmsType.SETTLE, smsType) ||
                Objects.equals(SmsType.OPERATION_PWD_RESET, smsType) ||
                Objects.equals(SmsType.LOGIN_PWD_RESET, smsType)||
                Objects.equals(SmsType.LOGOUT_ACCOUNT, smsType) ||
                Objects.equals(SmsType.SHOP_LOGIN_PWD_RESET, smsType);
        if (isTrue) {
            List<SmsLog> smsLogList = smsLogMapper.selectList(new LambdaQueryWrapper<SmsLog>()
                .gt(SmsLog::getRecDate, DateUtil.beginOfDay(new Date()))
                .lt(SmsLog::getRecDate, DateUtil.endOfDay(new Date()))
                .eq(SmsLog::getUserId, userId)
                .eq(SmsLog::getType, smsType.value())
                .orderByDesc(SmsLog::getRecDate)
            );
            if (smsLogList.size() >= TODAY_MAX_SEND_VALID_SMS_NUMBER) {
                throw new ServiceException("今日发送短信验证码次数已达到上限");
            }
            SmsLog smsLogLast = null;
            if (smsLogList.size() > INDEX) {
                smsLogLast = smsLogList.get(INDEX);
                long currentTimeMillis = System.currentTimeMillis();
                long timeDb = DateUtil.offsetSecond(smsLogLast.getRecDate(), 60).getTime();
                if (currentTimeMillis < timeDb) {
                    throw new ServiceException("一分钟内只能发送一次验证码");
                }
            }
            // 将上一条验证码失效
            smsLogMapper.invalidSmsByMobileAndType(mobile, smsType.value());
            String code = RandomUtil.randomNumbers(6);
            // 判断是否使用上一条验证码
            if (smsLogLast != null ){
                // 判断验证码是否过期
                DateTime offsetMinute = DateUtil.offsetMinute(smsLogLast.getRecDate(), EXPIRE_TIME);
                if (offsetMinute.getTime() > System.currentTimeMillis()) {
                    code = smsLogLast.getMobileCode();
                }
            }
            params.put("code", code);
        }
        smsLog.setType(smsType.value());
        smsLog.setMobileCode(params.get("code"));
        smsLog.setRecDate(new Date());
        smsLog.setStatus(1);
        smsLog.setUserId(userId);
        smsLog.setUserPhone(mobile);
        smsLog.setContent(formatContent(smsType, params));
        smsLog.setResponseCode(this.sendSms(mobile, smsType.getTemplateCode(), params));
        smsLogMapper.insert(smsLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean checkValidCode(String mobile, String code, SmsType smsType) {
        long checkValidCodeNum = RedisUtils.incr(CHECK_VALID_CODE_NUM_PREFIX + mobile, 1);
        if (checkValidCodeNum == 1) {
            // 半小时后失效
            RedisUtils.expire(CHECK_VALID_CODE_NUM_PREFIX + mobile, 1800);
        }
        if (checkValidCodeNum >= TIMES_CHECK_VALID_CODE_NUM) {
            throw new ServiceException("验证码校验过频繁，请稍后再试");
        }
        SmsLog sms = new SmsLog();
        sms.setUserPhone(mobile);
        sms.setMobileCode(code);
        sms.setStatus(1);
        sms.setType(smsType.value());

        SmsLog dbSms = smsLogMapper.selectOne(new LambdaQueryWrapper<SmsLog>()
            .eq(SmsLog::getUserPhone, mobile)
            .eq(SmsLog::getMobileCode, code)
            .eq(SmsLog::getStatus, 1)
            .eq(SmsLog::getType, smsType.value()));
        // 没有找到当前的验证码
        if (dbSms == null) {
            RedisUtils.incr(CHECK_VALID_CODE_NUM_PREFIX + mobile, 1);
            return false;
        }
        RedisUtils.del(CHECK_VALID_CODE_NUM_PREFIX + mobile);
        // 标记为失效状态
        dbSms.setStatus(0);
        smsLogMapper.updateById(dbSms);
        // 验证码已过期
        DateTime offsetMinute = DateUtil.offsetMinute(dbSms.getRecDate(), EXPIRE_TIME);
        if (offsetMinute.getTime() < System.currentTimeMillis()) {
            RedisUtils.incr(CHECK_VALID_CODE_NUM_PREFIX + mobile, 1);
            return false;
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean checkValidCode(String mobile, String code, String userId, SmsType smsType) {
        long checkValidCodeNum = RedisUtils.incr(CHECK_VALID_CODE_NUM_PREFIX + mobile, 1);
        if (checkValidCodeNum == 0) {
            // 半小时后失效
            RedisUtils.expire(CHECK_VALID_CODE_NUM_PREFIX + mobile, 1800);
        }
        if (checkValidCodeNum >= TIMES_CHECK_VALID_CODE_NUM) {
            throw new ServiceException("验证码校验过频繁，请稍后再试");
        }
        SmsLog sms = new SmsLog();
        sms.setUserPhone(mobile);
        sms.setMobileCode(code);
        sms.setStatus(1);
        sms.setType(smsType.value());

        SmsLog dbSms = smsLogMapper.selectOne(new LambdaQueryWrapper<SmsLog>()
            .eq(SmsLog::getUserPhone, mobile)
            .eq(SmsLog::getMobileCode, code)
            .eq(SmsLog::getStatus, 1)
            .eq(SmsLog::getType, smsType.value())
            .eq(SmsLog::getUserId, userId));
        // 没有找到当前的验证码
        if (dbSms == null) {
            RedisUtils.incr(CHECK_VALID_CODE_NUM_PREFIX + mobile, 1);
            return false;
        }
        RedisUtils.del(CHECK_VALID_CODE_NUM_PREFIX + mobile);
        // 标记为失效状态
        dbSms.setStatus(0);
        smsLogMapper.updateById(dbSms);
        // 验证码已过期
        DateTime offsetMinute = DateUtil.offsetMinute(dbSms.getRecDate(), 15);
        if (offsetMinute.getTime() < System.currentTimeMillis()) {
            RedisUtils.incr(CHECK_VALID_CODE_NUM_PREFIX + mobile, 1);
            return false;
        }

        TempLoginUser user = TempLoginUser.builder().mobileTel(mobile).build();
        RedisUtils.setCacheObject(TEMP_LOGIN_USER_PREFIX + userId, user, Duration.ofMinutes(Constants.CAPTCHA_EXPIRATION));

        return true;
    }


    public String sendSms(String mobile, String templateCode, Map<String, String> params) {
        String data;
        try {
            // 使用sms4j发送腾讯云短信
            SmsBlend smsBlend = SmsFactory.createSmsBlend(SupplierType.TENCENT);
            // 将Map转换为LinkedHashMap
            LinkedHashMap<String, String> linkedParams = new LinkedHashMap<>(params);
            SmsResponse smsResponse = smsBlend.sendMessage(mobile, templateCode, linkedParams);
            data = smsResponse.getMessage();
        } catch (Exception e) {
            throw new ServiceException("发送短信失败，请稍后再试:" + e.getMessage());
        }
        return data;
    }

    private String formatContent(SmsType smsType, Map<String, String> params) {
        if (MapUtil.isEmpty(params)) {
            return smsType.getContent();
        }
        String content = smsType.getContent();
        for (Entry<String, String> element : params.entrySet()) {
            content = content.replace("${" + element.getKey() + "}", element.getValue());
        }
        return content;
    }

    /**
     * 管理人员发送短信 不限制
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void adminSendSms(SmsType smsType, String userId, String mobile, Map<String, String> params) {

        SmsLog smsLog = new SmsLog();
        boolean isTrue = Objects.equals(SmsType.LOGIN, smsType) || Objects.equals(SmsType.REGISTER, smsType);
        if (isTrue) {
            List<SmsLog> smsLogList = smsLogMapper.selectList(new LambdaQueryWrapper<SmsLog>()
                .gt(SmsLog::getRecDate, DateUtil.beginOfDay(new Date()))
                .lt(SmsLog::getRecDate, DateUtil.endOfDay(new Date()))
                .eq(SmsLog::getUserId, userId)
                .eq(SmsLog::getType, smsType.value())
                .orderByDesc(SmsLog::getRecDate)
            );

            if (smsLogList.size() > INDEX) {
                SmsLog smsLogLast = smsLogList.get(INDEX);
                long currentTimeMillis = System.currentTimeMillis();
                long timeDb = DateUtil.offsetSecond(smsLogLast.getRecDate(), 60).getTime();
                if (currentTimeMillis < timeDb) {
                    throw new ServiceException("一分钟内只能发送一次验证码");
                }
            }
            // 将上一条验证码失效
            smsLogMapper.invalidSmsByMobileAndType(mobile, smsType.value());
            String code = RandomUtil.randomNumbers(6);
            params.put("code", code);
        }
        smsLog.setType(smsType.value());
        smsLog.setMobileCode(params.get("code"));
        smsLog.setRecDate(new Date());
        smsLog.setStatus(1);
        smsLog.setUserId(userId);
        smsLog.setUserPhone(mobile);
        smsLog.setContent(formatContent(smsType, params));
        smsLogMapper.insert(smsLog);

        this.sendSms(mobile, smsType.getTemplateCode(), params);
    }
}
