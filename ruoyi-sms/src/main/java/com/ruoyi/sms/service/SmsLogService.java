package com.ruoyi.sms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.sms.domain.SmsLog;
import com.ruoyi.sms.enums.SmsType;

import java.util.Map;

public interface SmsLogService extends IService<SmsLog> {

    void sendSms(SmsType smsType, String userId, String mobile, Map<String, String> params);

    boolean checkValidCode(String mobile, String code, SmsType smsType);

    boolean checkValidCode(String mobile, String code, String userId, SmsType smsType);

    /**
     * 管理人员发送短信
     *
     * @param smsType
     * @param userId
     * @param mobile
     * @param params
     */
    void adminSendSms(SmsType smsType, String userId, String mobile, Map<String, String> params);

    String sendSms(String mobile, String templateCode, Map<String, String> params);
}
