package com.ruoyi.test.mapper;

import com.ruoyi.mall.mapper.DeductionPaymentRecordMapper;
import com.ruoyi.mall.mapper.ShopOrderDetailMapper;
import com.ruoyi.mall.service.IShopOrderDetailService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DeductionPaymentRecordMapperTest {


    @Resource
    ShopOrderDetailMapper shopOrderDetailMapper;
    @Resource
    IShopOrderDetailService shopOrderDetailService;
    @Autowired
    private DeductionPaymentRecordMapper mapper;

    @Test
    public void test01() {

    }


}
