package com.ruoyi.web.controller.system;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.MimeTypeUtils;
import com.ruoyi.mall.domain.entity.Order;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.TZUserFocus;
import com.ruoyi.mall.service.IOrderService;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.mall.service.ITZUserFocusService;
import com.ruoyi.sms.enums.SmsType;
import com.ruoyi.sms.service.SmsLogService;
import com.ruoyi.system.domain.vo.SysOssVo;
import com.ruoyi.system.service.ISysOssService;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR> Li
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/user/profile")
public class SysProfileController extends BaseController {

    private final ISysUserService sysUserService;
    private final ISysOssService iSysOssService;
    private final SmsLogService smsLogService;
    private final IShopService shopService;
    private final IOrderService orderService;
    private final ITZUserFocusService userFocusService;

    /**
     * 个人信息
     */
    @GetMapping
    public R<Map<String, Object>> profile() {
        SysUser user = sysUserService.selectUserById(getUserId());
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("user", user);
        ajax.put("roleGroup", sysUserService.selectUserRoleGroup(user.getUserName()));
        ajax.put("postGroup", sysUserService.selectUserPostGroup(user.getUserName()));
        return R.ok(ajax);
    }

    /**
     * 修改用户
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> updateProfile(@RequestBody SysUser user) {
        if (StringUtils.isNotEmpty(user.getPhonenumber()) && !sysUserService.checkPhoneUnique(user)) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail()) && !sysUserService.checkEmailUnique(user)) {
            return R.fail("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUserId(getUserId());
        user.setUserName(null);
        user.setPassword(null);
        user.setAvatar(null);
        user.setDeptId(null);
        if (sysUserService.updateUserProfile(user) > 0) {
            return R.ok();
        }
        return R.fail("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     *
     * @param newPassword 新密码
     * @param oldPassword 旧密码
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public R<Void> updatePwd(String oldPassword, String newPassword) {
        SysUser user = sysUserService.selectUserById(LoginHelper.getUserId());
        String userName = user.getUserName();
        String password = user.getPassword();
        if (!BCrypt.checkpw(oldPassword, password)) {
            return R.fail("修改密码失败，旧密码错误");
        }
        if (BCrypt.checkpw(newPassword, password)) {
            return R.fail("新密码不能与旧密码相同");
        }

        if (sysUserService.resetUserPwd(userName, BCrypt.hashpw(newPassword)) > 0) {
            return R.ok();
        }
        return R.fail("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     *
     * @param avatarfile 用户头像
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/avatar", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Map<String, Object>> avatar(@RequestPart("avatarfile") MultipartFile avatarfile) {
        Map<String, Object> ajax = new HashMap<>();
        if (!avatarfile.isEmpty()) {
            String extension = FileUtil.extName(avatarfile.getOriginalFilename());
            if (!StringUtils.equalsAnyIgnoreCase(extension, MimeTypeUtils.IMAGE_EXTENSION)) {
                return R.fail("文件格式不正确，请上传" + Arrays.toString(MimeTypeUtils.IMAGE_EXTENSION) + "格式");
            }
            SysOssVo oss = iSysOssService.upload(avatarfile);
            String avatar = oss.getUrl();
            if (sysUserService.updateUserAvatar(getUsername(), avatar)) {
                ajax.put("imgUrl", avatar);
                return R.ok(ajax);
            }
        }
        return R.fail("上传图片异常，请联系管理员");
    }

    /**
     * 获取当前登录用户的手机号
     */
    @GetMapping("/phone")
    public R<String> getUserPhone() {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        SysUser user = sysUserService.selectUserById(userId);
        if (user == null) {
            return R.fail("用户不存在");
        }

        // 获取用户手机号
        String phone = user.getPhonenumber();
        if (StringUtils.isEmpty(phone)) {
            return R.fail("您尚未绑定手机号");
        }
        // 隐藏中间4位手机号
        String maskedPhone = phone.substring(0, 3) + "****" + phone.substring(7);
        return R.ok("", maskedPhone);
    }

    /**
     * 发送重置操作密码短信验证码
     */
    @GetMapping("/sendOperatePwdSmsCode")
    public R<Void> sendOperatePwdSmsCode() {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        SysUser user = sysUserService.selectUserById(userId);
        if (user == null) {
            return R.fail("用户不存在");
        }

        // 获取用户手机号
        String phone = user.getPhonenumber();
        if (StringUtils.isEmpty(phone)) {
            return R.fail("您尚未绑定手机号");
        }

        // 调用短信发送服务发送验证码
        try {
            // 发送短信
            smsLogService.sendSms(SmsType.OPERATION_PWD_RESET, userId.toString(), phone, new HashMap<>());
            return R.ok();
        } catch (Exception e) {
            log.error("发送短信验证码失败", e);
            return R.fail("发送验证码失败：" + e.getMessage());
        }
    }

    /**
     * 重置操作密码
     */
    @PostMapping("/resetOperationPwd")
    public R<Void> resetOperationPwd(@RequestBody Map<String, String> params) {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        SysUser user = sysUserService.selectUserById(userId);
        if (user == null) {
            return R.fail("用户不存在");
        }
        // 获取用户手机号
        String phone = user.getPhonenumber();
        if (StringUtils.isEmpty(phone)) {
            return R.fail("您尚未绑定手机号");
        }
        String code = params.get("code");
        String operationPassword = params.get("operationPassword");
        if (StringUtils.isEmpty(code) || StringUtils.isEmpty(operationPassword)) {
            return R.fail("参数不完整");
        }
        boolean flag = smsLogService.checkValidCode(phone, code, userId.toString(), SmsType.OPERATION_PWD_RESET);
        if (!flag) {
            return R.fail("验证码验证失败，请重新输入");
        }
        // 校验密码强度
        if (operationPassword.length() < 6) {
            return R.fail("操作密码长度不能小于6位");
        }
        // 加密操作密码
        String encryptOperationPwd = BCrypt.hashpw(operationPassword);
        try {
            // 更新操作密码
            if (sysUserService.updateOperationPwd(user.getUserName(), encryptOperationPwd) > 0) {
                log.info("用户 {} 成功重置操作密码", user.getUserName());
                return R.ok();
            } else {
                log.error("用户 {} 重置操作密码失败", user.getUserName());
                return R.fail("重置操作密码失败");
            }
        } catch (Exception e) {
            log.error("重置操作密码异常", e);
            return R.fail("系统异常，请联系管理员");
        }
    }

    /**
     * 发送登录密码重置短信验证码
     */
    @GetMapping("/sendLoginPwdSmsCode")
    public R<Void> sendLoginPwdSmsCode() {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        SysUser user = sysUserService.selectUserById(userId);
        if (user == null) {
            return R.fail("用户不存在");
        }

        // 获取用户手机号
        String phone = user.getPhonenumber();
        if (StringUtils.isEmpty(phone)) {
            return R.fail("您尚未绑定手机号");
        }

        // 调用短信发送服务发送验证码
        try {
            // 发送短信
            smsLogService.sendSms(SmsType.LOGIN_PWD_RESET, userId.toString(), phone, new HashMap<>());
            return R.ok();
        } catch (Exception e) {
            log.error("发送短信验证码失败", e);
            return R.fail("发送验证码失败：" + e.getMessage());
        }
    }

    /**
     * 重置登录密码
     */
    @PostMapping("/resetLoginPwd")
    public R<Void> resetLoginPwd(@RequestBody Map<String, String> params) {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        SysUser user = sysUserService.selectUserById(userId);
        if (user == null) {
            return R.fail("用户不存在");
        }
        // 获取用户手机号
        String phone = user.getPhonenumber();
        if (StringUtils.isEmpty(phone)) {
            return R.fail("您尚未绑定手机号");
        }
        String code = params.get("code");
        String loginPassword = params.get("loginPassword");
        if (StringUtils.isEmpty(code) || StringUtils.isEmpty(loginPassword)) {
            return R.fail("参数不完整");
        }
        boolean flag = smsLogService.checkValidCode(phone, code,  userId.toString(), SmsType.LOGIN_PWD_RESET);
        if (!flag) {
            return R.fail("验证码验证失败，请重新输入");
        }
        // 校验密码强度
        if (loginPassword.length() < 6) {
            return R.fail("登录密码长度不能小于6位");
        }
        // 加密登录密码
        String encryptLoginPwd = BCrypt.hashpw(loginPassword);
        try {
            // 更新登录密码
            if (sysUserService.resetUserPwd(user.getUserName(), encryptLoginPwd) > 0) {
                log.info("用户 {} 成功重置登录密码", user.getUserName());
                return R.ok();
            } else {
                log.error("用户 {} 重置登录密码失败", user.getUserName());
                return R.fail("重置登录密码失败");
            }
        } catch (Exception e) {
            log.error("重置登录密码异常", e);
            return R.fail("系统异常，请联系管理员");
        }
    }

    /**
     * 获取公司主页信息
     */
    @PostMapping("/getCompanyHome")
    public R<Map<String,Object>> getCompanyHome() {
        Map<String, Object> map = new HashMap<>();
        // 获取当前登录用户ID
        SysUser user = sysUserService.getById(getUserId());

        // 获取今天的开始和结束时间
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = LocalDateTime.of(today, LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(today, LocalTime.MAX);
        Date todayStartDate = Date.from(todayStart.atZone(ZoneId.systemDefault()).toInstant());
        Date todayEndDate = Date.from(todayEnd.atZone(ZoneId.systemDefault()).toInstant());

        LambdaQueryWrapper<Order> orderLambdaQueryWrapper = new LambdaQueryWrapper<Order>()
            .between(Order::getCreateTime, todayStartDate, todayEndDate);
        // 查询今天新增客户数
        long customerCount;
        if(ObjUtil.isNotNull(getLoginUser().getShopId())){
            customerCount = userFocusService.count(
                new LambdaQueryWrapper<TZUserFocus>()
                    .between(TZUserFocus::getUpdateTime, todayStartDate, todayEndDate)
                    .eq(TZUserFocus::getShopId, getLoginUser().getShopId())
                    .eq(TZUserFocus::getStatus, "0")
            );
            orderLambdaQueryWrapper.eq(Order::getShopId, getLoginUser().getShopId());
        }else{
            customerCount = shopService.count(
                new LambdaQueryWrapper<Shop>()
                    .between(Shop::getCreateTime, todayStartDate, todayEndDate)
            );
        }

        // 查询今日交易数量
        Long orderCount = orderService.count(orderLambdaQueryWrapper);
        map.put("orderCount",orderCount);
        map.put("customerCount",customerCount);
        map.put("verification",Optional.ofNullable(user).map(SysUser::getPlatformPromotionGold).orElse(BigDecimal.ZERO));
        return R.ok(map);
    }
}
