package com.ruoyi.web.controller.system;


import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.domain.vo.SysOssVo;
import com.ruoyi.system.service.ISysOssService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/oss")
public class SysOssController extends BaseController {

    private final ISysOssService iSysOssService;

//    /**
//     * 查询OSS对象存储列表
//     */
//    @SaCheckPermission("system:oss:list")
//    @GetMapping("/list")
//    public TableDataInfo<SysOssVo> list(@Validated(QueryGroup.class) SysOssBo bo, PageQuery pageQuery) {
//        return iSysOssService.queryPageList(bo, pageQuery);
//    }
//
//    /**
//     * 查询OSS对象基于id串
//     *
//     * @param ossIds OSS对象ID串
//     */
//    @SaCheckPermission("system:oss:list")
//    @GetMapping("/listByIds/{ossIds}")
//    public R<List<SysOssVo>> listByIds(@NotEmpty(message = "主键不能为空")
//                                       @PathVariable Long[] ossIds) {
//        List<SysOssVo> list = iSysOssService.listByIds(Arrays.asList(ossIds));
//        return R.ok(list);
//    }

    /**
     * 上传OSS对象存储
     *
     * @param file 文件
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Map<String, String>> upload(@RequestPart("file") MultipartFile file) {
        if (ObjectUtil.isNull(file)) {
            return R.fail("上传文件不能为空");
        }

        long maxSize = 1024L * 1024L * 15; // 设定最大允许大小为 15 MB
        String contentType = file.getContentType();//文件类型（图片/视频）
        if (contentType.startsWith("video/")) {//视频
            if (file.getSize() > maxSize) {
                return R.fail("视频大小不能超过15MB");
            }
        }

        SysOssVo oss = iSysOssService.upload(file);
        Map<String, String> map = new HashMap<>(4);
        map.put("url", oss.getUrl());
        map.put("filePath", oss.getFilePath());
        map.put("fileName", oss.getOriginalName());
        map.put("ossId", oss.getOssId().toString());
        return R.ok(map);
    }

    /**
     * 富文本编辑器图片上传
     *
     * @param file 文件
     */
    @SaIgnore
    @PostMapping(value = "/richUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Map<String, Object> richUpload(@RequestPart("file") MultipartFile file) {
        Map<String, Object> map = new HashMap<>(4);

        try {
            if (ObjectUtil.isNull(file)) {
                map.put("errno", 1);
                map.put("message", "上传文件不能为空");
                return map;
            }

            // 验证文件类型 - 只允许图片
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                map.put("errno", 1);
                map.put("message", "只能上传图片格式的文件");
                return map;
            }

            // 验证文件大小 - 限制1MB
            long maxSize = 1024L * 1024L; // 1MB
            if (file.getSize() > maxSize) {
                map.put("errno", 1);
                map.put("message", "图片大小不能超过1MB");
                return map;
            }

            SysOssVo oss = iSysOssService.upload(file);

            HashMap<String, Object> data = new HashMap<>();
            data.put("url", oss.getUrl());
            data.put("alt", oss.getOriginalName());
            data.put("href", oss.getUrl());

            map.put("errno", 0);
            map.put("data", data);
            return map;

        } catch (Exception e) {
            map.put("errno", 1);
            map.put("message", "上传失败: " + e.getMessage());
            return map;
        }
    }

//    /**
//     * 下载OSS对象
//     *
//     * @param ossId OSS对象ID
//     */
//    @SaCheckPermission("system:oss:download")
//    @GetMapping("/download/{ossId}")
//    public void download(@PathVariable Long ossId, HttpServletResponse response) throws IOException {
//        iSysOssService.download(ossId,response);
//    }
//
//    /**
//     * 删除OSS对象存储
//     *
//     * @param ossIds OSS对象ID串
//     */
//    @SaCheckPermission("system:oss:remove")
//    @Log(title = "OSS对象存储", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ossIds}")
//    public R<Void> remove(@NotEmpty(message = "主键不能为空")
//                          @PathVariable Long[] ossIds) {
//        return toAjax(iSysOssService.deleteWithValidByIds(Arrays.asList(ossIds), true));
//    }

}
