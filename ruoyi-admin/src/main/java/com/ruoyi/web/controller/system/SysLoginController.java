package com.ruoyi.web.controller.system;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.redis.RedisUtils;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.system.domain.vo.RouterVo;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysMenuService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.SysLoginService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录验证
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
public class SysLoginController {

    /**
     * 网关解密登录前端密码 秘钥
     */
    private static final String ENCODE_KEY = "thanks,znh4cloud";
    private static final String KEY_ALGORITHM = "AES";
    private final SysLoginService loginService;
    private final ISysMenuService menuService;
    private final ISysUserService userService;
    private final ISysConfigService configService;
    private final IShopService shopService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/login")
    public R<Map<String, Object>> login(@Validated @RequestBody LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
            loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/adminLogin")
    public R<Map<String, Object>> adminLogin(@Validated @RequestBody LoginBody loginBody) {
        // 构建前端对应解密AES 因子
        AES aes = new AES(Mode.CFB, Padding.NoPadding,
            new SecretKeySpec(ENCODE_KEY.getBytes(), KEY_ALGORITHM),
            new IvParameterSpec(ENCODE_KEY.getBytes()));
        String decryptPassword = aes.decryptStr(loginBody.getPassword());
        // 设置解密后的密码
        loginBody.setPassword(decryptPassword);
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        // 验证码开关
        if (captchaEnabled) {
            checkCode(loginBody);
        }
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.adminLogin(loginBody.getUsername(), loginBody.getPassword());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }


    /**
     * 对接A系统登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/ASystemLogin")
    public R<Map<String, Object>> ASystemLogin(@Validated @RequestBody LoginBody loginBody) {
        // 构建前端对应解密AES 因子
        AES aes = new AES(Mode.CFB, Padding.NoPadding,
            new SecretKeySpec(ENCODE_KEY.getBytes(), KEY_ALGORITHM),
            new IvParameterSpec(ENCODE_KEY.getBytes()));
        String decryptPassword = aes.decryptStr(loginBody.getPassword());
        // 设置解密后的密码
        loginBody.setPassword(decryptPassword);
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.ASystemLogin(loginBody.getUsername(), loginBody.getPassword());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

//    /**
//     * 短信登录
//     *
//     * @param smsLoginBody 登录信息
//     * @return 结果
//     */
//    @SaIgnore
//    @PostMapping("/smsLogin")
//    public R<Map<String, Object>> smsLogin(@Validated @RequestBody SmsLoginBody smsLoginBody) {
//        Map<String, Object> ajax = new HashMap<>();
//        // 生成令牌
//        String token = loginService.smsLogin(smsLoginBody.getPhonenumber(), smsLoginBody.getSmsCode());
//        ajax.put(Constants.TOKEN, token);
//        return R.ok(ajax);
//    }
//
//    /**
//     * 邮件登录
//     *
//     * @param body 登录信息
//     * @return 结果
//     */
//    @PostMapping("/emailLogin")
//    public R<Map<String, Object>> emailLogin(@Validated @RequestBody EmailLoginBody body) {
//        Map<String, Object> ajax = new HashMap<>();
//        // 生成令牌
//        String token = loginService.emailLogin(body.getEmail(), body.getEmailCode());
//        ajax.put(Constants.TOKEN, token);
//        return R.ok(ajax);
//    }
//
//    /**
//     * 小程序登录(示例)
//     *
//     * @param xcxCode 小程序code
//     * @return 结果
//     */
//    @SaIgnore
//    @PostMapping("/xcxLogin")
//    public R<Map<String, Object>> xcxLogin(@NotBlank(message = "{xcx.code.not.blank}") String xcxCode) {
//        Map<String, Object> ajax = new HashMap<>();
//        // 生成令牌
//        String token = loginService.xcxLogin(xcxCode);
//        ajax.put(Constants.TOKEN, token);
//        return R.ok(ajax);
//    }

    /**
     * 退出登录
     */
    @SaIgnore
    @PostMapping("/logout")
    public R<Void> logout() {
        loginService.logout();
        return R.ok("退出成功");
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public R<Map<String, Object>> getInfo() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysUser user = userService.selectUserById(loginUser.getUserId());
        if ("1".equals(user.getIsShop()) || "1".equals(user.getIsConsignment())){
            Shop one = shopService.getOne(
                new LambdaQueryWrapper<Shop>()
                    .eq(Shop::getPhone, user.getPhonenumber())
            );
            user.setShopName(one.getName());
            user.setShopLogo(one.getLogo());
            user.setBusinessName(one.getBusinessName());
        }
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("user", user);
        ajax.put("roles", loginUser.getRolePermission());
        ajax.put("permissions", loginUser.getMenuPermission());
        return R.ok(ajax);
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public R<List<RouterVo>> getRouters() {
        Long userId = LoginHelper.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return R.ok(menuService.buildMenus(menus));
    }

    /**
     * 校验验证码
     */
    private void checkCode(LoginBody loginBody) {
        // 设置code
        String code = loginBody.getCode();
        String randomStr = loginBody.getRandomStr();
        if (StrUtil.isBlank(code)) {
            throw new ServiceException("验证码不能为空");
        }
        if (StrUtil.isBlank(randomStr)) {
            throw new ServiceException("验证码不合法");
        }
        String key = CacheConstants.DEFAULT_CODE_KEY + randomStr;
        Object codeObj = RedisUtils.get(key);
        if (codeObj == null) {
            throw new ServiceException("验证码不合法");
        }
        String saveCode = codeObj.toString();
        if (StrUtil.isBlank(saveCode)) {
            RedisUtils.del(key);
            throw new ServiceException("验证码不合法");
        }
        if (!StrUtil.equals(saveCode, code)) {
            RedisUtils.del(key);
            throw new ServiceException("验证码不合法");
        }
        RedisUtils.del(key);
    }
}
