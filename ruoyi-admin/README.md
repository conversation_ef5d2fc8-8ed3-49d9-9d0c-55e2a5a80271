# ruoyi-admin 后台管理模块

## 模块介绍

`ruoyi-admin` 是MallB电商平台的系统入口模块，作为整个应用的启动入口，集成了所有其他功能模块。该模块负责配置管理、接口暴露和Web访问控制。

## 主要功能

- 系统启动入口
- 全局配置管理
- 全局异常处理
- 安全认证与授权
- 接口文档集成（Swagger）
- 服务监控集成
- Web控制器定义

## 技术组件

- Spring Boot
- Spring Security
- Swagger/Knife4j
- Spring AOP

## 部署说明

本模块可通过以下方式部署：

1. 直接运行JAR包：`java -jar ruoyi-admin-xxx.jar`
2. 使用Docker容器：基于Dockerfile构建和运行

## 配置文件

主要配置文件包括：

- application.yml - 全局配置
- application-dev.yml - 开发环境配置
- application-prod.yml - 生产环境配置 