# ruoyi-xxl-job-admin 分布式任务调度平台

## 模块介绍

`ruoyi-xxl-job-admin` 是MallB电商平台的分布式任务调度中心，基于XXL-Job实现，提供可视化的任务管理能力，支持各种定时任务、批处理任务的统一管理和监控。

## 主要功能

- 可视化任务管理界面
- 分布式任务调度执行
- 多种调度类型支持(CRON、固定频率等)
- 任务失败告警机制
- 任务执行日志记录
- 任务执行监控统计
- 任务动态暂停/恢复
- 任务执行失败重试机制
- 集群高可用支持

## 技术架构

- 调度中心：统一管理任务调度
- 执行器：负责任务的执行
- 调度通讯：基于HTTP通讯
- 任务注册：自动/手动注册
- 调度策略：分片、故障转移等

## 部署说明

1. 独立部署：
   ```
   java -jar ruoyi-xxl-job-admin.jar
   ```

2. Docker部署：
   ```
   docker build -t ruoyi-xxl-job-admin .
   docker run -p 8080:8080 ruoyi-xxl-job-admin
   ```

## 数据库配置

XXL-Job需要独立的数据库支持，相关配置在application.yml中：

```yaml
spring:
  datasource:
    url: ***************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

## 执行器接入

任务执行器需添加以下依赖并配置：

```xml
<dependency>
    <groupId>com.xuxueli</groupId>
    <artifactId>xxl-job-core</artifactId>
</dependency>
```

```java
@Bean
public XxlJobSpringExecutor xxlJobExecutor() {
    XxlJobSpringExecutor executor = new XxlJobSpringExecutor();
    executor.setAdminAddresses("http://xxl-job-admin:8080/xxl-job-admin");
    executor.setAppname("执行器名称");
    executor.setIp("执行器IP");
    executor.setPort(9999);
    executor.setAccessToken("访问令牌");
    executor.setLogPath("日志路径");
    return executor;
}
``` 