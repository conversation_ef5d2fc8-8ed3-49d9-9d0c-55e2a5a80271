# ruoyi-monitor-admin 系统监控模块

## 模块介绍

`ruoyi-monitor-admin` 是基于Spring Boot Admin实现的系统监控服务，提供对MallB电商平台各服务实例的运行状态监控、指标收集和运维管理功能。

## 主要功能

- 服务实例健康状态监控
- JVM内存使用情况监控
- GC活动监控与分析
- 线程状态与死锁检测
- 环境变量与配置查看
- HTTP请求性能追踪
- 日志级别动态调整
- 系统运行指标可视化
- 警报与通知配置

## 技术实现

- Spring Boot Admin
- Actuator Endpoints
- Micrometer
- WebSocket实时推送

## 部署方式

1. 独立部署：
   ```
   java -jar ruoyi-monitor-admin.jar
   ```

2. Docker部署：
   ```
   docker build -t ruoyi-monitor-admin .
   docker run -p 9090:9090 ruoyi-monitor-admin
   ```

## 配置说明

- `server.port`: 监控服务端口（默认9090）
- `spring.boot.admin.context-path`: 监控服务上下文路径
- `spring.security.user`: 安全认证配置

## 客户端接入

客户端服务需添加以下依赖和配置：

```xml
<dependency>
    <groupId>de.codecentric</groupId>
    <artifactId>spring-boot-admin-starter-client</artifactId>
</dependency>
```

```yaml
spring:
  boot:
    admin:
      client:
        url: http://监控服务地址:9090
        instance:
          name: 应用名称
``` 