# ruoyi-extend 扩展服务模块

## 模块介绍

`ruoyi-extend` 是MallB电商平台的扩展服务集合，整合了多个第三方或增强功能服务，提供系统运维、监控和任务调度等能力，以增强系统的可用性和可维护性。

## 子模块列表

### ruoyi-monitor-admin

系统监控服务，基于Spring Boot Admin实现，提供以下功能：

- 应用状态实时监控
- 性能指标可视化
- JVM运行状态查看
- 日志级别动态调整
- 内存使用监控
- HTTP请求追踪

### ruoyi-xxl-job-admin

分布式任务调度平台，基于XXL-Job实现，提供以下功能：

- 可视化任务管理
- 分布式任务执行
- 任务调度日志
- 失败任务告警
- 任务执行监控
- 动态任务编排

## 部署说明

各子模块支持独立部署，也可与主系统一起部署：

1. 独立部署：使用子模块下的Dockerfile单独构建容器
2. 集成部署：由主应用统一管理和引用

## 配置方式

- 监控服务：配置被监控应用的端点暴露
- 任务调度：配置执行器地址和任务参数

## 使用注意

- 监控服务对系统性能有轻微影响，生产环境应合理配置
- 任务调度服务需确保高可用性，避免单点故障 