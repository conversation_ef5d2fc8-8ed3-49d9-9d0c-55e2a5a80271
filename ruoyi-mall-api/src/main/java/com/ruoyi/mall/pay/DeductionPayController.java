package com.ruoyi.mall.pay;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.service.ITzUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * 抵扣金支付前端接口
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/deduction")
public class DeductionPayController extends BaseController {

    private final ITzUserService tzUserService;

    /**
     * 获取用户抵扣金余额
     */
    @GetMapping("/getDeductionBalance")
    public R<BigDecimal> getDeductionBalance() {
        TzUser user = tzUserService.getById(getUserId());
        if (user == null) {
            return R.fail("用户不存在");
        }

        BigDecimal balance = user.getDeductionMoney();
        if (balance == null) {
            balance = BigDecimal.ZERO;
        }

        return R.ok(balance);
    }
}
