package com.ruoyi.mall.pay;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.service.UserService;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OrderStatusEnum;
import com.ruoyi.common.enums.OrderVirtualStatusEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.mall.domain.bo.VirtualOrderBo;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.domain.vo.VirtualOrderVo;
import com.ruoyi.mall.enums.VirtualTypeEnum;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.service.ITzUserService;
import com.ruoyi.mall.service.IVirtualOrderService;
import com.ruoyi.mall.strategy.VirtualProduct.VirtualFactory;
import com.ruoyi.mall.strategy.VirtualProduct.VirtualStrategy;
import com.ruoyi.mall.utils.ServiceFeeCalculator;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 虚拟商品订单管理与支付
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/virtual")
public class TzVirtualController extends BaseController {

    private final IVirtualOrderService virtualOrderService;
    private final VirtualFactory virtualFactory;
    private final IConfigSettingService configSettingService;
    private final UserService userService;
    private final ITzUserService tzUserService;
    private final SysUserServiceImpl sysUserServiceImpl;

    /**
     * 创建代销订单
     */
    @PostMapping("/create")
    @Log(title = "创建虚拟商品订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    public R<String> createVirtualOrder() {
        TzUser tzUser = tzUserService.getById(getUserId());
        if (tzUser == null) {
            throw new ServiceException("用户不存在");
        }
        if (!"1".equals(tzUser.getIsRealName())){
            return R.fail("请先完成实名认证");
        }

        Integer type = 4;
        VirtualTypeEnum instance = VirtualTypeEnum.instance(type);
        // 如果是技术引流类型，检查是否开启了技术引流功能
        if (VirtualTypeEnum.JISHU_DRAINAGE.type().equals(type)) {
            JSONObject configValue = configSettingService.getConfigValue("configFive");
            if (configValue == null || "1".equals(configValue.getStr("isEnabled"))) {
                return R.fail("技术引流功能未开启，无法创建订单");
            }
        }

        VirtualOrderBo virtualOrderBo = new VirtualOrderBo();
        virtualOrderBo.setUserId(getUserId());
        virtualOrderBo.setUserName(getUsername());
        virtualOrderBo.setPhone(tzUser.getPhone());
        virtualOrderBo.setProductName(instance.productName());
        virtualOrderBo.setOrderType("2");
        // 默认的都是30天
        virtualOrderBo.setValidDays(30);
        VirtualStrategy paymentStrategy = virtualFactory.getPaymentStrategy(type);
        virtualOrderBo.setAmount(paymentStrategy.getAmount());

        return R.ok("", virtualOrderService.createVirtualOrder(virtualOrderBo, instance));
    }

    /**
     * 获取虚拟订单详情
     *
     * @param orderNo 订单编号
     * @return 订单详情
     */
    @GetMapping("/{orderNo}")
    public R<VirtualOrderVo> getVirtualOrder(@PathVariable String orderNo) {
        VirtualOrderVo order = virtualOrderService.getVirtualOrderDetail(orderNo);
        return R.ok(order);
    }

    /**
     * 获取商家待支付的虚拟订单列表
     *
     * @return
     */
    @GetMapping("/list")
    public R<List<VirtualOrder>> getVirtualOrderList() {

        Long tzUserId = getUserId();
        SysUser sysUser = sysUserServiceImpl.getOne(
            new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getTzUserId, tzUserId)
        );


        LambdaQueryWrapper<VirtualOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(
            wrapper -> wrapper.and(
                wrapper1 -> wrapper1
                    .eq(VirtualOrder::getUserId, tzUserId)
                    .eq(VirtualOrder::getOrderType, "2")
                )
                .or(sysUser != null,
                    wrapper1 -> {
                        if (sysUser != null) {
                            wrapper1
                                .eq(VirtualOrder::getUserId, sysUser.getUserId())
                                .eq(VirtualOrder::getOrderType, "1");
                        }
                    }
                )
            )
            .eq(VirtualOrder::getStatus, OrderVirtualStatusEnum.PENDING_PAYMENT.getCode())
            .orderByDesc(VirtualOrder::getCreateTime)
        ;

        List<VirtualOrder> list = virtualOrderService.list(queryWrapper);
        return R.ok(list);
    }

    /**
     * 为虚拟订单创建支付
     *
     * @param orderNo 订单编号
     * @param payType 支付方式：1-微信支付，2-支付宝支付，3-抵扣金支付，4-支付宝PC端支付，5-商家抵扣金支付
     * @return 支付参数
     */
    @PostMapping("/payment")
    @Log(title = "为虚拟订单创建支付", businessType = BusinessType.INSERT)
    public R<Map<String, String>> createVirtualPayment(@RequestParam String orderNo, @RequestParam String payType) {
        try {
            // 获取虚拟订单信息
            VirtualOrderVo order = virtualOrderService.getVirtualOrderDetail(orderNo);
            if (order == null) {
                return R.fail("订单不存在");
            }

            SysUser one = sysUserServiceImpl.getOne(
                new LambdaQueryWrapper<SysUser>()
                    .eq(SysUser::getTzUserId, getUserId())
            );
            if(one == null){
                if (!(order.getUserId().equals(getUserId()) && order.getOrderType().equals("2"))) {
                    return R.fail("无权操作此订单");
                }
            }else{
                if (
                    !(order.getUserId().equals(getUserId()) && order.getOrderType().equals("2"))
                    &&
                    !(order.getUserId().equals(one.getUserId()) && order.getOrderType().equals("1"))
                ) {
                    return R.fail("无权操作此订单");
                }
            }

            if (!OrderStatusEnum.PENDING_PAYMENT.getCode().equals(order.getStatus())) {
                return R.fail("订单状态不允许支付");
            }

            // 如果是技术引流订单，检查是否开启了技术引流功能
            if (VirtualTypeEnum.JISHU_DRAINAGE.type().equals(order.getProductType())) {
                JSONObject configValue = configSettingService.getConfigValue("configFive");
                if (configValue == null || "1".equals(configValue.getStr("isEnabled"))) {
                    return R.fail("技术引流功能未开启，无法支付");
                }else{//技术引流一天只能支付一次
                    LambdaQueryWrapper<VirtualOrder> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper
                        .eq(VirtualOrder::getUserId,one.getUserId())
                        .eq(VirtualOrder::getProductType,VirtualTypeEnum.JISHU_DRAINAGE.type())
                        .eq(VirtualOrder::getStatus,OrderVirtualStatusEnum.PENDING_SHIPMENT.getCode())
                        .eq(VirtualOrder::getDelFlag, "0")
                        .eq(VirtualOrder::getOrderType, payType);
                    if(virtualOrderService.count(queryWrapper)>0){
                        return R.fail("技术引流一种支付方式一天只能支付一次");
                    }
                }
            }

            // 特殊处理商家平台促销金支付
            if (PayOrder.PAY_TYPE_SHOP_DEDUCTION.equals(payType)) {
                // 在ruoyi-mall-server中调用shopService处理商家平台促销金扣减
                Long userId = getUserId();
                return processShopDeductionPayment(order, userId);
            }

            // 其他支付方式正常处理
            Map<String, String> payment = virtualOrderService.createPayment(
                order.getId(),
                order.getOrderNo(),
                getUserId(),
                order.getAmount(),
                payType
            );

            return R.ok(payment);
        } catch (Exception e) {
            log.error("创建虚拟商品支付订单失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 处理商家平台促销金支付
     *
     * @param order  订单信息
     * @param userId 用户ID
     * @return 支付结果
     */
    private R<Map<String, String>> processShopDeductionPayment(VirtualOrderVo order, Long userId) {
        try {
            // 验证订单类型，确保支持平台促销金支付
            Integer productType = order.getProductType();
            // 技术引流(7)、权限开通(1,2,3)、广告(6)、功能付费等虚拟商品均可使用平台促销金

            // 调用订单服务中的商家平台促销金支付方法
            Map<String, String> result = virtualOrderService.payWithShopDeduction(
                order.getId(),
                order.getOrderNo(),
                userId,
                order.getAmount()
            );

            return R.ok(result);
        } catch (ServiceException e) {
            log.error("商家平台促销金支付失败: {}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("商家平台促销金支付异常", e);
            return R.fail("支付处理失败，请稍后再试");
        }
    }

    /**
     * 取消虚拟订单
     *
     * @param orderNo 订单编号
     * @return 操作结果
     */
    @PostMapping("/cancel/{orderNo}")
    public R<Boolean> cancelVirtualOrder(@PathVariable String orderNo) {
        boolean result = virtualOrderService.cancelVirtualOrder(orderNo, getUserId());
        return R.ok(result);
    }

    /**
     * 查询支付状态
     *
     * @param paymentNo 支付单号
     * @return 支付状态
     */
    @GetMapping("/query/{paymentNo}")
    public R<Void> queryPayStatus(@PathVariable String paymentNo) {
        try {
            virtualOrderService.queryOrderStatus(paymentNo);
            return R.ok();
        } catch (Exception e) {
            log.error("查询支付状态失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取手续费信息
     *
     * @param amount 原始金额
     * @return 手续费信息
     */
    @GetMapping("/serviceFee")
    public R<JSONObject> getServiceFeeInfo(@RequestParam BigDecimal amount) {
        JSONObject result = new JSONObject();
        result.set("originalAmount", amount);
        result.set("serviceFee", ServiceFeeCalculator.calculateVirtualOrderServiceFee(amount));
        result.set("totalAmount", ServiceFeeCalculator.calculateTotalAmountWithServiceFee(amount));
        result.set("feeRate", ServiceFeeCalculator.getVirtualOrderFeeRatePercent());
        return R.ok(result);
    }
}
