package com.ruoyi.mall.pay;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.enums.OrderStatusEnum;
import com.ruoyi.mall.domain.entity.Order;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.service.IOrderService;
import com.ruoyi.mall.service.ITzUserService;
import com.ruoyi.mall.service.IVirtualOrderService;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.service.IPayService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 支付接口
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/pay")
public class TzPayController extends BaseController {

    private final IPayService payService;

    private final IOrderService orderService;

    private final IVirtualOrderService virtualOrderService;

    private final ITzUserService userService;

    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    @Log(title = "创建支付订单", businessType = BusinessType.INSERT)
    public R<Map<String, String>> createPayment(
        @Validated @RequestBody PayOrderDate payOrderDate
    ) {
        String orderNo = payOrderDate.getOrderNo();
        String payType = payOrderDate.getPayType();
        String payPassword = payOrderDate.getPayPassword();
        Order order = orderService.getOrderByOrderNo(orderNo);
        if (!OrderStatusEnum.PENDING_PAYMENT.getCode().equals(order.getStatus())) {
            return R.fail("订单已支付");
        }

        if (PayOrder.PAY_TYPE_DEDUCTION.equals(payType)) {
            if (StrUtil.isBlank(payPassword)) {
                return R.fail("请输入支付密码");
            }
            TzUser dbTzuser = userService.getById(getUserId());
            // 是否设置了密码
            if (StrUtil.isBlank(dbTzuser.getPayPassword())) {
                return R.fail("请先设置支付密码");
            }
            // 密码是否匹配
            if (!BCrypt.checkpw(payPassword, dbTzuser.getPayPassword())) {
                return R.fail("密码错误");
            }
        }

        try {
            Map<String, String> payment = orderService.createPayment(order, orderNo, getUserId(), payType);
            return R.ok(payment);
        } catch (Exception e) {
            log.error("创建支付订单失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据支付单号查询支付状态
     *
     * @param paymentNo 支付单号
     */
    @GetMapping("/queryPayStatusByPaymentNo/{paymentNo}")
    public R<String> queryPayStatusByPaymentNo(
        @PathVariable String paymentNo) {
        try {
            return R.ok("",orderService.queryPayStatus(paymentNo));
        } catch (Exception e) {
            log.error("查询支付状态失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 根据订单号查询支付状态
     * @param orderNo 订单编号
     * @return
     */
    @GetMapping("/queryOrderPayStatusByOrderNo/{orderNo}")
    public R<String> queryOrderPayStatusByOrderNo(
        @PathVariable String orderNo) {
        try {
            return R.ok("",orderService.queryOrderPayStatus(orderNo));
        } catch (Exception e) {
            log.error("查询支付状态失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 支付回调
     *
     * @param payType 支付方式
     */
    @PostMapping("/notify/{payType}")
    @Log(title = "支付回调", businessType = BusinessType.INSERT)
    public String handlePayCallback(@PathVariable("payType") String payType, @RequestBody Map<String, String> map) {
        try {

            log.info("收到支付回调: payType={}, params={}", payType, map);

            // 获取支付信息
            String paymentNo = map.get("out_trade_no");
            if (paymentNo == null) {
                log.error("支付回调缺少订单号");
                return getPayFailResponse(payType);
            }

            // 查询支付订单
            PayOrder payOrder = payService.getByPaymentNo(paymentNo);
            if (payOrder == null) {
                log.error("支付订单不存在: {}", paymentNo);
                return getPayFailResponse(payType);
            }

            boolean success;
            // 如果是虚拟订单支付 虚拟订单，订单编号V开头
            if (payOrder.getOrderNo() != null && StrUtil.startWith(payOrder.getOrderNo(), "V")) {
                // 处理虚拟订单支付成功
                success = virtualOrderService.handlePaymentSuccess(payType, map);
            } else {
                success = orderService.handlePayCallback(payType, map);
            }
            // 微信支付回调响应
            if ("1".equals(payType)) {
                return success ? getPaySuccessResponse(payType)
                    : getPayFailResponse(payType);
            }
            // 支付宝回调响应
            return success ? "success" : "fail";
        } catch (Exception e) {
            log.error("处理支付回调失败", e);
            return "fail";
        }
    }


    /**
     * 获取支付成功响应
     */
    private String getPaySuccessResponse(String payType) {
        if (PayOrder.PAY_TYPE_WECHAT.equals(payType)) {
            return "<xml><return_code><![CDATA[SUCCESS]]></return_code></xml>";
        } else {
            return "success";
        }
    }

    /**
     * 获取支付失败响应
     */
    private String getPayFailResponse(String payType) {
        if (PayOrder.PAY_TYPE_WECHAT.equals(payType)) {
            return "<xml><return_code><![CDATA[FAIL]]></return_code></xml>";
        } else {
            return "fail";
        }
    }
}

@Data
class PayOrderDate {

    /**
     * 业务订单编号
     */
    @NotBlank(message = "订单编号不能为空")
    private String orderNo;

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空")
    private String payType;

    /**
     * 支付密码
     */
    private String payPassword;
}
