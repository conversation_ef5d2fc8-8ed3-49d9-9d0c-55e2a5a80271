package com.ruoyi.mall.shop;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.mall.domain.dto.ShopBankAccountDTO;
import com.ruoyi.mall.domain.entity.ShopBankAccount;
import com.ruoyi.mall.service.IShopBankAccountService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 商家银行账户 - 商家端
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/shop/bank")
public class TzShopBankAccountController extends BaseController {

    private final IShopBankAccountService shopBankAccountService;

    /**
     * 查询当前商家的银行账户列表
     */
    @GetMapping("/list")
    public TableDataInfo<ShopBankAccount> list(ShopBankAccount shopBankAccount, PageQuery pageQuery) {
        // 设置当前商家ID
        shopBankAccount.setShopId(getShopId());
        return shopBankAccountService.selectShopBankAccountPage(shopBankAccount, pageQuery);
    }

    /**
     * 获取银行账户详细信息
     */
    @GetMapping(value = "/{id}")
    public R<ShopBankAccount> getInfo(@PathVariable("id") Long id) {
        ShopBankAccount account = shopBankAccountService.getById(id);
        // 校验是否为当前商家的账户
        if (account != null && !getShopId().equals(account.getShopId())) {
            return R.fail("无权访问该银行账户");
        }
        return R.ok(account);
    }

    /**
     * 新增银行账户
     */
    @PostMapping
    public R<Void> add(@Validated @RequestBody ShopBankAccountDTO bankAccountDTO) {
        return toAjax(shopBankAccountService.addShopBankAccount(bankAccountDTO, getShopId()));
    }

    /**
     * 修改银行账户
     */
    @PutMapping
    public R<Void> edit(@Validated @RequestBody ShopBankAccountDTO bankAccountDTO) {
        return toAjax(shopBankAccountService.updateShopBankAccount(bankAccountDTO, getShopId()));
    }

    /**
     * 删除银行账户
     */
    @DeleteMapping("/{id}")
    public R<Void> remove(@PathVariable Long id) {
        return toAjax(shopBankAccountService.deleteShopBankAccount(id, getShopId()));
    }

    /**
     * 设置默认银行账户
     */
    @PutMapping("/default/{id}")
    public R<Void> setDefault(@PathVariable Long id) {
        return toAjax(shopBankAccountService.setDefaultAccount(id, getShopId()));
    }

    /**
     * 获取当前商家ID
     */
    private Long getShopId() {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();
        // 实际项目中需要根据用户ID查询对应的商家ID
        // 这里简化处理，假设用户ID就是商家ID
        return userId;
    }
}
