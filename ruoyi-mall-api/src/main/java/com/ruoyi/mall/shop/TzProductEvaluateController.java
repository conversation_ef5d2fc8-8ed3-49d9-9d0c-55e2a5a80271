package com.ruoyi.mall.shop;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.OrderStatusEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.mall.domain.entity.Order;
import com.ruoyi.mall.domain.entity.OrderItem;
import com.ruoyi.mall.domain.entity.ProductEvaluate;
import com.ruoyi.mall.domain.query.QueryProductEvaluateDTO;
import com.ruoyi.mall.domain.vo.EvaluateVo;
import com.ruoyi.mall.service.IOrderItemService;
import com.ruoyi.mall.service.IOrderService;
import com.ruoyi.mall.service.IProductEvaluateService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.ruoyi.common.helper.LoginHelper.getUserId;

/**
 * 商品评价
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/product/evaluate")
public class TzProductEvaluateController {

    private final IProductEvaluateService productEvaluateService;
    private final IOrderItemService orderItemService;
    private final IOrderService orderService;

    /**
     * 新增商品评价
     */
    @PostMapping("/add")
    public R<Integer> addProductEvaluate(@Validated @RequestBody ProductEvaluate productEvaluate) {
        OrderItem orderItem = orderItemService.getById(productEvaluate.getItemId());
        if (orderItem == null) {
            throw new ServiceException("当前订单不存在");
        }
        if (!orderItem.getUserId().equals(getUserId())) {
            throw new ServiceException("当前订单不属于当前用户,不能进行评价");
        }
        if (orderItem.getCommentStatus().equals("1")) {
            throw new ServiceException("当前订单已评价");
        }
        if(!orderService.getOne(new LambdaQueryWrapper<Order>().eq(Order::getOrderNo, orderItem.getOrderNo())).getStatus().equals(OrderStatusEnum.COMPLETED.getCode())){
            throw new ServiceException("当前订单未完成");
        }

        productEvaluate.setUserId(LoginHelper.getUserId());
        productEvaluate.setOrderId(orderItem.getOrderId());
        productEvaluate.setOrderNo(orderItem.getOrderNo());
        productEvaluate.setProductId(orderItem.getProductId());
        productEvaluate.setSkuId(orderItem.getSkuId());
        productEvaluate.setProductName(orderItem.getProductName());
        productEvaluate.setLikeCount(0);
        productEvaluate.setDelFlag("0");
        productEvaluate.setStatus("0");

        boolean saveSuccess = productEvaluateService.save(productEvaluate);
        if (saveSuccess) {
            LambdaUpdateWrapper<OrderItem> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                .set(OrderItem::getCommentStatus, "1")
                .eq(OrderItem::getId, productEvaluate.getItemId());
            boolean updateSuccess = orderItemService.update(updateWrapper);
            if (!updateSuccess) {
                throw  new ServiceException("保存评价成功，但更新订单商品状态失败");
            }
            return R.ok(updateSuccess ? 1 : 0);
        } else {
            return R.fail("保存评价失败");
        }
    }


    /**
     * 根据iD删除评价
     * @param id 评价id
     */
    @DeleteMapping("delete/{id}")
    public R<Integer> deleteProductEvaluateById(@PathVariable Long id) {
        return R.ok(productEvaluateService.deleteProductEvaluateId(id));
    }

    /**
     * 批量删除评价
     */
    @DeleteMapping("/{ids}")
    public R<Integer> deleteProductEvaluateByIds(@PathVariable Long[] ids) {
        return R.ok(productEvaluateService.deleteProductEvaluateIds(ids));
    }

    /**
     * 查询产品下的评价
     */
    @GetMapping("/selProductEvaluate")
    public R<TableDataInfo<EvaluateVo>> selProductEvaluate(@Validated QueryProductEvaluateDTO queryProductEvaluateDTO, PageQuery pageQuery) {
        return R.ok(productEvaluateService.selProductEvaluate(queryProductEvaluateDTO.getProductId(),queryProductEvaluateDTO.getType(),pageQuery));
    }


    /**
     * 根据id修改评价
     *
     * @param productEvaluate 评价对象
     */
    @PutMapping("/edit")
    public R  editProductEvaluate(@RequestBody ProductEvaluate productEvaluate) {
        return R.ok(productEvaluateService.editProductEvaluate(productEvaluate));
    }

    /**
     * 根据商品项查询评价
     * @param itemId  商品项ID
     */
    @GetMapping("/getByItemId/{itemId}")
    public R<ProductEvaluate> selectProductEvaluateByItemId(@PathVariable Long itemId) {
        return R.ok(productEvaluateService.getProductEvaluateByItemId(itemId));
    }



}
