package com.ruoyi.mall.shop;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.mall.domain.entity.Category;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.ShopWalletRecord;
import com.ruoyi.mall.domain.entity.Sku;
import com.ruoyi.mall.domain.query.QueryProductByCategoryIdDTO;
import com.ruoyi.mall.domain.query.QueryProductByShopQueryDTO;
import com.ruoyi.mall.domain.query.QueryShopByTypeGetProductDTO;
import com.ruoyi.mall.domain.query.QueryShopWalletRecordDTO;
import com.ruoyi.mall.domain.vo.ProductInfoVo;
import com.ruoyi.mall.domain.vo.ProductVo;
import com.ruoyi.mall.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 店铺管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/shop")
public class TzShopController extends BaseController {

    private final IShopService shopService;
    private final IProductService productService;
    private final ISkuService skuService;
    private final IShopWalletRecordService shopWalletRecordService;
    private final ICategoryService categoryService;


    /**
     * 获取商铺信息
     *
     * @param shopId 商铺id
     */
    @GetMapping("/{shopId}")
    public R<Shop> getShop(@PathVariable Long shopId) {
        Shop shop = shopService.getById(shopId);
        if (shopId != null){
            return R.ok(shop);
        }
        return R.fail("店铺不存在");
    }

    /**
     * 店铺获取不同类型的产品列表
     */
    @GetMapping("/productByType")
    public R<TableDataInfo<ProductVo>> getShopProductByType(@Validated QueryShopByTypeGetProductDTO query, PageQuery pageQuery) {
        return R.ok(productService.getProductByType(query,pageQuery));
    }

    /**
     * 获取店铺下的商品分类
     *
     * @param shopId 商家ID
     * @return
     */
    @GetMapping("/category/list")
    public R<List<Category>> getCategoryList(@RequestParam Long shopId) {
        Category category = new Category();
        category.setShopId(shopId);
        category.setStatus("0");
        category.setVisible("0");
        category.setDelFlag("0");
        return R.ok(categoryService.selectCategoryTree(category));
    }

    /**
     * 根据店铺分类查询商品
     */
    @GetMapping("/productByCategory")
    public R<TableDataInfo<ProductVo>> getProductCategoryId(@Validated QueryProductByCategoryIdDTO query,PageQuery pageQuery) {
        return R.ok(productService.getShopProductByCategoryId(query,pageQuery));
    }

    /**
     * 根据关键字搜索产品
     */
    @GetMapping("/productByKeyword")
    public R<TableDataInfo<ProductVo>> getProductByShopQuery(@Validated QueryProductByShopQueryDTO query,PageQuery pageQuery) {
        return R.ok(productService.getProductByShopQuery(query,pageQuery));
    }

    /**
     * 获取商品详情
     *
     * @param productId 商品id
     * @param shopId    商家ID/代销用户ID
     */
    @GetMapping("/productInfo")
    public R<ProductInfoVo> getProductInfo(Long productId, Long shopId) {
        ProductInfoVo productVo = productService.getAppProductInfo(productId, shopId);
        return R.ok(productVo);
    }

    /**
     * 获取商品的sku
     *
     * @param productId 商品id
     */
    @GetMapping("/product/sku/{productId}")
    public R<List<Sku>> getProductSku(@PathVariable Long productId) {

        LambdaQueryWrapper<Sku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(Sku::getStatus, "0")
            .eq(Sku::getProductId, productId)
            .orderByAsc(Sku::getSort);
        List<Sku> skuList = skuService.list(queryWrapper);
        return R.ok(skuList);
    }

    /**
     * 获取商家平台促销金余额
     */
    @GetMapping("/getPromotionBalance")
    public R<BigDecimal> getShopPromotionBalance() {
        // 获取当前登录用户ID
        Long userId = LoginHelper.getUserId();

        // 获取商家ID
        Long shopId = shopService.getShopIdByUserId(userId);
        if (shopId == null) {
            return R.fail("未找到商家信息");
        }

        // 获取平台促销金余额
        BigDecimal balance = shopService.getShopPromotionBalance(shopId);
        return R.ok(balance);
    }

    /**
     * 获取店铺交易记录
     *
     * @return
     */
    @GetMapping("/wallet/list")
    public R<List<ShopWalletRecord>> getShopWalletList(@Validated QueryShopWalletRecordDTO query) {

        Long shopId = shopService.getShopIdByUserId(LoginHelper.getUserId());
        if (shopId == null) {
            return R.fail("当前用户不是商家");
        }
        query.setShopId(shopId);
        return R.ok(shopWalletRecordService.getShopWalletRecordList(query));
    }

    /**
     * 根据ID获取店铺交易记录信息
     *
     * @param id 交易ID
     * @return
     */
    @GetMapping("/wallet/{id}")
    public ShopWalletRecord getWalletRecordInfo(Long id) {
        return shopWalletRecordService.getById(id);
    }


}
