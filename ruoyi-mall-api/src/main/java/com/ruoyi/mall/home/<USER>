package com.ruoyi.mall.home;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.OrderVirtualStatusEnum;
import com.ruoyi.common.enums.TzUserStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.bo.VirtualOrderBo;
import com.ruoyi.mall.domain.entity.*;
import com.ruoyi.mall.domain.vo.ProductVo;
import com.ruoyi.mall.domain.vo.ShopVo;
import com.ruoyi.mall.enums.ComponentRecordTypeEnum;
import com.ruoyi.mall.enums.VirtualTypeEnum;
import com.ruoyi.mall.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;


/**
 * 首页
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/homePage")
public class HomePageController extends BaseController {

    private final IUserShopBrowseService userShopBrowseService;
    private final IShopService iShopService;
    private final IProductService productService;
    private final ITzUserService userService;
    private final ITzUserSearchHistoryService userSearchHistoryService;
    private final IShopAdvertisementService shopAdvertisementService;
    private final IUserComponentRecordService componentRecordService;
    private final IConfigSettingService configSettingService;
    private final ICategoryService categoryService;
    private final IVirtualOrderService virtualOrderService;
    private final ITZUserFocusService userFocusService;

    /**
     * 查询店铺
     */
    @GetMapping("/Shops")
    public R<TableDataInfo<ShopVo>> getUserShop(PageQuery pageQuery) {
        return R.ok(iShopService.selectUserShopList(getUserId(),pageQuery));
    }

    /**
     * 点击店铺事件
     * @param shopId 店铺id
     */
    @PostMapping("/clickShop/{shopId}")
    @RepeatSubmit()
    public R clickShop(@PathVariable Long shopId) {
        Map map = new HashMap<>();
        // 获取当前登录用户ID
        Long userId = getUserId();

        //判断用户是否存在
        TzUser tzUser = userService.getById(userId);
        if (tzUser == null) {
            throw new ServiceException("当前用户不存在");
        }else if(tzUser.getIsRealName().equals("0")){
            throw new ServiceException("请先实名认证");
        }

        BigDecimal component =BigDecimal.ZERO;
        Boolean is_click=true;

        Shop shop = iShopService.getById(shopId);
        //店铺不存在||店铺引流次数<=0||店铺状态为停用||被删除
        if (shop == null || shop.getDrainage() == 0 || shop.getStatus().equals("1") || shop.getDelFlag().equals("2") || shop.getAuthStatus().equals("0")) {
             component=Optional.ofNullable(tzUser.getComponent()).orElse(BigDecimal.ZERO);
             is_click=false;
        }
        //修改店铺信息
        LambdaUpdateWrapper<Shop> updateWrapper = new LambdaUpdateWrapper<>();

        //查询用户今天是否点击该店铺
        if (userShopBrowseService.todayUserShopBrowseIsClick(userId, shopId) == 0) {
            //判断用户是否关注店铺，没有默认关注店铺
            if(userFocusService.getUserIsFocus(userId, shopId).equals("1")){
               userFocusService.saveUserFocus(shopId, userId, "0");
            }

            //新增一条点击记录
            UserShopBrowse userShopBrowse = new UserShopBrowse();
            userShopBrowse.setUserId(userId);
            userShopBrowse.setShopId(shopId);
            if(userShopBrowseService.save(userShopBrowse)){
                //记录用户的分量情况
                UserComponentRecord userComponentRecord = new UserComponentRecord();
                userComponentRecord.setUserId(userId);
                userComponentRecord.setComponentValue(new BigDecimal(100));
                userComponentRecord.setRecordType(ComponentRecordTypeEnum.CLICK_SHOP.getValue());
                userComponentRecord.setDescription("商铺引流,点击商铺获得");
                userComponentRecord.setBusinessId(shopId);
                userComponentRecord.setDelFlag("0");
                if(componentRecordService.save(userComponentRecord)){
                    //技术引流次数-1
                    updateWrapper.set(Shop::getDrainage, shop.getDrainage()-1);

                    //修改用户分量+100
                    component = tzUser.getComponent().add(new BigDecimal(100));

                    LambdaUpdateWrapper<TzUser> wrapper = new LambdaUpdateWrapper<>();
                    wrapper.set(TzUser::getComponent, component);

                    //获取分量配置
                    JSONObject componentConfig = configSettingService.getConfigValue("configFour");
                    if (!componentConfig.isEmpty()) {
                        //初始累计多少分量达标
                        BigDecimal initialThreshold = componentConfig.getBigDecimal("initialThreshold");
                        //获取多少平台补贴金上限额度
                        Double dailyThresholdReward = componentConfig.getDouble("dailyThresholdReward");
                        if (component.compareTo(initialThreshold) > 0 && tzUser.getIsFirst().equals("0")) {
                            wrapper
                                .set(TzUser::getDeductionMoneyLimit, tzUser.getDeductionMoneyLimit() + dailyThresholdReward)
                                .set(TzUser::getIsFirst, "1");
                        }
                    }
                    wrapper.eq(TzUser::getUserId, userId);
                    userService.update(wrapper);
                }
            }
            updateWrapper.eq(Shop::getId,shopId);
            if(iShopService.update(updateWrapper)){
                map.put("is_click", false);
                map.put("component",component);

                return R.ok(map);
            }
        }else {
            map.put("is_click", false);
        }
        map.put("is_click", is_click);
        map.put("component",component);

        return R.fail(map);
    }

    /**
     * 根据不同类型获取产品列表
     */
    @GetMapping("/productByType")
    public R<TableDataInfo<ProductVo>> getProductType(@RequestParam Integer type,PageQuery pageQuery) {
        TableDataInfo<ProductVo> productVoTableDataInfo=productService.getProductByType(type, getUserId(), pageQuery);
        if(productVoTableDataInfo.getRows().size()==0&&pageQuery.getPageNum()==1){
            productVoTableDataInfo=productService.getRandomProductPage(pageQuery);
            productVoTableDataInfo.setTotal(productVoTableDataInfo.getTotal()>pageQuery.getPageSize()?pageQuery.getPageSize():productVoTableDataInfo.getTotal());
        }
        return R.ok(productVoTableDataInfo);
    }

    /**
     * 根据分类ID获取产品列表
     * @return
     */
    @GetMapping("getProductByCategoryId")
    public R<TableDataInfo<ProductVo>> getProductByCategoryId(@RequestParam Long categoryId,@RequestParam String query, PageQuery pageQuery) {

        if(StringUtils.isNotBlank( query)){
            //记录用户搜索内容
            recordUserSearchHistory(getUserId(), query);
        }
        return R.ok(productService.getHomeProductByCategoryId(categoryId,query,pageQuery));
    }

    /**
     * 根据关键字搜索商品
     * @param keyword   关键字
     * @return
     */
    @GetMapping("/getProductByKeyword")
    public R<TableDataInfo<ProductVo>> getProductByKeyword(@RequestParam String keyword, PageQuery pageQuery) {
        //记录用户搜索内容
        recordUserSearchHistory(LoginHelper.getUserId(), keyword);
        return R.ok(productService.getProductByKeyword(keyword, pageQuery));
    }

    /**
     * 记录用户搜索历史
     *
     * @param keyword 关键字
     * @return
     */
    public void recordUserSearchHistory(Long userId, String keyword) {

        //当关键字为空时，不记录
        if (StringUtils.isBlank( keyword)) {
            throw  new ServiceException("搜索内容不能为空");
        }
        if(userId==null){
            return;
        }

        LambdaQueryWrapper<TzUserSearchHistory> queryWrapper = new LambdaQueryWrapper();
        queryWrapper
            .eq(TzUserSearchHistory::getUserId, userId)
            .eq(TzUserSearchHistory::getKeyword, keyword);
        //判断是否存过该关键字
        TzUserSearchHistory userSearchHistory = userSearchHistoryService.getOne(queryWrapper);
        if (userSearchHistory == null) {
            TzUserSearchHistory tzUserSearchHistory = new TzUserSearchHistory();
            tzUserSearchHistory.setUserId(userId);
            tzUserSearchHistory.setKeyword(keyword);
            tzUserSearchHistory.setCreateTime(new Date());
            userSearchHistoryService.save(tzUserSearchHistory);
        }
    }


    /**
     * 分页随机查询产品列表（优先展示代销产品，不足时补充普通产品）
     *
     * @param pageQuery 分页参数
     */
    @GetMapping("/randomProduct")
    public R<TableDataInfo<ProductVo>> getRandomProductPage(PageQuery pageQuery) {
        return R.ok(productService.getRandomProductPage(pageQuery));
    }

    /**
     * 根据关键字查询店铺
     *
     * @param keyword   关键词
     * @param pageQuery 分页参数
     * @return
     */
    @GetMapping("/getShopByKeyWord")
    public R<TableDataInfo<ShopVo>> getShopListByKeyWord(@RequestParam String keyword, PageQuery pageQuery) {
        //记录用户搜索内容
        recordUserSearchHistory(LoginHelper.getUserId(), keyword);
        return R.ok(iShopService.selectShopListByKeyWord(getUserId(), keyword, pageQuery));
    }

    /**
     *不同页面展示不同的广告
     * @param type  广告类型 0 :首页 1:分享 2:商区
     * @param pageQuery 分页参数
     * @return
     */
    @GetMapping("/shopAdv")
    public R<TableDataInfo<ShopAdvertisement>> getShopAdvertisementList(@RequestParam Integer type, PageQuery pageQuery) {
        return R.ok(shopAdvertisementService.getAppShopAdvertisementList(type,pageQuery));
    }

    /**
     * 店铺广告点击事件
     *
     * @param id 店铺广告id
     * @return
     */
    @PutMapping("/clickAdv/{id}")
    public R<Void> clickShopAdvertisement(@PathVariable Long id) {
        return toAjax(shopAdvertisementService.updateShopAdvertisementClickNumber(id));
    }

    /**
     * 首页获取商品一级分类
     * @return
     */
    @GetMapping("/categoryList")
    public R<List<Category>> getCategoryList() {
        Category category = new Category();
        category.setParentId(0L);
        category.setStatus("0");
        category.setVisible("0");
        category.setDelFlag("0");
        return R.ok(categoryService.selectCategoryList(category));

    }

    /**
     * 获取商品二级分类
     * @param categoryId
     * @return
     */
    @GetMapping("/second/categoryList")
    public R<List<Category>> getSecondCategoryList(@RequestParam Long  categoryId){
        if (categoryId==null) {
            throw  new ServiceException("请选择上级分类");
        }
        Category category = new Category();
        category.setParentId(categoryId);
        category.setStatus("0");
        category.setVisible("0");
        category.setDelFlag("0");
        return R.ok(categoryService.selectCategoryList(category));
    }

    /**
     *获取代销开通权限列表
     * @return
     */
    @GetMapping("/config")
    public R<JSONObject>  getOpenConfig(){
        return R.ok(configSettingService.getConfigValue(ConfigSettingConstants.config.get(2)));
    }

    /**
     * 开通代销权限
     * @param month  开通代销权限的月数量
     */
    @PostMapping("/openPermissions")
    public R openPermissions(@RequestParam Integer month) {
        TzUser tzUser = userService.getById(getUserId());
        if(tzUser==null){
            throw  new ServiceException("当前用户不存在");
        }else if(tzUser.getStatus().equals(TzUserStatus.DISABLE.getCode())){
            throw new  ServiceException("当前账号已被停用");
        }else if(tzUser.getIsRealName().equals("0")){
            throw  new ServiceException("需要通过实名认证后才能进行开通代销权限");
        }

        //获取设置以几个月收费
        Integer monthNum = Integer.parseInt(configSettingService.getConfigValue(ConfigSettingConstants.config.get(2)).get("configValue").toString());
        //获取代销权限费用
        BigDecimal discountPrice =new BigDecimal(configSettingService.getConfigValue(ConfigSettingConstants.config.get(2)).get("discountPrice").toString());

//        LambdaQueryWrapper<VirtualOrder> wrapper=new LambdaQueryWrapper<>();
//        wrapper
//            .eq(VirtualOrder::getUserId,getUserId())
//            .eq(VirtualOrder::getProductType, VirtualTypeEnum.DAIXIAO_OPEN)
//            .orderByDesc(VirtualOrder::getCreateTime)
//            .last(" limit 1");
//
//        VirtualOrder virtualOrder1 = virtualOrderService.getOne(wrapper);
//        if(virtualOrder1!=null&&virtualOrder1.getStatus().equals(OrderVirtualStatusEnum.PENDING_PAYMENT.getCode())){//待支付
//            throw  new ServiceException("你还有未支付的订单,请勿重复下单");
//        }
        VirtualOrderBo virtualOrderBo = new VirtualOrderBo();
        virtualOrderBo.setUserId(getUserId());
        virtualOrderBo.setPhone(tzUser.getPhone());
        virtualOrderBo.setAmount(discountPrice.divide(BigDecimal.valueOf(monthNum),2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(month)));
        virtualOrderBo.setUserName(tzUser.getUsername());
        virtualOrderBo.setValidDays(getOpenDays(month));
        virtualOrderBo.setOrderType("2");

       return R.ok("",virtualOrderService.createVirtualOrder(virtualOrderBo,VirtualTypeEnum.DAIXIAO_OPEN));
    }


    //获取代销开通时间多少天
    public Integer getOpenDays(Integer  month){
        // 1. 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 2. 计算2个月后的日期
        LocalDate futureDate = currentDate.plusMonths(month);

        // 3. 计算两个日期的天数差
        long daysDifference = ChronoUnit.DAYS.between(currentDate, futureDate);

        return (int) daysDifference;
    }


}

