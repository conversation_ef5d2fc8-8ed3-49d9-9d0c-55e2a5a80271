package com.ruoyi.mall.user;

import cn.hutool.captcha.AbstractCaptcha;
import cn.hutool.captcha.generator.CodeGenerator;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.SecurityConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.*;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.redis.RedisUtils;
import com.ruoyi.common.utils.reflect.ReflectUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.mall.domain.dto.*;
import com.ruoyi.mall.domain.entity.*;
import com.ruoyi.mall.domain.vo.LoginVO;
import com.ruoyi.mall.domain.vo.TzUserInfo;
import com.ruoyi.mall.service.*;
import com.ruoyi.sms.enums.SmsType;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商城用户
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/user")
public class TzUserController extends BaseController {

    private final ITzUserService tzUserService;
    private final IShopAuditService shopAuditService;
    private final IPlatformAgreementService platformAgreementService;
    private final IAccountAuditService accountAuditService;
    private final ITzUserRealNameService userRealNameService;
    private final IOrderService orderService;
    private final IShopAdvertisementService shopAdvertisementService;
    private final IDeductionPaymentRecordService deductionPaymentRecordService;
    private final ITzUserDeductionMoneyLimitRecordService deductionMoneyLimitRecordService;

    /**
     * 发送登录验证码
     */
    @PostMapping("/login/code")
    public R<String> sendLoginCode(@RequestParam String phone) {
        String uuid = tzUserService.sendLoginCode(phone);
        return R.ok("", uuid);
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public R<LoginVO> login(@RequestBody @Validated LoginDTO loginDTO) {

        if (loginDTO.getLoginType() == null) {
            throw new ServiceException("登录类型不能为空");
        }
        if (loginDTO.getLoginType().equals(1)) {
            String password = loginDTO.getPassword();
            if (StrUtil.isBlank(password)) {
                throw new ServiceException("密码不能为空");
            }
            if (StrUtil.isBlank(loginDTO.getCode())) {
                throw new ServiceException("图片验证码验证码不能为空");
            }
            if (StrUtil.isBlank(loginDTO.getRandomStr())) {
                throw new ServiceException("生成图片验证码随机数不能为空");
            }
        } else if (loginDTO.getLoginType().equals(2)) {
            String code = loginDTO.getCode();
            String uuid = loginDTO.getUuid();
            if (StrUtil.isEmpty(code)) {
                throw new ServiceException("验证码不能为空");
            }
            if (StrUtil.isBlank(uuid)) {
                throw new ServiceException("唯一编码不能为空");
            }
        } else {
            throw new ServiceException("未知的登录类型");
        }
        return R.ok(tzUserService.login(loginDTO));
    }

    /**
     * 发送注册验证码
     */
    @PostMapping("/register/code")
    public R<String> sendRegisterCode(@RequestParam String phone) {
        String uuid = tzUserService.sendRegisterCode(SmsType.REGISTER, phone);
        return R.ok("", uuid);
    }

    /**
     * 密码重置获取验证码
     *
     * @param phone 手机号
     * @return
     */
    @PostMapping("/phone/code")
    public R<String> getPhoneCode(@RequestParam String phone) {
        String uuid = tzUserService.sendRegisterCode(SmsType.LOGIN_PWD_RESET, phone);
        return R.ok("", uuid);
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public R<LoginVO> register(@RequestBody @Validated RegisterDTO registerDTO) {
        return R.ok(tzUserService.register(registerDTO));
    }

    /**
     * 用户忘记密码
     *
     * @param forgetPasswordDTO 参数
     * @return
     */
    @PutMapping("/forget/password")
    public R<Void> forgetPassword(@RequestBody @Validated ForgetPasswordDTO forgetPasswordDTO) {
        return toAjax(tzUserService.forgetPassword(forgetPasswordDTO));
    }

    /**
     * 获取用户详细信息
     */
    @GetMapping("/getInfo")
    public R<TzUserInfo> getInfo() {
        TzUserInfo tzUserInfo = tzUserService.getLonginUserInfo(LoginHelper.getUserId());
        if (tzUserInfo != null) {
            return R.ok(tzUserInfo);
        }
        return R.fail("没有当前用户信息");
    }

    /**
     * 获取个人资料
     */
    @GetMapping("/myInfo")
    public R<TzUser> myInfo() {
        TzUser tzUser = tzUserService.getById(LoginHelper.getUserId());
        if (tzUser != null) {
            return R.ok(tzUser);
        }
        return R.fail();
    }

    /**
     * 修改用户信息
     */
    @PutMapping
    public R<Void> edit(@Validated @RequestBody TzUser tzUser) {
        LambdaUpdateWrapper<TzUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .set(StringUtils.isNotBlank(tzUser.getUsername()), TzUser::getUsername, tzUser.getUsername())
            .set(StrUtil.isNotBlank(tzUser.getNickname()), TzUser::getNickname, tzUser.getNickname())
            .set(StrUtil.isNotBlank(tzUser.getEmail()), TzUser::getEmail, tzUser.getEmail())
            .set(StrUtil.isNotBlank(tzUser.getAvatar()), TzUser::getAvatar, tzUser.getAvatar())
            .set(StrUtil.isNotBlank(tzUser.getSex()), TzUser::getSex, tzUser.getSex())
            .eq(TzUser::getUserId, LoginHelper.getUserId());
        return toAjax(tzUserService.update(updateWrapper));
    }

    /**
     * 邀请用户注册
     */
    @PostMapping("/inviteUserRegister")
    public R<Void>  inviteUserRegister(@Validated @RequestBody InviteUserRegisterDTO inviteUserRegisterDTO){
        return toAjax(tzUserService.inviteUserRegister(inviteUserRegisterDTO));
    }

    /**
     * 申请成为商家
     */
    @PostMapping("/apply")
    public R<Boolean> applyShop(@Validated @RequestBody ApplyShopDTO applyShopDTO) {
        LoginUser loginUser = getLoginUser();

        ShopAudit shopAudit = shopAuditService.getOne(
            new LambdaQueryWrapper<ShopAudit>()
                .select(ShopAudit::getAuditStatus)
                .eq(ShopAudit::getUserId, loginUser.getUserId())
                .eq(ShopAudit::getDelFlag, "0")
                .orderByDesc(ShopAudit::getCreateTime)
                .last("limit 1")
        );
        if(shopAudit!=null){
            if(shopAudit.getAuditStatus().equals("0")){
                throw new ServiceException("用户已申请");
            }else if(shopAudit.getAuditStatus().equals("1")){
                throw new ServiceException("用户已通过");
            }
        }

        return R.ok(shopAuditService.createShopAudit(applyShopDTO, loginUser.getUserId()));
    }

    /**
     * 获取邀请码
     */
    @GetMapping("/getInvitationCode")
    public R<String> getInvitationCode() {
        Long userId = getUserId();
        TzUser one = tzUserService.getOne(new LambdaQueryWrapper<TzUser>().eq(TzUser::getUserId, userId));
        // 防止以前的用户没有注册码
        String invitationCode = one.getInvitationCode();
        if (ObjUtil.isNull(invitationCode)) {
            invitationCode = tzUserService.getInvitationCode();
            tzUserService.update(new LambdaUpdateWrapper<TzUser>().eq(TzUser::getUserId, userId).set(TzUser::getInvitationCode, invitationCode));
        }
        return R.ok(invitationCode);
    }

    /**
     * 绑定邀请码
     */
    @PutMapping("/binding/{phone}")
    public R<Boolean> bindingInvitationCode(@PathVariable String phone) {
        Long userId = getUserId();
        TzUser one = tzUserService.getOne(new LambdaQueryWrapper<TzUser>().eq(TzUser::getUserId, userId));
        if (ObjUtil.isNotEmpty(one.getBindingInvitationCode())) {
            throw new ServiceException("已经绑定邀请码过了");
        }
        if (phone.equals(one.getPhone())) {
            throw new ServiceException("不能绑定自己");
        }
        // 获取邀请码的信息
        TzUser invitationUser = tzUserService.getOne(new LambdaQueryWrapper<TzUser>().eq(TzUser::getPhone, phone));
        if (invitationUser == null) {
            throw new ServiceException("邀请码异常");
        }

        boolean flag = tzUserService.update(
            new LambdaUpdateWrapper<TzUser>()
                .set(TzUser::getBindingInvitationCode, invitationUser.getPhone())
                .set(TzUser::getParentId, invitationUser.getUserId())
                .eq(TzUser::getUserId, userId)
        );
        return R.ok(flag);
    }

    /**
     * 创建图形验证码
     */
    @SneakyThrows
    @GetMapping("/image")
    public R<String> image(String randomStr) {
        if (StrUtil.isBlank(randomStr)) {
            return R.fail("获取验证码失败");
        }
        CodeGenerator codeGenerator = ReflectUtils.newInstance(CaptchaType.MATH.getClazz(), 1);
        AbstractCaptcha captcha = SpringUtils.getBean(CaptchaCategory.CIRCLE.getClazz());
        captcha.setGenerator(codeGenerator);
        captcha.createCode();
        ExpressionParser parser = new SpelExpressionParser();
        String code = captcha.getCode();
        Expression exp = parser.parseExpression(StringUtils.remove(code, "="));
        String result = exp.getValue(String.class);
        String key = CacheConstants.DEFAULT_CODE_KEY + randomStr;
        RedisUtils.set(key, result, SecurityConstants.CODE_TIME);

        return R.ok("", captcha.getImageBase64());
    }

    /**
     * 判断用户是否能申请成功商家
     */
    @GetMapping("/checkUserIsChangeShop")
    public R<Long> checkUserIsChangeShop(){
        return R.ok("",tzUserService.checkUserIsChangeShop(getUserId()));
    }

    /**
     * 获取用户商家审核信息
     * @return
     */
    @GetMapping("/getShopAudit")
    public R<ShopAudit> getShopAudit(){
        return R.ok( tzUserService.getShopAudit(getUserId()));
    }

    /**
     * 用户进行实名
     * @param userRealNameDTO 参数
     * @return
     */
    @PostMapping("/realName")
    public R<Void> UserRealName(@Validated @RequestBody UserRealNameDTO userRealNameDTO) {
        TzUserRealName tzUserRealName =userRealNameService.getUserRealNameById(getUserId());
        if(tzUserRealName!=null){
            throw new ServiceException("用户已提交过实名认证,不要重复提交");
        }
        userRealNameDTO.setUserId(getUserId());
        userRealNameDTO.setRealName(userRealNameDTO.getUsername());
//          return  toAjax(tzUserService.UserRealName(userRealNameDTO));
        return toAjax(userRealNameService.addUserRealName(userRealNameDTO));
    }

    /**
     * 获取用户是否实名
     * @return
     */
    @GetMapping("/checkUserIsRealName")
    public R checkUserIsRealName(){
       TzUserRealName userRealName= userRealNameService.getUserRealNameById(getUserId());
       if(userRealName==null){
           throw  new ServiceException("用户未实名");
       }
       return R.ok("申请实名信息",userRealName);
    }

    /**
     *  修改用户实名信息
     * @param userRealNameDTO
     * @return
     */
    @PutMapping("/editUserRealName")
    public R editUserRealName(@Validated @RequestBody UserRealNameDTO userRealNameDTO){
        TzUserRealName  userRealName=userRealNameService.getUserRealNameById(getUserId());
        if(userRealName==null){
            throw new ServiceException("用户实名信息不存在");
        }else if(userRealName.getStatus().equals("1")){
            throw new ServiceException("用户实名已经通过，请勿进行修改");
        }
        userRealNameDTO.setUserId(getUserId());
        userRealNameDTO.setRealName(userRealNameDTO.getUsername());
        return toAjax(userRealNameService.editUserRealName(userRealNameDTO));
    }

    /**
     * 获取协议内容
     * @param type 1：注册协议，2：隐私协议 3:商家入驻协议，4：注销协议，5：退款协议 6:提现协议
     * @return
     */
    @GetMapping("/agreement")
    public R<PlatformAgreement> getPlatformAgreement(@RequestParam Integer type){
        Long paramId = null;
        if(type.equals(1)){
            paramId=1945659031953514498L;
        }else if(type.equals(2)){
            paramId=1945659054200102914L;
        }else if(type.equals(3)){
            paramId=1945428042081857538L;
        }else if(type.equals(4)){
            paramId=1945322439271444481L;
        }else if(type.equals(5)){
            paramId=1945766718791923452L;
        }
        return R.ok(platformAgreementService.getById(paramId));
    }

    /**
     * 获取注销验证码
     * @return
     */
    @PostMapping("/logout/code")
    public R<String> getLogOutPhoneCode() {
        TzUser tzUser = tzUserService.getById(getUserId());
        if(tzUser==null||tzUser.getStatus().equals(TzUserStatus.DISABLE.getCode())){
            throw  new ServiceException("当前用户不存在");
        }
        LambdaQueryWrapper<UserAccountAudit> wrapper=new LambdaQueryWrapper<>();
        wrapper
            .eq(UserAccountAudit::getUserId, getUserId());

        UserAccountAudit userAccountAudit1=accountAuditService.getOne(wrapper);

        if(!userAccountAudit1.getAuditStatus().equals("2")){
            throw new ServiceException("当前用户已申请注销");
        }
        String uuid = tzUserService.sendRegisterCode(SmsType.LOGOUT_ACCOUNT, tzUser.getPhone());
        return R.ok("", uuid);
    }

    /**
     * 验证是否是本人在申请注销账号
     * @return
     */
    @PostMapping("/verifyIsMy")
    public R<Void>  verifyIsMy(@Validated @RequestBody ApplyLogOutDTO applyLogOutDTO){
        TzUser tzUser = tzUserService.getById(getUserId());
        if(tzUser==null||tzUser.getStatus().equals(TzUserStatus.DISABLE.getCode())){
            throw  new ServiceException("当前用户不存在");
        }
        applyLogOutDTO.setPhone(tzUser.getPhone());
        return toAjax(tzUserService.checkIsMy(applyLogOutDTO));
    }

    /**
     * 用户申请注销账号
     *
     * @return
     */
    @PostMapping("/apply/logout")
    public R  userApplyLogout(@Validated @RequestBody UserAccountAudit userAccountAudit){
        TzUser tzUser = tzUserService.getById(getUserId());
        if(tzUser==null||tzUser.getStatus().equals(TzUserStatus.DISABLE.getCode())){
            throw  new ServiceException("当前用户不存在");
        }
        List<Order> order = orderService.list(
            new LambdaQueryWrapper<Order>()
                .notIn(Order::getStatus, OrderStatusEnum.COMPLETED.getCode())
                .eq(Order::getUserId,getUserId())
                .isNotNull(Order::getReceiveTime)  // 确保已收货
                .apply("receive_time <= DATE_SUB(NOW(), INTERVAL 30 DAY)") // 30天前收货
        );

        if(!order.isEmpty()){
            throw new ServiceException("您有未完成订单，订单完结30天后才能注销");
        }

       LambdaQueryWrapper<UserAccountAudit> wrapper=new LambdaQueryWrapper<>();
        wrapper
            .eq(UserAccountAudit::getUserId, getUserId());

        UserAccountAudit userAccountAudit1=accountAuditService.getOne(wrapper);

        if(userAccountAudit1!=null&&!userAccountAudit1.getAuditStatus().equals("2")){
            throw new ServiceException("当前用户已申请注销");
        }else if(userAccountAudit1!=null&&userAccountAudit1.getAuditStatus().equals("2")){
            accountAuditService.removeById(userAccountAudit1);
        }

        userAccountAudit.setUserId(getUserId());
        userAccountAudit.setUsername(tzUser.getUsername());
        userAccountAudit.setNickname(tzUser.getNickname());
        userAccountAudit.setPhone(tzUser.getPhone());
        userAccountAudit.setEmail(tzUser.getEmail());
        userAccountAudit.setUserType(UserTypeEnum.CONSUMER.getAuditCode()); // 普通用户/消费者
        userAccountAudit.setDelFlag("0");
        userAccountAudit.setAuditStatus("0");

        return R.ok(accountAuditService.save(userAccountAudit));
    }

    /**
     * 随机获取广告
     * @return
     */
    @GetMapping("/advertisement/rand")
    public R<ShopAdvertisement>  getAdvertisementRand() {
        return R.ok(shopAdvertisementService.getAdvertisementRand());
    }

    /**
     * 获取设置支付密码验证码
     * @param phone
     * @return
     */
    @GetMapping("/getPayPasswordCode")
    public R<String> getPayPasswordCode(@RequestParam String phone){
        String uuid=tzUserService.sendRegisterCode(SmsType.OPERATION_PWD_RESET, phone);
        return R.ok("", uuid);
    }

    /**
     * 设置支付密码
     * @param setPayPasswordDTO
     * @return
     */
    @PostMapping("/setPayPassword")
     public R  setPayPassword(@Validated @RequestBody SetPayPasswordDTO setPayPasswordDTO){
       return toAjax(tzUserService.setPayPassword(setPayPasswordDTO));
    }

    /**
     * 验证支付密码是否正确
     * @param payPassword
     * @return
     */
    @PostMapping("/verifyIsMyShelf/{payPassword}")
    public R verifyIsMyShelf(@PathVariable String payPassword){
        return toAjax(tzUserService.verifyIsMyShelf(getUserId(),payPassword));
    }

    /**
     * 检验是否设置支付密码
     * @return
     */
    @GetMapping("/checkIsSetPayPassword")
    public R<Void> checkIsSetPayPassword(){
        return toAjax(tzUserService.checkIsSetPayPassword(getUserId()));
    }

    /**
     * 获取用户抵扣金列表
     * @return
     */
    @GetMapping("/getDeductionPaymentRecordList")
    public R<TableDataInfo<DeductionPaymentRecord>> getDeductionPaymentRecordList(PageQuery pageQuery) {

        DeductionPaymentRecord deductionPaymentRecord = new DeductionPaymentRecord();
        deductionPaymentRecord.setUserId(getUserId());
        deductionPaymentRecord.setPayType("1");
        deductionPaymentRecord.setStatus("0");

        return R.ok(deductionPaymentRecordService.queryPageList(deductionPaymentRecord,pageQuery));
    }

    /**
     * 获取用户抵扣金额度列表
     * @param pageQuery
     * @return
     */
    @GetMapping("/getDeductionMoneyLimitList")
    public R<TableDataInfo>  getDeductionMoneyLimitList(PageQuery pageQuery) {
        UserDeductionMoneyLimitRecord deductionMoneyLimitRecord = new UserDeductionMoneyLimitRecord();
        deductionMoneyLimitRecord.setUserId(getUserId());
      return R.ok(deductionMoneyLimitRecordService.selectTzUserDeductionMoneyLimitRecordPage(deductionMoneyLimitRecord,pageQuery));
    }
}

