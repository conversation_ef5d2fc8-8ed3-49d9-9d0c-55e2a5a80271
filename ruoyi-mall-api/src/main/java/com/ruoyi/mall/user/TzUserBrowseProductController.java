package com.ruoyi.mall.user;


import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.mall.service.ITzUserBrowseProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户浏览产品历史
 */

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/browseProduct")
public class TzUserBrowseProductController extends BaseController {

    private final ITzUserBrowseProductService userBrowseProductService;

    /**
     * 记录用户浏览产品历史、更新浏次数,时间
     */
    @PostMapping("/add")
    public R addUserBrowseProduct(@RequestParam Long productId) {
        return  toAjax(userBrowseProductService.addUserBrowseProduct(productId));
    }

    /**
     * 查询个人浏览产品历史
     */
    @GetMapping("/getBrowseList")
    public R<List<Map<String, Object>>> getUserBrowseProductList(PageQuery pageQuery) {
        return R.ok(userBrowseProductService.getUserBrowseProductList(pageQuery));
    }


}
