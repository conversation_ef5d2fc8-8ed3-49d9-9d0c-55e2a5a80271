package com.ruoyi.mall.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.mall.domain.entity.TzUserAddress;
import com.ruoyi.mall.service.ITzUserAddressService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 用户收货地址
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/userAddress")
public class TzUserAddressController extends BaseController {
    private final ITzUserAddressService tzUserAddressService;

    /**
     * 查询用户收货地址列表
     */
    @GetMapping
    public R<List<TzUserAddress>> list() {
        Long userId = getUserId();
        List<TzUserAddress> list = tzUserAddressService.list(new LambdaQueryWrapper<TzUserAddress>().eq(TzUserAddress::getUserId, userId));
        return R.ok(list);
    }

    /**
     * 获取用户收货地址详细信息
     */
    @GetMapping(value = "/{addressId}")
    public R<TzUserAddress> getInfo(@PathVariable("addressId") Long addressId) {
        Long userId = getUserId();
        TzUserAddress one = tzUserAddressService.getOne(
            new LambdaQueryWrapper<TzUserAddress>()
                .eq(TzUserAddress::getUserId, userId)
                .eq(TzUserAddress::getAddressId, addressId)
        );
        return R.ok(one);
    }

    /**
     * 新增用户收货地址
     */
    @PostMapping
    public R<Void> add(@Validated @RequestBody TzUserAddress tzUserAddress) {
        tzUserAddress.setUserId(getUserId());
        //判断用户是否第一次新增收货地址
        if(tzUserAddressService.list(new LambdaQueryWrapper<TzUserAddress>().eq(TzUserAddress::getUserId, tzUserAddress.getUserId())).size()==0){//如果没有收货地址默认第一个为默认地址
            tzUserAddress.setIsDefault(1);
        }
        //当前地址为默认，其他地址为不默认
        if(tzUserAddress.getIsDefault()==1){
           LambdaUpdateWrapper<TzUserAddress> lqw = new LambdaUpdateWrapper<>();
           lqw
               .set(TzUserAddress::getIsDefault, 0)
               .eq(TzUserAddress::getUserId, getUserId());
           tzUserAddressService.update(lqw);
        }
        return toAjax(tzUserAddressService.save(tzUserAddress));
    }

    /**
     * 修改用户收货地址
     */
    @PutMapping
    public R<Void> edit(@Validated @RequestBody TzUserAddress tzUserAddress) {
        return toAjax(tzUserAddressService.updateById(tzUserAddress));
    }

    /**
     * 删除用户收货地址
     */
    @DeleteMapping("/{addressIds}")
    public R<Void> remove(@PathVariable Long[] addressIds) {
        return toAjax(tzUserAddressService.removeBatchByIds(Arrays.asList(addressIds)));
    }

    /**
     * 设置默认收货地址
     */
    @PutMapping("/default/{addressId}")
    public R<Void> setDefault(@PathVariable Long addressId) {
        return toAjax(tzUserAddressService.setDefaultAddress(addressId));
    }

    /**
     * 获取用户默认地址
     * @return
     */
    @GetMapping("/default")
    public R<TzUserAddress> getUserDefaultAddress(){
      return R.ok(tzUserAddressService.getUserDefaultAddress(getUserId()));
    }
}
