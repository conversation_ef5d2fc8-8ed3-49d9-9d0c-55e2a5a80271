package com.ruoyi.mall.user;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.Product;
import com.ruoyi.mall.domain.entity.TzUserSearchHistory;
import com.ruoyi.mall.service.IProductService;
import com.ruoyi.mall.service.ITzUserSearchHistoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

import static com.ruoyi.common.helper.LoginHelper.getUserId;

/**
 * 用户搜索历史管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/userSearchHistory")
public class TzUserSearchHistoryController {


    private final ITzUserSearchHistoryService tzUserSearchHistoryService;

    private final IProductService productService;


    /**
     * 获取用户搜索列表历史
     *
     * @param keyword 关键字
     * @return
     */
    @GetMapping("/list")
    public R<List<String>> getUserSearchHistoryList(@RequestParam String keyword) {

        Long userId = getUserId();  //获取当前用户

        if (StringUtils.isEmpty(keyword)) {
            return R.ok();
        }

        LambdaQueryWrapper<TzUserSearchHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(TzUserSearchHistory::getKeyword)  // 指定查询字段
            .eq(TzUserSearchHistory::getUserId, userId)  // 添加 userId 过滤条件
            .like(TzUserSearchHistory::getKeyword, keyword) // 添加 keyword 过滤条件
            .orderByDesc(TzUserSearchHistory::getCreateTime) //按最近搜索时间排序
            .last("limit 10");//返回数据为10条

        List<TzUserSearchHistory> list = tzUserSearchHistoryService.list(wrapper);

        // 提取 keyword 字段
        List<String> keywords = list.stream().map(TzUserSearchHistory::getKeyword).collect(Collectors.toList());

        return R.ok(keywords);
    }

    /**
     * 猜想用户搜索关键字
     *
     * @param keyword 关键字
     * @return
     */
    @GetMapping("/guess")
    public R<List<String>> guessUserSearch(@RequestParam String keyword) {

        LambdaQueryWrapper<Product> productLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productLambdaQueryWrapper.select(Product::getName)
            .like(Product::getName, keyword)
            .last(" limit 10");

        List<Product> productList = productService.list(productLambdaQueryWrapper);

        List<String> keywords = productList.stream().map(Product::getName).collect(Collectors.toList());

        return R.ok(keywords);
    }


}
