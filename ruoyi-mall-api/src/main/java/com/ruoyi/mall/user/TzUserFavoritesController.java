package com.ruoyi.mall.user;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.TzUserFavorites;
import com.ruoyi.mall.domain.query.QueryFavoritesDTO;
import com.ruoyi.mall.service.ITzUserFavoritesService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

import static com.ruoyi.common.helper.LoginHelper.getUserId;


/**
 * 用户收藏的功能
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/userFavorites")
public class TzUserFavoritesController {


    private final ITzUserFavoritesService tzUserFavoritesService;

    /**
     * 用户收藏或者取消收藏
     */
    @PostMapping("/isCollect")
    public R<Void> TzUserFavoritesIsCollect(@RequestBody TzUserFavorites tzUserFavorites) {

        Long userId = getUserId();

        LambdaQueryWrapper<TzUserFavorites> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzUserFavorites::getProductId, tzUserFavorites.getProductId())
            .eq(TzUserFavorites::getUserId, userId);

        tzUserFavorites = tzUserFavoritesService.getOne(queryWrapper);
        if (tzUserFavorites == null) {
            tzUserFavorites = new TzUserFavorites();
            tzUserFavorites.setUserId(userId);
            tzUserFavorites.setProductId(tzUserFavorites.getProductId());
            tzUserFavorites.setStatus("0");
            tzUserFavorites.setCreateTime(new Date());
            tzUserFavorites.setUpdateTime(new Date());
            tzUserFavorites.setDelFlag("0");

            tzUserFavoritesService.save(tzUserFavorites);
        }

        LambdaUpdateWrapper<TzUserFavorites> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .set(TzUserFavorites::getUpdateTime, new Date())
            .set(TzUserFavorites::getDelFlag, "0")
            .eq(TzUserFavorites::getUserId, userId);

        tzUserFavoritesService.update(updateWrapper);

        return R.ok();
    }

    /**
     * 用户收藏列表
     *
     * @param queryFavoritesDTO 查询条件
     * @return
     */
    @GetMapping("/userFavoritesList")
    public R<TableDataInfo<TzUserFavorites>> UserFavoritesList(@Validated QueryFavoritesDTO queryFavoritesDTO) {
        Long userId = getUserId();
        MPJLambdaWrapper<TzUserFavorites> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper
            .select("t.*,p.name as productName,p.cover as productCover,p.price,s.name as shopName,s.id as shopId,s.logo as shopLogo ")
            .leftJoin("mall_product p on t.product_id=p.id ")
            .leftJoin("mall_shop s on p.shop_id=s.id")
            .eq(TzUserFavorites::getUserId, userId)
            .eq(TzUserFavorites::getStatus, "0")
            .eq(TzUserFavorites::getDelFlag, "0");

        if (StringUtils.isNotEmpty(queryFavoritesDTO.getQuery()) && !queryFavoritesDTO.getQuery().equals("")) {//搜索内容
            queryWrapper.like(TzUserFavorites::getProductName, queryFavoritesDTO.getQuery());
        }

        if (queryFavoritesDTO.getTimeSort().equals("1") || StringUtils.isEmpty(queryFavoritesDTO.getTimeSort())) {//时间排序
            queryWrapper.last("order by t.update_time ");
        } else if (queryFavoritesDTO.getTimeSort().equals("2")) {
            queryWrapper.last("order by t.update_time desc");
        }

        if (queryFavoritesDTO.getPageSize().equals("1")) {//价格排序(低——高)
            queryWrapper.last("order by p.price");
        } else if (queryFavoritesDTO.getPriceSort().equals("2")) {//价格排序(高——低)
            queryWrapper.last("order by p.price desc");
        }

        queryWrapper.last("limit  " + queryFavoritesDTO.getPageNum() + "," + queryFavoritesDTO.getPageSize() + "");

        List<TzUserFavorites> tzUserFavoritesList = tzUserFavoritesService.list(queryWrapper);

        return R.ok(TableDataInfo.build(tzUserFavoritesList));
    }


}




