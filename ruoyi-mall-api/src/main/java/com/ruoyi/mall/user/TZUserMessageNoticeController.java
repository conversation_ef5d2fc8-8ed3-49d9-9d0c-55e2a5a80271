package com.ruoyi.mall.user;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.MessageNotice;
import com.ruoyi.mall.service.IMessageNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 消息通知
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/user/message")
public class TZUserMessageNoticeController {


    private final IMessageNoticeService messageNoticeService;

    /**
     * 获取消息列表
     *
     * @param messageNotice 消息通知对象
     * @param pageQuery     分页对象
     * @return
     */
    @GetMapping("/list")
    public R<TableDataInfo<MessageNotice>> getUserMessageNoticeList(@RequestBody MessageNotice messageNotice, PageQuery pageQuery) {
        return R.ok(messageNoticeService.getMessageNoticeList(messageNotice, pageQuery));
    }

    /**
     * 标记消息为已读
     *
     * @param id 消息ID
     * @return
     */
    @PutMapping("/{id}")
    public R editMessageNoticeIsRead(@PathVariable Long id) {
        return R.ok(messageNoticeService.editMessageNoticeIsRead(id));
    }

    /**
     * 根基id获取消息详情
     *
     * @param id 消息id
     * @return
     */
    @GetMapping("/{id}")
    public R getMessageNoticeById(@PathVariable Long id) {
        return R.ok(messageNoticeService.getMessageNoticeById(id));
    }

    /**
     * 批量删除消息ids
     *
     * @param ids 消息id集合
     * @return
     */
    @DeleteMapping("/{ids}")
    public R deleteMessageNoticeByIds(@PathVariable Long[] ids) {
        return R.ok(messageNoticeService.deleteMessageNoticeByIds(ids));
    }

    /**
     * 根据ID删除消息
     *
     * @param id 消息ID
     * @return
     */
    @DeleteMapping("/{id}")
    public R deleteMessageNoticeById(@PathVariable Long id) {
        return R.ok(messageNoticeService.removeById(id));
    }


}
