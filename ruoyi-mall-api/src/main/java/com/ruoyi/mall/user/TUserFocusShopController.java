package com.ruoyi.mall.user;

import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.mall.domain.entity.TZUserFocus;
import com.ruoyi.mall.service.ITZUserFocusService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户关注
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/user/focus")
public class TUserFocusShopController extends BaseController {

    private final ITZUserFocusService userFocusService;

    /**
     * 用户关注/取消关注店铺
     */
    @PostMapping("/shop")
    @RepeatSubmit()
    public R<Void> userAttentionShop(@RequestBody TZUserFocus tzUserFocus) {
        return toAjax(userFocusService.saveUserFocus(tzUserFocus.getShopId(), getUserId(),tzUserFocus.getStatus()));
    }

    /**
     * 查询用户关注的店铺
     *
     * @param pageQuery 分页参数
     */
    @GetMapping("/list")
    public R<List<TZUserFocus>> getUserAttentionsShops(PageQuery pageQuery) {
        return R.ok(userFocusService.getUserFocusList(getUserId(), pageQuery));
    }

    /**
     * 查询用户是否关注店铺
     *
     * @param shopId 店铺id
     */
    @GetMapping("/isFocus")
    public R<String> getUserIsFocus(Long shopId) {
        return R.ok("",userFocusService.getUserIsFocus(shopId, getUserId()));
    }

}
