package com.ruoyi.mall.user;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.mall.domain.entity.UserComponentRecord;
import com.ruoyi.mall.service.IUserComponentRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户分量记录 - 用户端
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/user/component")
public class TzUserComponentRecordController extends BaseController {

    private final IUserComponentRecordService userComponentRecordService;

    /**
     * 查询当前用户的分量记录列表
     */
    @GetMapping("/record/list")
    public TableDataInfo<UserComponentRecord> list(UserComponentRecord userComponentRecord, PageQuery pageQuery) {
        userComponentRecord.setUserId(LoginHelper.getUserId());
        return userComponentRecordService.selectUserComponentRecordPage(userComponentRecord, pageQuery);
    }

    /**
     * 获取当前用户的分量统计数据
     */
//    @GetMapping("/statistics")
//    public R<Object> getStatistics() {
//        return R.ok(userComponentRecordService.getUserComponentStatistics(LoginHelper.getUserId()));
//    }
}
