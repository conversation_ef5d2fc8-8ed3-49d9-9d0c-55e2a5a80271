package com.ruoyi.mall.order;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.CartListDTO;
import com.ruoyi.mall.domain.entity.Cart;
import com.ruoyi.mall.domain.query.CartQueryParamDTO;
import com.ruoyi.mall.domain.vo.CartInfoVo;
import com.ruoyi.mall.service.ICartService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/cart")
public class TzCartController extends BaseController {

    private final ICartService cartService;

    /**
     * 查询购物车列表
     */
    @GetMapping("/list")
    public TableDataInfo<CartInfoVo> list(CartQueryParamDTO cartQueryParamDTO, PageQuery pageQuery) {
        cartQueryParamDTO.setUserId(getUserId());
        return cartService.selectCartPage(cartQueryParamDTO,pageQuery);
    }

    /**
     * 获取购物车详细信息
     */
    @GetMapping(value = "/{id}")
    public R<Cart> getInfo(@PathVariable("id") Long id) {
        return R.ok(cartService.selectCartById(id));
    }

    /**
     * 添加购物车
     */
    @PostMapping
    public R<Void> add(@Validated @RequestBody Cart cart) {
        cart.setUserId(getUserId());
        return toAjax(cartService.addCart(cart));
    }

    /**
     * 修改购物车
     */
    @PutMapping
    public R<Void> edit(@Validated @RequestBody Cart cart) {
        return toAjax(cartService.updateCart(cart));
    }

    /**
     * 删除购物车
     */
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Long[] ids) {
        return toAjax(cartService.deleteCartByIds(ids));
    }

    /**
     * 清空购物车
     */
    @DeleteMapping("/clear")
    public R<Void> clear() {
        return toAjax(cartService.clearCart(getUserId()));
    }

    /**
     * 修改购物车商品选中状态
     */
    @PutMapping("/selected/{id}")
    public R<Void> updateSelected(@PathVariable("id") Long id, @RequestParam("selected") String selected) {
        return toAjax(cartService.updateCartSelected(id, selected));
    }

    /**
     * 批量修改购物车商品选中状态
     */
    @PutMapping("/selected/batch")
    public R<Void> updateSelectedBatch(@RequestParam("ids") Long[] ids, @RequestParam("selected") String selected) {
        return toAjax(cartService.updateCartSelectedBatch(ids, selected));
    }

    /**
     * 按店铺分组查询购物车列表
     */
    @GetMapping("/listByShop")
    public R<List<CartListDTO>> listByShop() {
        return R.ok("", cartService.selectCartListByShop(getUserId()));
    }

    /**
     * 全选/取消全选
     * @param selected
     * @return
     */
    @PutMapping("/allSelected")
    public R<Void> updateCartAllSelected(@RequestParam String selected ){
        return toAjax(cartService.CartAllSelected(getUserId(),selected));
    }

    /**
     * 修改数量改变总价值
     * @param cartId 购物车ID
     * @param quantity 数量
     * @return 总价格
     */
    @PutMapping("/updateCartQuantity")
    public R<BigDecimal> updateCartQuantity(Long cartId, Integer quantity){
       return R.ok(cartService.updateCartQuantity(cartId,quantity));
    }


}
