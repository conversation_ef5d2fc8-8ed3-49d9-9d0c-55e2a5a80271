package com.ruoyi.mall.order;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.OrderStatusEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.mall.domain.dto.OrderRefundDTO;
import com.ruoyi.mall.domain.entity.Order;
import com.ruoyi.mall.domain.entity.OrderItem;
import com.ruoyi.mall.domain.entity.OrderRefund;
import com.ruoyi.mall.enums.ReturnMoneyStsEnum;
import com.ruoyi.mall.service.IOrderItemService;
import com.ruoyi.mall.service.IOrderRefundService;
import com.ruoyi.mall.service.IOrderService;
import com.ruoyi.pay.config.PayConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 订单退款
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/orderRefund")
public class TzOrderRefundController extends BaseController {

    private final IOrderService orderService;
    private final IOrderRefundService orderRefundService;
    private final PayConfig payConfig;
    private final IOrderItemService orderItemService;

    /**
     * 用户发起退款
     */
    @PostMapping("/applyRefund")
    public R<Void> applyRefund(@Valid @RequestBody OrderRefundDTO orderRefundDto) {
        // 获取订单信息
        Order order = orderService.getById(orderRefundDto.getOrderId());
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        if (!order.getUserId().equals(getUserId())) {
            throw  new ServiceException("无权申请退款");
        }
        // 订单状态已退款，禁止申请退款
        if (OrderStatusEnum.REFUNDED.getCode().equals(order.getStatus())) {
           throw new ServiceException("订单已退款");
        }
        // 订单状态还在待支付状态，禁止申请退款
        if (OrderStatusEnum.PENDING_PAYMENT.getCode().equals(order.getStatus())) {
            throw new ServiceException("订单状态还在待支付状态，禁止申请退款");
        }
        //线下不支持退款
        if(order.getReceiverType().equals("2")){
            throw new ServiceException("线下自提不支持退款,有需要联系商家");
        }

        // 获取所有正在进行中的退款订单
        List<OrderRefund> orderRefunds = orderRefundService.list(
            new LambdaQueryWrapper<OrderRefund>()
                .eq(OrderRefund::getOrderId, order.getId())
                .ne(OrderRefund::getReturnMoneySts, ReturnMoneyStsEnum.SUCCESS.value())
                .ne(OrderRefund::getReturnMoneySts, ReturnMoneyStsEnum.CANCEL.value())
                .ne(OrderRefund::getReturnMoneySts, ReturnMoneyStsEnum.REJECT.value())
                .ne(OrderRefund::getReturnMoneySts, ReturnMoneyStsEnum.FAIL.value())
        );

        if (!orderRefunds.isEmpty()) {
            throw new ServiceException("该订单正在进行退款，无法进行新的退款操作");
        }

        // 生成退款单信息
        OrderRefund newOrderRefund = new OrderRefund();
        newOrderRefund.setShopId(order.getShopId());
        newOrderRefund.setUserId(order.getUserId());
        newOrderRefund.setOrderId(order.getId());
        newOrderRefund.setRefundSn(UUID.randomUUID().toString().replaceAll("-", ""));
        newOrderRefund.setBuyerReason(orderRefundDto.getBuyerReason());
        newOrderRefund.setBuyerMobile(orderRefundDto.getBuyerMobile());
        newOrderRefund.setApplyTime(new Date());
        newOrderRefund.setReturnMoneySts(ReturnMoneyStsEnum.APPLY.value());


        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Order::getRefundStatus, "1")
            .eq(Order::getId, order.getId());

        orderService.update(updateWrapper);

        return toAjax(orderRefundService.applyRefund(newOrderRefund));
    }

    /**
     * 获取用户退款列表
     *
     * @param pageQuery 分页查询条件
     * @return 退款列表
     */
    @GetMapping("/list")
    public TableDataInfo<OrderRefund> list(PageQuery pageQuery) {
        Long userId = getUserId();

        OrderRefund queryParam = new OrderRefund();
        queryParam.setUserId(userId);

        return orderRefundService.selectRefundPage(queryParam, pageQuery);
    }

    /**
     * 获取退款详情
     *
     * @param refundId 退款ID
     * @return 退款详情
     */
    @GetMapping("/{refundId}")
    public R<OrderRefund> getRefundDetail(@PathVariable Long refundId) {
        Long userId = getUserId();

        // 用户模式，不加载订单详情
        OrderRefund orderRefund = orderRefundService.getRefundDetail(refundId, false);

        if (orderRefund == null) {
            return R.fail("退款记录不存在");
        }

        if (!orderRefund.getUserId().equals(userId)) {
            return R.fail("无权查看此退款记录");
        }

        return R.ok(orderRefund);
    }

    /**
     * 用户取消退款申请
     *
     * @param orderId 訂單id
     * @return 操作结果
     */
    @PostMapping("/cancel/{orderId}")
    public R<Void> cancelRefund(@PathVariable Long orderId) {

        LambdaQueryWrapper<OrderRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(OrderRefund::getOrderId, orderId)
            .eq(OrderRefund::getUserId, getUserId())
            .orderByDesc(OrderRefund::getCreateTime)
            .last("limit 1");

        OrderRefund orderRefund = orderRefundService.getOne(queryWrapper);

        if (orderRefund == null) {
            return R.fail("退款记录不存在");
        }

        if (!orderRefund.getUserId().equals(getUserId())) {
            return R.fail("无权操作此退款记录");
        }

        // 只有申请中或处理中的退款可以取消
        if (!ReturnMoneyStsEnum.APPLY.value().equals(orderRefund.getReturnMoneySts()) &&
            !ReturnMoneyStsEnum.PROCESSING.value().equals(orderRefund.getReturnMoneySts())) {
            return R.fail("当前状态无法取消退款申请");
        }

        return toAjax(orderRefundService.cancelRefund(orderRefund.getRefundId()));
    }


    /**
     * 取消退款项
     * @param itemId 退款项id
     * @return
     */
    @PostMapping("/cancelItem/{itemId}")
    public R<Void> cancelRefundItem(@PathVariable  Long itemId) {

        OrderItem orderItem = orderItemService.getById(itemId);
        if (orderItem == null) {
            return R.fail("商品项不存在");
        }
        OrderRefund orderRefund = orderRefundService.getOne(new LambdaQueryWrapper<OrderRefund>()
            .eq(OrderRefund::getItemId,itemId)
            .eq(OrderRefund::getUserId, getUserId())
            .orderByDesc(OrderRefund::getCreateTime)
            .last("limit 1"));


        if (orderRefund == null) {
            return R.fail("退款记录不存在");
        }
        if (!orderRefund.getUserId().equals(getUserId())) {
            return R.fail("无权操作此退款记录");
        }

        // 只有申请中或处理中的退款可以取消
        if (!ReturnMoneyStsEnum.APPLY.value().equals(orderRefund.getReturnMoneySts()) &&
            !ReturnMoneyStsEnum.PROCESSING.value().equals(orderRefund.getReturnMoneySts())) {
            return R.fail("当前状态无法取消退款申请");
        }

        return toAjax(orderRefundService.cancelRefund(orderRefund.getRefundId()));
    }

}


