package com.ruoyi.mall.order;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.mall.domain.dto.CreateOrderByProductIdDTO;
import com.ruoyi.mall.domain.dto.OrderCreateDTO;
import com.ruoyi.mall.domain.dto.OrderPreviewDTO;
import com.ruoyi.mall.domain.entity.Order;
import com.ruoyi.mall.domain.entity.OrderItem;
import com.ruoyi.mall.domain.query.QueryOrderTypeDTO;
import com.ruoyi.mall.domain.vo.OrderVo;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.service.IOrderItemService;
import com.ruoyi.mall.service.IOrderService;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.enums.PayStatusEnum;
import com.ruoyi.pay.service.IPayService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 订单
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/order")
public class TzOrderController extends BaseController {

    private final IOrderService orderService;
    private final IOrderItemService orderItemService;
    private final IPayService payService;
    private final IConfigSettingService configSettingService;

    /**
     * 订单预览(指定商品)
     */
    @PostMapping("/preview")
    public R<OrderPreviewDTO> preview(@RequestBody List<Long> cartIds) {
        return R.ok(orderService.previewOrder(cartIds));
    }

    /**
     * 订单预览(已选中商品)
     */
    @GetMapping("/preview/selected")
    public R<OrderPreviewDTO> previewSelected() {
        return R.ok(orderService.previewSelectedCarts());
    }

    /**
     * 查询订单列表
     */
    @GetMapping("/list")
    public TableDataInfo<Order> list(Order order, PageQuery pageQuery) {
        order.setUserId(getUserId());
        return orderService.selectOrderPage(order, pageQuery);
    }

    /**
     * 获取订单详细信息
     */
    @GetMapping(value = "/{orderNo}")
    public R<Order> getInfo(@PathVariable("orderNo") String orderNo) {
        return R.ok(orderService.selectOrderByOrderNo(orderNo));
    }

    /**
     * 根据购物车下单创建订单
     */
    @PostMapping
    public R<String> add(@Validated @RequestBody OrderCreateDTO orderCreate) {
        return R.ok("",orderService.createOrderByCartIds(orderCreate));
    }

    /**
     * 根据商品下单创建订单
     * @param createOrderByProductIdDTO
     * @return
     */
    @PostMapping("/add")
    public R<String> createOrder(@Validated @RequestBody CreateOrderByProductIdDTO createOrderByProductIdDTO){
        return R.ok("",orderService.createOrderByProductId(createOrderByProductIdDTO));
    }


    /**
     * 取消订单
     */
    @PutMapping("/cancel/{orderNo}")
    public R<Void> cancel(@PathVariable("orderNo") String orderNo) {
        return toAjax(orderService.cancelOrder(orderNo));
    }

    /**
     * 删除订单
     */
    @DeleteMapping("/{orderNos}")
    public R<Void> remove(@PathVariable String[] orderNos) {
        return toAjax(orderService.deleteOrderByIds(orderNos));
    }

    /**
     * 确认收货
     */
    @PutMapping("/confirm/{orderNo}")
    public R<Void> confirm(@PathVariable("orderNo") String orderNo) {
        return toAjax(orderService.confirmReceive(orderNo));
    }

    /**
     * 根据不同类型获取订单列表
     *
     * @param queryOrderTypeDTO 参数对象
     * @return
     */
    @GetMapping("/listByType")
    public R<TableDataInfo<OrderVo>> getOrderListByType(@Validated QueryOrderTypeDTO queryOrderTypeDTO,PageQuery pageQuery) {
        return R.ok(orderService.getOrderListByType(getLoginUser().getUserId(), queryOrderTypeDTO,pageQuery));
    }

    /**
     * 根据订单获取订单详情
     * @param orderId 订单ID
     * @return
     */
    @GetMapping("/orderInfo/{orderId}")
    public R<OrderVo> getOrderInfo(@PathVariable Long orderId) {
        return R.ok(orderService.getOrderInfo(orderId));
    }

    /**
     * 根据商品项ID获取详情
     * @param itemId 商品项ID
     * @return
     */
    @GetMapping("/orderItemInfo/{itemId}")
    public R getOrderItemInfo(@PathVariable  Long  itemId){
        return R.ok(orderItemService.getById(itemId));
    }

    /**
     * 使用抵扣金支付需要收取的手续费
     * @return
     */
    @GetMapping("/deductionAmount")
    public R<JSONObject>  getDeductionAmount(){
        return R.ok(configSettingService.getConfigValue(ConfigSettingConstants.config.get(10)));
    }

    /**
     * 查询订单支付状态
     * @param paymentNo 单号
     * @return
     */
    @GetMapping("/query/{paymentNo}")
    public R<Void> getOrderPayStatus(String paymentNo){
        if (paymentNo == null) {
            throw new ServiceException("支付单号不能为空");
        }

        PayOrder payOrder =payService.getByPaymentNo(paymentNo);
        if(payOrder==null) {
            throw new ServiceException("该订单号不存在");
        }

        // 2. 支付状态是否为待支付或支付中
        if (!(PayStatusEnum.PENDING.getCode().equals(payOrder.getPayStatus()) ||
            PayStatusEnum.DURING.getCode().equals(payOrder.getPayStatus()))) {
            throw  new ServiceException("该订单已支付");
        }

        String payStatus= payService.getByPaymentNo(paymentNo).getPayStatus();
        if(payStatus.equals(PayStatusEnum.SUCCESS.getCode())){
            return R.ok("支付成功");
        }
        return R.fail("支付失败");
    }

    /**
     * 评价
     * @param  type 1：待评价 2 ：已评价
     * @param pageQuery
     * @return
     */
    @GetMapping("/orderItem/evaluate")
    public R<List<OrderItem>> getOrderItemList(@RequestParam Integer type,PageQuery pageQuery) {
       return R.ok(orderItemService.toEvaluate(getUserId(),type,pageQuery));
    }


}
