# ruoyi-mall-api 商城前台API模块

## 模块介绍

`ruoyi-mall-api` 是MallB电商平台的前台API服务模块，面向商城用户(买家)和商家(卖家)
提供接口服务，支持商品展示、搜索、下单、支付、用户中心等前台交互功能，是连接用户与商城系统的桥梁。

## 主要功能

- 用户操作
    - 注册/登录/注销
    - 个人信息管理
    - 收货地址管理
    - 账户安全设置
- 商品展示
    - 商品列表与详情
    - 商品分类浏览
    - 商品搜索与筛选
    - 商品评价展示
- 购物流程
    - 购物车管理
    - 订单创建与支付
    - 订单跟踪查询
    - 物流信息查询
- 支付功能
    - 多种支付方式集成
    - 支付流程处理
    - 支付结果回调
- 商家功能
    - 店铺管理
    - 商品管理
    - 订单处理
    - 收款管理
- 互动功能
    - 商品评价
    - 客服沟通
    - 售后申请

## 技术特点

- 接口安全：Token认证、签名验证、防重放攻击
- 性能优化：缓存策略、接口限流
- 数据一致性：分布式事务处理
- 异常处理：友好的错误提示
- 接口文档：Swagger API文档自动生成

## 与其他模块交互

- 调用ruoyi-mall-server：获取核心业务处理
- 调用ruoyi-pay：支付相关功能
- 调用ruoyi-oss：文件上传处理
- 调用ruoyi-sms：短信通知功能

## API设计规范

1. 统一入口：`/api/v1/` 前缀
2. 资源命名：使用复数名词，如 `/products`
3. HTTP方法：
    - GET：查询资源
    - POST：创建资源
    - PUT：更新资源
    - DELETE：删除资源
4. 状态码：遵循HTTP状态码规范
5. 版本控制：URI版本号控制 