package com.ruoyi.common.enums;

/**
 * 商城用户状态
 * 状态  正常  禁用（后台操作）
 * 失效(适用于B端，CB 端)，从技术引流判断7天后自动失效，超出7天如果再有技术引流从第二天开始计算，没超过7天要7天后时间算
 * 睡眠 （超过3天不登录）
 * 无效(未实名)
 *
 * <AUTHOR>
 */
public enum TzUserStatus {
    OK("0", "正常"),
    DISABLE("1", "禁用"),
    DELETED("2", "删除"),
    FAILURE("3", "失效"),
    SLEEP("4", "睡眠"),
    INVALID("5", "无效"),
    CHARGEBACK("6", "退单");

    private final String code;
    private final String info;

    TzUserStatus(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
