package com.ruoyi.common.enums;

import lombok.Getter;

/**
 * 订单状态
 */
@Getter
public enum OrderVirtualStatusEnum {

    PENDING_PAYMENT("0", "待支付"),
    DURING_PAYMENT("1", "支付中"),
    PENDING_SHIPMENT("2", "已支付"),
    PENDING_RECEIVE("3", "已取消"),
    COMPLETED("4", "已过期");

    private final String code;
    private final String info;

    OrderVirtualStatusEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

}
