package com.ruoyi.common.enums;

/**
 * 用户类型枚举
 * 统一管理不同表中的用户类型定义
 */
public enum UserTypeEnum {

    /**
     * 普通用户/消费者
     */
    CONSUMER("3", "C", "普通用户/消费者"),

    /**
     * 代销商
     */
    CONSIGNMENT("2", "CB", "代销商"),

    /**
     * 商家
     */
    SHOP("1", "B", "商家");

    /**
     * UserAccountAudit表中使用的用户类型编码
     */
    private final String auditCode;

    /**
     * TzUser表中使用的用户类型编码
     */
    private final String tzUserCode;

    /**
     * 用户类型描述
     */
    private final String description;

    UserTypeEnum(String auditCode, String tzUserCode, String description) {
        this.auditCode = auditCode;
        this.tzUserCode = tzUserCode;
        this.description = description;
    }

    public String getAuditCode() {
        return auditCode;
    }

    public String getTzUserCode() {
        return tzUserCode;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据TzUser的用户类型编码转换为UserAccountAudit的用户类型编码
     *
     * @param tzUserCode TzUser表中的用户类型编码(C/CB/B)
     * @return UserAccountAudit表中的用户类型编码(3/2/1)
     */
    public static String convertToAuditCode(String tzUserCode) {
        for (UserTypeEnum type : values()) {
            if (type.tzUserCode.equals(tzUserCode)) {
                return type.auditCode;
            }
        }
        return null;
    }

    /**
     * 根据UserAccountAudit的用户类型编码转换为TzUser的用户类型编码
     *
     * @param auditCode UserAccountAudit表中的用户类型编码(3/2/1)
     * @return TzUser表中的用户类型编码(C/CB/B)
     */
    public static String convertToTzUserCode(String auditCode) {
        for (UserTypeEnum type : values()) {
            if (type.auditCode.equals(auditCode)) {
                return type.tzUserCode;
            }
        }
        return null;
    }

    /**
     * 根据TzUser的用户类型编码获取枚举
     *
     * @param tzUserCode TzUser表中的用户类型编码
     * @return 对应的枚举值
     */
    public static UserTypeEnum getByTzUserCode(String tzUserCode) {
        for (UserTypeEnum type : values()) {
            if (type.tzUserCode.equals(tzUserCode)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据UserAccountAudit的用户类型编码获取枚举
     *
     * @param auditCode UserAccountAudit表中的用户类型编码
     * @return 对应的枚举值
     */
    public static UserTypeEnum getByAuditCode(String auditCode) {
        for (UserTypeEnum type : values()) {
            if (type.auditCode.equals(auditCode)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据用户类型编码获取描述
     *
     * @param auditCode UserAccountAudit表中的用户类型编码
     * @return 用户类型描述
     */
    public static String getDescriptionByAuditCode(String auditCode) {
        UserTypeEnum type = getByAuditCode(auditCode);
        return type != null ? type.getDescription() : "未知用户类型";
    }

    /**
     * 判断是否为普通用户
     *
     * @param tzUserCode TzUser表中的用户类型编码
     * @return 是否为普通用户
     */
    public static boolean isConsumer(String tzUserCode) {
        return CONSUMER.tzUserCode.equals(tzUserCode);
    }

    /**
     * 判断是否为代销商
     *
     * @param tzUserCode TzUser表中的用户类型编码
     * @return 是否为代销商
     */
    public static boolean isConsignment(String tzUserCode) {
        return CONSIGNMENT.tzUserCode.equals(tzUserCode);
    }

    /**
     * 判断是否为商家
     *
     * @param tzUserCode TzUser表中的用户类型编码
     * @return 是否为商家
     */
    public static boolean isShop(String tzUserCode) {
        return SHOP.tzUserCode.equals(tzUserCode);
    }
}
