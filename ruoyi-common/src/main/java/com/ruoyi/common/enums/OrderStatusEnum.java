package com.ruoyi.common.enums;

import lombok.Getter;

/**
 * 订单状态
 */
@Getter
public enum OrderStatusEnum {

    PENDING_PAYMENT("0", "待支付"),
    DURING_PAYMENT("1", "支付中"),
    PENDING_SHIPMENT("2", "待发货"),
    PENDING_RECEIVE("3", "待收货"),
    COMPLETED("4", "已完成"),
    CANCELLED("5", "已取消"),
    REFUNDED("6", "退款/售后中");

    private final String code;
    private final String info;

    OrderStatusEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

}
