package com.ruoyi.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 定制化配置
 *
 * <AUTHOR>
 */
public interface ConfigSettingConstants {

    /**
     * 代销商品权限的配置
     */
    List<String> config = Arrays.asList(
        "",
        "configOne",
        "configTwo",
        "configThree",
        "configFour",
        "configFive",
        "configSix",
        "configSeven",
        "configEight",
        "configNine",
        "configTen",
        "configEleven",
        "configTwelve",
        "configThirteen",
        "configFourteen",
        "configFifteen"
    );
    /**
     * 代销商品权限的配置
     */
    List<String> configName = Arrays.asList(
        "",
        "代销商品权限的配置",
        "开启代销费用配置",
        "代销自动续费配置",
        "分量配置",
        "技术引流配置",
        "广告费用配置",
        "财务配置",
        "进化量配置",
        "关系链自动生成配置",
        "用户使用抵扣金购买费用配置",
        "代销权限一配置",
        "代销权限二配置",
        "产品开通商品属性费用配置",
        "开启量化权限",
        "开启区域权限"
    );


}

