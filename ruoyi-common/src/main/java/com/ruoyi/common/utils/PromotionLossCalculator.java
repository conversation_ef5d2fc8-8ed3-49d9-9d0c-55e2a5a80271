package com.ruoyi.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 平台促销金千六损耗计算工具类
 */
public class PromotionLossCalculator {

    /**
     * 千六损耗率：千分之六 (0.6%)
     */
    private static final BigDecimal LOSS_RATE = new BigDecimal("0.006");

    /**
     * 计算千六损耗金额
     *
     * @param amount 赠送金额
     * @return 损耗金额
     */
    public static BigDecimal calculateLossAmount(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        return amount.multiply(LOSS_RATE).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算实际到账金额
     *
     * @param amount 赠送金额
     * @return 实际到账金额
     */
    public static BigDecimal calculateActualAmount(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal lossAmount = calculateLossAmount(amount);
        return amount.subtract(lossAmount).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算千六损耗详情
     *
     * @param amount 赠送金额
     * @return 损耗计算结果
     */
    public static LossCalculationResult calculateLossDetail(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return new LossCalculationResult(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }

        BigDecimal lossAmount = calculateLossAmount(amount);
        BigDecimal actualAmount = calculateActualAmount(amount);

        return new LossCalculationResult(amount, lossAmount, actualAmount);
    }

    /**
     * 损耗计算结果
     */
    public static class LossCalculationResult {
        /**
         * 原始赠送金额
         */
        private final BigDecimal originalAmount;

        /**
         * 千六损耗金额
         */
        private final BigDecimal lossAmount;

        /**
         * 实际到账金额
         */
        private final BigDecimal actualAmount;

        public LossCalculationResult(BigDecimal originalAmount, BigDecimal lossAmount, BigDecimal actualAmount) {
            this.originalAmount = originalAmount;
            this.lossAmount = lossAmount;
            this.actualAmount = actualAmount;
        }

        public BigDecimal getOriginalAmount() {
            return originalAmount;
        }

        public BigDecimal getLossAmount() {
            return lossAmount;
        }

        public BigDecimal getActualAmount() {
            return actualAmount;
        }

        @Override
        public String toString() {
            return String.format("赠送金额: %s, 千六损耗: %s, 实际到账: %s",
                    originalAmount, lossAmount, actualAmount);
        }
    }

    /**
     * 获取损耗率
     *
     * @return 损耗率
     */
    public static BigDecimal getLossRate() {
        return LOSS_RATE;
    }

    /**
     * 获取损耗率百分比字符串
     *
     * @return 损耗率百分比 (如: "0.6%")
     */
    public static String getLossRatePercentage() {
        return LOSS_RATE.multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP) + "%";
    }
}
