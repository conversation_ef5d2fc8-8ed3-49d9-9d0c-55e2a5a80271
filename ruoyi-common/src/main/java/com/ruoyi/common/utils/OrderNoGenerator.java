package com.ruoyi.common.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * 订单号生成工具类
 */
public class OrderNoGenerator {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");
    private static final Random RANDOM = new Random();

    /**
     * 生成订单号
     *
     * @param prefix 前缀
     * @return 订单号
     */
    public static String generateOrderNo(String prefix) {
        // 日期时间部分
        String dateTime = DATE_FORMAT.format(new Date());

        // 随机数部分（4位随机数）
        int randomNum = RANDOM.nextInt(10000);
        String randomStr = String.format("%04d", randomNum);

        // 组装订单号
        return prefix + dateTime + randomStr;
    }

    /**
     * 生成默认订单号（无前缀）
     *
     * @return 订单号
     */
    public static String generateOrderNo() {
        return generateOrderNo("");
    }
}
