# ruoyi-common 通用工具模块

## 模块介绍

`ruoyi-common` 是MallB电商平台的基础设施模块，提供各个业务模块共用的工具类、通用抽象和核心功能组件。该模块不包含业务逻辑，专注于提供技术支撑。

## 主要内容

- 通用工具类
- 常量定义
- 异常处理框架
- 通用注解
- 核心实体类
- 数据结构封装
- 安全相关工具
- 文件处理工具
- HTTP交互工具
- JSON处理工具
- 日期时间工具

## 关键组件

- 通用响应对象 (R)
- 分页查询工具
- 全局异常处理
- 字符串处理工具
- 加密解密工具
- 参数验证工具
- Excel导入导出
- 缓存操作封装
- 上下文信息获取

## 开发指南

1. 所有模块可直接依赖该模块获取通用功能
2. 添加新工具类时确保无业务逻辑耦合
3. 保持工具类的高复用性和低耦合性
4. 统一异常处理和返回结构 