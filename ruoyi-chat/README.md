# ruoyi-chat 即时通讯模块

## 模块介绍

`ruoyi-chat` 是MallB电商平台的即时通讯服务模块，提供客户与商家、用户与客服之间的实时聊天功能，增强平台互动体验。

## 主要功能

- 一对一即时聊天
- 群聊支持
- 消息持久化
- 聊天记录查询
- 在线状态管理
- 未读消息提醒
- 图片/文件传输支持

## 技术实现

- WebSocket 服务
- STOMP协议
- 消息队列集成
- Redis缓存支持

## 使用场景

- 用户与客服实时沟通
- 买家与卖家商品咨询
- 平台通知消息推送
- 售后服务在线支持

## 开发指南

1. 客户端集成WebSocket连接
2. 消息格式遵循统一JSON规范
3. 身份认证通过token传递

## 功能特点

- 商家与用户实时通信
- WebSocket消息推送
- 多种消息类型支持
- 未读消息计数
- 消息撤回功能
- 响应式前端界面

## 数据库结构

聊天系统包含以下主要表：

1. `chat_conversation`: 聊天会话表
2. `chat_message`: 聊天消息表
3. `chat_attachment`: 聊天附件表
4. `chat_user_setting`: 用户会话设置表
5. `chat_message_archive`: 聊天历史记录归档表（可选）

初始化SQL脚本位于 `src/main/resources/db/chat_tables.sql`

## 如何通过Apifox测试

1. 导入API文档：
    - Apifox中选择「导入数据」→「导入OpenAPI(Swagger)」
    - 选择本地文件导入，路径：`ruoyi-chat/src/main/resources/apifox/mall-chat-api.json`
    - 导入完成后，可以看到「商城聊天」分组下的所有API

2. 准备测试环境：
    - 确保项目已启动（`ruoyi-admin`模块）
    - 登录系统获取JWT令牌

3. 配置环境变量：
    - 在Apifox中添加环境变量：
        - `baseUrl`: `http://localhost:8080` (或你的项目URL)
        - `token`: 你的JWT令牌值

4. 测试流程：

   **基本测试流程**：
    1. 调用「获取用户会话列表」API查看现有会话
    2. 调用「创建或获取与商家的会话」API创建新会话
    3. 调用「获取会话消息列表」API查看会话消息
    4. 调用「发送聊天消息」API发送消息
    5. 再次调用「获取会话消息列表」API查看消息是否发送成功
    6. 调用「标记会话中的所有消息为已读」API
    7. 调用「撤回消息」API测试消息撤回功能

## API接口列表

1. `GET /api/mall/chat/conversations` - 获取用户会话列表
2. `GET /api/mall/chat/merchant/conversations` - 获取商家会话列表
3. `POST /api/mall/chat/conversation` - 创建或获取与商家的会话
4. `GET /api/mall/chat/messages/{conversationId}` - 获取会话消息列表
5. `POST /api/mall/chat/message/send` - 发送聊天消息
6. `POST /api/mall/chat/message/read/{messageId}` - 标记消息为已读
7. `POST /api/mall/chat/conversation/read/{conversationId}` - 标记会话中的所有消息为已读
8. `POST /api/mall/chat/message/recall/{messageId}` - 撤回消息

## WebSocket连接

除了REST API外，系统还提供WebSocket端点用于实时通信：

- WebSocket URL: `ws://localhost:8080/websocket/chat/{token}/{userType}`
- `token`: 用户JWT令牌
- `userType`: 用户类型，可选值为`user`或`merchant`

## 注意事项

1. 测试前请确保数据库中已创建相关表结构
2. 确保JWT令牌有效
3. 消息撤回功能仅在消息发送后2分钟内有效
4. 为保证测试环境的独立性，建议使用测试数据进行测试 