<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.chat.mapper.ChatConversationMapper">

    <resultMap type="com.ruoyi.chat.domain.ChatConversation" id="ChatConversationResult">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="userId" column="user_id"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="lastMessageId" column="last_message_id"/>
        <result property="lastMessageTime" column="last_message_time"/>
        <result property="unreadCount" column="unread_count"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <select id="selectUserConversationList" resultType="com.ruoyi.chat.domain.ChatConversation">
        select c.*, s.name merchantName, s.logo merchantAvatar, m.content
        from chat_conversation c
                 left join mall_shop s on s.id = c.merchant_id
                 left join chat_message m on m.id = c.last_message_id
        where c.user_id = #{userId}
          and c.del_flage = '0' oredr by c.last_message_time desc
    </select>
    <select id="selectPlatFormConversationList" resultType="com.ruoyi.chat.domain.ChatConversation">

    </select>
    <select id="selectMerchantConversationList" resultType="com.ruoyi.chat.domain.ChatConversation">
        select c.*, u.username userName, u.avatar userAvatar, m.content
        from chat_conversation c
                 left join tz_user u on u.user_id = c.user_id
                 left join chat_message m on m.id = c.last_message_id
        where c.merchant_id = #{merchantId}
          and c.del_flage = '0' oredr by c.last_message_time desc
    </select>

    <select id="getUserConversations" resultType="com.ruoyi.chat.domain.ChatConversation">
        SELECT
            t.id,
            t.title,
            t.type,
            t.user_id,
            t.merchant_id,
            t.platform_id,
            t.platform_status,
            t.last_message_id,
            t.last_message_time,
            t.unread_count,
            t.STATUS,
            t.del_flag,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t1.nickname AS userName,
            t1.avatar AS userAvatar,
            t2.business_name AS merchantName,
            t2.logo AS merchantAvatar,
            t3.user_name as platformName
        FROM
            chat_conversation t
                LEFT JOIN tz_user t1 ON ( t1.user_id = t.user_id )
                LEFT JOIN mall_shop t2 ON ( t2.user_id = t.merchant_id and t2.del_flag = '0')
                LEFT JOIN sys_user t3 on (t3.user_id = t.platform_id and t3.del_flag = '0')
        WHERE
            t.del_flag = '0'
          AND ( t.user_id = #{userId} )
        ORDER BY
            t.last_message_time DESC
    </select>

    <select id="getMerchantConversations" resultType="com.ruoyi.chat.domain.ChatConversation">
        SELECT
            t.id,
            t.title,
            t.type,
            t.user_id,
            t.merchant_id,
            t.platform_id,
            t.platform_status,
            t.last_message_id,
            t.last_message_time,
            t.unread_count,
            t.STATUS,
            t.del_flag,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t1.nickname AS userName,
            t1.avatar AS userAvatar,
            t2.business_name AS merchantName,
            t2.logo AS merchantAvatar,
            t3.user_name as platformName
        FROM
            chat_conversation t
                LEFT JOIN tz_user t1 ON ( t1.user_id = t.user_id )
                LEFT JOIN mall_shop t2 ON ( t2.user_id = t.merchant_id and t2.del_flag = '0')
                LEFT JOIN sys_user t3 on (t3.user_id = t.platform_id and t3.del_flag = '0')
        WHERE
            t.del_flag = '0'
          AND ( t.merchant_id = #{merchantId} )
        ORDER BY
            t.last_message_time DESC
    </select>

    <select id="getPlatformConversations" resultType="com.ruoyi.chat.domain.ChatConversation">
        SELECT
            t.id,
            t.title,
            t.type,
            t.user_id,
            t.merchant_id,
            t.platform_id,
            t.platform_status,
            t.last_message_id,
            t.last_message_time,
            t.unread_count,
            t.STATUS,
            t.del_flag,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t1.nickname AS userName,
            t1.avatar AS userAvatar,
            t2.business_name AS merchantName,
            t2.logo AS merchantAvatar,
            t3.user_name as platformName
        FROM
            chat_conversation t
                LEFT JOIN tz_user t1 ON ( t1.user_id = t.user_id )
                LEFT JOIN mall_shop t2 ON ( t2.user_id = t.merchant_id and t2.del_flag = '0')
                LEFT JOIN sys_user t3 on (t3.user_id = t.platform_id and t3.del_flag = '0')
        WHERE
            t.del_flag = '0'
          AND ( t.platform_id = #{platformId} )
        ORDER BY
            t.last_message_time DESC
    </select>

</mapper>
