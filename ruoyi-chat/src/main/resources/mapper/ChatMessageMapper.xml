<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.chat.mapper.ChatMessageMapper">

    <resultMap type="com.ruoyi.chat.domain.ChatMessage" id="ChatMessageResult">
        <id property="id" column="id"/>
        <result property="conversationId" column="conversation_id"/>
        <result property="senderId" column="sender_id"/>
        <result property="senderType" column="sender_type"/>
        <result property="receiverId" column="receiver_id"/>
        <result property="receiverType" column="receiver_type"/>
        <result property="content" column="content"/>
        <result property="contentType" column="content_type"/>
        <result property="sendTime" column="send_time"/>
        <result property="readStatus" column="read_status"/>
        <result property="readTime" column="read_time"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

</mapper>
