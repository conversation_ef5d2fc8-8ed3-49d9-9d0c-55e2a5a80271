package com.ruoyi.chat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户聊天设置实体对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("chat_user_setting")
public class ChatUserSetting extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 设置ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会话ID（为空表示全局设置）
     */
    private Long conversationId;

    /**
     * 是否开启通知
     */
    private Boolean notificationEnabled;

    /**
     * 是否开启声音
     */
    private Boolean soundEnabled;

    /**
     * 是否开启震动
     */
    private Boolean vibrationEnabled;

    /**
     * 是否置顶
     */
    private Boolean topStatus;

    /**
     * 是否免打扰
     */
    private Boolean disturbStatus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
