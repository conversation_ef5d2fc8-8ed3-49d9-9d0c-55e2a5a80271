package com.ruoyi.chat.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 聊天会话实体对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("chat_conversation")
public class ChatConversation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会话标题
     */
    private String title;

    /**
     * 会话类型（1-用户对商家，2-商家对用户，3-群聊, 4:用户-平台 5:商家-平台会话）
     */
    private String type;

    /**
     * 用户ID（如果是用户发起）
     */
    private Long userId;

    /**
     * 商家ID（如果是商家发起）
     */
    private Long merchantId;

    /**
     * 平台方ID（如果平台介入）
     */
    private Long platformId;

    /**
     * 平台介入状态（0-未介入，1-已介入）
     */
    private String platformStatus;

    /**
     * 最后一条消息ID
     */
    private Long lastMessageId;

    /**
     * 最后一条消息时间
     */
    private Date lastMessageTime;

    /**
     * 未读消息数
     */
    private Integer unreadCount;

    /**
     * 状态（0-正常，1-禁用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 用户名称
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 用户头像
     */
    @TableField(exist = false)
    private String userAvatar;

    /**
     * 商铺名称
     */
    @TableField(exist = false)
    private String merchantName;

    /**
     * 商铺logo
     */
    @TableField(exist = false)
    private String merchantAvatar;

    /**
     * 平台名称
     */
    @TableField(exist = false)
    private String platformName;

    /**
     * 消息内容
     */
    @TableField(exist = false)
    private String content;


}
