package com.ruoyi.chat.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 聊天消息实体对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("chat_message")
public class ChatMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会话ID
     */
    private Long conversationId;

    /**
     * 发送者ID
     */
    private Long senderId;

    /**
     * 发送者类型（1-用户，2-商家，3-平台方）
     */
    private String senderType;

    /**
     * 接收者ID
     */
    private Long receiverId;

    /**
     * 接收者类型（1-用户，2-商家，3-平台方）
     * 如果平台介入，用户发送给商家时，接收者类型为平台
     * 如果平台介入，商家发送给用户时，接收者类型为平台
     * 如果平台介入，如果平台发送信息，接收者类型为用户
     */
    private String receiverType;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 内容类型（1-文本，2-图片，3-语音，4-视频，5-文件，6-商品）
     */
    private String contentType;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 读取状态（0-未读，1-已读）
     */
    private String readStatus;

    /**
     * 读取时间
     */
    private Date readTime;

    /**
     * 状态（0-正常，1-撤回，2-删除）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
