package com.ruoyi.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.chat.domain.ChatMessage;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 聊天消息服务接口
 */
public interface IChatMessageService extends IService<ChatMessage> {

    /**
     * 发送消息
     *
     * @param conversationId 会话ID
     * @param senderId       发送者ID
     * @param receiverId     接收者ID
     * @param content        消息内容
     * @param contentType    内容类型
     * @return 消息对象
     */
    ChatMessage sendMessage(Long conversationId, Long senderId, String senderType,
                            Long receiverId, String receiverType,
                            String content, String contentType);

    /**
     * 获取会话的消息列表（使用PageQuery）
     *
     * @param conversationId 会话ID
     * @param pageQuery      分页查询对象
     * @return 表格数据信息
     */
    TableDataInfo<ChatMessage> getConversationMessages(Long conversationId, PageQuery pageQuery);

    /**
     * 标记消息为已读
     *
     * @param messageId 消息ID
     * @return 是否成功
     */
    boolean markMessageAsRead(Long messageId);

    /**
     * 标记会话中的所有消息为已读
     *
     * @param conversationId 会话ID
     * @param userId         用户ID
     * @return 是否成功
     */
    boolean markAllMessagesAsRead(Long conversationId, Long userId);

    /**
     * 撤回消息
     *
     * @param messageId  消息ID
     * @param operatorId 操作者ID
     * @return 是否成功
     */
    boolean recallMessage(Long messageId, Long operatorId);
}
