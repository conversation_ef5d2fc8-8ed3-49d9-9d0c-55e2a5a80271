package com.ruoyi.chat.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.chat.domain.ChatConversation;

import java.util.List;

/**
 * 聊天会话服务接口
 */
public interface IChatConversationService extends IService<ChatConversation> {

    /**
     * 获取用户的会话列表
     *
     * @param userId 用户ID
     * @return 会话列表
     */
    List<ChatConversation> getUserConversations(Long userId);

    /**
     * 获取商家的会话列表
     *
     * @param merchantId 商家ID
     * @return 会话列表
     */
    List<ChatConversation> getMerchantConversations(Long merchantId);

    /**
     * 获取平台方的会话列表
     *
     * @param platformId 平台方ID
     * @return 会话列表
     */
    List<ChatConversation> getPlatformConversations(Long platformId);

    /**
     * 用户创建商家的会话
     * @param userId 用户ID
     * @param shopId 商家ID
     * @param title 会话标题
     * @return
     */
    ChatConversation createShopConversation(Long userId, Long shopId, String title);

    /**
     * 获取用户与商家之间的会话
     *
     * @param userId     用户ID
     * @param merchantId 商家ID
     * @param title      会话标题
     * @return 会话对象
     */
    ChatConversation createOrGetConversation(Long userId, Long merchantId, String title);

    /**
     * 创建或获取用户与平台之间的会话
     *
     * @param userId     用户ID
     * @param platformId 平台ID
     * @param title      会话标题
     * @return 会话对象
     */
    ChatConversation createOrGetUserPlatformConversation(Long userId, Long platformId, String title);

    /**
     * 创建商家和平台方对话
     *
     * @param merchantId 商家id
     * @param platformId 平台id
     * @param title
     * @return
     */
    ChatConversation createMerchantAndPlatFormConversation(Long merchantId, Long platformId, String title);

    /**
     * 平台方介入会话
     *
     * @param conversationId 会话ID
     * @param platformId     平台方ID
     * @return 是否成功
     */
    boolean platformJoinConversation(Long conversationId, Long platformId);

    /**
     * 平台方退出会话
     *
     * @param conversationId 会话ID
     * @return 是否成功
     */
    boolean platformLeaveConversation(Long conversationId);

    /**
     * 更新会话最后一条消息ID
     *
     * @param conversationId 会话ID
     * @param messageId      消息ID
     * @return 是否成功
     */
    boolean updateLastMessage(Long conversationId, Long messageId);

    /**
     * 增加会话未读消息数
     *
     * @param conversationId 会话ID
     * @return 是否成功
     */
    boolean incrementUnreadCount(Long conversationId);

    /**
     * 重置会话未读消息数
     *
     * @param conversationId 会话ID
     * @return 是否成功
     */
    boolean resetUnreadCount(Long conversationId);
}
