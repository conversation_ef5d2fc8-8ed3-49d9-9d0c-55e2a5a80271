package com.ruoyi.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.chat.domain.ChatConversation;
import com.ruoyi.chat.mapper.ChatConversationMapper;
import com.ruoyi.chat.service.IChatConversationService;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.mapper.ShopMapper;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.mall.service.ITzUserService;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 聊天会话服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatConversationServiceImpl extends ServiceImpl<ChatConversationMapper, ChatConversation> implements IChatConversationService {

    private final ChatConversationMapper chatConversationMapper;
    private final ISysUserService iSysUserService;
    private final ShopMapper shopMapper;
    private final ITzUserService tzUserService;
    private final IShopService shopService;

    @Override
    public List<ChatConversation> getUserConversations(Long userId) {
        return chatConversationMapper.getUserConversations(userId);
    }

    @Override
    public List<ChatConversation> getMerchantConversations(Long merchantId) {
        return chatConversationMapper.getMerchantConversations(merchantId);
    }

    @Override
    public List<ChatConversation> getPlatformConversations(Long platformId) {
        return chatConversationMapper.getPlatformConversations(platformId);
    }


    /**
     * @param userId     用户ID
     * @param shopId 商家ID
     * @param title      会话标题
     */
    @Override
    public ChatConversation createShopConversation(Long userId, Long shopId, String title) {

        Shop shop = shopMapper.selectById(shopId);

        // 先查询是否已存在会话
        LambdaQueryWrapper<ChatConversation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatConversation::getUserId, userId)
            .eq(ChatConversation::getMerchantId, shop.getUserId())
            .eq(ChatConversation::getDelFlag, "0");

        ChatConversation conversation = getOne(wrapper);

        // 如果不存在，创建新会话
        if (conversation == null) {
            conversation = new ChatConversation();
            conversation.setUserId(userId);
            conversation.setMerchantId(shop.getUserId());
            conversation.setTitle(title);
            conversation.setType("1"); // 用户对商家
            conversation.setUnreadCount(0);
            conversation.setStatus("0"); // 正常
            conversation.setDelFlag("0");
            conversation.setPlatformStatus("0"); // 平台未介入
            conversation.setCreateTime(new Date());

            save(conversation);
        }

        TzUser user = tzUserService.getById(userId);
        if(user != null){
            conversation.setUserName(user.getNickname());
            conversation.setUserAvatar(user.getAvatar());
        }
        conversation.setMerchantName(shop.getBusinessName());
        conversation.setMerchantAvatar(shop.getLogo());

        return conversation;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChatConversation createOrGetConversation(Long userId, Long merchantId, String title) {
        // 先查询是否已存在会话
        LambdaQueryWrapper<ChatConversation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatConversation::getUserId, userId)
            .eq(ChatConversation::getMerchantId, merchantId)
            .eq(ChatConversation::getType, "1")
            .eq(ChatConversation::getDelFlag, "0");

        ChatConversation conversation = getOne(wrapper);

        // 如果不存在，创建新会话
        if (conversation == null) {
            conversation = new ChatConversation();
            conversation.setUserId(userId);
            conversation.setMerchantId(merchantId);
            conversation.setTitle(title);
            conversation.setType("1"); // 用户对商家
            conversation.setUnreadCount(0);
            conversation.setStatus("0"); // 正常
            conversation.setDelFlag("0");
            conversation.setPlatformStatus("0"); // 平台未介入
            conversation.setCreateTime(new Date());

            save(conversation);
        }
        TzUser user = tzUserService.getById(userId);
        if(user != null){
            conversation.setUserName(user.getNickname());
            conversation.setUserAvatar(user.getAvatar());
        }
        Shop shop = shopService.getOne(
            new LambdaQueryWrapper<Shop>()
                .eq(Shop::getUserId, merchantId)
        );
        if (shop != null){
            conversation.setMerchantName(shop.getBusinessName());
            conversation.setMerchantAvatar(shop.getLogo());
        }

        return conversation;
    }

    @Override
    public ChatConversation createOrGetUserPlatformConversation(Long userId, Long platformId, String title) {
        // 先查询是否已存在会话
        LambdaQueryWrapper<ChatConversation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatConversation::getUserId, userId)
            .eq(ChatConversation::getPlatformId, platformId)
            .eq(ChatConversation::getType, "4") // 用户对平台
            .eq(ChatConversation::getDelFlag, "0");

        ChatConversation conversation = getOne(wrapper);

        // 如果不存在，创建新会话
        if (conversation == null) {
            conversation = new ChatConversation();
            conversation.setUserId(userId);
            conversation.setPlatformId(platformId);
            conversation.setTitle(title);
            conversation.setType("4"); // 用户对平台
            conversation.setUnreadCount(0);
            conversation.setStatus("0"); // 正常
            conversation.setDelFlag("0");
            conversation.setPlatformStatus("1"); // 平台默认介入
            conversation.setCreateTime(new Date());

            save(conversation);
        }

        return conversation;
    }

    @Override
    public ChatConversation createMerchantAndPlatFormConversation(Long merchantId, Long platformId, String title) {

        LambdaQueryWrapper<ChatConversation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatConversation::getMerchantId, merchantId)
            .eq(ChatConversation::getPlatformId, platformId)
            .eq(ChatConversation::getType, "5") // 确保只查找商家对平台类型的会话
            .eq(ChatConversation::getDelFlag, "0");

        ChatConversation conversation = getOne(wrapper);

        if (conversation == null) {
            conversation = new ChatConversation();
            conversation.setMerchantId(merchantId);
            conversation.setPlatformId(platformId);
            conversation.setType("5");//商家对平台
            conversation.setTitle(title);
            conversation.setStatus("0");//正常
            conversation.setDelFlag("0");//未删除
            conversation.setPlatformStatus("1");//平台默认介入
            conversation.setUnreadCount(0);
            conversation.setCreateTime(new Date());

            save(conversation);
        }

        return conversation;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean platformJoinConversation(Long conversationId, Long platformId) {
        ChatConversation conversation = getById(conversationId);
        if (conversation == null) {
            return false;
        }

        LambdaUpdateWrapper<ChatConversation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatConversation::getId, conversationId)
            .set(ChatConversation::getPlatformId, platformId)
            .set(ChatConversation::getPlatformStatus, "1")
            // 更新为群聊
            .set(ChatConversation::getType, "3");

        return update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean platformLeaveConversation(Long conversationId) {
        ChatConversation conversation = getById(conversationId);
        if (conversation == null) {
            return false;
        }

        LambdaUpdateWrapper<ChatConversation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatConversation::getId, conversationId)
            .set(ChatConversation::getPlatformStatus, "0")
            .set(ChatConversation::getType, "1"); // 恢复为用户对商家会话类型

        return update(updateWrapper);
    }

    @Override
    public boolean updateLastMessage(Long conversationId, Long messageId) {
        ChatConversation conversation = getById(conversationId);
        if (conversation == null) {
            return false;
        }

        LambdaUpdateWrapper<ChatConversation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatConversation::getId, conversationId)
            .set(ChatConversation::getLastMessageId, messageId)
            .set(ChatConversation::getLastMessageTime, new Date());

        return update(updateWrapper);
    }

    @Override
    public boolean incrementUnreadCount(Long conversationId) {
        ChatConversation conversation = getById(conversationId);
        if (conversation == null) {
            return false;
        }

        LambdaUpdateWrapper<ChatConversation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatConversation::getId, conversationId)
            .setSql("unread_count = unread_count + 1");

        return update(updateWrapper);
    }

    @Override
    public boolean resetUnreadCount(Long conversationId) {
        ChatConversation conversation = getById(conversationId);
        if (conversation == null) {
            return false;
        }

        LambdaUpdateWrapper<ChatConversation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatConversation::getId, conversationId)
            .set(ChatConversation::getUnreadCount, 0);

        return update(updateWrapper);
    }
}
