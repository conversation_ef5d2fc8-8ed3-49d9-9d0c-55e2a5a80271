package com.ruoyi.chat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.chat.domain.ChatMessage;
import com.ruoyi.chat.mapper.ChatMessageMapper;
import com.ruoyi.chat.service.IChatConversationService;
import com.ruoyi.chat.service.IChatMessageService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 聊天消息服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatMessageServiceImpl extends ServiceImpl<ChatMessageMapper, ChatMessage> implements IChatMessageService {

    private final IChatConversationService conversationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChatMessage sendMessage(Long conversationId, Long senderId, String senderType,
                                   Long receiverId, String receiverType,
                                   String content, String contentType) {
        // 1. 创建消息
        ChatMessage message = new ChatMessage();
        message.setConversationId(conversationId);
        message.setSenderId(senderId);
        message.setSenderType(senderType);
        message.setReceiverId(receiverId);
        message.setReceiverType(receiverType);
        message.setContent(content);
        message.setContentType(contentType);
        message.setSendTime(new Date());
        message.setReadStatus("0"); // 未读
        message.setStatus("0"); // 正常
        message.setDelFlag("0");
        message.setCreateTime(new Date());

        // 2. 保存消息
        save(message);

        // 3. 更新会话的最后一条消息
        conversationService.updateLastMessage(conversationId, message.getId());

        // 4. 增加未读消息计数
        conversationService.incrementUnreadCount(conversationId);

        return message;
    }

    @Override
    public TableDataInfo<ChatMessage> getConversationMessages(Long conversationId, PageQuery pageQuery) {
        // 查询会话消息
        LambdaQueryWrapper<ChatMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChatMessage::getConversationId, conversationId)
            .eq(ChatMessage::getDelFlag, "0")
            .orderByDesc(ChatMessage::getSendTime);
        Page<ChatMessage> page = baseMapper.selectPage(pageQuery.build(), wrapper);
        return TableDataInfo.build(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markMessageAsRead(Long messageId) {
        ChatMessage message = getById(messageId);
        if (message == null || "1".equals(message.getReadStatus())) {
            return false;
        }

        LambdaUpdateWrapper<ChatMessage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatMessage::getId, messageId)
            .set(ChatMessage::getReadStatus, "1")
            .set(ChatMessage::getReadTime, new Date());

        return update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAllMessagesAsRead(Long conversationId, Long userId) {
        // 查找所有未读的消息
        LambdaQueryWrapper<ChatMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatMessage::getConversationId, conversationId)
            .eq(ChatMessage::getReceiverId, userId)
            .eq(ChatMessage::getReadStatus, "0")
            .eq(ChatMessage::getDelFlag, "0");

        List<ChatMessage> unreadMessages = list(queryWrapper);
        if (unreadMessages.isEmpty()) {
            return true;
        }

        // 批量更新为已读
        LambdaUpdateWrapper<ChatMessage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatMessage::getConversationId, conversationId)
            .eq(ChatMessage::getReceiverId, userId)
            .eq(ChatMessage::getReadStatus, "0")
            .set(ChatMessage::getReadStatus, "1")
            .set(ChatMessage::getReadTime, new Date());

        boolean result = update(updateWrapper);

        // 重置会话的未读计数
        if (result) {
            conversationService.resetUnreadCount(conversationId);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recallMessage(Long messageId, Long operatorId) {
        ChatMessage message = getById(messageId);
        if (message == null) {
            return false;
        }

        // 检查是否是发送者本人撤回
        if (!message.getSenderId().equals(operatorId)) {
            return false;
        }

        // 检查消息发送时间是否在2分钟内
        long currentTime = System.currentTimeMillis();
        long sendTime = message.getSendTime().getTime();
        if (currentTime - sendTime > 2 * 60 * 1000) {
            return false; // 超过2分钟不能撤回
        }

        LambdaUpdateWrapper<ChatMessage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatMessage::getId, messageId)
            .set(ChatMessage::getStatus, "1"); // 撤回状态

        return update(updateWrapper);
    }
}
