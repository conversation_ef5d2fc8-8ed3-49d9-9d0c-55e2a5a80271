package com.ruoyi.chat.controller;

import com.ruoyi.chat.domain.ChatConversation;
import com.ruoyi.chat.domain.ChatMessage;
import com.ruoyi.chat.service.IChatConversationService;
import com.ruoyi.chat.service.IChatMessageService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 平台客服聊天API接口
 */
@Tag(name = "平台客服聊天API接口")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/chatPlatform")
public class PlatformChatController extends BaseController {

    private final IChatConversationService conversationService;
    private final IChatMessageService messageService;

    /**
     * 获取平台方的会话列表
     */
    @Operation(summary = "获取平台方会话列表")
    @GetMapping("/conversations")
    public R<List<ChatConversation>> getPlatformConversations() {
        Long platformId = getUserId(); // 假设平台方ID与用户ID相同，实际项目中可能需要更复杂的逻辑
        List<ChatConversation> conversations = conversationService.getPlatformConversations(platformId);
        return R.ok(conversations);
    }

    /**
     * 获取会话的消息列表
     */
    @Operation(summary = "获取会话消息列表")
    @GetMapping("/messages/{conversationId}")
    public TableDataInfo<ChatMessage> getConversationMessages(@Parameter(description = "会话ID") @PathVariable Long conversationId, PageQuery pageQuery) {
        return messageService.getConversationMessages(conversationId, pageQuery);
    }

    /**
     * 发送消息
     */
    @Operation(summary = "发送聊天消息")
    @PostMapping("/message/send")
    public R<ChatMessage> sendMessage(@RequestBody Map<String, Object> params) {
        Long conversationId = Long.valueOf(params.get("conversationId").toString());
        String content = params.get("content").toString();
        String contentType = params.getOrDefault("contentType", "1").toString();
        Long receiverId = Long.valueOf(params.get("receiverId").toString());
        String receiverType = params.getOrDefault("receiverType", "1").toString(); // 默认发送给用户

        Long platformId = getUserId();
        String senderType = "3"; // 平台方

        ChatMessage message = messageService.sendMessage(
            conversationId, platformId, senderType, receiverId, receiverType, content, contentType);

        return R.ok(message);
    }

    /**
     * 平台方介入会话
     */
    @Operation(summary = "平台方介入会话")
    @PostMapping("/join/{conversationId}")
    public R<Boolean> joinConversation(@PathVariable Long conversationId) {
        Long platformId = 1L;
        boolean result = conversationService.platformJoinConversation(conversationId, platformId);
        return R.ok(result);
    }

    /**
     * 平台方退出会话
     */
    @Operation(summary = "平台方退出会话")
    @PostMapping("/leave")
    public R<Boolean> leaveConversation(@RequestParam Long conversationId) {
        boolean result = conversationService.platformLeaveConversation(conversationId);
        return R.ok(result);
    }

    /**
     * 标记消息为已读
     */
    @Operation(summary = "标记消息为已读")
    @PostMapping("/message/read/{messageId}")
    public R<Boolean> markMessageAsRead(@PathVariable Long messageId) {
        boolean result = messageService.markMessageAsRead(messageId);
        return R.ok(result);
    }

    /**
     * 标记会话中的所有消息为已读
     */
    @Operation(summary = "标记会话中的所有消息为已读")
    @PostMapping("/conversation/read/{conversationId}")
    public R<Boolean> markAllMessagesAsRead(@PathVariable Long conversationId) {
        Long platformId = getUserId();
        boolean result = messageService.markAllMessagesAsRead(conversationId, platformId);
        return R.ok(result);
    }
}
