package com.ruoyi.chat.controller;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.chat.domain.ChatConversation;
import com.ruoyi.chat.domain.ChatMessage;
import com.ruoyi.chat.service.IChatConversationService;
import com.ruoyi.chat.service.IChatMessageService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.vo.SysOssVo;
import com.ruoyi.system.service.ISysOssService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 聊天控制器
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/chat")
public class ChatController extends BaseController {

    private final IChatConversationService conversationService;
    private final IChatMessageService messageService;
    private final ISysOssService iSysOssService;

    /**
     * 获取用户的会话列表
     */
    @GetMapping("/conversations")
    public R<List<ChatConversation>> getUserConversations() {
        Long userId = getUserId();
        List<ChatConversation> conversations = conversationService.getUserConversations(userId);
        return R.ok(conversations);
    }

    /**
     * 用户创建商家的会话
     */
    @PostMapping("/createShopConversation")
    public R<ChatConversation> createShopConversation(@RequestParam Long shopId, @RequestParam String title) {
        Long userId = getUserId();
        ChatConversation conversation = conversationService.createShopConversation(userId, shopId, title);
        return R.ok(conversation);
    }

    /**
     * 用户获取与商家的会话
     */
    @PostMapping("/conversation")
    public R<ChatConversation> createOrGetConversation(@RequestParam Long merchantId, @RequestParam String title) {
        Long userId = getUserId();
        ChatConversation conversation = conversationService.createOrGetConversation(userId, merchantId, title);
        return R.ok(conversation);
    }

    /**
     * 用户创建或获取与平台的会话
     */
    @PostMapping("/platform/conversation")
    public R<ChatConversation> createOrGetUserPlatformConversation(@RequestParam String title) {
        Long userId = getUserId();
        // 平台ID默认为1，实际项目中可能需要从配置或其他地方获取
        Long platformId = 1L;
        ChatConversation conversation = conversationService.createOrGetUserPlatformConversation(userId, platformId, title);
        return R.ok(conversation);
    }

    /**
     * 获取商家的会话列表
     */
    @GetMapping("/merchant/conversations")
    public R<List<ChatConversation>> getMerchantConversations() {
        // 获得ID
        Long merchantId = getUserId();
        List<ChatConversation> conversations = conversationService.getMerchantConversations(merchantId);
        return R.ok(conversations);
    }

    /**
     * 商家创建或获取与平台的会话
     */
    @PostMapping("/merchant/platform/conversation")
    public R<ChatConversation> createOrGetMerchantPlatformConversation(@RequestParam String title) {
        Long merchantId = getUserId();
        // 平台ID默认为1，实际项目中可能需要从配置或其他地方获取
        Long platformId = 1L;
        ChatConversation conversation = conversationService.createMerchantAndPlatFormConversation(merchantId, platformId, title);
        return R.ok(conversation);
    }

    /**
     * 上传OSS对象存储
     * 客服聊天发送图片和视频(不超过5MB)
     *
     * @param file 文件
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Map<String, String>> upload(@RequestPart("file") MultipartFile file) {
        if (ObjectUtil.isNull(file)) {
            return R.fail("上传文件不能为空");
        }

        long maxSize = 1024L * 1024L * 5; // 设定最大允许大小为 15 MB
        String contentType = file.getContentType();//文件类型（图片/视频）
        if (contentType.startsWith("video/")) {//视频
            if (file.getSize() > maxSize) {
                return R.fail("视频大小不能超过5MB");
            }
        }

        SysOssVo oss = iSysOssService.upload(file);
        Map<String, String> map = new HashMap<>(4);
        map.put("url", oss.getUrl());
        map.put("filePath", oss.getFilePath());
        map.put("fileName", oss.getOriginalName());
        map.put("ossId", oss.getOssId().toString());
        return R.ok(map);
    }

    /**
     * 获取会话的消息列表
     */
    @GetMapping("/messages/{conversationId}")
    public TableDataInfo<ChatMessage> getConversationMessages(@PathVariable Long conversationId, PageQuery pageQuery) {
        return messageService.getConversationMessages(conversationId, pageQuery);
    }

    /**
     * 标记消息为已读
     */
    @PostMapping("/message/read/{messageId}")
    public R<Boolean> markMessageAsRead(@PathVariable Long messageId) {
        boolean result = messageService.markMessageAsRead(messageId);
        return R.ok(result);
    }

    /**
     * 标记会话中的所有消息为已读
     */
    @PostMapping("/conversation/read/{conversationId}")
    public R<Boolean> markAllMessagesAsRead(@PathVariable Long conversationId) {
        Long userId = getUserId();
        boolean result = messageService.markAllMessagesAsRead(conversationId, userId);
        return R.ok(result);
    }

    /**
     * 撤回消息
     */
    @PostMapping("/message/recall/{messageId}")
    public R<Boolean> recallMessage(@PathVariable Long messageId) {
        Long userId = getUserId();
        boolean result = messageService.recallMessage(messageId, userId);
        return R.ok(result);
    }
}
