package com.ruoyi.chat.websocket;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.chat.domain.ChatConversation;
import com.ruoyi.chat.domain.ChatMessage;
import com.ruoyi.chat.service.IChatConversationService;
import com.ruoyi.chat.service.IChatMessageService;
import com.ruoyi.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket聊天端点
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/chat/{token}/{type}")
public class ChatEndpoint {

    /**
     * 用户连接集合，key为用户ID，value为WebSocket会话
     */
    private static final Map<String, Session> USER_SESSIONS = new ConcurrentHashMap<>();

    /**
     * 商家连接集合，key为商家ID，value为WebSocket会话
     */
    private static final Map<String, Session> MERCHANT_SESSIONS = new ConcurrentHashMap<>();

    /**
     * 平台方连接集合，key为平台方ID，value为WebSocket会话
     */
    private static final Map<String, Session> PLATFORM_SESSIONS = new ConcurrentHashMap<>();

    /**
     * 发送消息给用户
     */
    public static void sendMessageToUser(String userId, String message) {
        Session session = USER_SESSIONS.get(userId);
        if (session != null && session.isOpen()) {
            try {
                session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                log.error("发送消息给用户异常", e);
            }
        }
    }

    /**
     * 发送消息给商家
     */
    public static void sendMessageToMerchant(String merchantId, String message) {
        Session session = MERCHANT_SESSIONS.get(merchantId);
        if (session != null && session.isOpen()) {
            try {
                session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                log.error("发送消息给商家异常", e);
            }
        }
    }

    /**
     * 发送消息给平台方
     */
    public static void sendMessageToPlatform(String platformId, String message) {
        Session session = PLATFORM_SESSIONS.get(platformId);
        if (session != null && session.isOpen()) {
            try {
                session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                log.error("发送消息给平台方异常", e);
            }
        }
    }

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("token") String token, @PathParam("type") String type) {
        try {
            // 验证token
            Object loginIdString = StpUtil.getLoginIdByToken(token);
            String[] parts = loginIdString.toString().split(":");
            Long loginId = Long.parseLong(parts[1]);
            if (loginId == null) {
                sendMessage(session, createErrorMessage("未登录或登录已过期"));
                session.close();
                return;
            }

            String id = loginId.toString();

            log.info("当前登录用户id" + loginId);

            // 根据类型存储会话
            if ("user".equals(type)) {
                USER_SESSIONS.put(id, session);
                log.info("用户连接成功，用户ID: {}", id);
            } else if ("merchant".equals(type)) {
                MERCHANT_SESSIONS.put(id, session);
                log.info("商家连接成功，商家ID: {}", id);
            } else if ("platform".equals(type)) {
                PLATFORM_SESSIONS.put(id, session);
                log.info("平台方连接成功，平台方ID: {}", id);
            } else {
                sendMessage(session, createErrorMessage("无效的连接类型"));
                session.close();
                return;
            }

            // 发送连接成功消息
            sendMessage(session, createSuccessMessage("连接成功"));
        } catch (Exception e) {
            log.error("WebSocket连接异常", e);
            try {
                sendMessage(session, createErrorMessage("连接异常: " + e.getMessage()));
                session.close();
            } catch (IOException ex) {
                log.error("关闭WebSocket连接异常", ex);
            }
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(Session session, @PathParam("token") String token, @PathParam("type") String type) {
        try {
            Object loginIdString = StpUtil.getLoginIdByToken(token);
            if (loginIdString == null){
                return;
            }
            String[] parts = loginIdString.toString().split(":");
            if (parts.length < 2){
                return;
            }
            long loginId = Long.parseLong(parts[1]);
            String id = Long.toString(loginId);

            if ("user".equals(type)) {
                Session remove = USER_SESSIONS.remove(id);
                if (remove != null && remove.isOpen()) {
                    remove.close();
                }
                log.info("用户连接关闭，用户ID: {}", id);
            } else if ("merchant".equals(type)) {
                Session remove = MERCHANT_SESSIONS.remove(id);
                if (remove != null && remove.isOpen()) {
                    remove.close();
                }
                log.info("商家连接关闭，商家ID: {}", id);
            } else if ("platform".equals(type)) {
                Session remove = PLATFORM_SESSIONS.remove(id);
                if (remove != null && remove.isOpen()) {
                    remove.close();
                }
                log.info("平台方连接关闭，平台方ID: {}", id);
            }
        } catch (Exception e) {
            log.error("WebSocket关闭异常", e);
        }
    }

    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session, @PathParam("token") String token, @PathParam("type") String type) {
        try {
            log.info("收到消息: {}", message);

            // 验证token
            Object loginIdString = StpUtil.getLoginIdByToken(token);
            if (loginIdString == null) {
                sendMessage(session, createErrorMessage("未登录或登录已过期"));
                return;
            }
            String[] parts = loginIdString.toString().split(":");
            Long loginId = Long.parseLong(parts[1]);
            if (loginId == null) {
                sendMessage(session, createErrorMessage("未登录或登录已过期"));
                return;
            }

            // 解析消息
            JSONObject jsonMessage = JSONUtil.parseObj(message);
            // 发送的信息类型
            String contentType = jsonMessage.getStr("contentType");
            // 发送动作
            String action = jsonMessage.getStr("action");

            // 根据不同的action处理消息
            switch (action) {
                case "sendMessage":
                    handleSendMessage(jsonMessage, String.valueOf(loginId), type, contentType);
                    break;
                case "markRead":
                    handleMarkRead(jsonMessage, String.valueOf(loginId));
                    break;
                case "platformJoin":
                    handlePlatformJoin(jsonMessage, String.valueOf(loginId), contentType);
                    break;
                case "platformLeave":
                    handlePlatformLeave(jsonMessage, String.valueOf(loginId));
                    break;
                case "heartbeat":
                    // 心跳消息，直接返回成功
                    sendMessage(session, createSuccessMessage("pong"));
                    break;
                default:
                    sendMessage(session, createErrorMessage("未知的操作类型"));
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息异常", e);
            try {
                sendMessage(session, createErrorMessage("消息处理异常: " + e.getMessage()));
            } catch (IOException ex) {
                log.error("发送错误消息异常", ex);
            }
        }
    }

    /**
     * 发生错误时调用的方法
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket发生错误", error);
    }

    /**
     * 处理发送消息
     *
     * @param jsonMessage 发送的信息
     * @param senderId    发送人的 ID
     * @param senderType  发送者的类型
     * @param contentType 发送内容的类型
     */
    private void handleSendMessage(JSONObject jsonMessage, String senderId, String senderType, String contentType) throws IOException {
        // 会话ID
        Long conversationId = jsonMessage.getLong("conversationId");
        //  内容
        String content = jsonMessage.getStr("content");
        // 接收人的 ID
        Long receiverId = jsonMessage.getLong("receiverId");

        // 根据发送者类型确定接收者类型
        String senderTypeCode;
        String receiverType;
        // 判断接收者是商家还是平台
        IChatConversationService conversationService = SpringUtils.getBean(IChatConversationService.class);
        ChatConversation conversation = conversationService.getById(conversationId);

        // 检查必要参数
        if (conversationId == null || receiverId == null || StrUtil.isBlank(content)) {
            log.error("消息参数不完整: conversationId={}, receiverId={}, content={}", conversationId, receiverId, content);
            throw new IllegalArgumentException("参数不完整");
        }

        // 根据会话类型和发送者类型确定接收者类型
        if ("user".equals(senderType)) {
            senderTypeCode = "1"; // 用户
            // 根据会话类型确定接收者类型
            if (conversation != null && "4".equals(conversation.getType())){
                receiverType = "3"; // 商家
            }else{
                receiverType = "2"; // 商家
            }
        } else if ("merchant".equals(senderType)) {
            senderTypeCode = "2"; // 商家
            // 根据会话类型确定接收者类型
            if (conversation != null && "5".equals(conversation.getType())) {
                // 商家对平台会话
                receiverType = "3"; // 平台
            } else {
                // 商家对用户会话
                receiverType = "1"; // 用户
            }
        } else if ("platform".equals(senderType)) {
            senderTypeCode = "3"; // 平台方
            // 根据会话类型确定接收者类型
            if (conversation != null && "5".equals(conversation.getType())) {
                // 平台对商家会话
                receiverType = "2"; // 商家
            } else if (conversation != null && "4".equals(conversation.getType())) {
                // 平台对用户会话
                receiverType = "1"; // 用户
            } else {
                // 默认发送给用户
                receiverType = "1";
            }
        } else {
            log.error("无效的发送者类型: {}", senderType);
            throw new IllegalArgumentException("无效的发送者类型");
        }

        log.info("发送消息: 会话ID={}, 发送者ID={}, 发送者类型={}, 接收者ID={}, 接收者类型={}",
                conversationId, senderId, senderTypeCode, receiverId, receiverType);

        // 获取服务
        IChatMessageService messageService = SpringUtils.getBean(IChatMessageService.class);

        // 发送消息
        ChatMessage chatMessage = messageService.sendMessage(
            conversationId,
            Long.valueOf(senderId),
            senderTypeCode,
            receiverId,
            receiverType,
            content,
            contentType //内容类型（1-文本，2-图片，3-语音，4-视频，5-文件，6-商品）
        );

        // 构建返回消息
        JSONObject responseMessage = new JSONObject();
        responseMessage.set("type", "message");
        responseMessage.set("data", JSONUtil.parseObj(chatMessage));

        // 发送给发送者
        Session senderSession = getSenderSession(senderType, senderId);
        if (senderSession != null && senderSession.isOpen()) {
            sendMessage(senderSession, responseMessage.toString());
        }

        // 发送给接收者
        String receiverIdStr = receiverId.toString();
        Session receiverSession = getReceiverSession(receiverType, receiverIdStr);
        if (receiverSession != null && receiverSession.isOpen()) {
            sendMessage(receiverSession, responseMessage.toString());
        }

        // 特殊处理：平台发送消息时，如果是在商家-平台会话中，需要发送给商家
        if ("platform".equals(senderType) && conversation != null && !"5".equals(conversation.getType())) {
            Session merchantSession = getReceiverSession("2", String.valueOf(conversation.getMerchantId()));
            if(merchantSession != null && merchantSession.isOpen()){
                sendMessage(merchantSession, responseMessage.toString());
            }
        }

        // 特殊处理：如果是三方会话，且平台已介入，则消息需要发送给平台（除非发送者就是平台）
        // 注意：排除平台发送消息的情况，以及商家-平台会话和用户-平台会话，避免重复发送
        if (conversation != null &&
            "1".equals(conversation.getPlatformStatus()) &&
            !"platform".equals(senderType) &&
            !"5".equals(conversation.getType()) &&
            !"4".equals(conversation.getType())) { // 排除商家-平台会话和用户-平台会话，避免重复发送

            String platformIdStr = conversation.getPlatformId().toString();
            Session platformSession = PLATFORM_SESSIONS.get(platformIdStr);
            if (platformSession != null && platformSession.isOpen()) {
                sendMessage(platformSession, responseMessage.toString());
            }
        }
    }

    /**
     * 获取发送者会话
     */
    private Session getSenderSession(String senderType, String senderId) {
        if ("user".equals(senderType)) {
            return USER_SESSIONS.get(senderId);
        } else if ("merchant".equals(senderType)) {
            return MERCHANT_SESSIONS.get(senderId);
        } else if ("platform".equals(senderType)) {
            return PLATFORM_SESSIONS.get(senderId);
        }
        return null;
    }

    /**
     * 获取接收者会话
     */
    private Session getReceiverSession(String receiverType, String receiverId) {
        if ("1".equals(receiverType)) {
            return USER_SESSIONS.get(receiverId);
        } else if ("2".equals(receiverType)) {
            return MERCHANT_SESSIONS.get(receiverId);
        } else if ("3".equals(receiverType)) {
            return PLATFORM_SESSIONS.get(receiverId);
        }
        return null;
    }

    /**
     * 处理平台介入会话
     */
    private void handlePlatformJoin(JSONObject jsonMessage, String platformId, String contentType) throws IOException {
        Long conversationId = jsonMessage.getLong("conversationId");

        if (conversationId == null) {
            throw new IllegalArgumentException("参数不完整");
        }

        // 获取服务
        IChatConversationService conversationService = SpringUtils.getBean(IChatConversationService.class);

        // 平台介入会话
        boolean success = conversationService.platformJoinConversation(conversationId, Long.parseLong(platformId));

        // 构建返回消息
        JSONObject responseMessage = new JSONObject();
        responseMessage.set("type", "platformJoin");
        responseMessage.set("success", success);
        responseMessage.set("conversationId", conversationId);

        // 发送给平台方
        Session platformSession = PLATFORM_SESSIONS.get(platformId);
        if (platformSession != null && platformSession.isOpen()) {
            sendMessage(platformSession, responseMessage.toString());
        }

        // 获取会话信息，通知用户和商家
        if (success) {
            ChatConversation conversation = conversationService.getById(conversationId);
            if (conversation != null) {
                // 通知用户
                String userId = conversation.getUserId().toString();
                Session userSession = USER_SESSIONS.get(userId);
                if (userSession != null && userSession.isOpen()) {
                    sendMessage(userSession, responseMessage.toString());
                }

                // 通知商家
                String merchantId = conversation.getMerchantId().toString();
                Session merchantSession = MERCHANT_SESSIONS.get(merchantId);
                if (merchantSession != null && merchantSession.isOpen()) {
                    sendMessage(merchantSession, responseMessage.toString());
                }

                // 发送系统消息通知
                IChatMessageService messageService = SpringUtils.getBean(IChatMessageService.class);
                messageService.sendMessage(
                    conversationId,
                    Long.parseLong(platformId),
                    "3", // 平台方
                    0L, // 系统消息
                    "0", // 系统
                    "平台客服已介入会话",
                    contentType //内容类型（1-文本，2-图片，3-语音，4-视频，5-文件，6-商品）
                );
            }
        }
    }

    /**
     * 处理平台退出会话
     */
    private void handlePlatformLeave(JSONObject jsonMessage, String platformId) throws IOException {
        Long conversationId = jsonMessage.getLong("conversationId");

        if (conversationId == null) {
            throw new IllegalArgumentException("参数不完整");
        }

        // 获取服务
        IChatConversationService conversationService = SpringUtils.getBean(IChatConversationService.class);

        // 平台退出会话
        boolean success = conversationService.platformLeaveConversation(conversationId);

        // 构建返回消息
        JSONObject responseMessage = new JSONObject();
        responseMessage.set("type", "platformLeave");
        responseMessage.set("success", success);
        responseMessage.set("conversationId", conversationId);

        // 发送给平台方
        Session platformSession = PLATFORM_SESSIONS.get(platformId);
        if (platformSession != null && platformSession.isOpen()) {
            sendMessage(platformSession, responseMessage.toString());
        }

        // 获取会话信息，通知用户和商家
        if (success) {
            ChatConversation conversation = conversationService.getById(conversationId);
            if (conversation != null) {
                // 通知用户
                String userId = conversation.getUserId().toString();
                Session userSession = USER_SESSIONS.get(userId);
                if (userSession != null && userSession.isOpen()) {
                    sendMessage(userSession, responseMessage.toString());
                }

                // 通知商家
                String merchantId = conversation.getMerchantId().toString();
                Session merchantSession = MERCHANT_SESSIONS.get(merchantId);
                if (merchantSession != null && merchantSession.isOpen()) {
                    sendMessage(merchantSession, responseMessage.toString());
                }

                // 发送系统消息通知
                IChatMessageService messageService = SpringUtils.getBean(IChatMessageService.class);
                messageService.sendMessage(
                    conversationId,
                    0L, // 系统
                    "0", // 系统
                    0L, // 系统消息
                    "0", // 系统
                    "平台客服已退出会话",
                    "1" // 文本
                );
            }
        }
    }

    /**
     * 处理标记已读
     */
    private void handleMarkRead(JSONObject jsonMessage, String userId) throws IOException {
        Long conversationId = jsonMessage.getLong("conversationId");

        if (conversationId == null) {
            throw new IllegalArgumentException("参数不完整");
        }

        // 获取服务
        IChatMessageService messageService = SpringUtils.getBean(IChatMessageService.class);

        // 标记已读
        boolean success = messageService.markAllMessagesAsRead(conversationId, Long.parseLong(userId));

        // 构建返回消息
        JSONObject responseMessage = new JSONObject();
        responseMessage.set("type", "markRead");
        responseMessage.set("success", success);
        responseMessage.set("conversationId", conversationId);

        // 发送给用户
        Session userSession = USER_SESSIONS.get(userId);
        if (userSession != null && userSession.isOpen()) {
            sendMessage(userSession, responseMessage.toString());
        }
    }

    /**
     * 发送消息
     */
    private void sendMessage(Session session, String message) throws IOException {
        if (session != null && session.isOpen()) {
            session.getBasicRemote().sendText(message);
        }
    }

    /**
     * 创建错误消息
     */
    private String createErrorMessage(String errorMessage) {
        JSONObject response = new JSONObject();
        response.set("type", "error");
        response.set("message", errorMessage);
        return response.toString();
    }

    /**
     * 创建成功消息
     */
    private String createSuccessMessage(String message) {
        JSONObject response = new JSONObject();
        response.set("type", "success");
        response.set("message", message);
        return response.toString();
    }
}
