package com.ruoyi.chat.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.chat.domain.ChatConversation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聊天会话Mapper接口
 */
@Mapper
public interface ChatConversationMapper extends BaseMapper<ChatConversation> {

    /**
     * 查询用户会话列表
     *
     * @param userId 用户id
     * @return
     */
    List<ChatConversation> selectUserConversationList(Long userId);

    /**
     * 查询用户平台会话列表
     *
     * @param platformId 平台id
     * @return
     */
    List<ChatConversation> selectPlatFormConversationList(Long platformId);

    /**
     * 查询商家会话列表
     *
     * @param merchartId 商家id
     * @return
     */
    List<ChatConversation> selectMerchantConversationList(Long merchartId);

    List<ChatConversation> getUserConversations(@Param("userId") Long userId);

    List<ChatConversation> getMerchantConversations(@Param("merchantId") Long merchantId);

    List<ChatConversation> getPlatformConversations(@Param("platformId") Long platformId);

}
