<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.pay.mapper.PayOrderMapper">

    <resultMap type="com.ruoyi.pay.domain.PayOrder" id="PayOrderResult">
        <id property="id" column="id"/>
        <result property="paymentNo" column="payment_no"/>
        <result property="orderId" column="order_id"/>
        <result property="orderNo" column="order_no"/>
        <result property="userId" column="user_id"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="payType" column="pay_type"/>
        <result property="payStatus" column="pay_status"/>
        <result property="transactionId" column="transaction_id"/>
        <result property="payTime" column="pay_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

</mapper>
