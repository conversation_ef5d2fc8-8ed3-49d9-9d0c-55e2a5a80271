package com.ruoyi.pay.enums;

/**
 * 抵扣金支付记录状态枚举
 */
public enum DeductionPaymentStatusEnum {

    /**
     * 支付成功
     */
    SUCCESS("0", "支付成功"),

    /**
     * 支付失败
     */
    FAILED("1", "支付失败"),

    /**
     * 支付中
     */
    PENDING("2", "支付中");

    private final String code;
    private final String desc;

    DeductionPaymentStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据状态码获取枚举
     */
    public static DeductionPaymentStatusEnum getByCode(String code) {
        for (DeductionPaymentStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 根据状态码获取描述
     */
    public static String getDescByCode(String code) {
        DeductionPaymentStatusEnum status = getByCode(code);
        return status != null ? status.getDesc() : "未知状态";
    }
}
