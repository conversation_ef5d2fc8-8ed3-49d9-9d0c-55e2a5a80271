package com.ruoyi.pay.enums;

import lombok.Getter;

/**
 * 支付状态枚举
 */
@Getter
public enum PayStatusEnum {

    PENDING("0", "待支付"),
    DURING("1", "支付中"),
    SUCCESS("2", "支付成功"),
    FAILED("3", "支付失败"),
    CLOSED("4", "已关闭"),
    REFUND("5", "已退款");

    private final String code;
    private final String info;

    PayStatusEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

}
