package com.ruoyi.pay.strategy;

import com.ruoyi.pay.domain.PayOrder;

import java.util.Map;

/**
 * 支付策略接口
 */
public interface PayStrategy {
    /**
     * 创建支付订单
     *
     * @param payOrder 支付记录
     * @return 支付参数
     */
    Map<String, String> createAppPayment(PayOrder payOrder);

    /**
     * 处理支付回调
     *
     * @param params 回调参数
     * @return 处理结果
     */
    boolean handlePayCallback(Map<String, String> params);

    /**
     * 查询支付状态
     *
     * @param payOrder 支付记录
     * @return 支付状态
     */
    String queryPayStatus(PayOrder payOrder);

    /**
     * 取消支付
     *
     * @param payOrder 支付记录
     * @return 取消结果
     */
    boolean cancelPayment(PayOrder payOrder);

    /**
     * 退款操作
     *
     * @param outTradeNo    外部交易号
     * @param refundAmount  退款金额
     * @param refundReason  退款原因
     * @return 退款结果
     */
    Boolean RefundPayment(String outTradeNo, String refundAmount, String refundReason);
}
