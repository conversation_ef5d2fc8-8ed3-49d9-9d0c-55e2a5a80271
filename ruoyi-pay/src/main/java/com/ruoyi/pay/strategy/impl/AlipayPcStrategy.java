package com.ruoyi.pay.strategy.impl;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.diagnosis.DiagnosisUtils;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.domain.AlipayTradeRefundModel;
import com.alipay.api.request.AlipayTradeCloseRequest;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.response.AlipayTradeCloseResponse;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.ruoyi.pay.config.PayConfig;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.strategy.PayStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

import static com.alipay.api.AlipayConstants.*;

/**
 * 支付宝PC端支付策略实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AlipayPcStrategy implements PayStrategy {

    private final PayConfig payConfig;
    private DefaultAlipayClient alipayClient;

    @PostConstruct
    public void init() throws AlipayApiException {
        AlipayConfig alipayConfig = new AlipayConfig();
        //设置网关地址
        alipayConfig.setServerUrl(payConfig.getAlipay().getGatewayUrl());
        //设置应用APPID
        alipayConfig.setAppId(payConfig.getAlipay().getAppId());
        //设置应用私钥
        alipayConfig.setPrivateKey(payConfig.getAlipay().getPrivateKey());
        //设置应用公钥证书路径
        alipayConfig.setAppCertPath(payConfig.getAlipay().getAppCertPath());
        //设置支付宝公钥证书路径
        alipayConfig.setAlipayPublicCertPath(payConfig.getAlipay().getAlipayPublicCertPath());
        //设置支付宝根证书路径
        alipayConfig.setRootCertPath(payConfig.getAlipay().getRootCertPath());
        //设置请求格式，固定值json
        alipayConfig.setFormat(FORMAT_JSON);
        //设置字符集
        alipayConfig.setCharset(CHARSET_UTF8);
        //设置签名类型
        alipayConfig.setSignType(SIGN_TYPE_RSA2);
        alipayClient = new DefaultAlipayClient(alipayConfig);
    }

    @Override
    public Map<String, String> createAppPayment(PayOrder payOrder) {
        try {
            // 构造client
            AlipayTradePagePayRequest request = getAlipayTradePagePayRequest(payOrder);
            AlipayTradePagePayResponse response = alipayClient.pageExecute(request);
            if (response.isSuccess()) {
                // 正确返回支付页面内容
                Map<String, String> map = new HashMap<>();
                map.put("paymentUrl", response.getBody());
                log.info("支付宝PC端支付表单: {}", response.getBody());
                return map;
            } else {
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                log.error("支付宝PC端支付调用失败，诊断链接：{}", diagnosisUrl);
                throw new RuntimeException("支付调用失败，请联系管理员");
            }
        } catch (AlipayApiException e) {
            log.error("创建支付宝PC端支付订单失败", e);
            throw new RuntimeException("创建支付订单失败");
        }
    }

    @NotNull
    private AlipayTradePagePayRequest getAlipayTradePagePayRequest(PayOrder payOrder) {
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
        // 设置异步通知地址
        request.setNotifyUrl(payConfig.getAlipay().getNotifyUrl() + "/4");
        // 设置同步返回地址，支付成功后跳转页面
        request.setReturnUrl(payConfig.getAlipay().getNotifyUrl() + "/4");

        AlipayTradePagePayModel model = new AlipayTradePagePayModel();
        // 商户订单号，商户网站订单系统中唯一订单号
        model.setOutTradeNo(payOrder.getPaymentNo());
        // 订单总金额，单位为元，精确到小数点后两位
        model.setTotalAmount(payOrder.getPayAmount().toString());
        // 订单标题
        model.setSubject("商城订单-" + payOrder.getOrderNo());
        // 电脑网站支付场景固定为：FAST_INSTANT_TRADE_PAY
        model.setProductCode("FAST_INSTANT_TRADE_PAY");

        // 可选的其他参数
        // 订单描述
        model.setBody("商城订单购买商品");
        // 绝对超时时间，格式为yyyy-MM-dd HH:mm:ss
        // model.setTimeExpire("2022-12-31 23:59:59");

        request.setBizModel(model);
        return request;
    }

    @Override
    public boolean handlePayCallback(Map<String, String> params) {
        try {
            // 验证签名
            boolean signVerified = com.alipay.api.internal.util.AlipaySignature.rsaCheckV1(params, payConfig.getAlipay().getPublicKey(), "UTF-8", "RSA2");

            if (!signVerified) {
                return false;
            }

            String tradeStatus = params.get("trade_status");
            return "TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus);
        } catch (AlipayApiException e) {
            log.error("处理支付宝回调失败", e);
            return false;
        }
    }

    @Override
    public String queryPayStatus(PayOrder payOrder) {
        try {
            AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
            AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
            model.setOutTradeNo(payOrder.getPaymentNo());
            request.setBizModel(model);

            AlipayTradeQueryResponse response = alipayClient.certificateExecute(request);
            if (response.isSuccess()) {
                return response.getTradeStatus();
            } else {
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                log.error("交易查询失败，诊断链接：{}", diagnosisUrl);
                throw new RuntimeException("查询失败：" + response.getSubMsg());
            }
        } catch (AlipayApiException e) {
            log.error("查询支付宝支付状态失败", e);
            throw new RuntimeException("查询处理异常");
        }
    }

    @Override
    public boolean cancelPayment(PayOrder payOrder) {
        try {
            AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();
            AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
            model.setOutTradeNo(payOrder.getPaymentNo());
            request.setBizModel(model);

            AlipayTradeCloseResponse response = alipayClient.certificateExecute(request);
            return response.isSuccess();
        } catch (AlipayApiException e) {
            log.error("取消支付宝支付订单失败", e);
            return false;
        }
    }

    /**
     * 退款
     *
     * @param paymentNo    支付单号
     * @param refundAmount 退款金额
     * @param refundReason 退款理由
     */
    @Override
    public Boolean RefundPayment(String paymentNo, String refundAmount, String refundReason) {
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        AlipayTradeRefundModel model = new AlipayTradeRefundModel();
        model.setOutTradeNo(paymentNo);
        model.setRefundAmount(refundAmount);
        model.setRefundReason(refundReason);
        request.setBizModel(model);
        try {
            AlipayTradeRefundResponse response = alipayClient.certificateExecute(request);
            if (response.isSuccess()) {
                return true;
            } else {
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                log.error("支付宝退款失败，诊断链接：{}", diagnosisUrl);
                return false;
            }
        } catch (AlipayApiException e) {
            log.error("支付宝退款异常", e);
            return false;
        }
    }
}
