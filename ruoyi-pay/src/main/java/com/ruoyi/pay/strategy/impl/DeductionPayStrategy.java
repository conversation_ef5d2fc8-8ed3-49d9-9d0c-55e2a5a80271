package com.ruoyi.pay.strategy.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.enums.PayStatusEnum;
import com.ruoyi.pay.strategy.PayStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 抵扣金组合支付策略实现
 * 注意：此策略不再处理抵扣金扣减，只处理支付宝支付部分
 * 抵扣金扣减已在OrderServiceImpl中完成
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DeductionPayStrategy implements PayStrategy {

    private final AlipayStrategy alipayStrategy;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> createAppPayment(PayOrder payOrder) {
        // 注意：抵扣金扣减已在OrderServiceImpl中完成
        // 这里只处理支付宝支付部分（手续费）

        log.info("处理组合支付的支付宝部分 - 订单号: {}, 手续费: {}元",
                payOrder.getPaymentNo(), payOrder.getPayAmount());

        // 直接调用支付宝策略处理手续费支付
        Map<String, String> alipayResult = alipayStrategy.createAppPayment(payOrder);

        // 添加组合支付标识信息
        alipayResult.put("payType", "deduction_alipay");
        if (payOrder.getOriginalAmount() != null) {
            alipayResult.put("deductionAmount", payOrder.getOriginalAmount().toString());
        }
        alipayResult.put("serviceFee", payOrder.getFeeAmount().toString());
        alipayResult.put("deductionUsed", "true");

        log.info("组合支付支付宝部分创建成功 - 订单号: {}, 手续费: {}元",
                payOrder.getPaymentNo(), payOrder.getPayAmount());

        return alipayResult;
    }

    @Override
    public boolean handlePayCallback(Map<String, String> params) {
        // 组合支付的回调处理：主要处理支付宝支付部分的回调
        try {
            // 委托给支付宝策略处理回调
            boolean alipayResult = alipayStrategy.handlePayCallback(params);

            log.info("组合支付回调处理完成，支付宝回调结果：{}", alipayResult);
            return alipayResult;

        } catch (Exception e) {
            log.error("组合支付回调处理失败：{}", e.getMessage());
            return false;
        }
    }

    @Override
    public String queryPayStatus(PayOrder payOrder) {
        // 组合支付状态查询：查询支付宝支付部分的状态
        try {
            return alipayStrategy.queryPayStatus(payOrder);
        } catch (Exception e) {
            log.error("查询组合支付状态失败：{}", e.getMessage());
            return PayStatusEnum.FAILED.getCode();
        }
    }

    @Override
    public boolean cancelPayment(PayOrder payOrder) {
        // 组合支付取消：只处理支付宝支付部分
        // 抵扣金退回由OrderServiceImpl处理
        try {
            boolean alipayCancel = alipayStrategy.cancelPayment(payOrder);
            log.info("取消组合支付的支付宝部分 - 订单号: {}, 结果: {}",
                    payOrder.getPaymentNo(), alipayCancel);
            return alipayCancel;
        } catch (Exception e) {
            log.error("取消组合支付的支付宝部分失败：{}", e.getMessage());
            return false;
        }
    }

    @Override
    public Boolean RefundPayment(String outTradeNo, String refundAmount, String refundReason) {
        // 组合支付退款：只处理支付宝部分退款
        // 抵扣金退款由业务层处理
        if (StringUtils.isEmpty(outTradeNo) || StringUtils.isEmpty(refundAmount)) {
            return false;
        }

        try {
            // 直接处理支付宝退款（这里的refundAmount应该是手续费金额）
            Boolean alipayRefund = alipayStrategy.RefundPayment(outTradeNo, refundAmount, refundReason);

            log.info("组合支付支付宝部分退款处理 - 订单号: {}, 退款金额: {}元, 结果: {}",
                    outTradeNo, refundAmount, alipayRefund);

            return alipayRefund;

        } catch (Exception e) {
            log.error("组合支付支付宝部分退款失败", e);
            return false;
        }
    }
}
