package com.ruoyi.pay.strategy.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.enums.PayStatusEnum;
import com.ruoyi.pay.mapper.PayOrderMapper;
import com.ruoyi.pay.service.IDeductionPaymentRecordService;
import com.ruoyi.pay.strategy.PayStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 商家平台促销金支付策略实现
 * 该策略负责记录支付数据并返回支付结果
 * 适用于技术引流、权限开通、功能付费等虚拟商品
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ShopDeductionPayStrategy implements PayStrategy {

    // 无手续费
    private static final BigDecimal FEE_AMOUNT = BigDecimal.ZERO;
    private final PayOrderMapper payOrderMapper;
    private final IDeductionPaymentRecordService deductionPaymentRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> createAppPayment(PayOrder payOrder) {
        log.info("商家平台促销金支付处理 - 订单号: {}, 金额: {}", payOrder.getPaymentNo(), payOrder.getPayAmount());

        // 更新支付订单状态为支付成功
        payOrder.setPayStatus(PayStatusEnum.SUCCESS.getCode());
        payOrder.setPayTime(new Date());
        payOrder.setTransactionId("SHOP_DEDUCTION_" + System.currentTimeMillis()); // 生成一个唯一的交易号

        // 更新支付订单
        payOrderMapper.updateById(payOrder);

        // 返回支付成功信息
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("payStatus", PayStatusEnum.SUCCESS.getCode()); // 支付成功
        resultMap.put("payTime", String.valueOf(System.currentTimeMillis()));
        resultMap.put("paymentNo", payOrder.getPaymentNo());
        resultMap.put("transactionId", payOrder.getTransactionId());
        resultMap.put("feeAmount", FEE_AMOUNT.toString()); // 添加手续费信息

        // 记录商家平台兑换金使用记录
        try {
            deductionPaymentRecordService.recordDeductionPayment(
                payOrder.getUserId(),
                payOrder.getPaymentNo(),
                payOrder.getOrderNo(),
                payOrder.getPayAmount(),
                FEE_AMOUNT,
                payOrder.getPayTime(),
                "0", // 成功
                "2"  // 商家平台兑换金支付
            );
            log.info("记录商家平台兑换金支付信息成功 - 订单号: {}, 金额: {}", payOrder.getPaymentNo(), payOrder.getPayAmount());
        } catch (Exception e) {
            log.error("记录商家平台兑换金支付信息失败", e);
        }

        return resultMap;
    }

    @Override
    public boolean handlePayCallback(Map<String, String> params) {
        // 商家平台促销金支付是实时完成的，不需要异步回调处理
        return true;
    }

    @Override
    public String queryPayStatus(PayOrder payOrder) {
        // 商家平台促销金支付是实时完成的，状态直接返回成功
        return PayStatusEnum.SUCCESS.getCode();
    }

    @Override
    public boolean cancelPayment(PayOrder payOrder) {
        // 返回取消成功，由调用方负责处理退回平台促销金
        log.info("取消商家平台促销金支付 - 订单号: {}, 金额: {}", payOrder.getPaymentNo(), payOrder.getPayAmount());
        return true;
    }

    @Override
    public Boolean RefundPayment(String outTradeNo, String refundAmount, String refundReason) {
        // 退款处理，由调用方负责处理退回平台促销金
        if (StringUtils.isEmpty(outTradeNo) || StringUtils.isEmpty(refundAmount)) {
            return false;
        }

        log.info("商家平台促销金退款处理: 订单号={}, 退款金额={}, 退款原因={}", outTradeNo, refundAmount, refundReason);
        return true;
    }
}
