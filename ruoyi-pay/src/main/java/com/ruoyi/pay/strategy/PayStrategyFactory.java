package com.ruoyi.pay.strategy;

import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.strategy.impl.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 支付策略工厂
 * <p>
 * 支持的支付类型：
 * 1 - 微信支付
 * 2 - 支付宝支付
 * 3 - 抵扣金支付
 * 4 - 支付宝PC端支付
 * 5 - 商家平台促销金支付（用于技术引流、权限开通、广告和功能付费等虚拟商品）
 */
@Component
@RequiredArgsConstructor
public class PayStrategyFactory {

    private final WechatPayStrategy wechatPayStrategy;
    private final AlipayPcStrategy alipayPcStrategy;
    private final AlipayStrategy alipayStrategy;
    private final ShopDeductionPayStrategy shopDeductionPayStrategy;
    private final DeductionPayStrategy deductionPayStrategy;

    /**
     * 获取支付策略
     *
     * @param payType PayOrder的
     * @return 支付策略
     */
    public PayStrategy getPaymentStrategy(String payType) {
        switch (payType) {
            case PayOrder.PAY_TYPE_ALIPAY:
                return alipayStrategy;
            case PayOrder.PAY_TYPE_DEDUCTION:
                return deductionPayStrategy;
            case PayOrder.PAY_TYPE_SHOP_DEDUCTION:
                return shopDeductionPayStrategy;
            default:
                throw new RuntimeException("不支持的支付方式");
        }
//        switch (payType) {
//            case PayOrder.PAY_TYPE_WECHAT:
//                return wechatPayStrategy;
//            case PayOrder.PAY_TYPE_ALIPAY:
//                return alipayStrategy;
//            case PayOrder.PAY_TYPE_ALIPAY_PC:
//                return alipayPcStrategy;
//            case PayOrder.PAY_TYPE_SHOP_DEDUCTION:
//                return shopDeductionPayStrategy;
//            default:
//                throw new RuntimeException("不支持的支付方式");
//        }
    }
}
