package com.ruoyi.pay.strategy.impl;

import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayOrderCloseResult;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.ruoyi.pay.config.PayConfig;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.strategy.PayStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付策略实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WechatPayStrategy implements PayStrategy {

    private final PayConfig payConfig;
    private WxPayService wxPayService;

    @PostConstruct
    public void init() {
        WxPayConfig config = new WxPayConfig();
        config.setAppId(payConfig.getWechatPay().getAppId());
        config.setMchId(payConfig.getWechatPay().getMchId());
        config.setMchKey(payConfig.getWechatPay().getApiKey());
        config.setNotifyUrl(payConfig.getWechatPay().getNotifyUrl() + "/1");
        config.setKeyPath(payConfig.getWechatPay().getCertPath());

        wxPayService = new WxPayServiceImpl();
        wxPayService.setConfig(config);
    }

    @Override
    public Map<String, String> createAppPayment(PayOrder payOrder) {
        try {
            WxPayUnifiedOrderRequest orderRequest = WxPayUnifiedOrderRequest.newBuilder()
                .outTradeNo(payOrder.getPaymentNo())
                .totalFee(payOrder.getPayAmount().multiply(new java.math.BigDecimal("100")).intValue()) // 金额转为分
                .body("商城订单-" + payOrder.getOrderNo())
                .tradeType("JSAPI") // 公众号支付
                .openid(payOrder.getUserId().toString()) // 这里需要转换为微信openid
                .build();

            // TODO 具体实现微信支付
            WxPayMpOrderResult result = wxPayService.createOrder(orderRequest);
            Map<String, String> payParams = new HashMap<>();
            payParams.put("appId", result.getAppId());
            payParams.put("timeStamp", result.getTimeStamp());
            payParams.put("nonceStr", result.getNonceStr());
            payParams.put("package", result.getPackageValue());
            payParams.put("signType", result.getSignType());
            payParams.put("paySign", result.getPaySign());
            return payParams;
        } catch (Exception e) {
            log.error("创建微信支付订单失败", e);
            throw new RuntimeException("创建支付订单失败");
        }
    }

    @Override
    public boolean handlePayCallback(Map<String, String> params) {
        try {
            // 直接解析XML数据
            WxPayOrderNotifyResult result = wxPayService.parseOrderNotifyResult(params.get("xmlData"));
            return "SUCCESS".equals(result.getResultCode());
        } catch (Exception e) {
            log.error("处理微信支付回调失败", e);
            return false;
        }
    }

    @Override
    public String queryPayStatus(PayOrder payOrder) {
        try {
            return wxPayService.queryOrder(payOrder.getPaymentNo(), null).getTradeState();
        } catch (Exception e) {
            log.error("查询微信支付状态失败", e);
            return null;
        }
    }

    @Override
    public boolean cancelPayment(PayOrder payOrder) {
        try {
            WxPayOrderCloseResult result = wxPayService.closeOrder(payOrder.getPaymentNo());
            return result != null && "SUCCESS".equals(result.getResultCode());
        } catch (Exception e) {
            log.error("取消微信支付订单失败", e);
            return false;
        }
    }

    @Override
    public Boolean RefundPayment(String outTradeNo, String refundAmount, String refundReason) {
        return null;
    }
}
