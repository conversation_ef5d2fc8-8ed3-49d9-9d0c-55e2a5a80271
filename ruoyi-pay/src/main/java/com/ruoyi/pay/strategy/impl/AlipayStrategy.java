package com.ruoyi.pay.strategy.impl;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.diagnosis.DiagnosisUtils;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.domain.AlipayTradeRefundModel;
import com.alipay.api.domain.AlipayTradeWapPayModel;
import com.alipay.api.request.AlipayTradeCloseRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.response.AlipayTradeCloseResponse;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.alipay.api.response.AlipayTradeWapPayResponse;
import com.ruoyi.pay.config.PayConfig;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.enums.PayStatusEnum;
import com.ruoyi.pay.strategy.PayStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

import static com.alipay.api.AlipayConstants.*;

/**
 * 支付宝支付策略实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AlipayStrategy implements PayStrategy {

    private final PayConfig payConfig;
    private DefaultAlipayClient alipayClient;

    @PostConstruct
    public void init() throws AlipayApiException {
        AlipayConfig alipayConfig = new AlipayConfig();
        //设置网关地址
        alipayConfig.setServerUrl(payConfig.getAlipay().getGatewayUrl());
        //设置应用APPID
        alipayConfig.setAppId(payConfig.getAlipay().getAppId());
        //设置应用私钥
        alipayConfig.setPrivateKey(payConfig.getAlipay().getPrivateKey());
        // 设置应用公钥
//        alipayConfig.setAlipayPublicKey(payConfig.getAlipay().getPublicKey());
        //设置应用公钥证书路径
        alipayConfig.setAppCertPath(payConfig.getAlipay().getAppCertPath());
        //设置支付宝公钥证书路径
        alipayConfig.setAlipayPublicCertPath(payConfig.getAlipay().getAlipayPublicCertPath());
        //设置支付宝根证书路径
        alipayConfig.setRootCertPath(payConfig.getAlipay().getRootCertPath());
        //设置请求格式，固定值json
        alipayConfig.setFormat(FORMAT_JSON);
        //设置字符集
        alipayConfig.setCharset(CHARSET_UTF8);
        //设置签名类型
        alipayConfig.setSignType(SIGN_TYPE_RSA2);
        alipayClient = new DefaultAlipayClient(alipayConfig);
    }

    @Override
    public Map<String, String> createAppPayment(PayOrder payOrder) {
        try {
            // 构造client
            AlipayTradeWapPayRequest request = getAlipayTradeWapPayRequest(payOrder);
            AlipayTradeWapPayResponse response = alipayClient.pageExecute(request); // 修改执行方式
            if (response.isSuccess()) {
                // 正确返回支付页面内容
                Map<String, String> map = new HashMap<>();
                map.put("paymentUrl", response.getBody());
                log.info(response.getBody());
                return map;
            } else {
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                log.error("支付宝支付调用失败，诊断链接：{}", diagnosisUrl);
                throw new RuntimeException("支付调用失败，请联系管理员");
            }
        } catch (AlipayApiException e) {
            log.error("创建支付宝支付订单失败", e);
            throw new RuntimeException("创建支付订单失败");
        }
    }

    /**
     * 创建支付宝H5支付订单
     *
     * @return 直接是from表单
     * <p>
     * <form name=\"punchout_form\" method=\"post\"
     * action=\"https://openapi.alipay.com/gateway.do?app_cert_sn=7d8b03968b3c81ec4fa1c0 1c2551c069
     * &charset=UTF-8
     * &alipay_root_cert_sn=687b59193f3f462dd5336e5abf83c5d8_02941eef3187dddf3d3b83462e1dfcf6
     * &method=alipay.trade.wap.pay&sign=O80osd4Efk7nv9z5jhZp4Qbu6i1blOiKrKHTLdQnE1BpHW2kAhPCrqemSptzCm7p5YMy
     * uXbvR5mU%2BHfMywDI92Qx%2B9HTMhcwfuVEqZzSCj4sQibQaMAXnXJ8AJ7pyGf7mGW1pT2XM7YNWj6uNkYl%2BHUFt8HOO3iK
     * CP9sc7l6Z1JOudwFPh0P99Ewt4dpFIRUm6ffly5gwqDBVi995xkKeMs2OhLfHCPvYTIZ6VFTyEplhXdYRUdckG9ouusrTrO9ks
     * EBLxSfZHTK8Y63Ss8yi0aaeg62MPmZXZkcfjxWz4lqpMdQNpkox8xRcnr1lIIKqnQnO3XQmgEu8gzF85tzAg%3D%3D
     * &return_url=http%3A%2F%2Fwww.zhongnanhui.net%2Fapi%2Fapp%2Fpay%2Fnotify%2F2
     * &notify_url=http%3A%2F%2Fwww.zhongnanhui.net%2Fapi%2Fapp%2Fpay%2Fnotify%2F2
     * &version=1.0
     * &app_id=2021002163693911
     * &sign_type=RSA2
     * &timestamp=2025-06-12+17%3A11%3A35
     * &alipay_sdk=alipay-sdk-java-4.40.66.ALL
     * &format=json\"
     * >\n
     * <input type=\"hidden\" name=\"biz_content\"
     * value=\"{&quot;out_trade_no&quot;:&quot;PAY202506121612336188ff&quot;,
     * &quot;product_code&quot;:&quot;QUICK_WAP_WAY&quot;,&quot;subject&quot;:
     * &quot;商城订单-V202506111802553383&quot;,&quot;total_amount&quot;
     * :&quot;99.00&quot;}\"
     * >\n
     * <input type=\"submit\" value=\"立即支付\" style=\"display:none\" >\n</form>\n<script>document.forms[0].submit();</script>
     */
    @NotNull
    private AlipayTradeWapPayRequest getAlipayTradeWapPayRequest(PayOrder payOrder) {
        AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest(); // 修改请求类型
//        request.setReturnUrl(payConfig.getAlipay().getNotifyUrl() + "/2");
        request.setNotifyUrl(payConfig.getAlipay().getNotifyUrl() + "/2");

        AlipayTradeWapPayModel model = new AlipayTradeWapPayModel(); // 修改模型类型
        model.setOutTradeNo(payOrder.getPaymentNo());
        model.setTotalAmount(payOrder.getPayAmount().toString());
//        model.setTotalAmount("0.01");
        model.setSubject("商城订单-" + payOrder.getOrderNo());
        model.setProductCode("QUICK_WAP_WAY"); // H5支付专用产品码
        request.setBizModel(model);
        return request;
    }

//    /**
//     * 按照官方给的api，进行支付
//     * 官方文档：https://opendocs.alipay.com/open/cd12c885_alipay.trade.app.pay?scene=20&pathHash=ab686e33
//     * @return 一些参数
//     */
//    @NotNull
//    private AlipayTradeAppPayRequest getAlipayTradeWapPayRequest1(PayOrder payOrder) {
//        AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest(); // 修改请求类型
//        request.setReturnUrl(payConfig.getAlipay().getNotifyUrl() + "/2");
//        request.setNotifyUrl(payConfig.getAlipay().getNotifyUrl() + "/2");
//        AlipayTradeWapPayModel model = new AlipayTradeWapPayModel(); // 修改模型类型
//        model.setOutTradeNo(payOrder.getPaymentNo());
//        model.setTotalAmount(payOrder.getPayAmount().toString());
//        model.setSubject("商城订单-" + payOrder.getOrderNo());
//        model.setProductCode("QUICK_WAP_WAY"); // H5支付专用产品码
//        request.setBizModel(model);
//        return request;
//    }

    @Override
    public boolean handlePayCallback(Map<String, String> params) {
        try {
            // 验证签名
            boolean signVerified = com.alipay.api.internal.util.AlipaySignature.rsaCertCheckV2(params, payConfig.getAlipay().getPublicKey(), "UTF-8", "RSA2");

            if (!signVerified) {
                return false;
            }

            String tradeStatus = params.get("trade_status");
            return "TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus);
        } catch (AlipayApiException e) {
            log.error("处理支付宝回调失败", e);
            return false;
        }
    }

    @Override
    public String queryPayStatus(PayOrder payOrder) {
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        // 1. 使用正确的Model
        AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
        model.setOutTradeNo(payOrder.getPaymentNo());
        request.setBizModel(model);

        try {
            AlipayTradeQueryResponse response = alipayClient.certificateExecute(request);

            // 2. 检查响应是否为空
            if (response == null) {
                log.error("支付宝查询响应为空。");
                return PayStatusEnum.DURING.getCode();
            }

            // 4. 全面处理不同的交易状态
            JSONObject jsonObject = JSONObject.parseObject(response.getBody());
            JSONObject alipayResponse = jsonObject.getJSONObject("alipay_trade_query_response");
            // 4. ✅ 检查接口返回码（code字段）
            String code = alipayResponse.getString("code");
            String subCode = alipayResponse.getString("sub_code");

            // ✅ 处理40004等错误码
            if ("10000".equals(code)) {
                // 接口调用成功，解析交易状态
                String tradeStatus = alipayResponse.getString("trade_status");
                return parseTradeStatus(tradeStatus);
            } else if ("40004".equals(code)) {
                // 业务失败，根据sub_code细化处理
                return handle40004Error(subCode, payOrder);
            } else {
                // 其他错误码
                log.error("支付宝查询失败: {}", alipayResponse.getString("msg"));
                return PayStatusEnum.FAILED.getCode();
            }
        }catch (AlipayApiException e) {
            log.error("调用支付宝交易查询API失败", e);
            return PayStatusEnum.DURING.getCode();
        } catch (Exception e) {
            log.error("解析支付宝响应异常", e);
            return PayStatusEnum.DURING.getCode();
        }
    }

    @Override
    public boolean cancelPayment(PayOrder payOrder) {
        try {
            AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();
            AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
            model.setOutTradeNo(payOrder.getPaymentNo());
            request.setBizModel(model);

            AlipayTradeCloseResponse response = alipayClient.certificateExecute(request);
            return response.isSuccess();
        } catch (AlipayApiException e) {
            log.error("取消支付宝支付订单失败", e);
            return false;
        }
    }

    /**
     * 退款
     *
     * @param paymentNo    支付单号
     * @param refundAmount 退款金额
     * @param refundReason 退款理由
     */
    @Override
    public Boolean RefundPayment(String paymentNo, String refundAmount, String refundReason) {
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        AlipayTradeRefundModel model = new AlipayTradeRefundModel();
        model.setOutTradeNo(paymentNo);
        model.setRefundAmount(refundAmount);
        model.setRefundReason(refundReason);
        request.setBizModel(model);
        try {
            AlipayTradeRefundResponse response = alipayClient.certificateExecute(request);
            if (response.isSuccess()) {
                return true;
            } else {
                String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
                log.error("支付宝退款失败，诊断链接：{}", diagnosisUrl);
                return false;
            }
        } catch (AlipayApiException e) {
            log.error("支付宝退款异常", e);
            return false;
        }
    }
    // 解析交易状态
    private String parseTradeStatus(String tradeStatus) {
        switch (tradeStatus) {
            case "TRADE_SUCCESS":
            case "TRADE_FINISHED":
                return PayStatusEnum.SUCCESS.getCode();
            case "WAIT_BUYER_PAY":
                return PayStatusEnum.DURING.getCode();
            case "TRADE_CLOSED":
            default:
                return PayStatusEnum.FAILED.getCode();
        }
    }

    /**
     * 处理40004错误
     * @param subCode 错误码
     * @param payOrder 支付订单
     * @return
     */
    private String handle40004Error(String subCode, PayOrder payOrder) {
        if ("ACQ.TRADE_NOT_EXIST".equals(subCode)) {
            return handleTradeNotExist(payOrder);
        } else if ("ACQ.SYSTEM_ERROR".equals(subCode)) {
            log.warn("支付宝系统繁忙，订单号: {}", payOrder.getPaymentNo());
            return PayStatusEnum.DURING.getCode(); // 系统错误，可重试
        } else if ("ACQ.INVALID_PARAMETER".equals(subCode)) {
            log.error("支付宝查询参数无效，订单号: {}", payOrder.getPaymentNo());
            return PayStatusEnum.FAILED.getCode(); // 参数错误，不可重试
        } else if ("ACQ.ACCESS_FORBIDDEN".equals(subCode)) {
            log.error("支付宝访问权限不足，订单号: {}", payOrder.getPaymentNo());
            return PayStatusEnum.FAILED.getCode(); // 权限错误，不可重试
        } else {
            log.error("支付宝业务失败，未知子错误码: {}，订单号: {}", subCode, payOrder.getPaymentNo());
            return PayStatusEnum.FAILED.getCode();
        }
    }

    /**
     * 处理交易不存在的情况
     * 可能原因：
     * 1. 用户还在输入密码中，支付宝订单还未创建
     * 2. 用户划掉了应用，支付流程中断
     * 3. 网络延迟，支付宝订单创建延迟
     *
     * @param payOrder 支付订单
     * @return 支付状态
     */
    private String handleTradeNotExist(PayOrder payOrder) {
        // 获取当前重试次数，如果为null则初始化为0
        Integer currentRetryCount = payOrder.getRetryCount();
        if (currentRetryCount == null) {
            currentRetryCount = 0;
        }

        // 增加重试次数
        currentRetryCount++;
        payOrder.setRetryCount(currentRetryCount);

        // 最大重试次数
        final int MAX_RETRY_COUNT = 3;

        if (currentRetryCount <= MAX_RETRY_COUNT) {
            log.warn("支付宝订单不存在，可能用户还在支付中，订单号: {}，重试次数: {}/{}",
                    payOrder.getPaymentNo(), currentRetryCount, MAX_RETRY_COUNT);
            return PayStatusEnum.DURING.getCode(); // 继续等待，保持支付中状态
        } else {
            log.error("支付宝订单不存在，重试次数已达上限，判定为支付失败，订单号: {}，重试次数: {}",
                    payOrder.getPaymentNo(), currentRetryCount);
            return PayStatusEnum.FAILED.getCode(); // 超过重试次数，判定为支付失败
        }
    }
}
