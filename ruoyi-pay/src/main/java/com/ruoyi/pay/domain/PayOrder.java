package com.ruoyi.pay.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付订单对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pay_order")
public class PayOrder extends BaseEntity {
    /**
     * 支付方式：微信支付
     */
    public static final String PAY_TYPE_WECHAT = "1";
    /**
     * 支付方式：支付宝支付
     */
    public static final String PAY_TYPE_ALIPAY = "2";
    /**
     * 支付方式：抵扣金 + 支付宝支付
     */
    public static final String PAY_TYPE_DEDUCTION = "3";
    /**
     * 支付方式：支付宝PC端支付
     */
    public static final String PAY_TYPE_ALIPAY_PC = "4";
    /**
     * 支付方式：商家抵扣金支付
     */
    public static final String PAY_TYPE_SHOP_DEDUCTION = "5";

    private static final long serialVersionUID = 1L;
    /**
     * 支付ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 业务订单ID
     */
    private Long orderId;

    /**
     * 业务订单编号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 原价
     */
    private BigDecimal originalAmount;

    /**
     * 手续费金额
     */
    private BigDecimal feeAmount;



    /**
     * 支付方式：1-微信支付，2-支付宝支付，3-抵扣金支付，4-支付宝PC端支付，5-商家抵扣金支付
     */
    private String payType;

    /**
     * 支付状态：0-待支付，1-支付中，2-支付成功，3-支付失败 4-已关闭 5-已退款
     */
    private String payStatus;

    /**
     * 第三方支付交易号
     */
    private String transactionId;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String remark;

    /**
     * 查询重试次数（用于处理支付宝订单不存在的情况）
     */
    private Integer retryCount;
}
