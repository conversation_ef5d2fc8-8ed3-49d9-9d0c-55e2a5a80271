package com.ruoyi.pay.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.pay.domain.PayOrder;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 支付服务接口
 */
public interface
IPayService extends IService<PayOrder> {
    /**
     * 创建支付订单
     *
     * @param orderId 业务订单ID
     * @param orderNo 业务订单编号
     * @param userId  用户ID
     * @param amount  支付金额
     * @param payType 支付方式：1-微信支付，2-支付宝支付
     * @return 支付参数
     */
    Map<String, String> createPayment(Long orderId, String orderNo, Long userId, BigDecimal amount, String payType);

    /**
     * 创建支付订单（带手续费）
     *
     * @param orderId   订单ID
     * @param orderNo   订单号
     * @param userId    用户ID
     * @param amount    支付金额
     * @param feeAmount 手续费
     * @param payType   支付类型
     * @return 支付参数
     */
    Map<String, String> createPayment(Long orderId, String orderNo, Long userId, BigDecimal amount,BigDecimal feeAmount, String payType);

    /**
     * 创建抵扣金组合支付订单
     * @param orderId        订单ID
     * @param orderNo        订单号
     * @param userId         用户ID
     * @param deductionAmount 抵扣金金额（已扣减）
     * @param serviceFee     手续费金额（需支付宝支付）
     * @return
     */
    Map<String, String> createDeductionPayment(Long orderId, String orderNo, Long userId,
                                              BigDecimal deductionAmount, BigDecimal serviceFee);

    /**
     * 处理支付回调
     *
     * @param payType 支付方式
     * @param params  回调参数
     * @return 处理结果
     */
    boolean handlePayCallback(String payType, Map<String, String> params);

    /**
     * 查询支付状态
     *
     * @return 支付状态：PayStatusEnum
     */
    String queryPayStatus(PayOrder payOrder);

    /**
     * 取消支付
     *
     * @param paymentNo 支付单号
     * @return 取消结果
     */
    boolean cancelPayment(String paymentNo);

    /**
     * 根据支付单号获取支付订单
     *
     * @param paymentNo 支付单号
     * @return 支付订单
     */
    PayOrder getByPaymentNo(String paymentNo);

    /**
     * 根据业务订单号获取支付订单
     *
     * @param orderNo 业务订单号
     * @return 支付订单
     */
    PayOrder getByOrderNo(String orderNo);

}
