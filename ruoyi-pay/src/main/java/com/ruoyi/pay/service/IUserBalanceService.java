package com.ruoyi.pay.service;

import java.math.BigDecimal;

/**
 * 用户余额服务接口
 */
public interface IUserBalanceService {

    /**
     * 获取用户抵扣金余额
     *
     * @param userId 用户ID
     * @return 抵扣金余额
     */
    BigDecimal getUserDeductionBalance(Long userId);

    /**
     * 扣减用户抵扣金
     *
     * @param userId 用户ID
     * @param amount 扣减金额
     * @return 是否成功
     */
    boolean deductUserBalance(Long userId, BigDecimal amount);

    /**
     * 扣减用户抵扣金（带手续费）
     *
     * @param userId    用户ID
     * @param amount    支付金额
     * @param feeAmount 手续费金额
     * @return 是否成功
     */
    boolean deductUserBalanceWithFee(Long userId, BigDecimal amount, BigDecimal feeAmount);

    /**
     * 增加用户抵扣金
     *
     * @param userId 用户ID
     * @param amount 增加金额
     * @return 是否成功
     */
    boolean increaseUserBalance(Long userId, BigDecimal amount);

    /**
     * 检查用户是否存在
     *
     * @param userId 用户ID
     * @return 是否存在
     */
    boolean checkUserExists(Long userId);
}
