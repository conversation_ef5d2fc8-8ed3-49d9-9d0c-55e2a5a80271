package com.ruoyi.pay.service;

import com.ruoyi.common.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 抵扣金支付记录服务接口
 */
public interface IDeductionPaymentRecordService {

    /**
     * 记录抵扣金支付
     *
     * @param userId    用户ID
     * @param paymentNo 支付单号
     * @param orderNo   业务订单编号
     * @param payAmount 支付金额
     * @param feeAmount 手续费
     * @param payTime   支付时间
     * @param status    支付状态
     * @param payType   支付类型：1-用户抵扣金支付，2-商家平台兑换金支付
     * @return 是否记录成功
     */
    boolean recordDeductionPayment(Long userId, String paymentNo, String orderNo,
                                   BigDecimal payAmount, BigDecimal feeAmount,
                                   Date payTime, String status, String payType);

    /**
     * 更新抵扣金支付记录状态（只更新支付中状态的记录）
     *
     * @param paymentNo 支付单号
     * @param status    新的支付状态：0-成功，1-失败
     * @return 是否更新成功
     */
    boolean updateDeductionPaymentStatus(String paymentNo, String status);

    /**
     * 查询抵扣金支付记录的当前状态
     *
     * @param paymentNo 支付单号
     * @return 支付记录状态，如果有多条记录返回最新的状态
     */
    String getDeductionPaymentStatus(String paymentNo);


}
