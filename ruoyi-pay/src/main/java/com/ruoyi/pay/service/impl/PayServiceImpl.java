package com.ruoyi.pay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.OrderNoGenerator;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.enums.DeductionPaymentStatusEnum;
import com.ruoyi.pay.enums.PayStatusEnum;
import com.ruoyi.pay.mapper.PayOrderMapper;
import com.ruoyi.pay.service.IDeductionPaymentRecordService;
import com.ruoyi.pay.service.IPayService;
import com.ruoyi.pay.strategy.PayStrategy;
import com.ruoyi.pay.strategy.PayStrategyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 支付服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PayServiceImpl extends ServiceImpl<PayOrderMapper, PayOrder> implements IPayService {

    private final PayStrategyFactory payStrategyFactory;
    private final IDeductionPaymentRecordService deductionPaymentRecordService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> createPayment(Long orderId, String orderNo, Long userId, BigDecimal amount, String payType) {
        return createPayment(orderId, orderNo, userId, amount, BigDecimal.ZERO , payType);
    }

    /**
     * 创建支付订单
     * @param orderId   订单ID
     * @param orderNo   订单号
     * @param userId    用户ID
     * @param amount    支付金额
     * @param feeAmount 手续费
     * @param payType   支付类型
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> createPayment(Long orderId, String orderNo, Long userId, BigDecimal amount,BigDecimal feeAmount, String payType ) {
        // 1. 检查是否已存在支付记录
        PayOrder existPayOrder = getByOrderNo(orderNo);
        if (existPayOrder != null) {
            // 如果已支付失败，允许重新支付
            if (PayStatusEnum.FAILED.getCode().equals(existPayOrder.getPayStatus())) {
                existPayOrder.setPayStatus(PayStatusEnum.DURING.getCode());
                existPayOrder.setRetryCount(0);
                updateById(existPayOrder);
            }
            // 如果是待支付或支付中状态，返回原支付参数
            if (PayStatusEnum.PENDING.getCode().equals(existPayOrder.getPayStatus()) ||
                PayStatusEnum.DURING.getCode().equals(existPayOrder.getPayStatus())) {
                PayStrategy payStrategy = payStrategyFactory.getPaymentStrategy(payType);
                return payStrategy.createAppPayment(existPayOrder);
            }
        }

        // 2. 创建支付记录
        PayOrder payOrder = new PayOrder();
        payOrder.setOrderId(orderId);
        payOrder.setOrderNo(orderNo);
        payOrder.setUserId(userId);
        // 支付金额 = 订单金额 + 手续费
        // 抵扣金支付
        BigDecimal totalAmount = amount.add(feeAmount);
        payOrder.setFeeAmount(feeAmount);
        payOrder.setPayAmount(totalAmount);

        payOrder.setPayType(payType);
        payOrder.setPaymentNo(generatePaymentNo());

        // 微信支付 支付宝支付等待回调修改成功
        payOrder.setPayStatus(PayStatusEnum.DURING.getCode());

        payOrder.setDelFlag("0");
        payOrder.setRetryCount(0);

        // 3. 保存支付记录
        save(payOrder);

        // 4. 调用支付策略创建支付订单
        PayStrategy payStrategy = payStrategyFactory.getPaymentStrategy(payType);
        Map<String, String> payParams = payStrategy.createAppPayment(payOrder);

        // 5. 如果是抵扣金支付，记录支付信息
        if (PayOrder.PAY_TYPE_DEDUCTION.equals(payType)) {
            // 获取用户抵扣金的手续费
            String feeAmountStr = payParams.get("feeAmount");
            feeAmount = feeAmountStr != null ? new BigDecimal(feeAmountStr) : BigDecimal.ZERO;

            // 记录抵扣金支付信息
            try {
                deductionPaymentRecordService.recordDeductionPayment(
                    userId,
                    payOrder.getPaymentNo(),
                    orderNo,
                    amount,
                    feeAmount,
                    new Date(),
                    "0", // 成功
                    "1"  // 用户抵扣金支付
                );
            } catch (Exception e) {
                log.error("记录抵扣金支付信息失败", e);
            }
        }

        return payParams;
    }

    /**
     * 创建抵扣金组合支付订单
     * @param orderId        订单ID
     * @param orderNo        订单号
     * @param userId         用户ID
     * @param deductionAmount 抵扣金金额（已扣减）
     * @param serviceFee     手续费金额（需支付宝支付）
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> createDeductionPayment(Long orderId, String orderNo, Long userId,
                                                     BigDecimal deductionAmount, BigDecimal serviceFee) {
        // 1. 检查是否已存在支付记录
        PayOrder existPayOrder = getByOrderNo(orderNo);
        if (existPayOrder != null) {
            // 如果已支付失败，允许重新支付
            if (PayStatusEnum.FAILED.getCode().equals(existPayOrder.getPayStatus())) {
                existPayOrder.setPayStatus(PayStatusEnum.DURING.getCode());
                updateById(existPayOrder);
            }
            // 如果是待支付或支付中状态，返回原支付参数
            if (PayStatusEnum.PENDING.getCode().equals(existPayOrder.getPayStatus()) ||
                PayStatusEnum.DURING.getCode().equals(existPayOrder.getPayStatus())) {
                // 对于组合支付，直接调用支付宝策略
                PayStrategy alipayStrategy = payStrategyFactory.getPaymentStrategy(PayOrder.PAY_TYPE_ALIPAY);
                Map<String, String> result = alipayStrategy.createAppPayment(existPayOrder);
                // 添加组合支付标识
                result.put("payType", "deduction_alipay");
                result.put("deductionAmount", existPayOrder.getOriginalAmount().toString());
                result.put("serviceFee", existPayOrder.getFeeAmount().toString());
                return result;
            }
        }

        // 2. 创建支付记录
        PayOrder payOrder = new PayOrder();
        payOrder.setOrderId(orderId);
        payOrder.setOrderNo(orderNo);
        payOrder.setUserId(userId);
        payOrder.setOriginalAmount(deductionAmount); // 记录抵扣金金额
        payOrder.setPayAmount(serviceFee); // 支付宝支付金额（手续费）
        payOrder.setFeeAmount(serviceFee); // 手续费金额
        payOrder.setPayType(PayOrder.PAY_TYPE_DEDUCTION); // 标记为抵扣金支付类型
        payOrder.setPaymentNo(generatePaymentNo());
        payOrder.setPayStatus(PayStatusEnum.DURING.getCode());
        payOrder.setDelFlag("0");

        // 3. 保存支付记录
        save(payOrder);

        // 4. 调用支付宝策略处理手续费支付
        PayStrategy alipayStrategy = payStrategyFactory.getPaymentStrategy(PayOrder.PAY_TYPE_ALIPAY);
        Map<String, String> alipayResult = alipayStrategy.createAppPayment(payOrder);

        // 5. 添加组合支付标识信息
        alipayResult.put("payType", "deduction_alipay");
        alipayResult.put("deductionAmount", deductionAmount.toString());
        alipayResult.put("serviceFee", serviceFee.toString());
        alipayResult.put("deductionUsed", "true");

        // 6. 记录抵扣金支付信息（初始状态为支付中，等支付宝回调成功后更新为成功）
        try {
            deductionPaymentRecordService.recordDeductionPayment(
                userId,
                payOrder.getPaymentNo(),
                orderNo,
                deductionAmount,
                serviceFee,
                new Date(),
                DeductionPaymentStatusEnum.PENDING.getCode(), // 支付中（等待支付宝支付完成）
                "1"  // 用户抵扣金支付
            );
        } catch (Exception e) {
            log.error("记录抵扣金支付信息失败", e);
        }

        log.info("创建抵扣金组合支付成功 - 订单号: {}, 抵扣金: {}元, 手续费: {}元",
                orderNo, deductionAmount, serviceFee);

        return alipayResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handlePayCallback(String payType, Map<String, String> params) {
        // 1. 获取支付单号
        String paymentNo = params.get("out_trade_no");
        if (paymentNo == null) {
            log.error("支付回调参数错误：缺少支付单号");
            return false;
        }

        // 2. 查询支付记录
        PayOrder payOrder = getByPaymentNo(paymentNo);
        if (payOrder == null) {
            log.error("支付回调失败：支付记录不存在，paymentNo={}", paymentNo);
            return false;
        }

        // 2. 支付状态是否为待支付或支付中
        if (!(PayStatusEnum.PENDING.getCode().equals(payOrder.getPayStatus()) ||
            PayStatusEnum.DURING.getCode().equals(payOrder.getPayStatus()))) {
            log.error("支付回调失败：已支付，paymentNo={}", paymentNo);
            return false;
        }

        // 3. 调用支付策略处理回调
        PayStrategy payStrategy = payStrategyFactory.getPaymentStrategy(payType);
        boolean success = payStrategy.handlePayCallback(params);

        if (success) {
            // 4. 更新支付记录
            payOrder.setPayStatus(PayStatusEnum.SUCCESS.getCode()); // 支付成功
            payOrder.setTransactionId(params.get("transaction_id"));
            payOrder.setPayTime(new Date());
            return updateById(payOrder);
        } else {
            // 5. 更新支付记录为支付失败
            payOrder.setPayStatus(PayStatusEnum.FAILED.getCode()); // 支付失败
            updateById(payOrder);
            return false;
        }
    }

    @Override
    public String queryPayStatus(PayOrder payOrder) {
        if (payOrder == null) {
            return null;
        }

        // 如果本地状态是终态，直接返回
        if (PayStatusEnum.FAILED.getCode().equals(payOrder.getPayStatus()) ||
            PayStatusEnum.CLOSED.getCode().equals(payOrder.getPayStatus())) {
            return payOrder.getPayStatus();
        }

        // 记录查询前的重试次数
        Integer originalRetryCount = payOrder.getRetryCount();

        // 查询第三方支付状态
        PayStrategy payStrategy = payStrategyFactory.getPaymentStrategy(payOrder.getPayType());
        String thirdPartyStatus = payStrategy.queryPayStatus(payOrder);

        // 检查是否需要更新数据库
        boolean needUpdate = false;

        // 状态发生变化
        if (thirdPartyStatus != null && !thirdPartyStatus.equals(payOrder.getPayStatus())) {
            payOrder.setPayStatus(thirdPartyStatus);
            if (PayStatusEnum.SUCCESS.getCode().equals(thirdPartyStatus)) {
                payOrder.setPayTime(new Date());
            }
            needUpdate = true;
        }

        // 重试次数发生变化
        if (!Objects.equals(originalRetryCount, payOrder.getRetryCount())) {
            needUpdate = true;
        }

        // 更新数据库
        if (needUpdate) {
            updateById(payOrder);
        }

        return payOrder.getPayStatus();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelPayment(String paymentNo) {
        PayOrder payOrder = getByPaymentNo(paymentNo);
        if (payOrder == null) {
            throw new RuntimeException("支付订单不存在");
        }
        // 只有待支付或支付中的订单可以取消
        if (PayStatusEnum.SUCCESS.getCode().equals(payOrder.getPayStatus())|| PayStatusEnum.DURING.getCode().equals(payOrder.getPayStatus())) {
            throw new RuntimeException("当前订单状态不可取消支付");
        }

        // 调用支付策略取消支付
        PayStrategy payStrategy = payStrategyFactory.getPaymentStrategy(payOrder.getPayType());
        boolean success = payStrategy.cancelPayment(payOrder);

        if (success) {
            // 更新支付记录状态
            payOrder.setPayStatus(PayStatusEnum.CLOSED.getCode()); // 已关闭
            return updateById(payOrder);
        }

        return false;
    }

    @Override
    public PayOrder getByPaymentNo(String paymentNo) {
        LambdaQueryWrapper<PayOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PayOrder::getPaymentNo, paymentNo)
            .eq(PayOrder::getDelFlag, "0")
            .orderByDesc(PayOrder::getCreateTime)
            .last(" limit 1");
        return getOne(wrapper);
    }

    @Override
    public PayOrder getByOrderNo(String orderNo) {
        LambdaQueryWrapper<PayOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PayOrder::getOrderNo, orderNo)
            .eq(PayOrder::getDelFlag, "0")
            .orderByDesc(PayOrder::getCreateTime)
            .last(" limit 1");
        return getOne(wrapper);
    }

    /**
     * 生成支付单号
     */
    private String generatePaymentNo() {
        return OrderNoGenerator.generateOrderNo("PAY");
    }
}
