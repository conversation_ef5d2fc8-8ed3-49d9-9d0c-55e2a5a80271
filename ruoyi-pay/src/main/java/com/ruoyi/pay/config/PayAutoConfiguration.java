package com.ruoyi.pay.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.context.annotation.PropertySource;

/**
 * 支付模块自动配置
 * 根据当前环境加载对应的配置文件
 */
@Configuration
public class PayAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(PayAutoConfiguration.class);

    /**
     * 开发环境配置
     */
    @Configuration
    @Profile("dev")
    @PropertySource(value = "classpath:pay-dev.yml", factory = YamlPropertySourceFactory.class)
    public static class DevConfiguration {
        public DevConfiguration() {
            log.info("加载支付模块开发环境配置: pay-dev.yml");
        }
    }

    /**
     * 测试环境配置
     */
    @Configuration
    @Profile("test")
    @PropertySource(value = "classpath:pay-test.yml", factory = YamlPropertySourceFactory.class)
    public static class TestConfiguration {
        public TestConfiguration() {
            log.info("加载支付模块测试环境配置: pay-test.yml");
        }
    }

    /**
     * 生产环境配置
     */
    @Configuration
    @Profile("prod")
    @PropertySource(value = "classpath:pay-prod.yml", factory = YamlPropertySourceFactory.class)
    public static class ProdConfiguration {
        public ProdConfiguration() {
            log.info("加载支付模块生产环境配置: pay-prod.yml");
        }
    }
}
