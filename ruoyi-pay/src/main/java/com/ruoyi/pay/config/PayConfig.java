package com.ruoyi.pay.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@Configuration
@ConfigurationProperties(prefix = "ruoyi.pay")
public class PayConfig {
    /**
     * 退款配置
     */
    private Refund refund = new Refund();

    /**
     * 微信支付配置
     */
    private WechatPay wechatPay = new WechatPay();

    /**
     * 支付宝配置
     */
    private Alipay alipay = new Alipay();

    /**
     * 获取退款手续费率（转换为小数）
     * 例如：配置为0.6，返回0.006
     *
     * @return 退款手续费率（小数形式）
     */
    public BigDecimal getRefundFeeRateDecimal() {
        try {
            // 将配置值从百分比转换为小数（例如：0.6% -> 0.006）
            return new BigDecimal(refund.getFeeRate()).divide(new BigDecimal("100"), 6, RoundingMode.HALF_UP);
        } catch (Exception e) {
            // 如果配置解析失败，使用默认值0.6%
            return new BigDecimal("0.006");
        }
    }

    @Data
    public static class Refund {
        /**
         * 退款手续费率
         */
        private String feeRate;
    }

    @Data
    public static class WechatPay {
        /**
         * 应用ID
         */
        private String appId;
        /**
         * 商户号
         */
        private String mchId;
        /**
         * API密钥
         */
        private String apiKey;
        /**
         * 回调地址
         */
        private String notifyUrl;
        /**
         * 证书路径
         */
        private String certPath;
    }

    @Data
    public static class Alipay {
        /**
         * 应用ID
         */
        private String appId;
        /**
         * 商户私钥
         */
        private String privateKey;
        /**
         * 支付宝公钥
         */
        private String publicKey;
        /**
         * 回调地址
         */
        private String notifyUrl;
        /**
         * 设置应用证书路径根目录
         */
        private String appPath;
        /**
         * 设置应用公钥证书路径
         */
        private String appCertPath;
        /**
         * 设置支付宝公钥证书路径
         */
        private String alipayPublicCertPath;
        /**
         * 设置支付宝根证书路径
         */
        private String rootCertPath;
        /**
         * 网关地址
         */
        private String gatewayUrl = "https://openapi.alipay.com/gateway.do";
    }
}
