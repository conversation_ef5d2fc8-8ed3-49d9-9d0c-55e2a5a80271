# ruoyi-pay 支付服务模块

## 模块介绍

`ruoyi-pay` 是MallB电商平台的支付集成服务模块，提供统一的支付接入接口，集成多种主流支付方式，处理支付流程、退款流程和结果通知等功能，保障交易安全和支付体验。

## 主要功能

- 支付接口集成
    - 支付下单
    - 支付状态查询
    - 退款申请
    - 退款查询
    - 交易账单下载
- 支付结果处理
    - 异步通知处理
    - 支付结果验证
    - 业务系统通知
- 支付流程管理
    - 支付超时处理
    - 支付重试机制
    - 幂等性保障
- 安全机制
    - 签名验证
    - 防篡改机制
    - 敏感数据加密
    - 防重放攻击

## 支持的支付方式

- 支付宝（PC、手机网站、APP、当面付）
- 微信支付（公众号、小程序、APP、扫码）
- 银联支付
- PayPal
- 钱包支付（平台内部余额）

## 使用示例

### 创建支付订单

```java
@Autowired
private PayService payService;

public String createPayOrder(PayOrderDTO orderDTO) {
    return payService.createOrder(orderDTO);
}
```

### 处理支付回调

```java
@PostMapping("/notify/{channel}")
public String handlePayNotify(@PathVariable String channel, HttpServletRequest request) {
    return payService.processNotify(channel, request);
}
```

### 申请退款

```java
public RefundResult refund(RefundDTO refundDTO) {
    return payService.refund(refundDTO);
}
```

## 配置说明

在application.yml中配置支付参数：

```yaml
pay:
  alipay:
    appId: your-app-id
    privateKey: your-private-key
    alipayPublicKey: alipay-public-key
    notifyUrl: https://your-domain/api/pay/notify/alipay
    returnUrl: https://your-domain/pay/return
  
  wxpay:
    appId: your-app-id
    mchId: your-merchant-id
    mchKey: your-merchant-key
    notifyUrl: https://your-domain/api/pay/notify/wxpay
    keyPath: classpath:cert/wxpay/apiclient_cert.p12
```

## 开发指南

1. 关键操作需要加入事务保障
2. 支付结果必须进行签名验证
3. 支付状态变更需要保证幂等性
4. 敏感支付信息需要加密存储 