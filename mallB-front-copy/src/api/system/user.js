import request from '@/utils/request'

// 发送手机验证码
export function sendOperatePwdSmsCode() {
    return request({
        url: '/system/user/profile/sendOperatePwdSmsCode',
        method: 'get'
    })
}

// 发送登录密码重置验证码
export function sendLoginPwdSmsCode() {
    return request({
        url: '/system/user/profile/sendLoginPwdSmsCode',
        method: 'get'
    })
}

// 获取当前用户手机号
export function getUserPhone() {
    return request({
        url: '/system/user/profile/phone',
        method: 'get'
    })
}

// 重置操作密码
export function resetOperationPwd(data) {
    return request({
        url: '/system/user/profile/resetOperationPwd',
        method: 'post',
        data: data
    })
}

// 重置登录密码(新方式，通过短信验证码)
export function resetLoginPwd(data) {
    return request({
        url: '/system/user/profile/resetLoginPwd',
        method: 'post',
        data: data
    })
}

// 获取公司主页信息
export function getCompanyHome(data) {
    return request({
        url: '/system/user/profile/getCompanyHome',
        method: 'post',
        data: data
    })
}
