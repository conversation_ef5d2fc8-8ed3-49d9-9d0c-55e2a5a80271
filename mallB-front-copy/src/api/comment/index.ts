import request from '../../utils/request';

/**
 * 获取评论列表
 * @param params 查询参数
 */
export const getCommentList = (params: any) => {
    return request({
        url: '/mall/productEvaluate/list',
        method: 'get',
        params
    });
};

/**
 * 审核评论
 * @param id 评论ID
 * @param status 状态（1:通过，2:拒绝）
 */
export const reviewComment = (id: number, status: string) => {
    return request({
        url: `/mall/productEvaluate/examine/${id}/${status}`,
        method: 'put'
    });
};

/**
 * 商家回复评论
 * @param id 评论ID
 * @param reply 回复内容
 */
export const replyComment = (id: number, reply: string) => {
    return request({
        url: `/mall/productEvaluate/reply/${id}`,
        method: 'put',
        data: {reply}
    });
};

/**
 * 获取评论详情
 * @param id 评论ID
 */
export const getCommentDetail = (id: number) => {
    return request({
        url: `/mall/productEvaluate/${id}`,
        method: 'get'
    });
};

/**
 * 删除评论
 * @param ids 评论ID
 */
export const deleteComment = (ids: string) => {
    return request({
        url: `/mall/productEvaluate/${ids}`,
        method: 'delete'
    });
};
