import request from '../../utils/request'

/**
 * 获取商家的会话列表
 */
export function getMerchantConversations() {
    return request({
        url: '/chat/merchant/conversations',
        method: 'get'
    })
}

/**
 * 获取平台的会话列表
 */
export function getPlatformConversations() {
    return request({
        url: '/chatPlatform/conversations',
        method: 'get'
    })
}

/**
 * 获取会话的消息列表
 * @param conversationId 会话ID
 * @param pageNum 页码
 * @param pageSize 每页条数
 */
export function getConversationMessages(conversationId: number, pageNum: number = 1, pageSize: number = 100) {
    return request({
        url: `/chat/messages/${conversationId}`,
        method: 'get',
        params: {pageNum, pageSize}
    })
}

/**
 * 标记会话中的所有消息为已读
 * @param conversationId 会话ID
 */
export function markAllMessagesAsRead(conversationId: number) {
    return request({
        url: `/chat/conversation/read/${conversationId}`,
        method: 'post'
    })
}

/**
 * 标记单条消息为已读
 * @param messageId 消息ID
 */
export function markMessageAsRead(messageId: number) {
    return request({
        url: `/chat/message/read/${messageId}`,
        method: 'post'
    })
}

/**
 * 撤回消息
 * @param messageId 消息ID
 */
export function recallMessage(messageId: number) {
    return request({
        url: `/chat/message/recall/${messageId}`,
        method: 'post'
    })
}

/**
 * 上传聊天文件（图片、视频等）
 * @param file 文件对象
 */
export function uploadChatFile(file: File) {
    const formData = new FormData()
    formData.append('file', file)

    return request({
        url: '/chat/upload',
        method: 'post',
        data: formData
    })
}

/**
 * 创建或获取与用户的会话
 * @param userId 用户ID
 * @param title 会话标题
 */
export function createOrGetConversation(userId: number, title: string) {
    return request({
        url: '/chat/conversation',
        method: 'post',
        data: {userId, title}
    })
}

/**
 * 商家创建或获取与平台的会话
 * @param title 会话标题
 */
export function createOrGetMerchantPlatformConversation(title: string) {
    return request({
        url: '/chat/merchant/platform/conversation',
        method: 'post',
        params: {title}
    })
}
