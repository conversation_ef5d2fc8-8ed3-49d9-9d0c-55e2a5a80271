import request from '../../utils/request'

/**
 * 发送忘记密码验证码
 * @param {string} phone 手机号
 * @returns {Promise} 返回包含uuid的响应
 */
export function sendForgotPasswordCode(phone) {
    return request({
        url: '/mall/shop/sendForgotPasswordCode',
        method: 'get',
        params: {
            phone: phone
        }
    })
}

/**
 * 重置密码
 * @param {Object} data 重置密码数据
 * @param {string} data.phone 手机号
 * @param {string} data.code 验证码
 * @param {string} data.uuid 唯一标识
 * @param {string} data.password 新密码
 * @returns {Promise} 返回重置结果
 */
export function resetPassword(data) {
    return request({
        url: '/mall/shop/resetPassword',
        method: 'post',
        data: data
    })
}
