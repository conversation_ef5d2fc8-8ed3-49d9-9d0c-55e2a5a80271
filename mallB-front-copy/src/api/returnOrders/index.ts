import request from '../../utils/request';

/**
 * 获取退货地址列表
 * @param params 查询参数
 */
export const getReturnAddressList = (params?: any) => {
    return request({
        url: '/mall/shopReturnAddress/list',
        method: 'get',
        params
    });
};

/**
 * 添加退货地址
 * @param data 退货地址数据
 */
export const addReturnAddress = (data: any) => {
    return request({
        url: '/mall/shopReturnAddress',
        method: 'post',
        data
    });
};

/**
 * 更新退货地址
 * @param data 退货地址数据
 */
export const updateReturnAddress = (data: any) => {
    return request({
        url: '/mall/shopReturnAddress',
        method: 'put',
        data
    });
};

/**
 * 删除退货地址
 * @param ids 地址ID数组
 */
export const deleteReturnAddress = (ids: string) => {
    return request({
        url: `/mall/shopReturnAddress/${ids}`,
        method: 'delete'
    });
};

/**
 * 设置默认退货地址
 * @param id 地址ID
 */
export const setDefaultReturnAddress = (id: number) => {
    return request({
        url: `/mall/shopReturnAddress/default/${id}`,
        method: 'put'
    });
};

/**
 * 获取退货地址详情
 * @param id 地址ID
 */
export const getReturnAddressDetail = (id: number) => {
    return request({
        url: `/mall/shopReturnAddress/${id}`,
        method: 'get'
    });
}; 