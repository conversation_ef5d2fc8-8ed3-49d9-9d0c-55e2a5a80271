import request from '../../utils/request';

//退款列表
export const getOrderRefundList = (data) => {
    return request({
        url: '/mall/orderRefund/list',
        method: 'get',
        params: data
    })
}
//退款详情
export const getOrderRefund = (refundId) => {
    return request({
        url: '/mall/orderRefund/' + refundId,
        method: 'get'
    })
}
//同意退款
export const getProcessRefund = (refundId) => {
    return request({
        url: '/mall/orderRefund/process/' + refundId,
        method: 'post'
    })
}
//拒绝退款
export const getRejectRefund = (refundId, rejectMessage) => {
    return request({
        url: '/mall/orderRefund/reject/' + refundId,
        method: 'post',
        params: rejectMessage
    })
}
//拒绝退款
export const updateAmount = (refundId, data) => {
    return request({
        url: '/mall/orderRefund/updateAmount/' + refundId,
        method: 'post',
        params: data
    })
}
