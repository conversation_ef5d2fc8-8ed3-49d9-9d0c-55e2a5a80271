import request from '../../utils/request';

// 根据电话号码查询商家信息
export function getMerchantByPhone(params): Promise<any> {
    return request({
        url: '/mall/userFreezeRecord/getUserByPhone',
        method: 'get',
        params: params
    });
}

// 获取商家货款冻结记录（支持分页和搜索）
export function getShopFreezeRecords(params: {
    shopId?: number,
    shopName?: string,
    shopPhone?: string,
    status?: string,
    pageNum?: number,
    pageSize?: number
}): Promise<any> {
    return request({
        url: '/mall/userFreezeRecord/getFreezeRecords',
        method: 'get',
        params: params
    });
}

// 冻结商家货款
export function freezeMerchantAmount(data: {
    shopId: number,
    amount: number,
    reason: string
}): Promise<any> {
    return request.post('/mall/userFreezeRecord/freezeAmount', data)
}
