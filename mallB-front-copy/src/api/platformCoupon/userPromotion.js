import request from '@/utils/request'

// 查询商户平台促销金列表
export function listUserPromotion(query) {
    return request({
        url: '/mall/user/promotion/list',
        method: 'get',
        params: query
    })
}

// 查询商家促销金明细列表
export function listUserPromotionDetail(query) {
    return request({
        url: '/mall/user/promotion/listPromotionDetail',
        method: 'get',
        params: query
    })
}

// 导出商家促销金明细列表
export function exportUserPromotionDetail(query) {
    return request({
        url: '/mall/user/promotion/exportPromotionDetail',
        method: 'post',
        params: query,
        responseType: 'blob'
    })
}


