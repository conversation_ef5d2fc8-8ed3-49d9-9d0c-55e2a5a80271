import request from '@/utils/request'

// 查询用户平台抵扣金列表
export function listUserDeduction(query) {
    return request({
        url: '/mall/user/deduction/list',
        method: 'get',
        params: query
    })
}


// 查询用户抵扣金明细列表
export function listUserDeductionDetail(query) {
    return request({
        url: '/mall/user/deduction/listDeductionDetail',
        method: 'get',
        params: query
    })
}

// 导出用户平台抵扣金明细列表
// export function exportUserDeductionDetail(query) {
//     return request({
//         url: '/mall/user/deduction/exportDetail',
//         method: 'post',
//         data: query,
//         responseType: 'blob'
//     })
// }

// 导出用户抵扣金明细列表
export function exportUserDeductionDetail(query) {
    return request({
        url: '/mall/user/deduction/exportDeductionDetail',
        method: 'post',
        params: query,
        responseType: 'blob'
    })
}


