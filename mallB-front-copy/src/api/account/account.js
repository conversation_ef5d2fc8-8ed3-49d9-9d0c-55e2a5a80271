import request from '@/utils/request'


/**
 * 获取账户注销审核状态
 * @param {number} userId - 用户ID
 * @returns {Promise} 包含账户状态的Promise
 */
export function getAccountStatus(userId) {
    return request({
        url: '/mall/user/account/status',
        method: 'get',

    })
}

/**
 * 保存账户注销审核状态
 * @param {Object} data - 包含状态数据的对象
 * @returns {Promise} 操作结果的Promise
 */
export function saveAccountStatus(data) {
    return request({
        url: '/mall/user/account/status',
        method: 'post',
        data
    })
}


/*


/!**
 * 获取账户注销状态
 * @param {number} userId - 用户ID
 * @returns {Promise} - 包含账户状态的Promise
 *!/
export function getAccountStatus(userId) {
    return request({
        url: '/mall/user/account/status',
        method: 'get',
        params: { userId }
    })
}



/!*

/!**
 * 更新账户注销状态
 * @param {Object} data - 包含状态更新数据的对象
 * @returns {Promise} - 更新操作的Promise
 *!/
export function updateAccountStatus(data) {
    return request({
        url: '/mall/user/account/status',
        method: 'put',
        data
    })
}
*!/


/!**
 * 创建或更新账户注销状态
 * @param {Object} data - 包含状态数据的对象
 * @returns {Promise} 操作结果的Promise
 *!/
export function createOrUpdateAccountStatus(data) {
    return request({
        url: '/mall/user/account/status',
        method: 'post',
        data
    })
}
*/


/*export function getAccountStatus(userId) {
    return request({
        url: `/account/status/${userId}`,
        method: 'get'
    })
}

export function updateAccountStatus(data) {
    return request({
        url: '/account/status/update',
        method: 'post',
        data
    })
}*/



