import request from '@/utils/request'

/**
 * 获取账户注销审核列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getAccountAuditList(params) {
    return request({
        url: '/mall/user/account/list',
        method: 'get',
        params
    })
}

/**
 * 获取账户注销审核详情
 * @param {Number} id 审核ID
 * @returns {Promise}
 */
// export function getShopAuditDetail(id) {
//     return request({
//         url: `/mall/shopAudit/${id}`,
//         method: 'get'
//     })
// }

/**
 * 审核账户注销申请
 * @param {Object} data 审核信息，包含id、auditStatus、auditRemark字段
 * @returns {Promise}
 */
export function auditAccount(data) {
    return request({
        url: '/mall/user/account/audit',
        method: 'post',
        data
    })
}
