import request from '../../utils/request'

/**
 * 获取关系链列表
 * @param data 查询参数
 * @returns 关系链列表
 */
export function getRelationChainList(data: any) {
    return request({
        url: '/mall/relationChain/list',
        method: 'get',
        params: data
    })
}

/**
 * 修改用户状态
 * @param data 用户ID和状态
 * @returns 操作结果
 */
export function changeUserStatus(data: any) {
    return request({
        url: '/mall/relationChain/changeStatus',
        method: 'post',
        data
    })
}

/**
 * 获取关系链设置
 * @returns 关系链设置
 */
export function getRelationChainSettings() {
    return request({
        url: '/mall/config/key/9',
        method: 'get'
    })
}

/**
 * 更新关系链设置
 * @param data 设置参数
 * @returns 操作结果
 */
export function updateRelationChainSettings(data: any) {
    return request({
        url: '/mall/config/9',
        method: 'post',
        data
    })
}
