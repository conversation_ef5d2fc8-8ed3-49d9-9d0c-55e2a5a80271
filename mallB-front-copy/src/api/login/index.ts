import request from '../../utils/request';
// import other from '../../utils/other';
import other from '../../utils/other'

/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

/**
 * 获取验证码
 * @param data
 */
export const getCode = (randomStr) => {
    return request({
        url: '/image',
        method: 'get',
        params: {
            randomStr: randomStr
        }
    });
};
/**
 * 登录
 * @param data
 */
export const login = (data: any) => {
    let encPassword = data.password;
    // 密码加密
    encPassword = other.encryption(data.password, 'thanks,znh4cloud');
    return request({
        url: '/adminLogin',
        method: 'post',
        data: {...data, password: encPassword},
        headers: {
            // skipToken: true,
            'Content-Type': 'application/json; charset=UTF-8',
            'Authorization': 'Basic em5oOnpuaA=='
        },
    });
};
/**
 * 获取用户信息
 */
export const getUserInfo = () => {
    return request({
        url: '/getInfo',
        method: 'get'
    });
};

//获取用户菜单数据
export const getUserMessage = (type) => {
    return request({
        url: '/system/user/',
        method: 'get',
        params: {
            type: type
        }
    });
};

/**
 * 退出登录
 */
export const logout = () => {
    return request({
        url: '/logout',
        method: 'post'
    });
};
