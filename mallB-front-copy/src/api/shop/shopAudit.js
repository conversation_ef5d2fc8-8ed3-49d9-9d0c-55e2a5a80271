import request from '@/utils/request'

/**
 * 获取商家审核列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getShopAuditList(params) {
    return request({
        url: '/mall/shopAudit/list',
        method: 'get',
        params
    })
}

/**
 * 获取商家审核记录
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getShopAuditRecordList(params) {
    return request({
        url: '/mall/shopAudit/recordList',
        method: 'get',
        params
    })
}

/**
 * 获取商家审核详情
 * @param {Number} id 审核ID
 * @returns {Promise}
 */
export function getShopAuditDetail(id) {
    return request({
        url: `/mall/shopAudit/${id}`,
        method: 'get'
    })
}

/**
 * 审核商家申请
 * @param {Object} data 审核信息，包含id、auditStatus、auditRemark字段
 * @returns {Promise}
 */
export function auditShop(data) {
    return request({
        url: '/mall/shopAudit/audit',
        method: 'post',
        data
    })
}
