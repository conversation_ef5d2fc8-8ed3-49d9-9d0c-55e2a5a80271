import request from '@/utils/request'

/**
 * 获取当前商家信息
 */
export function getShopInfo() {
    return request({
        url: '/mall/shop',
        method: 'get'
    })
}

/**
 * 获取商家银行账户信息
 */
export function getShopBankAccount() {
    return request({
        url: '/mall/shopBank',
        method: 'get'
    })
}

/**
 * 获取当前登录用户信息
 */
export function getLoginInfo() {
    return request({
        url: '/getInfo',
        method: 'get'
    })
}

/**
 * 获取商家平台促销金余额
 */
export function getPromotionBalance() {
    return request({
        url: '/mall/shop/getPromotionBalance',
        method: 'get'
    })
}
