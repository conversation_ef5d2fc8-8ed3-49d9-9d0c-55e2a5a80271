import request from '@/utils/request'

// 获取用户关注列表
export const getUserFocusPage = (params: any) => {
    return request({
        url: '/mall/shopFocus/getUserFocusPage',
        method: 'get',
        params
    })
}

// 代销商家取消用户关注
export const cancelUserFocus = (focusUserId: number) => {
    return request({
        url: `/mall/shopFocus/cancelFocus/${focusUserId}`,
        method: 'post'
    })
}
