import request from '../../utils/request'

// 定义API响应类型 - 列表响应
export interface ApiListResponse<T> {
    code: number;
    msg: string;
    rows: T[];
    total: number;
}

// 定义API响应类型 - 单个对象响应
export interface ApiObjectResponse<T> {
    code: number;
    msg: string;
    data: T;
}

// 广告实体定义
export interface Advertisement {
    id: string;
    adName: string;
    adType: string;
    adTypeText?: string;
    adUrl: string;
    price: number;
    jumpUrl: string;
    createTime?: string;
    startTime?: string;
    endTime?: string;
    status?: string;
    statusText?: string;
    clickNumber?: number;
    type?: string; // 广告位置: 0-首页
    typeText?: string; // 广告位置文本描述
}

/**
 * 获取广告列表
 * @param params 查询参数
 */
export function getAdvertisementList(params: any) {
    return request.get<ApiListResponse<Advertisement>>('/mall/shopAd/list', {
        params
    });
}

/**
 * 获取广告详情
 * @param id 广告ID
 */
export function getAdvertisementDetail(id: string | number) {
    return request.get<ApiObjectResponse<Advertisement>>(`/mall/shopAd/${id}`);
}

/**
 * 添加广告
 * @param data 广告数据
 */
export function addAdvertisement(data: Advertisement) {
    console.log('API调用 - 添加广告请求数据:', data);
    return request.post<ApiObjectResponse<any>>('/mall/shopAd/add', data);
}

/**
 * 更新广告
 * @param data 广告数据
 */
export function updateAdvertisement(data: Advertisement) {
    console.log('API调用 - 更新广告请求数据:', data);
    return request.put<ApiObjectResponse<boolean>>('/mall/shopAd/edit', data);
}

/**
 * 删除广告
 * @param id 广告ID
 */
export function deleteAdvertisement(id: string | number) {
    return request.delete<ApiObjectResponse<boolean>>(`/mall/shopAd/${id}`);
}

/**
 * 审核广告
 * @param id 广告ID
 * @param status 审核状态：1-通过(待支付)，3-拒绝
 * @param remark 审核备注
 */
export function auditAdvertisement(id: string | number, status: string, remark: string) {
    return request.post<ApiObjectResponse<boolean>>(`/mall/shopAd/audit/${id}`, {
        status,
        remark
    });
}

/**
 * 批量审核广告
 * @param ids 广告ID数组
 * @param status 审核状态：1-通过(待支付)，3-拒绝
 * @param remark 审核备注
 */
export function batchAuditAdvertisement(ids: (string | number)[], status: string, remark: string) {
    return request.post<ApiObjectResponse<boolean>>('/mall/shopAd/batchAudit', {
        ids,
        status,
        remark
    });
}

/**
 * 创建广告支付订单
 * @param adId 广告ID
 */
export function createAdPayOrder(adId: string | number) {
    let data = {
        adId: adId
    }
    return request.post<ApiObjectResponse<string>>('/virtual/create/6', data);
}

/**
 * 获取虚拟订单详情
 * @param orderNo 订单号
 */
export function getVirtualOrderDetail(orderNo: string) {
    return request.get<ApiObjectResponse<any>>(`/virtual/getVirtualOrderDetail/${orderNo}`);
}

/**
 * 取消虚拟订单
 * @param orderNo 订单号
 */
export function cancelVirtualOrder(orderNo: string) {
    return request.post<ApiObjectResponse<boolean>>(`/virtual/cancel/${orderNo}`);
}
