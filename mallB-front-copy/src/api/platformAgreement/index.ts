import request from '../../utils/request';

// 查询分类数据
export const list = () => {
    return request({
        url: '/mall/platformAgreement/list',
        method: 'get',
    })
}

/**
 * 创建
 */
export const add = (data: Object) => {
    return request({
        url: '/mall/platformAgreement',
        method: 'post',
        data: data,
    });
};

// 修改页面
export const update = (data) => {
    return request({
        url: "/mall/platformAgreement",
        method: 'put',
        data: data
    })
}

//根据id查询详情
export const getInfo = (id) => {
    return request({
        url: '/mall/platformAgreement/' + id,
        method: 'get'
    })
}
/**
 * 删除
 * @param id
 */
export const deleteById = (id) => {
    return request({
        url: '/mall/platformAgreement/' + id,
        method: 'delete'
    })
}


/**
 * 根据title获取协议内容
 * @param title
 */
export const getInfoByTitle = (title) => {
    return request({
        url: '/mall/platformAgreement/getInfoByTitle',
        method: 'get',
        params: {
            title: title
        }
    })
}

