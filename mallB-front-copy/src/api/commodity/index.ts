import request from '../../utils/request';
// import other from '../../utils/other';

/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

/**
 * 查询代销数据
 *
 */

export const getConsignmentCount = () => {
    return request({
        url: '/mall/product/getConsignmentCount',
        method: 'get'
    });
};
/**
 * 查询代销数据
 *
 */

export const getEditDaixiao = (data) => {
    return request({
        url: '/mall/product/editDaixiao',
        method: 'put',
        data: data
    });
};

// 获取商品详情
export const getProductById = (id) => {
    return request({
        url: '/mall/product/' + id,
        method: 'get'
    });
};

// 更新商品信息
export const updateProduct = (data) => {
    return request({
        url: '/mall/product',
        method: 'put',
        data: data
    });
};

// 获取分类详情
export const getCategoryById = (id) => {
    return request({
        url: '/mall/category/' + id,
        method: 'get'
    });
};
