import request from '../../utils/request';

// 查询分类数据
export const list = () => {
    return request({
        url: '/mall/delivery/list',
        method: 'get',
    })
}

/**
 * 创建
 */
export const add = (data: Object) => {
    return request({
        url: '/mall/delivery',
        method: 'post',
        data: data,
    });
};

// 修改页面
export const update = (data) => {
    return request({
        url: "/mall/delivery",
        method: 'put',
        data: data
    })
}

//根据id查询详情
export const getInfo = (id) => {
    return request({
        url: '/mall/delivery/' + id,
        method: 'get'
    })
}
/**
 * 删除
 * @param id
 */
export const deleteById = (id) => {
    return request({
        url: '/mall/delivery/' + id,
        method: 'delete'
    })
}



