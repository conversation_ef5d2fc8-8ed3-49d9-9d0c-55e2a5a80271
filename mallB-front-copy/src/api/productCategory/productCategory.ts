import request from '../../utils/request';

// 查询分类数据
export const getCategoryList = (data) => {
    return request({
        url: '/mall/category/tree',
        method: 'get',
        params: data
    })
}

/**
 * 创建分类
 * @param data
 */

export const save = (data: Object) => {
    return request({
        url: '/mall/category',
        method: 'post',
        data: data,
    });
};


// start   修改分类页面
export const getCategoryById = (id) => {
    return request({
        url: "/mall/category/" + id,
        method: 'get'
    })
}


//根据id值查询分类列表
export const getCategoryDetail = (userId) => {
    return request({
        url: '/system/user/' + userId,
        method: 'get'
    })
}


// 查询商品分类树数据
// /mall/category/tree
export const getCategoryTree = () => {
    return request({
        url: '/mall/category/tree',
        method: 'get'
    })
}

export const getShopDefaultCategory = () => {
    return request({
        url: '/mall/shop/getShopDefaultCategory',
        method: 'get'
    })
}

// 修改分类数据
export const updateCategory = (data) => {
    return request({
        url: '/mall/category',
        method: 'put',
        data: data
    })
}

//保存修改分类信息
export const saveUpdateUser = (data: Object) => {
    return request({
        url: '/system/user',
        method: 'put',
        data: data
    })
}

//删除分类信息
export const deleteCategory = (ids) => {
    return request({
        url: '/mall/category/' + ids,
        method: 'delete'
    })
}

//发布商品信息
export const publishProduct = (data) => {
    return request({
        url: '/mall/product',
        method: 'post',
        data: data
    })
}

