import request from '../../utils/request';

//快递公司
export const deliveryCompany = (data) => {
    return request({
        url: '/mall/delivery/list',
        method: 'get',
        params: data
    })
}

//提交订单发货
export const shipmentOder = (data) => {
    return request({
        url: '/mall/order/shipmentOder',
        method: 'post',
        data: data
    })
}

// 获取商家的技术引流次数
export const getDrainageCount = (data) => {
    return request({
        url: '/mall/shop/getDrainageCount',
        method: 'get'
    })
}

// 创建引流订单
export const createDrainageOrder = (data) => {
    return request({
        url: '/virtual/create/5',
        method: 'post',
        data: data
    })
}

// 获取店铺首页信息
export const getShopHomeInfo = () => {
    return request({
        url: '/mall/shop/homeInfo',
        method: 'get'
    })
}

// 获取店铺首页信息
export const getShopInfo = () => {
    return request({
        url: '/mall/shop',
        method: 'get'
    })
}

// 保存银行卡信息
export const saveShopBankAccount = (data) => {
    return request({
        url: '/mall/shopBank',
        method: 'post',
        data: data
    })
}

// 修改银行卡信息
export const updateShopBankAccount = (data) => {
    return request({
        url: '/mall/shopBank',
        method: 'put',
        data: data
    })
}

// 修改店铺信息
export const updateShop = (data) => {
    return request({
        url: '/mall/shop',
        method: 'put',
        data: data
    })
}

// 商家平台促销金赠送给用户
export const donatePromotionGold = (data) => {
    return request({
        url: '/mall/shop/donatePromotionGold',
        method: 'post',
        data: data
    })
}

// 查询商家赠送记录
export const getDonationRecords = (params) => {
    return request({
        url: '/mall/shop/donationRecords',
        method: 'get',
        params: params
    })
}

// 赠送促销金给用户
export const DonatePromotionGold = (data) => {
    return request({
        url: '/mall/shopDonation/donate',
        method: 'post',
        data
    })
}

// 代销赠送抵扣金给用户
export const DonateDeductionByDistributor = (data) => {
    return request({
        url: '/mall/shopDonation/donateByDistributor',
        method: 'post',
        data
    })
}

// 获取赠送记录列表
export const GetDonationRecords = (params) => {
    return request({
        url: '/mall/shopDonation/list',
        method: 'get',
        params
    })
}

// 获取代销赠送记录列表
export const GetDistributorDonationRecords = (params) => {
    return request({
        url: '/mall/shopDonation/list',
        method: 'get',
        params
    })
}

// 导出赠送记录
export const ExportDonationRecords = (params) => {
    return request({
        url: '/mall/shopDonation/export',
        method: 'post',
        params,
        responseType: 'blob'
    })
}

// 导出代销赠送记录
export const ExportDistributorDonationRecords = (params) => {
    return request({
        url: '/mall/shopDonation/export',
        method: 'post',
        params: {...params, donationType: 2},
        responseType: 'blob'
    })
}
