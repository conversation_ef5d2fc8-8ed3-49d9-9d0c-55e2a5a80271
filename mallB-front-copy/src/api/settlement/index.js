import request from '@/utils/request'

/**
 * 获取结算单列表
 * @param {Object} params 查询参数
 */
export function getSettlementList(params) {
    return request({
        url: '/mall/shopSettlement/list',
        method: 'get',
        params
    })
}

/**
 * 获取结算单详情
 * @param {number} id 结算单ID
 */
export function getSettlementDetail(id) {
    return request({
        url: `/mall/shopSettlement/${id}`,
        method: 'get'
    })
}

/**
 * 发送结算验证码
 * @param {Object} data 请求数据，包含contactPhone字段
 */
export function sendSettlementSmsCode(data) {
    return request({
        url: '/mall/shopSettlement/sendSmsCode',
        method: 'post',
        data
    })
}

/**
 * 验证结算验证码并申请结算
 * @param {Object} data 请求数据，包含contactPhone、code、amount、accountName、accountNo、bankName字段
 */
export function verifyAndApplySettlement(data) {
    return request({
        url: '/mall/shopSettlement/verifyAndApply',
        method: 'post',
        data
    })
}

/**
 * 审核结算单（管理员操作）
 * @param {Object} data 请求数据，包含id、status、auditRemark字段
 */
export function auditSettlement(data) {
    return request({
        url: '/mall/shopSettlement/audit',
        method: 'post',
        data
    })
}

/**
 * 确认打款（管理员操作）
 * @param {Object} data 请求数据，包含id、payProof、payRemark字段
 */
export function confirmPayment(data) {
    return request({
        url: '/mall/shopSettlement/payment',
        method: 'post',
        data
    })
}

/**
 * 完成结算（管理员操作）
 * @param {number} id 结算单ID
 */
export function completeSettlement(id) {
    return request({
        url: `/mall/shopSettlement/complete/${id}`,
        method: 'post'
    })
}

/**
 * 取消结算单（管理员操作）
 * @param {number} id 结算单ID
 * @param {string} remark 取消原因
 */
export function cancelSettlement(id, remark) {
    return request({
        url: `/mall/shopSettlement/cancel/${id}`,
        method: 'post',
        params: {remark}
    })
}
