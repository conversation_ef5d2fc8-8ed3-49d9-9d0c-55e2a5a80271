import request from '../../utils/request';

/**
 * 创建角色
 * @param data
 */

export const save = (data: Object) => {
    return request({
        url: '/system/role',
        method: 'post',
        data: data,
    });
};

//查询上级菜单
export const getMenuTreeList = () => {
    return request({
        url: '/system/menu/tree',
        method: 'get'
    })
};


//根据角色id查询上级菜单
export const getTreeList = (roleId) => {
    return request({
        url: '/system/menu/roleMenuTreeSelect/' + roleId,
        method: 'get',
        data: roleId
    })
}

//查询角色数据
export const getRoleList = () => {
    return request({
        url: '/system/role/list',
        method: 'get',
    })
}


//通过角色id值查询角色信息
export const getRoleInformation = (roleId) => {
    return request({
        url: '/system/role/' + roleId,
        method: 'get'
    })
}

//修改角色保存
export const getRoleUpdate = (data) => {
    return request({
        url: '/system/role',
        method: 'put',
        data: data
    })
}
