import request from '../../utils/request';
// import other from '../../utils/other';

/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

/**
 * 通过和审核数据
 * @param data
 */

export const getThroughList = (data: Object, index) => {
    return request({
        url: '/mall/config/' + index,
        method: 'post',
        data: data
    });
};

/**
 * 通过和审核数据
 * @param data
 */

export const configurationData = (index) => {
    return request({
        url: '/mall/config/key/' + index,
        method: 'get'
    });
};





