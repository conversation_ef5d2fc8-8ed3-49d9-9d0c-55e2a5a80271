import request from '../../utils/request';
// import other from '../../utils/other';

/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

/**
 * 通过和审核数据
 * @param data
 */

export const getThroughList = (data: Object) => {
    return request({
        url: '/mall/product/audit',
        method: 'post',
        data: data,
    });
};

/**
 * 通过和审核数据
 * @param data
 */

export const batchAudit = (data: Object) => {
    return request({
        url: '/mall/product/batchAudit',
        method: 'post',
        data: data,
    });
};





