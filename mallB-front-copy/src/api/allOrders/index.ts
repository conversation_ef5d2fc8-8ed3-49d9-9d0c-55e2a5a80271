import request from '../../utils/request';
// import other from '../../utils/other';
/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

/**
 * 查询订单数据列表
 * @param data
 */

export const getOrderList = (data) => {
    return request({
        url: '/mall/order/list',
        method: 'get',
        params: data
    });
};

/**
 * 导出订单数据
 * @param data
 */
export const exportOrderList = (data) => {
    return request({
        url: '/mall/order/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    });
};

/**
 * 查询订单数据列表
 * @param data
 */
export const UpdateOrderAddress = (data) => {
    return request({
        url: '/mall/order/updateAddress',
        method: 'put',
        data: data
    });
};

/**
 * 获取订单详情
 * @param orderNo 订单编号
 */
export const getOrderDetail = (orderNo) => {
    return request({
        url: `/mall/order/${orderNo}`,
        method: 'get'
    });
};





