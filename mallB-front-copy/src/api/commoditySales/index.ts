import request from '../../utils/request';
// import other from '../../utils/other';

/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

/**
 * 查询代销数据
 * @param data
 */
export const getDaiXiaoProductList = (data: Object) => {
    return request({
        url: '/mall/product/getDaiXiaoProductList',
        method: 'get',
        params: data
    });
};


/**
 * 添加代销能代销的商品
 */
export const insertConsignmentProduct = (productId: String) => {
    return request({
        url: '/mall/consignment/product/insert',
        method: 'post',
        params: {
            productId
        }
    });
};

/**
 * 取消代销能代销的商品
 */
export const cancelConsignmentProduct = (productId: String) => {
    return request({
        url: '/mall/consignment/product/cancel',
        method: 'put',
        params: {
            productId
        }
    });
};





