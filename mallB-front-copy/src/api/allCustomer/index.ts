import request from '../../utils/request';
// import other from '../../utils/other';
/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

/**
 * 查询商家信息列表
 * @param data
 */

export const getShopList = (data) => {
    return request({
        url: '/mall/shop/list',
        method: 'get',
        params: data
    });
};

/**
 * 禁用店铺
 * @param data
 */

export const disableEnableFun = (data) => {
    return request({
        url: '/mall/shop/editStatus',
        method: 'post',
        data: data
    });
};

/**
 * 获取商家详情
 * @param id 商家ID
 */
export const getShopDetail = (id) => {
    return request({
        url: `/mall/shop/${id}`,
        method: 'get'
    });
};

/**
 * 导出商家数据
 * @param data 查询参数
 */
export const exportShopData = (data) => {
    return request({
        url: '/mall/shop/export',
        method: 'get',
        params: data,
        responseType: 'blob'
    });
};


