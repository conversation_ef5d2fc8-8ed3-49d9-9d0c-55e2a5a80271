import request from '../../utils/request';
// import other from '../../utils/other';

/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

/**
 * 创建菜单
 * @param data
 */

export const save = (data: Object) => {
    return request({
        url: '/system/menu',
        method: 'post',
        data: data,
    });
};

//菜单列表
// /system/menu/list


//查询菜单
export const getMenuList = (data: Object) => {
    return request({
        url: '/system/menu/tree',
        method: 'get',
        params: data
    })
}

//查询上级菜单
export const getTreeList = (data: Object) => {
    return request({
        url: '/system/menu/tree',
        method: 'get',
        data: data
    })
}

// 修改菜单  1.先查询一条菜单数据 2.查询上一级菜单数据 3.保存菜单树
export const getIndependenceMenu = (menuId) => {
    return request({
        url: '/system/menu/' + menuId,
        method: 'get'
    })
}

// 保存菜单
export const updateMenu = (data: Object) => {
    return request({
        url: '/system/menu',
        method: 'put',
        data: data
    })
}

//删除菜单
export const getDeleteMenu = (menuId) => {
    return request({
        url: '/system/menu/' + menuId,
        method: 'delete'
    })
}
