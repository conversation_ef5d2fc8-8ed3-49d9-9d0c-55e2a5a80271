import request from '../../utils/request';
// import other from '../../utils/other';

/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

/**
 * 查询权限功能
 * @path data
 */

export const activateThepermission = (index) => {
    return request({
        url: '/mall/config/key/' + index,
        method: 'get'
    });
};

/**
 * 开通权限功能
 * @path data
 */
export const openpermission = (type) => {
    return request({
        url: '/virtual/create/' + type,
        method: 'post'
    });
};

//权限数据
// getVirtualOrderDetail
export const getVirtualOrderDetail = (orderNo) => {
    return request({
        url: '/virtual/getVirtualOrderDetail/' + orderNo,
        method: 'get'
    });
};


