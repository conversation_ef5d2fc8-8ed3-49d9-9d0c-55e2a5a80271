import request from '../../utils/request'

//渲染图表数据
export const getQuantizationRate = (data) => {
    return request({
        url: '/mall/QuantizationRate/getQuantizationRate',
        method: 'get',
        params: data
    })
}
//渲染图表数据
export const getUserQuantizationValue = () => {
    return request({
        url: '/mall/QuantizationRate/getUserQuantizationValue',
        method: 'get'
    })
}

// 量化值转换为量化
export const convertToFaith = (amount) => {
    return request({
        url: '/mall/shopValue/convertToFaith',
        method: 'post',
        params: {amount}
    })
}

// 量化转换为平台促销金
export const convertToGold = (amount) => {
    return request({
        url: '/mall/shopValue/convertToGold',
        method: 'post',
        params: {amount}
    })
}


// 获取量化转化统计数据（分页版本）
export const getFaithStatisticsPage = (params) => {
    return request({
        url: '/mall/shopValue/statistics/faith/page',
        method: 'get',
        params
    })
}
// 导出量化进化明细
export const exportFaithRecords = (params) => {
    return request({
        url: '/mall/shopValue/export/faith',
        method: 'get',
        params,
        responseType: 'blob'
    })
}

// 获取量化值转化统计数据（分页版本）
export const getQuantificationStatisticsPage = (params) => {
    return request({
        url: '/mall/shopValue/statistics/quantification/page',
        method: 'get',
        params
    })
}

// 导出量化值进化明细
export const exportQuantificationRecords = (params) => {
    return request({
        url: '/mall/shopValue/export/quantification',
        method: 'get',
        params,
        responseType: 'blob'
    })
}


// 获取量化转化统计数据（分页版本）
export const getAdminFaithStatisticsPage = (params) => {
    return request({
        url: '/mall/shopValue/statistics/adminFaith',
        method: 'get',
        params
    })
}
// 获取量化值转化统计数据（分页版本）
export const getAdminQuantificationStatisticsPage = (params) => {
    return request({
        url: '/mall/shopValue/statistics/adminQuantification',
        method: 'get',
        params
    })
}
export const yesterdayAmount = (data) => {
    return request({
        url: '/virtual/yesterdayAmount',
        method: 'get',
        params: data
    })
}

// 获取指定日期量化值进化统计
export const getDateQuantificationStats = (date) => {
    return request({
        url: '/mall/shopValue/statistics/date/quantification',
        method: 'get',
        params: {date}
    })
}

// 获取指定日期量化进化统计
export const getDateFaithStats = (date) => {
    return request({
        url: '/mall/shopValue/statistics/date/faith',
        method: 'get',
        params: {date}
    })
}
