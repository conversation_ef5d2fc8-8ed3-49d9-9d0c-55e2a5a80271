import request from '../../utils/request';

//商品列表
export const getProductList = (data) => {
    return request({
        url: '/mall/product/list',
        method: 'get',
        params: data
    })
}

//商品上架
export const getSale = (productId) => {
    return request({
        url: '/mall/product/publish/' + productId,
        method: 'put'
    })
}
//批量上架
export const getSales = (data) => {
    return request({
        url: '/mall/product/batchPublish',
        method: 'put',
        data: data
    })
}
//下架
export const getshelves = (productId) => {
    return request({
        url: '/mall/product/unpublish/' + productId,
        method: 'put'
    })
}
//批量下架
export const batchgetshelves = (data) => {
    return request({
        url: '/mall/product/batchUnpublish',
        method: 'put',
        data: data
    })
}
//删除
export const getdelete = (ids) => {
    return request({
        url: '/mall/product/' + ids,
        method: 'delete'
    })
}

//导出商品列表
export const exportProductList = (params) => {
    return request({
        url: '/mall/product/export',
        method: 'get',
        params: params,
        responseType: 'blob',
        headers: {
            'Accept': 'application/vnd.ms-excel'
        },
        timeout: 60000 // 增加超时时间为60秒
    })
}

//获取商品详情
export const getProductDetail = (productId) => {
    return request({
        url: '/mall/product/' + productId,
        method: 'get'
    })
}
