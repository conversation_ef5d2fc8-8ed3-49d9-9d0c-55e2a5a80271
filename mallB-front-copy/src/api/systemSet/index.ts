import request from '../../utils/request'

const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';
// 获取下拉企业信息
export const getAllCooperateEnterprise = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/getAllCooperateEnterprise',
        method: 'get',
        data
    })
}
//企业开关
export const setEnterpriseDataSwitch = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/setEnterpriseDataSwitch',
        method: 'POST',
        data
    })
}
// 获取企业信息
export const getEnterpriseDataSet = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/getEnterpriseDataSet',
        method: 'get',
        data
    })
}
//新增企业信息设置
// export const  addEnterpriseDataSet = (name)=>{
//     return request({
//         url:'/mall-project/cooperateEnterprise/addEnterpriseDataSet',
//         method:'post',
//        data:{name}
//     })
// }
// 添加企业
export const createNewEnterprise = (enterpriseId) => {
    return request({
        url: '/mall-project/cooperateEnterprise/addEnterpriseDataSet',
        method: 'post',
        data: {enterpriseId: enterpriseId}
    });
};


// 删除企业
export const deleteNewEnterprise = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/deleteEnterpriseDataSet',
        method: 'delete',
        data: data
    });
};

//删除企业交易数据
export const deleteNewEnterpriseDataName = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/deleteEnterpriseDataName',
        method: 'delete',
        data
    })
}
//查询企业交易数据
export const getEnterpriseDataName = (params) => {
    return request({
        url: '/mall-project/cooperateEnterprise/getEnterpriseDataName',
        method: 'get',
        params
    })
}
//新增企业交易数据
export const addEnterpriseDataName = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/addEnterpriseDataName',
        method: 'put',
        data: data
    })
}
//查看自定义常数
export const getCustomConstants = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/getCustomConstants',
        method: 'get',
        data
    })
}
//查询自定义常数
export const setCustomConstants = (customConstants) => {
    return request({
        url: '/mall-project/cooperateEnterprise/setCustomConstants',
        method: 'put',
        data: customConstants
    })
}
//获取下拉已添加各企业系统每日更新总累计量化数企业名称
export const getEnterpriseQuantitySet = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/getEnterpriseQuantitySet',
        method: 'get',
        data
    })
}
//添加各企业系统每日更新总累计量化数企业名称
export const addEnterpriseQuantity = (enterpriseId) => {
    return request({
        url: '/mall-project/cooperateEnterprise/addEnterpriseQuantitySet',
        method: 'put',
        data: {enterpriseId: enterpriseId}
    })
}
//删除已添加各企业系统每日更新总累计量化数企业名称
export const deleteEnterpriseQuantitySet = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/deleteEnterpriseQuantitySet',
        method: 'delete',
        data
    })
}
//查询各企业系统每日更新总累计量化数交易数据
export const getEnterpriseQuantityName = (params) => {
    return request({
        url: '/mall-project/cooperateEnterprise/getEnterpriseQuantityName',
        method: 'get',
        params
    })
}
//新增各企业系统每日更新总累计量化数交易数据
export const addEnterpriseQuantityName = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/addEnterpriseQuantityName',
        method: 'put',
        data
    })
}
//删除各企业系统每日更新总累计量化数交易数据
export const deleteEnterpriseQuantityName = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/deleteEnterpriseQuantityName',
        method: "delete",
        data
    })
}
//时间，id号，功能数据，今日功能数据，今日总功能数据
// 各企业系统每日更新总累计量化数 开、关
export const setQuantityDataSwitch = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/setQuantityDataSwitch',
        method: 'post',
        data
    })
}
//自定义常数开、关
export const setCustomConstantsSwitch = (data) => {
    return request({
        url: '/mall-project/cooperateEnterprise/setCustomConstantsSwitch',
        method: 'post',
        data
    })
}
