import request from '../../utils/request';

/**
 * 获取省份列表
 */
export const getProvinces = () => {
    return request({
        url: '/mall/area/provinces',
        method: 'get'
    });
};

/**
 * 获取城市列表
 * @param provinceCode 省份编码
 */
export const getCities = (provinceCode: string) => {
    return request({
        url: `/mall/area/cities/${provinceCode}`,
        method: 'get'
    });
};

/**
 * 获取区县列表
 * @param cityCode 城市编码
 */
export const getDistricts = (cityCode: string) => {
    return request({
        url: `/mall/area/districts/${cityCode}`,
        method: 'get'
    });
};

/**
 * 获取街道列表
 * @param districtCode 区县编码
 */
export const getTowns = (districtCode: string) => {
    return request({
        url: `/mall/area/towns/${districtCode}`,
        method: 'get'
    });
};

/**
 * 根据父级ID获取区域列表
 * @param data
 */
export const getAreasByParentId = (pId) => {
    return request({
        url: `/mall/area/children/${pId}`,
        method: 'get'
    });
};
