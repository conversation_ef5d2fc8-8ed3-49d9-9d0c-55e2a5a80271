import request from '../../utils/request'

/**
 * 获取商城用户列表
 * @param params 查询参数
 */
export function getUserList(params: {
    username?: string;
    nickname?: string;
    phone?: string;
    status?: string;
    startTime?: string;
    endTime?: string;
    pageNum: number;
    pageSize: number;
}) {
    return request({
        url: '/mall/user/list',
        method: 'get',
        params
    })
}

/**
 * 获取用户详细信息
 * @param userId 用户ID
 */
export function getUserDetail(userId: string | number) {
    return request({
        url: `/mall/user/${userId}`,
        method: 'get'
    })
}

/**
 * 修改用户状态
 * @param userId 用户ID
 * @param status 状态
 */
export function updateUserStatus(userId: string | number, status: string) {
    return request({
        url: '/mall/user/updateStatus',
        method: 'put',
        data: {userId, status}
    })
}

/**
 * 重置用户密码
 * @param userId 用户ID
 */
export function resetUserPassword(userId: string | number) {
    return request({
        url: `/mall/user/resetPwd/${userId}`,
        method: 'put'
    })
}
