import request from '@/utils/request'

// 查询用户实名认证列表
export function listUserRealName(query) {
    return request({
        url: '/mall/userRealName/list',
        method: 'get',
        params: query
    })
}

// 查询用户实名认证详细
export function getUserRealName(id) {
    return request({
        url: '/mall/userRealName/' + id,
        method: 'get'
    })
}

// 审核用户实名认证
export function auditUserRealName(data) {
    return request({
        url: '/mall/userRealName/audit',
        method: 'post',
        data: data
    })
}

// 删除用户实名认证
export function delUserRealName(id) {
    return request({
        url: '/mall/userRealName/' + id,
        method: 'delete'
    })
}
