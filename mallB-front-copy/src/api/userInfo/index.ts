import request from '../../utils/request';
import {Session} from '../../utils/storage';

/**
 * 获取用户信息
 */
export const getUserInfo = () => {
    const basicAuth = Session.get('basicAuth');
    return request({
        url: '/getInfo',
        headers: {
            skipToken: true,
            Authorization: 'Bearer' + basicAuth,
            'Content-Type': 'application/json; charset=UTF-8',
        },
        method: 'get',
        params: {
            token: Session.getToken()
        }
    });
};
