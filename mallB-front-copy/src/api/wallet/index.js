import request from '@/utils/request'

/**
 * 获取当前商家钱包信息
 */
export function getWalletInfo() {
    return request({
        url: '/mall/shop/wallet/info',
        method: 'get'
    })
}

/**
 * 钱包入账
 * @param {Object} data 入账数据
 */
export function walletIncome(data) {
    return request({
        url: '/mall/shop/wallet/income',
        method: 'post',
        data
    })
}

/**
 * 钱包出账
 * @param {Object} data 出账数据
 */
export function walletExpense(data) {
    return request({
        url: '/mall/shop/wallet/expense',
        method: 'post',
        data
    })
}
