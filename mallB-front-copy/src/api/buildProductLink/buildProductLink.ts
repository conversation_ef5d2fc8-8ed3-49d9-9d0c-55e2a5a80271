import request from '../../utils/request';
// import other from '../../utils/other';

/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

/**
 * 查询商品链接
 * @param data
 */

export const getShopDaiXiaoProductList = (data: Object) => {
    return request({
        url: '/mall/product/getShopDaiXiaoProductList',
        method: 'get',
        params: data
    });
};

/**
 * 生成商品链接
 * @param data
 */

export const editDaiXiaoProductUrl = (productId) => {
    return request({
        url: '/mall/product/editDaiXiaoProductUrl/' + productId,
        method: 'put'
    });
};

export const cancelConsignment = (productId: String) => {
    return request({
        url: '/mall/product/cancel/consignmentProduct',
        method: 'put',
        params: {
            productId
        }
    });
};
