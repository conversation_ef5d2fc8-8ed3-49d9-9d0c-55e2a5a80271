import request from '../../utils/request';

/**
 * 查询订单数据功能
 * @param data 包含分页参数和搜索条件
 * @returns Promise
 */
export const getLoginVirtualOrder = (data: {
    pageNum: number;
    pageSize: number;
    productName?: string;
    orderNo?: string;
    status?: string;
}) => {
    return request({
        url: '/virtual/getLoginVirtualOrder',
        method: 'get',
        params: data
    });
};

/**
 * 取消虚拟订单数据
 * @param orderNo 订单编号
 * @returns Promise
 */
export const cancelTheOrder = (orderNo: string) => {
    return request({
        url: '/virtual/cancel/' + orderNo,
        method: 'post'
    });
};

/**
 * 删除虚拟订单数据
 * @param orderNo 订单编号
 * @returns Promise
 */
export const deleteOrder = (orderNo: string) => {
    return request({
        url: '/virtual/' + orderNo,
        method: 'delete'
    });
};

/**
 * 删除虚拟订单数据
 * @param orderNo 订单编号
 * @returns Promise
 */
export const payment = (data) => {
    return request({
        url: '/virtual/payment',
        method: 'post',
        data: data
    });
};

/**
 * 获取代销权限信息
 * @returns Promise
 */
export const getConsignmentPermissionInfo = () => {
    return request({
        url: '/virtual/consignmentPermissionInfo',
        method: 'get'
    });
};

/**
 * 创建虚拟订单
 * @param data 订单数据
 * @returns Promise
 */
export const createVirtualOrder = (data: {
    payType: number;
    count: number;
}) => {
    return request({
        url: '/virtual/create/' + data.payType,
        method: 'post',
        data: {count: data.count}
    });
};

