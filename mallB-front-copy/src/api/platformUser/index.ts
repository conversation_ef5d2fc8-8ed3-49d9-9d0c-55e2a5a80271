import request from '../../utils/request';

// 查询用户数据
export const getUserList = (data) => {
    return request({
        url: '/system/user/list',
        method: 'get',
        params: data,
    })
}

/**
 * 创建用户
 * @param data
 */

export const save = (data: Object) => {
    return request({
        url: '/system/user',
        method: 'post',
        data: data,
    });
};


// start   修改用户页面

//根据id值查询用户信息
export const getUserDetail = (userId) => {
    return request({
        url: '/system/user/' + userId,
        method: 'get'
    })
}


//保存修改用户信息
export const saveUpdateUser = (data: Object) => {
    return request({
        url: '/system/user',
        method: 'put',
        data: data
    })
}

//删除用户信息
export const deleteUser = (userId) => {
    return request({
        url: '/system/user/' + userId,
        method: 'delete'
    })
}

//查询上级菜单
export const getMenuTreeList = () => {
    return request({
        url: '/system/menu/tree',
        method: 'get'
    })
}

// 重置密码
export const resetUserPwd = (data) => {
    return request({
        url: '/system/user/resetPwd',
        method: 'put',
        data: data
    })
}

//查询授权数据
// export const getMenuTreeList=()=>{
// 	return request({
// 		url:'/system/menu/tree',
// 		method:'get'
// 	})
// }
