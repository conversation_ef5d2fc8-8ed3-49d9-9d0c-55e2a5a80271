import {createApp} from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'
import axios from './http/index'
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
// 引入echarts
import * as echarts from "echarts";
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 1. 先创建 app
const app = createApp(App)
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
// 全局挂载 echarts
app.config.globalProperties.$echarts = echarts
// 2. 然后再配置全局属性和插件
app.config.globalProperties.$api = axios
// app.config.globalProperties.$axios = axios
//app.use(axios)  // 如果 axios 是 Vue 插件，否则可能需要手动封装
app.use(router)
app.use(ElementPlus)
app.use(echarts)
// 3. 最后挂载
app.mount('#app')
