<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="rightBox">
            <div class="head">
                <div class="product-row">
                    <span class="pro">{{ state.permission1.name }}</span>
                    <span class="price">
              原价:<span class="original-price">￥{{ state.permission1.originalPrice }}</span>/月
            </span>
                    <span class="price">
              <span>现价：￥{{ state.permission1.discountPrice }}</span>
            </span>
                    <span class="price">
              <span>代销数量：{{ state.permission1.authorityOne }}</span>
            </span>
                    <button v-if="state.jurisdiction==='0'" class="open-btn"
                            @click="openPermissionOne(1,state.permission1)">开通
                    </button>
                    <button v-else class="open-btn-gray">已开通</button>
                    <span v-if="state.jurisdictionExpireTime && state.jurisdiction == 1">过期时间：{{
                            state.jurisdictionExpireTime
                        }}</span>
                </div>
                <div class="arrows">
            <span class="longString">
                <span class="longStringBottom">V</span>
            </span>
                </div>
                <div class="product-row">
                    <span class="pro">{{ state.permission2.name }}</span>
                    <span class="price">
              原价:<span class="original-price">￥{{ state.permission2.originalPrice }}</span>/月
            </span>
            <span class="price">
              <span>现价：￥{{ state.permission2.discountPrice }}</span>
            </span>
            <span class="price">
              <span>代销数量：{{ state.permission2.authorityTwo }}</span>
            </span>
                    <button v-if="(state.jurisdiction !=='2' && state.jurisdiction !=='3')" class="open-btn"
                            @click="openPermissionTwo(2,state.permission2)">开通
                    </button>
                    <button v-else class="open-btn-gray">已开通</button>
                    <span v-if="state.jurisdictionExpireTime  && state.jurisdiction == 2">过期时间：{{
                            state.jurisdictionExpireTime
                        }}</span>
                </div>
                <div class="arrows">
                <span class="longString">
                    <span class="longStringBottom">V</span>
                </span>
                </div>
                <div class="product-row">
                    <span class="pro">{{ state.permission3.name }}</span>
                    <span class="price">
              原价：<span class="original-price">￥{{ state.permission3.originalPrice }}</span>/月
            </span>
                    <span class="price">
              <span>现价：￥{{ state.permission3.discountPrice }}</span>
            </span>

            <span class="price">
              <span>代销数量：{{ state.permission3.authorityThree }}</span>
            </span>
                    <button v-if="state.jurisdiction !== '3'" class="open-btn"
                            @click="openPermissionThree(3,state.permission3)">开通
                    </button>
                    <button v-else class="open-btn-gray">已开通</button>
                    <span v-if="state.jurisdictionExpireTime  && state.jurisdiction == 3">过期时间：{{
                            state.jurisdictionExpireTime
                        }}</span>
                </div>
            </div>
            <!--            <div class="bottom">
                            &lt;!&ndash; <span class="pay">实付金额:￥50</span> &ndash;&gt;
                            <div class="payment-section">
                                <button class="sure-btn"
                                >订单生成
                                    <div class="agreement-check">
                                        <input id="agreement" v-model="state.agreed" type="checkbox">
                                        <label for="agreement">我已阅读并同意<a class="agreement-link" href="/service-agreement">《服务协议》</a></label>
                                    </div>
                                </button>
                            </div>
                        </div>-->
            <div class="bottom">
                <div class="payment-section">
                    <button
                        :disabled="!state.agreed"
                        class="sure-btn"
                        @click="handleSubmit"
                    >
                        订单生成
                        <div class="agreement-check">
                            <el-checkbox v-model="state.agreed" class="agree-checkbox">
                                我已阅读并同意
                            </el-checkbox>
                            <a
                                class="agreement-link"
                                @click.prevent="showAgreementDialog"
                            >
                                《服务协议》
                            </a>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- 协议弹窗 -->
        <el-dialog
            v-model="state.agreementDialogVisible"
            custom-class="agreement-dialog"
            title="服务协议"
            width="50%"
        >
            <div
                v-loading="state.agreementLoading"
                class="agreement-content"
            >
                <div
                    v-if="state.agreementHtml"
                    class="content-box"
                    v-html="state.agreementHtml"
                ></div>
                <el-empty v-else description="暂无协议内容"/>
            </div>
            <template #footer>
                <el-button
                    type="primary"
                    @click="agreeAndClose"
                >
                    同意并关闭
                </el-button>
            </template>
        </el-dialog>


        <PaymentDialog
            v-model:visible="state.dialogTableVisible"
            :orderData="state.tableData1"
            @cancel-success="dialogAddCategoryReturn"
        />
    </div>
</template>
<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {nextTick, onMounted, reactive} from 'vue'
import {jurisdiction} from '../../stores/productSellApply'
import {ElMessage} from 'element-plus'
import {Session} from '../../utils/storage'
import PaymentDialog from '../../components/PaymentDialog.vue'

import {getAgreementContent} from '@/api/common/protocolCommon'

const agreementId = '1945323476291399681'


const state = reactive({
    agreed: false,
    // 新增协议弹窗
    agreementDialogVisible: false,
    agreementHtml: '',
    agreementLoading: false,
    // 原有
    jurisdiction: "0",  //0未开通,1已开通,
    jurisdictionExpireTime: null,
    permission1: {
        name: "权限一",
        originalPrice: null,
        discountPrice: null
    },
    permission2: {
        name: "权限二",
        originalPrice: null,
        discountPrice: null
    },
    permission3: {
        name: "权限三",
        originalPrice: null,
        discountPrice: null
    },
    tableData1: [],
    buttonList: [],
    amount: null,
    dialogTableVisible: false,
    orderNo: null
})


// 显示协议弹窗
const showAgreementDialog = async () => {
    state.agreementDialogVisible = true
    if (!state.agreementHtml) {
        state.agreementLoading = true
        try {
            const res = await getAgreementContent(agreementId)
            if (res.code === 200) {
                state.agreementHtml = res.data.value
            } else {
                ElMessage.error(res.msg || '获取协议内容失败')
            }
        } catch (error) {
            ElMessage.error('网络错误，请稍后重试')
        } finally {
            state.agreementLoading = false
        }
    }
}

// 同意协议并关闭
const agreeAndClose = () => {
    state.agreed = true
    state.agreementDialogVisible = false
}

// 提交
const handleSubmit = () => {
    if (!state.agreed) {
        ElMessage.warning('请先阅读并同意服务协议')
        return
    }

}


//新增分类数据弹窗
const router = useRouter()
const handleButtonClick = (item) => {
    router.push(item.component)
}
//取消订单
const dialogAddCategoryReturn = async () => {
    state.dialogTableVisible = false
    getList()
}
//开通权限一
const openPermissionOne = async (item, data) => {
    if (state.agreed) {
        let result = await jurisdiction().Openpermission(1);
        if (result.code == 200) {
            let orderNo = result.data
            state.orderNo = result.data
            jurisdiction().GetVirtualOrderDetail(orderNo).then(result => {
                state.dialogTableVisible = true
                state.tableData1 = result.data
                state.amount = result.data.amount
            })
        }
    } else {
        ElMessage.warning("请勾选服务协议")
    }
}
//开通权限二
const openPermissionTwo = async (item, data) => {
    if (state.jurisdiction == '0') {
        ElMessage.error('请开通权限一')
        return false
    }
    if (state.agreed) {
        let result = await jurisdiction().Openpermission(2);
        if (result.code == 200) {
            let orderNo = result.data
            state.orderNo = result.data
            jurisdiction().GetVirtualOrderDetail(orderNo).then(result => {
                state.dialogTableVisible = true
                state.tableData1 = result.data
                state.amount = result.data.amount
            })
        }
    } else {
        ElMessage.warning("请勾选服务协议")
    }
}

//开通权限三
const openPermissionThree = async (item) => {
    if (state.jurisdiction == '0') {
        ElMessage.error('请开通权限一')
        return false
    } else if (state.jurisdiction == '1') {
        ElMessage.error('请开通权限二')
        return false
    }
    if (state.agreed) {
        let result = await jurisdiction().Openpermission(3);
        if (result.code == 200) {
            let orderNo = result.data
            state.orderNo = result.data
            jurisdiction().GetVirtualOrderDetail(orderNo).then(result => {
                state.dialogTableVisible = true
                state.tableData1 = result.data
                state.amount = result.data.amount
            })
        }
    } else {
        ElMessage.warning("请勾选服务协议")
    }
}
const getList = async () => {
    let result = await jurisdiction().GetActivateThepermission(1);
    let data = result.data
    state.jurisdiction = data.jurisdiction
    state.jurisdictionExpireTime = data.jurisdictionExpireTime
    state.permission1.originalPrice = data.permission1.originalPrice
    state.permission1.discountPrice = data.permission1.discountPrice
    state.permission1.authorityOne = data.authorityOne
    state.permission2.originalPrice = data.permission2.originalPrice
    state.permission2.discountPrice = data.permission2.discountPrice
    state.permission2.authorityTwo = data.authorityTwo
    state.permission3.originalPrice = data.permission3.originalPrice
    state.permission3.discountPrice = data.permission3.discountPrice
    state.permission3.authorityThree = data.authorityThree
}
onMounted(() => {
    getList()
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })
})
// 取消订单成功的处理函数
const handleCancelSuccess = () => {
    // 重置相关状态或刷新数据
    state.dialogTableVisible = false;
}
</script>
<style lang="scss" scoped>
.cancelTheOrder {
    margin-top: 20px;
    text-align: center;
    margin-left: 300px;
}

.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.amount {
    padding-top: 4px;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 30px);
    overflow-y: scroll;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.rightBox {
    width: 1343px;
    height: 664px;
    margin-top: 37px;
    margin-left: 254px;
    border: 1px solid #3A58CF;
    display: flex;
    flex-direction: column;
}

.head {
    width: 100%;
    padding: 0 20px;
    margin-top: 37px; /* 添加顶部间距 */
}

.product-row {
    display: flex;
    align-items: center;
    gap: 30px;
    margin-top: 37px; /* 添加顶部间距 */
}

.pro {
    font-size: 30px;
    color: #fff;
    background-color: #3A58CF;
    padding: 10px 20px;
    border-radius: 4px;
    min-width: 200px;
    text-align: center;
}

.arrows {
    margin-top: 10px;
    width: 200px;
    text-align: center;
}

.longString {
    width: 2px;
    height: 30px;
    background-color: #b5b5b5;
    display: inline-block;
    position: relative;
}

.longStringBottom {
    color: #b5b5b5;
    display: block;
    position: absolute;
    top: 22px;
    left: -4px;
}

.open-btn-gray {
    width: 142px;
    height: 52px;
    background-color: #b5b5b5;
    color: #fff;
    font-size: 30px;
    border: none;
    border-radius: 4px;
}

.price, .discount-price {
    font-size: 24px;
    color: #fff;
    background-color: #3A58CF;
    padding: 10px 20px;
    border-radius: 4px;
    white-space: nowrap;
}

.original-price {
    text-decoration: line-through;
}

.open-btn {
    width: 142px;
    height: 52px;
    background-color: #3A58CF;
    color: #fff;
    font-size: 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
        background-color: #2a48bf;
    }
}

.bottom {
    width: 100%;
    height: 266px;
    margin-top: 39px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .pay {
        font-size: 24px;
        font-weight: bold;
    }

    .payment-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .sure-btn {
        width: 267px;
        height: 85px;
        background-color: #FF8D1A;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 24px;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
            background-color: #e67e17;
        }
    }

    .agreement-check {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        margin-left: 16px;
        margin-top: -20px; /* 新增：向上移动5像素 */

        :deep(.el-checkbox__label) {
            transform: translateY(-2px); /* 微调文字垂直位置 */
        }

        .agreement-link {
            color: #3A58CF;
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}


/* 新增或修改的样式 */
.bottom {
    .sure-btn {
        position: relative;
        padding-top: 15px;

        &:disabled {
            background-color: #FFB347;
            cursor: not-allowed;
        }
    }

    .agreement-check {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: -7px;

        .agree-checkbox {
            margin-right: 5px;

            :deep(.el-checkbox__label) {
                color: #666;
                font-size: 14px;
            }
        }

        .agreement-link {
            color: #3A58CF;
            text-decoration: none;
            margin-left: 5px;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}

/* 协议弹窗样式 */
:deep(.agreement-dialog) {
    .el-dialog {
        max-height: 80vh;
        display: flex;
        flex-direction: column;

        &__body {
            padding: 0;
            flex: 1;
            overflow: hidden;
        }
    }

    .agreement-content {
        height: 60vh;
        overflow-y: auto;
        padding: 20px;

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(144, 147, 153, 0.3);
            border-radius: 3px;
        }

        &::-webkit-scrollbar-track {
            background-color: rgba(144, 147, 153, 0.1);
        }

        .content-box {
            p {
                margin: 0.5em 0;
                line-height: 1.6;
            }
        }
    }
}


</style>
