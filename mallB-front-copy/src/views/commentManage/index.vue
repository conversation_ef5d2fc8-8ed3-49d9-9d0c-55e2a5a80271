<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="content-area">
            <div class="headerBox">
                <div class="main">
                    <div class="search-area">
                        <div class="search-row">
                            <div class="search-item">
                                <span class="label">商品名称</span>
                                <el-input v-model="searchForm.productName" class="input"
                                          placeholder="请输入商品名称"></el-input>
                            </div>
                            <el-select v-model="searchForm.status" class="status-select" placeholder="评论状态">
                                <el-option label="全部评价" value=""></el-option>
                                <el-option label="待审核" value="0"></el-option>
                                <el-option label="已通过" value="1"></el-option>
                                <el-option label="已拒绝" value="2"></el-option>
                            </el-select>
                        </div>
                        <div class="search-row">
                            <div class="search-item">
                                <span class="label">开始时间</span>
                                <el-date-picker
                                    v-model="searchForm.beginTime"
                                    class="date-input"
                                    format="YYYY-MM-DD"
                                    placeholder="选择开始日期"
                                    type="date"
                                    value-format="YYYY-MM-DD"
                                ></el-date-picker>
                                <span class="to">至</span>
                                <span class="label">结束时间</span>
                                <el-date-picker
                                    v-model="searchForm.endTime"
                                    class="date-input"
                                    format="YYYY-MM-DD"
                                    placeholder="选择结束日期"
                                    type="date"
                                    value-format="YYYY-MM-DD"
                                ></el-date-picker>
                            </div>
                        </div>
                        <div class="search-button">
                            <el-button :icon="Search" class="custom-search-btn" type="primary" @click="handleSearch">
                                搜索
                            </el-button>
                            <el-button plain @click="handleReset">重置</el-button>
                        </div>
                    </div>
                </div>
            </div>

            <div v-loading="loading" class="comment-table-wrapper">
                <el-table
                    :data="commentList"
                    :header-cell-style="{ backgroundColor: '#3A58CF', color: 'white' }"
                    style="width: 100%"
                >
                    <el-table-column type="expand">
                        <template #default="props">
                            <div class="comment-detail">
                                <div class="comment-content">
                                    <p><strong>评价内容：</strong>{{ props.row.content }}</p>
                                    <div v-if="props.row.img" class="comment-images">
                                        <el-image
                                            v-for="(url, index) in props.row.img.split(',')"
                                            :key="index"
                                            :preview-src-list="props.row.img.split(',').map(img => getImageUrl(img))"
                                            :src="getImageUrl(url)"
                                            class="comment-image"
                                            fit="cover"
                                        ></el-image>
                                    </div>
                                    <div v-if="props.row.video" class="comment-video">
                                        <video :src="getImageUrl(props.row.video)" class="comment-video-player"
                                               controls></video>
                                    </div>

                                    <div v-if="props.row.reply" class="merchant-reply">
                                        <p class="reply-title"><strong>商家回复：</strong></p>
                                        <p class="reply-content">{{ props.row.reply }}</p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column label="商品信息" min-width="20%" prop="productName">
                        <template #default="scope">
                            <div class="product-info">
                                <div class="product-name">{{ scope.row.productName || '未知商品' }}</div>
                                <div class="product-rating">
                                    <el-rate v-model="scope.row.level" disabled show-score></el-rate>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column label="评价者" min-width="15%" prop="userName">
                        <template #default="scope">
                            <div class="user-info">
                                <div class="user-name">{{ scope.row.userName || '匿名用户' }}</div>
                                <div v-if="scope.row.isShow === '1'" class="is-anonymous">(匿名评价)</div>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column label="评价时间" min-width="15%" prop="createTime"></el-table-column>

                    <el-table-column label="状态" min-width="15%" prop="status">
                        <template #default="scope">
                            <el-tag v-if="scope.row.status === '0'" type="warning">待审核</el-tag>
                            <el-tag v-else-if="scope.row.status === '1'" type="success">已通过</el-tag>
                            <el-tag v-else-if="scope.row.status === '2'" type="danger">已拒绝</el-tag>
                            <el-tag v-else type="info">未知状态</el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column label="操作" min-width="20%">
                        <template #default="scope">
                            <div class="action-buttons">
                                <el-button
                                    v-if="scope.row.status === '0'"
                                    size="small"
                                    type="success"
                                    @click="handleApprove(scope.row.id)">通过
                                </el-button>
                                <el-button
                                    v-if="scope.row.status === '0'"
                                    size="small"
                                    type="warning"
                                    @click="handleReject(scope.row.id)">拒绝
                                </el-button>
                                <el-button
                                    v-if="scope.row.status === '1'"
                                    size="small"
                                    type="primary"
                                    @click="handleReply(scope.row)">回复
                                </el-button>
                                <el-button
                                    size="small"
                                    type="danger"
                                    @click="handleDelete(scope.row.id)">删除
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.currentPage"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    ></el-pagination>
                </div>
            </div>
        </div>
    </div>

    <!-- 回复评论对话框 -->
    <el-dialog
        v-model="replyDialogVisible"
        title="商家回复"
        width="600px"
    >
        <div class="reply-dialog-content">
            <div class="customer-comment">
                <h4>用户评价：</h4>
                <p>{{ replyForm.content }}</p>
            </div>
            <div class="merchant-reply-form">
                <h4>商家回复：</h4>
                <el-input
                    v-model="replyForm.reply"
                    :rows="4"
                    placeholder="请输入回复内容"
                    type="textarea"
                ></el-input>
            </div>
        </div>
        <template #footer>
                <span class="dialog-footer">
                    <el-button @click="replyDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitReply">确认回复</el-button>
                </span>
        </template>
    </el-dialog>
</template>
<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {Search} from '@element-plus/icons-vue'
import {computed, nextTick, onMounted, reactive, ref} from 'vue'
import {Session} from '../../utils/storage'
import {ElMessage, ElMessageBox} from 'element-plus'
import {useCommentStore} from '../../stores/comment'
import {COS_URL} from '../../config/baseUrl'

const router = useRouter()
const commentStore = useCommentStore()

const state = reactive({
    buttonList: [] as any[]
})

// 搜索表单
const searchForm = reactive({
    productName: '',
    status: '',
    beginTime: '',
    endTime: ''
})

// 评论列表
const commentList = computed(() => commentStore.commentList)

// 分页信息
const pagination = computed(() => commentStore.pagination)

// 加载状态
const loading = computed(() => commentStore.loading)

// 回复对话框
const replyDialogVisible = ref(false)
const replyForm = reactive({
    id: null,
    content: '',
    reply: ''
})

const handleButtonClick = (item) => {
    router.push(item.component)
}

// 搜索
const handleSearch = async () => {
    commentStore.setSearchParams({
        productName: searchForm.productName,
        status: searchForm.status,
        beginTime: searchForm.beginTime,
        endTime: searchForm.endTime
    })
    pagination.value.currentPage = 1
    await commentStore.getComments()
}

// 重置搜索
const handleReset = () => {
    Object.assign(searchForm, {
        productName: '',
        status: '',
        beginTime: '',
        endTime: ''
    })
    commentStore.resetSearchParams()
    commentStore.getComments()
}

// 分页大小变更
const handleSizeChange = (size) => {
    pagination.value.pageSize = size
    commentStore.getComments()
}

// 页码变更
const handleCurrentChange = (page) => {
    pagination.value.currentPage = page
    commentStore.getComments()
}

// 通过评论
const handleApprove = (id) => {
    ElMessageBox.confirm('确认通过该评论？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
    }).then(async () => {
        const success = await commentStore.reviewComment(id, '1')
        if (success) {
            commentStore.getComments()
        }
    }).catch(() => {
    })
}

// 拒绝评论
const handleReject = (id) => {
    ElMessageBox.confirm('确认拒绝该评论？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const success = await commentStore.reviewComment(id, '2')
        if (success) {
            commentStore.getComments()
        }
    }).catch(() => {
    })
}

// 回复评论
const handleReply = (comment) => {
    replyForm.id = comment.id
    replyForm.content = comment.content
    replyForm.reply = comment.reply || ''
    replyDialogVisible.value = true
}

// 提交回复
const submitReply = async () => {
    if (!replyForm.reply.trim()) {
        ElMessage.warning('请输入回复内容')
        return
    }

    const success = await commentStore.replyComment(replyForm.id, replyForm.reply)
    if (success) {
        replyDialogVisible.value = false
        commentStore.getComments()
    }
}

// 删除评论
const handleDelete = (id) => {
    ElMessageBox.confirm('确认删除该评论？删除后无法恢复！', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const success = await commentStore.deleteComment(id)
        if (success) {
            commentStore.getComments()
        }
    }).catch(() => {
    })
}

// 获取图片URL
const getImageUrl = (url) => {
    if (!url) return '';

    // 如果已经包含完整URL，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }

    // 添加前缀
    return `${COS_URL}/${url}`;
}

onMounted(() => {
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId')
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index]
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index]
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })

    // 加载评论列表
    commentStore.getComments()
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 30px);
    overflow-y: scroll;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.content-area {
    display: flex;
    flex-direction: column;
    margin-left: 260px;
    width: 1347px;
    position: relative;
    z-index: 1;
}

.headerBox {
    margin-top: 40px;
    border: 3px solid #3A58CF;
    border-radius: 8px;
    //overflow: hidden;
    //position: relative;
    //z-index: 2;

    .header {
        background-color: #3A58CF;
        height: 76px;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        z-index: 3;

        .revList {
            font-size: 30px;
            color: white;
            padding: 0 160px;
            cursor: pointer;
        }
    }

    .main {
        padding: 30px 40px 20px;

        .title {
            font-size: 30px;
            margin-left: 180px;
            color: #000;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .search-area {
            margin-bottom: 20px;

            .search-row {
                display: flex;
                align-items: center;
                margin-bottom: 15px;
            }

            .search-item {
                display: flex;
                align-items: center;
                margin-right: 20px;
            }

            .label {
                margin-right: 10px;
                font-size: 16px;
                white-space: nowrap;
            }

            .input {
                width: 200px;
            }

            .date-input {
                width: 180px;
            }

            .to {
                margin: 0 10px;
            }

            .status-select {
                width: 150px;
                margin-left: 20px;
            }

            .search-button {
                margin-top: 15px;
                display: flex;
                gap: 10px;
            }
        }
    }
}

.comment-table-wrapper {
    margin-top: 20px;
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: relative;

    :deep(.el-table) {
        overflow: visible !important;
    }

    :deep(.el-table__body) {
        overflow: visible !important;
    }

    :deep(.el-table__body-wrapper) {
        overflow: visible !important;
    }

    :deep(.el-table__expanded-cell) {
        overflow: visible !important;
        background-color: #f9f9f9;
        position: relative !important;
        z-index: 999 !important;
    }

    :deep(.el-table__row) {
        position: static !important;
    }

    :deep(.el-table__expand-icon--expanded) {
        transform: rotate(90deg);
    }
}

.comment-detail {
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 4px;
    position: relative;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.comment-content {
    margin-bottom: 10px;
}

.comment-images {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.comment-image {
    width: 100px;
    height: 100px;
    border-radius: 4px;
    object-fit: cover;
}

.comment-video {
    margin-top: 10px;
}

.comment-video-player {
    max-width: 400px;
    max-height: 300px;
}

.product-info {
    display: flex;
    flex-direction: column;
}

.product-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 5px;
}

.is-anonymous {
    color: #999;
    font-size: 12px;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

/* 商家回复样式 */
.merchant-reply {
    margin-top: 15px;
    padding: 10px;
    background-color: #f0f9ff;
    border-radius: 4px;
    border-left: 4px solid #3A58CF;
}

.reply-title {
    color: #3A58CF;
    margin-bottom: 5px;
}

.reply-content {
    color: #333;
    line-height: 1.5;
}

/* 回复对话框样式 */
.reply-dialog-content {
    padding: 10px;
}

.customer-comment {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
}

.customer-comment h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #333;
}

.merchant-reply-form h4 {
    margin-bottom: 10px;
    color: #3A58CF;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>
