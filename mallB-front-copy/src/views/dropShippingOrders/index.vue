<script setup>
import {useRouter} from 'vue-router'
import {ref} from 'vue'

const router = useRouter()
const handleButtonClick = (item) => {
    if (item === '全部订单') router.push('/allOrders')
    if (item === '待支付订单') router.push('/notPayOrders')
    if (item === '代发货订单') router.push('/dropShippingOrders')
    if (item === '待收货订单') router.push('/notPendingOrders')
    if (item === '已完成订单') router.push('/finishOrders')
    if (item === '支付异常') router.push('/unusualPay')
    if (item === '支付回调异常') router.push('/payCallbackUnu')
    if (item === '退货订单') router.push('/returnOrders')
    if (item === '已退款') router.push('/refunded')
    if (item === '发货') router.push('/shipping')
}

const buttonList = [
    '全部订单', '待支付订单', '代发货订单', '待收货订单',
    '已完成订单', '支付异常', '支付回调异常', '退货订单',
    '已退款', '发货', '发货单号'
]

// 筛选表单数据
const filterForm = ref({
    IDNumber: '',
    pendingOrder: '',
    dropshipOrder: '',
    completeOrder: '',
    notReturnOrder: '',
    trackingNumber: '',
    orderNumber: '',
    receiverPhone: ''
})

// 表格数据
const tableData = ref([
    {
        IDNumber: '115644',
        pendingOrder: '1315646',
        dropshipOrder: '1216464',
        completeOrder: '54131',
        notReturnOrder: '513121516',
        trackingNumber: 'SF874315',
        orderNumber: '123144546',
        receiverPhone: '111111222'
    },
    {
        IDNumber: '11312312',
        pendingOrder: '1131346',
        dropshipOrder: '848464',
        completeOrder: '3222211',
        notReturnOrder: '989121516',
        trackingNumber: '*********',
        orderNumber: '66424546',
        receiverPhone: '95175312648'

    },

])
</script>

<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item }}
            </el-button>
        </div>

        <div class="right-content">
            <div class="filter-section">
                <div class="title">
                    <span>订单筛选</span>
                </div>

                <div class="filter-form">
                    <el-form :model="filterForm">
                        <div class="form-row">
                            <el-input
                                v-model="filterForm.userPhone"
                                class="filter-input"
                                placeholder="用户手机号"
                            />
                            <el-input
                                v-model="filterForm.sender"
                                class="filter-input"
                                placeholder="发件人"
                            />
                            <el-input
                                v-model="filterForm.receiverPhone"
                                class="filter-input"
                                placeholder="收件手机号"
                            />
                        </div>

                        <div class="form-row">
                            <el-input
                                v-model="filterForm.shippingAddress"
                                class="filter-input"
                                placeholder="发货地"
                            />
                            <el-input
                                v-model="filterForm.trackingNumber"
                                class="filter-input"
                                placeholder="快递号"
                            />
                            <el-input
                                v-model="filterForm.productName"
                                class="filter-input"
                                placeholder="商品名称"
                            />
                        </div>

                        <div class="form-row">
                            <el-input
                                v-model="filterForm.orderNumber"
                                class="filter-input"
                                placeholder="订单号"
                            />
                            <el-date-picker
                                v-model="filterForm.shippingTime"
                                class="filter-input"
                                placeholder="发货时间"
                                type="datetime"
                            />
                            <el-button
                                class="search-btn"
                                type="primary"
                            >
                                查询
                            </el-button>
                        </div>
                    </el-form>

                </div>
            </div>

            <el-table :data="tableData" class="order-table">
                <el-table-column
                    header-class-name="table-header"
                    label="ID号"
                    prop="IDNumber"
                    width="100"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="待支付订单"
                    prop="pendingOrder"
                    width="120"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="代发货订单"
                    prop="dropshipOrder"
                    width="150"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="已完成订单"
                    prop="completeOrder"
                    width="150"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="待收货订单"
                    prop="notReturnOrder"
                    width="200"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="快递号"
                    prop="trackingNumber"
                    width="150"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="订单号"
                    prop="orderNumber"
                    width="200"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="收件手机号"
                    prop="receiverPhone"
                    width="200"
                />


            </el-table>
        </div>
    </div>
    <!--  </ManageBg>-->
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.right-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.filter-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.title {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
}

.filter-form {
    .form-row {
        display: flex;
        margin-bottom: 15px;
        gap: 15px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.filter-input {
    flex: 1;
}

.search-btn {
    width: 120px;
}

.order-table {
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-table__header) {
        .table-header {
            background-color: #3A58CF;
            color: white;
        }
    }

    :deep(.el-table__cell) {
        padding: 12px 0;
    }
}
</style>
