<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="content-area">
            <!-- 使用 el-form 组件优化搜索表单 -->
            <el-form :inline="true" :model="searchForm" label-width="100px" size="large">
                <el-form-item label="商品名称" prop="productName">
                    <el-input
                        v-model="searchForm.productName"
                        clearable
                        placeholder="请输入商品名称"
                        show-word-limit
                    />
                </el-form-item>
                <el-form-item class="search-actions">
                    <el-button
                        :icon="Search"
                        :loading="loading"
                        size="large"
                        type="primary"
                        @click="handleSearch"
                    >
                        搜索
                    </el-button>
                    <el-button
                        :icon="RefreshRight"
                        size="large"
                        @click="handleReset"
                    >
                        重置
                    </el-button>
                </el-form-item>
            </el-form>
            <el-table
                v-loading="loading"
                :data="state.tableData"
                empty-text="暂无数据"
                style="width: 100%"
                @selection-change="handleSelectionChange"
            >
                <el-table-column align="center" label="序号" type="index" width="60"/>
                <el-table-column label="商品名称" prop="name" show-overflow-tooltip/>
                <el-table-column align="center" label="商品封面" prop="cover" width="120">
                    <template #default="scope">
                        <el-image
                            :preview-src-list="[getImageUrl(scope.row.cover)]"
                            :src="getImageUrl(scope.row.cover)"
                            fit="cover"
                            preview-teleported
                            style="width: 80px; height: 80px; border-radius: 4px;"
                        >
                            <template #error>
                                <div class="image-slot">
                                    <el-icon>
                                        <Picture/>
                                    </el-icon>
                                </div>
                            </template>
                        </el-image>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="代销数量" prop="number">
                    <template #default="scope">
                        <el-tag type="info">{{ scope.row.number || 0 }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="利润" prop="profit">
                    <template #default="scope">
                        <span class="profit-text">¥{{ formatPrice(scope.row) }}</span>
                    </template>
                </el-table-column>
                <el-table-column align="center" fixed="right" label="操作">
                    <template #default="scope">
                        <el-button
                            size="small"
                            type="danger"
                            @click="handleCancelConsignment(scope.row)"
                        >
                            取消代销
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
                <el-pagination
                    v-model:current-page="pagination.pageNum"
                    v-model:page-size="pagination.pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="pagination.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import {nextTick, onMounted, reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {Picture, RefreshRight, Search} from '@element-plus/icons-vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Session} from '../../utils/storage'
import {buildProductLink} from '../../stores/buildProductLink'
import {COS_URL} from '../../config/baseUrl'

const router = useRouter()

// 响应式数据
const state = reactive({
    buttonList: [],
    tableData: []
})

// 搜索表单
const searchForm = reactive({
    productName: '',
})

// 分页数据
const pagination = reactive({
    pageNum: 1,
    pageSize: 10,
    total: 0
})

// 加载状态
const loading = ref(false)

// 选中的行
const selectedRows = ref([])

// 处理按钮点击
const handleButtonClick = (item) => {
    router.push(item.component)
}

// 获取图片URL
const getImageUrl = (cover: string) => {
    if (!cover) return ''
    return cover.startsWith('http') ? cover : `${COS_URL}/${cover}`
}

// 格式化价格
const formatPrice = (row) => {
    if (row.daixiaoType == 1) {
        return Number(row.daixiaoTypeValue).toFixed(2)
    } else {
        return Number(row.daixiaoTypeValue / 100 * row.price).toFixed(2)
    }
}

// 获取商品数据
const productLinkGeneration = async () => {
    try {
        loading.value = true
        const params = {
            pageSize: pagination.pageSize,
            pageNum: pagination.pageNum,
            ...searchForm,
        }

        // 移除空值
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key]
            }
        })

        const result = await buildProductLink().GetShopDaiXiaoProductList(params)

        if (result && result.rows) {
            state.tableData = result.rows.map(item => ({
                ...item,
                generating: false
            }))
            pagination.total = result.total || 0
        } else {
            state.tableData = []
            pagination.total = 0
        }
    } catch (error) {
        console.error('获取商品数据失败:', error)
        ElMessage.error('获取数据失败，请稍后重试')
        state.tableData = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 搜索处理
const handleSearch = () => {
    pagination.pageNum = 1
    productLinkGeneration()
}

// 重置搜索
const handleReset = () => {
    Object.assign(searchForm, {
        productName: ''
    })
    pagination.pageNum = 1
    productLinkGeneration()
}
// 分页大小改变
const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.pageNum = 1
    productLinkGeneration()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
    pagination.pageNum = page
    productLinkGeneration()
}

// 选择改变
const handleSelectionChange = (selection: any[]) => {
    selectedRows.value = selection
}

// 处理取消代销
const handleCancelConsignment = async (row: any) => {
    try {
        await ElMessageBox.confirm(
            `确定要取消代销商品"${row.name}"吗？`,
            '确认取消',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        // 这里调用取消代销API
        const res = await buildProductLink().CancelConsignment(row.productId)
        if (res.code == 200){
            ElMessage.success('取消代销成功')
        }

        productLinkGeneration() // 刷新数据
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('取消代销失败，请稍后重试')
        }
    }
}

// 初始化菜单
const initMenu = () => {
    nextTick(() => {
        const menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId')

        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index]
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }

        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index]
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })
}

// 组件挂载
onMounted(() => {
    productLinkGeneration()
    initMenu()
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 30px);
    overflow-y: scroll;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.content-area {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    margin-left: 240px;
}

.headBox {
    width: 1347px;
    min-height: 360px;
    border: 3px solid #3A58CF;
    border-radius: 8px;
    margin-bottom: 20px;
    position: relative;
    padding: 20px;

    .search-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .search-form {
            width: 100%;
            max-width: 1200px;

            :deep(.el-form-item) {
                margin-bottom: 20px;

                .el-form-item__label {
                    font-size: 16px;
                    font-weight: 600;
                    color: #303133;
                }

                .el-input {
                    .el-input__inner {
                        height: 44px;
                        font-size: 14px;
                        border-radius: 6px;
                        border: 1px solid #dcdfe6;
                        transition: all 0.3s;

                        &:focus {
                            border-color: #3A58CF;
                            box-shadow: 0 0 0 2px rgba(58, 88, 207, 0.1);
                        }
                    }

                    .el-input__prefix {
                        color: #909399;
                    }
                }

                .el-select {
                    width: 100%;

                    .el-input__inner {
                        height: 44px;
                        font-size: 14px;
                        border-radius: 6px;
                    }
                }

                .el-date-editor {
                    width: 100%;

                    .el-input__inner {
                        height: 44px;
                        font-size: 14px;
                        border-radius: 6px;
                    }
                }

                .el-input-number {
                    .el-input__inner {
                        height: 44px;
                        font-size: 14px;
                        border-radius: 6px;
                    }
                }
            }

            .search-actions {
                text-align: center;
                margin-top: 10px;

                .el-form-item__content {
                    justify-content: center;
                }

                :deep(.el-button-group) {
                    .el-button {
                        padding: 12px 20px;
                        font-size: 14px;
                        font-weight: 500;
                        border-radius: 6px;
                        transition: all 0.3s;

                        &:hover {
                            transform: translateY(-1px);
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                        }

                        &.el-button--primary {
                            background: linear-gradient(135deg, #3A58CF, #4A69E2);
                            border: none;

                            &:hover {
                                background: linear-gradient(135deg, #2A48BF, #3A59D2);
                            }
                        }

                        &.el-button--success {
                            background: linear-gradient(135deg, #67C23A, #77D24A);
                            border: none;

                            &:hover {
                                background: linear-gradient(135deg, #57B22A, #67C23A);
                            }
                        }

                        &.el-button--warning {
                            background: linear-gradient(135deg, #E6A23C, #F6B24C);
                            border: none;

                            &:hover {
                                background: linear-gradient(135deg, #D6922C, #E6A23C);
                            }
                        }
                    }
                }
            }
        }
    }
}

.main {
    width: 1344px;
    height: 527px;
    border: 3px solid #3A58CF;
    border-radius: 8px;
    overflow: hidden;

    :deep(.el-table) {
        height: 100%;

        th {
            background-color: #3A58CF !important;
            color: white;
            height: 66px;
            font-size: 18px;
        }

        td {
            height: 60px;
            font-size: 16px;
        }

        .el-button {
            margin-right: 5px;

            &:last-child {
                margin-right: 0;
            }
        }
    }
}

.pagination-container {
    padding: 20px;
    display: flex;
    justify-content: right;
    background: #fff;
    border-top: 1px solid #ebeef5;
}

.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    background: #f5f7fa;
    color: #909399;
    border-radius: 4px;
}

.profit-text {
    color: #e6a23c;
    font-weight: 600;
}

.link-container {
    display: flex;
    align-items: center;
    gap: 8px;

    .link-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #409EFF;
        cursor: pointer;

        &:hover {
            text-decoration: underline;
        }
    }

    .copy-btn {
        padding: 4px 8px;
        font-size: 12px;
    }
}

.no-link {
    color: #909399;
    font-style: italic;
}

// 响应式设计
@media (max-width: 1400px) {
    .headBox {
        width: 100%;
    }

    .main {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }

    .left-buttons {
        position: relative;
        width: 100%;
        height: auto;
        margin-bottom: 20px;

        :deep(.el-button.data-button) {
            display: inline-block;
            width: auto;
            height: 40px;
            margin: 5px;
            border-radius: 4px;
        }
    }

    .content-area {
        margin-left: 0;
        padding: 10px;
    }

    .headBox {
        .search-content {
            .search-area {
                .search-row {
                    flex-direction: column;

                    .search-item {
                        min-width: 100%;

                        .label {
                            min-width: 60px;
                            text-align: left;
                        }
                    }
                }
            }
        }
    }
}
</style>
