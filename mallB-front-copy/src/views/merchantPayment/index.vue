<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div v-loading="merchantPaymentStore.loading" class="main">
            <!-- 商家搜索部分 -->
            <div class="search-section">
                <el-form :inline="true" class="search-form">
                    <el-form-item label="商家电话">
                        <el-input v-model="merchantPaymentStore.phone" placeholder="请输入商家电话号码"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="searchMerchant">搜索</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 商家信息展示 -->
            <div v-if="merchantPaymentStore.merchantInfo" class="merchant-info">
                <h3>商家信息</h3>
                <el-descriptions :column="3" border>
                    <el-descriptions-item label="商家名称">{{
                            merchantPaymentStore.merchantInfo.shopName
                        }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系电话">{{
                            merchantPaymentStore.merchantInfo.phone
                        }}
                    </el-descriptions-item>
                    <el-descriptions-item label="当前货款余额">¥{{
                            merchantPaymentStore.merchantInfo.balance
                        }}
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 冻结货款表单 -->
                <div class="freeze-section">
                    <el-form ref="freezeFormRef" :model="freezeForm" label-width="120px">
                        <el-form-item :rules="[{ required: true, message: '请输入冻结金额' }]" label="冻结金额"
                                      prop="amount">
                            <el-input v-model.number="freezeForm.amount" placeholder="请输入要冻结的金额"></el-input>
                        </el-form-item>
                        <el-form-item :rules="[{ required: true, message: '请输入冻结原因' }]" label="冻结原因"
                                      prop="reason">
                            <el-input v-model="freezeForm.reason" placeholder="请输入冻结原因"
                                      type="textarea"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="freezeAmount">冻结货款</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <!-- 冻结记录表格 -->
            <div class="freeze-record-section">
                <h3>货款冻结记录</h3>

                <!-- 冻结记录搜索表单 -->
                <div class="record-search-section">
                    <el-form :inline="true" :model="recordSearchForm" class="record-search-form">
                        <el-form-item label="商家名称">
                            <el-input v-model="recordSearchForm.shopName" clearable
                                      placeholder="请输入商家名称"></el-input>
                        </el-form-item>
                        <el-form-item label="商家电话">
                            <el-input v-model="recordSearchForm.shopPhone" clearable
                                      placeholder="请输入商家电话"></el-input>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="recordSearchForm.status" clearable placeholder="请选择状态">
                                <el-option label="全部" value=""></el-option>
                                <el-option label="已冻结" value="frozen"></el-option>
                                <el-option label="已解冻" value="unfrozen"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="searchRecords">搜索</el-button>
                            <el-button @click="resetSearch">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <el-table v-loading="merchantPaymentStore.loading" :data="merchantPaymentStore.freezeRecords" border
                          style="width: 100%">
                    <el-table-column label="记录ID" prop="id" width="80"></el-table-column>
                    <el-table-column label="商家名称" prop="shopName"></el-table-column>
                    <el-table-column label="电话号码" prop="shopPhone"></el-table-column>
                    <el-table-column label="冻结金额" prop="freezeAmount" width="120">
                        <template #default="scope">¥{{ scope.row.amount }}</template>
                    </el-table-column>
                    <el-table-column label="冻结原因" prop="reason"></el-table-column>
                    <el-table-column label="冻结时间" prop="createTime" width="180"></el-table-column>
                    <el-table-column label="状态" prop="status" width="100">
                        <template #default="scope">
                            <el-tag :type="scope.row.status === 'frozen' ? 'danger' : 'success'">
                                {{ scope.row.status === 'frozen' ? '已冻结' : '已解冻' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120">
                        <template #default="scope">
                            <el-button
                                v-if="scope.row.status === 'frozen'"
                                size="small"
                                type="primary"
                                @click="unfreezeAmount(scope.row)"
                            >
                                解冻
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页组件 -->
                <div class="pagination-section">
                    <el-pagination
                        v-model:current-page="merchantPaymentStore.pagination.pageNum"
                        v-model:page-size="merchantPaymentStore.pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="merchantPaymentStore.pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import {useRouter} from 'vue-router'
import {Session} from '../../utils/storage'
import {nextTick, onMounted, reactive, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {useMerchantPaymentStore} from '../../stores/merchantPayment'

const state = reactive({
    buttonList: []
})

// 冻结表单
const freezeForm = reactive({
    amount: '',
    reason: ''
})

// 冻结记录搜索表单
const recordSearchForm = reactive({
    shopName: '',
    shopPhone: '',
    status: ''
})

const freezeFormRef = ref(null)

const router = useRouter()
const merchantPaymentStore = useMerchantPaymentStore()

const handleButtonClick = (item) => {
    router.push(item.component)
}

// 搜索商家
const searchMerchant = async () => {
    if (!merchantPaymentStore.phone) {
        ElMessage.warning('请输入商家电话号码')
        return
    }

    await merchantPaymentStore.searchMerchantByPhone(merchantPaymentStore.phone)
}

// 冻结货款
const freezeAmount = () => {
    if (!merchantPaymentStore.merchantInfo) {
        ElMessage.warning('请先搜索商家')
        return
    }

    freezeFormRef.value.validate(async (valid) => {
        if (valid) {
            // 调用后端API冻结货款
            ElMessageBox.confirm(
                `确定要冻结该商家的 ${freezeForm.amount} 元货款吗？`,
                '确认冻结',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }
            ).then(async () => {
                // 调用store方法冻结货款
                const success = await merchantPaymentStore.freezeAmount({
                    shopId: merchantPaymentStore.merchantInfo.shopId,
                    amount: freezeForm.amount,
                    reason: freezeForm.reason
                })

                if (success) {
                    searchMerchant()
                    // 重置表单
                    freezeForm.amount = ''
                    freezeForm.reason = ''
                }
            }).catch(() => {
                // 用户取消操作
            })
        }
    })
}

// 解冻货款
const unfreezeAmount = (row) => {
    ElMessageBox.confirm(
        `确定要解冻该笔 ${row.amount} 元的冻结货款吗？`,
        '确认解冻',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(async () => {
        // 调用store方法解冻货款
        await merchantPaymentStore.unfreezeAmount(row.id)
        searchMerchant()
    }).catch(() => {
        // 用户取消操作
    })
}

// 搜索冻结记录
const searchRecords = async () => {
    // 设置搜索条件
    merchantPaymentStore.setSearchParams(recordSearchForm)
    // 重置到第一页
    merchantPaymentStore.setPagination(1)
    // 执行搜索
    await merchantPaymentStore.getFreezeRecords()
}

// 重置搜索
const resetSearch = async () => {
    // 重置搜索表单
    recordSearchForm.shopName = ''
    recordSearchForm.shopPhone = ''
    recordSearchForm.status = ''

    // 重置store中的搜索条件
    merchantPaymentStore.resetSearchParams()

    // 重新加载数据
    await merchantPaymentStore.getFreezeRecords()
}

// 分页大小改变
const handleSizeChange = async (newSize) => {
    merchantPaymentStore.setPagination(merchantPaymentStore.pagination.pageNum, newSize)
    await merchantPaymentStore.getFreezeRecords()
}

// 当前页改变
const handleCurrentChange = async (newPage) => {
    merchantPaymentStore.setPagination(newPage)
    await merchantPaymentStore.getFreezeRecords()
}

onMounted(() => {
    // 初始化加载冻结记录
    merchantPaymentStore.getFreezeRecords()

    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.main {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-section {
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 8px;
    margin-bottom: 20px;

    :deep(.el-form-item) {
        margin-bottom: 0;
    }

    :deep(.el-form-item__label) {
        font-size: 16px;
        color: #333;
    }

    :deep(.el-input__inner) {
        height: 40px;
        font-size: 14px;
    }

    :deep(.el-button) {
        height: 40px;
        padding: 0 30px;
    }
}

.merchant-info {
    padding: 20px;
    background-color: #f0f5ff;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 5px solid #3A58CF;

    h3 {
        margin-top: 0;
        color: #3A58CF;
        font-size: 20px;
        margin-bottom: 15px;
    }

    :deep(.el-descriptions__label) {
        font-weight: bold;
        width: 120px !important;
    }

    :deep(.el-descriptions__cell) {
        font-size: 16px;
    }
}

.freeze-section {
    margin-top: 10px;
    padding-top: 20px;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #ebeef5;

    h3 {
        margin-top: 0;
        color: #3A58CF;
        font-size: 18px;
    }

    :deep(.el-form-item) {
        margin-bottom: 20px;
    }

    :deep(.el-form-item__label) {
        font-size: 14px;
        color: #333;
    }

    :deep(.el-textarea__inner) {
        min-height: 80px;
    }

    :deep(.el-button) {
        padding: 12px 30px;
        font-size: 16px;
    }
}

.freeze-record-section {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #ebeef5;

    h3 {
        margin-top: 0;
        color: #3A58CF;
        font-size: 20px;
        margin-bottom: 20px;
    }
}

.record-search-section {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;

    .record-search-form {
        :deep(.el-form-item) {
            margin-bottom: 0;
            margin-right: 20px;
        }

        :deep(.el-form-item__label) {
            font-size: 14px;
            color: #333;
        }

        :deep(.el-input) {
            width: 200px;
        }

        :deep(.el-select) {
            width: 150px;
        }

        :deep(.el-button) {
            height: 32px;
            padding: 0 20px;
        }
    }
}

.pagination-section {
    margin-top: 20px;
    text-align: right;

    :deep(.el-pagination) {
        justify-content: right;
    }
}

// 响应式优化
@media (max-width: 1200px) {
    .container {
        flex-direction: column;
        height: auto;
    }

    .left-buttons {
        width: 100%;
        height: auto;
        margin: 10px;
        display: flex;
        flex-wrap: wrap;

        :deep(.el-button.data-button) {
            width: calc(50% - 10px);
            margin: 5px;
            height: 50px;
            font-size: 16px;
        }
    }

    .main {
        margin: 10px;
    }
}

@media (max-width: 768px) {
    .main {
        padding: 15px;
    }

    .search-section,
    .merchant-info,
    .freeze-section,
    .freeze-record-section {
        padding: 15px;
    }

    .left-buttons :deep(.el-button.data-button) {
        width: 100%;
        font-size: 18px;
    }

    :deep(.el-descriptions) {
        display: block;
    }

    :deep(.el-descriptions__body) {
        overflow-x: auto;
    }
}
</style>
