<script setup>
import {useRoute, useRouter} from 'vue-router'
import {computed, onMounted, reactive, ref} from 'vue'
import {ElMessage} from 'element-plus'
import {useReturnAddressStore} from '../../stores/returnAddress'
import {useAreaStore} from '../../stores/area.js'

const router = useRouter()
const route = useRoute()
const returnAddressStore = useReturnAddressStore()
const areaStore = useAreaStore()

const handleButtonClick = (item) => {
    if (item === '商品列表') router.push('/productList')
    if (item === '发布商品') router.push('/commodity')
    if (item === '商品分类') router.push('/productCategory')
    if (item === '品牌管理') router.push('/brandManage')
    if (item === '配送管理') router.push('./deliveryManage')
    if (item === '评论管理') router.push('./commentManage')
    if (item === '退货地址') router.push('./returnAddress')
    if (item === '商品链接') router.push('./productLink')
    if (item === '商品链接生成') router.push('./buildProductLink')
    if (item === '商品链接导入') router.push('./productLinkImport')
}

const buttonList = [
    '发布商品', '商品列表', '商品分类', '品牌管理', '配送管理',
    '评论管理', '退货地址', '平台促销券', '商品链接', '商品链接生成',
    '商品链接导入', '商品代销申请'
]

const handleToReturnAddress = () => {
    router.push('/returnAddress')
}

// 地址选择数据
const provinces = ['广东省', '北京市', '上海市', '浙江省']
const cities = {
    '广东省': ['深圳市', '广州市', '东莞市'],
    '北京市': ['朝阳区', '海淀区', '东城区'],
    '上海市': ['浦东新区', '静安区', '黄浦区'],
    '浙江省': ['杭州市', '宁波市', '温州市']
}
const districts = {
    '深圳市': ['南山区', '福田区', '罗湖区'],
    '广州市': ['天河区', '越秀区', '海珠区'],
    '朝阳区': ['三里屯街道', '建国门街道'],
    '浦东新区': ['陆家嘴街道', '张江镇']
}
const streets = {
    '南山区': ['科技园街道', '蛇口街道'],
    '福田区': ['华强北街道', '香蜜湖街道'],
    '陆家嘴街道': ['世纪大道', '浦东大道']
}

const selectedProvince = ref('')
const selectedCity = ref('')
const selectedDistrict = ref('')
const selectedStreet = ref('')

// 表单数据
const addressForm = reactive({
    id: null,
    receiverName: '',
    receiverPhone: '',
    provinceCode: '',
    provinceName: '',
    cityCode: '',
    cityName: '',
    districtCode: '',
    districtName: '',
    detailAddress: '',
    postCode: '',
    isDefault: '0',
    status: '0',
    addressRemark: ''
})

// 表单验证规则
const rules = {
    receiverName: [
        {required: true, message: '请输入联系人姓名', trigger: 'blur'},
        {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
    ],
    receiverPhone: [
        {required: true, message: '请输入联系电话', trigger: 'blur'},
        {pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur'}
    ],
    detailAddress: [
        {required: true, message: '请输入详细地址', trigger: 'blur'},
        {min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur'}
    ]
}

// 地址选择器相关
const provinceOptions = ref([])
const cityOptions = ref([])
const districtOptions = ref([])
const townOptions = ref([])
const provinceSelected = ref('')
const citySelected = ref('')
const districtSelected = ref('')
const townSelected = ref('')

// 加载状态
const loading = ref(false)
const addressFormRef = ref(null)

// 是否为编辑模式
const isEdit = computed(() => {
    return !!route.query.id
})

// 加载省份数据
const loadProvinceData = async () => {
    try {
        const provinces = await areaStore.getProvinces()
        provinceOptions.value = provinces || []
    } catch (error) {
        ElMessage.error('加载省份数据失败')
    }
}

// 加载城市数据
const loadCityData = async (provinceCode) => {
    if (!provinceCode) return
    try {
        const cities = await areaStore.getCities(provinceCode)
        cityOptions.value = cities || []
    } catch (error) {
        ElMessage.error('加载城市数据失败')
    }
}

// 加载区县数据
const loadDistrictData = async (cityCode) => {
    if (!cityCode) return
    try {
        const districts = await areaStore.getDistricts(cityCode)
        districtOptions.value = districts || []
    } catch (error) {
        ElMessage.error('加载区县数据失败')
    }
}

// 加载街道数据
const loadTownData = async (districtCode) => {
    if (!districtCode) return
    try {
        const towns = await areaStore.getTowns(districtCode)
        townOptions.value = towns || []
    } catch (error) {
        ElMessage.error('加载街道数据失败')
    }
}

// 省份变更处理
const handleProvinceChange = async (provinceCode) => {
    // 清空城市、区县和街道
    citySelected.value = ''
    districtSelected.value = ''
    townSelected.value = ''
    cityOptions.value = []
    districtOptions.value = []
    townOptions.value = []

    if (provinceCode) {
        // 查找选中的省份名称
        const selectedProvince = provinceOptions.value.find(p => Number(p.code) === Number(provinceCode))

        // 更新省份代码和名称
        addressForm.provinceCode = provinceCode
        addressForm.provinceName = selectedProvince ? selectedProvince.name : ''

        // 清空城市、区县和街道代码及名称
        addressForm.cityCode = ''
        addressForm.cityName = ''
        addressForm.districtCode = ''
        addressForm.districtName = ''

        // 加载城市数据
        await loadCityData(provinceCode)
    }
}

// 城市变更处理
const handleCityChange = async (cityCode) => {
    // 清空区县和街道
    districtSelected.value = ''
    townSelected.value = ''
    districtOptions.value = []
    townOptions.value = []

    if (cityCode) {
        // 查找选中的城市名称
        const selectedCity = cityOptions.value.find(c => Number(c.code) === Number(cityCode))

        // 更新城市代码和名称
        addressForm.cityCode = cityCode
        addressForm.cityName = selectedCity ? selectedCity.name : ''

        // 清空区县和街道代码及名称
        addressForm.districtCode = ''
        addressForm.districtName = ''

        // 加载区县数据
        await loadDistrictData(cityCode)
    }
}

// 区县变更处理
const handleDistrictChange = async (districtCode) => {
    // 清空街道
    townSelected.value = ''
    townOptions.value = []

    if (districtCode) {
        // 查找选中的区县名称
        const selectedDistrict = districtOptions.value.find(d => Number(d.code) === Number(districtCode))

        // 更新区县代码和名称
        addressForm.districtCode = districtCode
        addressForm.districtName = selectedDistrict ? selectedDistrict.name : ''

        // 加载街道数据
        await loadTownData(districtCode)
    }
}

// 街道变更处理
const handleTownChange = (townCode) => {
    if (townCode) {
        // 查找选中的街道名称
        const selectedTown = townOptions.value.find(t => Number(t.code) === Number(townCode))

        // 更新详细地址
        if (selectedTown && selectedTown.name) {
            addressForm.detailAddress = (addressForm.detailAddress || '') + selectedTown.name
        }
    }
}

// 获取地址详情
const getAddressDetail = async (id) => {
    loading.value = true
    try {
        const data = await returnAddressStore.getAddressDetail(id)
        if (data) {
            // 填充表单数据
            Object.assign(addressForm, data)

            // 设置选择器的值
            provinceSelected.value = data.provinceCode

            // 加载并设置城市数据
            await loadCityData(data.provinceCode)
            citySelected.value = data.cityCode

            // 加载并设置区县数据
            await loadDistrictData(data.cityCode)
            districtSelected.value = data.districtCode

            // 加载并设置街道数据
            await loadTownData(data.districtCode)
            townSelected.value = data.townCode
        }
    } finally {
        loading.value = false
    }
}

// 提交表单
const submitForm = async () => {
    if (!addressForm.provinceCode || !addressForm.cityCode || !addressForm.districtCode) {
        ElMessage.warning('请选择完整的地区信息')
        return
    }

    loading.value = true
    try {
        let success
        if (isEdit.value) {
            success = await returnAddressStore.updateAddress(addressForm)
        } else {
            success = await returnAddressStore.addAddress(addressForm)
        }

        if (success) {
            router.push('/returnAddress')
        }
    } finally {
        loading.value = false
    }
}

// 返回列表页
const goBack = () => {
    router.push('/returnAddress')
}

// 页面加载
onMounted(async () => {
    // 加载省份数据
    await loadProvinceData()

    // 如果是编辑模式，加载地址详情
    const id = route.query.id
    if (id) {
        await getAddressDetail(id)
    }
})
</script>

<template>
    <div class="add-return-address-container">
        <div class="header">
            <h2>{{ isEdit ? '编辑退货地址' : '添加退货地址' }}</h2>
        </div>

        <div v-loading="loading" class="form-container">
            <el-form
                ref="addressFormRef"
                :model="addressForm"
                :rules="rules"
                class="address-form"
                label-width="100px">

                <el-form-item label="联系人" prop="receiverName">
                    <el-input v-model="addressForm.receiverName" placeholder="请输入联系人姓名"></el-input>
                </el-form-item>

                <el-form-item label="联系电话" prop="receiverPhone">
                    <el-input v-model="addressForm.receiverPhone" placeholder="请输入联系电话"></el-input>
                </el-form-item>

                <el-form-item label="所在地区" prop="area">
                    <div class="area-selects">
                        <!-- 省份选择 -->
                        <el-select
                            v-model="provinceSelected"
                            class="address-select"
                            placeholder="请选择省份"
                            @change="handleProvinceChange">
                            <el-option
                                v-for="item in provinceOptions"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>

                        <!-- 城市选择 -->
                        <el-select
                            v-model="citySelected"
                            class="address-select"
                            placeholder="请选择城市"
                            @change="handleCityChange">
                            <el-option
                                v-for="item in cityOptions"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>

                        <!-- 区县选择 -->
                        <el-select
                            v-model="districtSelected"
                            class="address-select"
                            placeholder="请选择区/县"
                            @change="handleDistrictChange">
                            <el-option
                                v-for="item in districtOptions"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>

                        <!-- 街道选择 -->
                        <el-select
                            v-model="townSelected"
                            class="address-select"
                            placeholder="请选择街道"
                            @change="handleTownChange">
                            <el-option
                                v-for="item in townOptions"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>
                    </div>
                </el-form-item>

                <el-form-item label="详细地址" prop="detailAddress">
                    <el-input
                        v-model="addressForm.detailAddress"
                        placeholder="请输入详细地址信息，如道路、门牌号、小区、楼栋号、单元等"
                        rows="3"
                        type="textarea"></el-input>
                </el-form-item>

                <el-form-item label="设为默认" prop="isDefault">
                    <el-switch v-model="addressForm.isDefault" active-value="1" inactive-value="0"></el-switch>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="submitForm">保存</el-button>
                    <el-button @click="goBack">取消</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.add-return-address-container {
    padding: 20px;
}

.header {
    margin-bottom: 20px;
}

.form-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 4px;
}

.address-form {
    max-width: 800px;
}

.area-selects {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.address-select {
    width: 160px;
}

@media (max-width: 768px) {
    .area-selects {
        flex-direction: column;
    }

    .address-select {
        width: 100%;
    }
}
</style>
