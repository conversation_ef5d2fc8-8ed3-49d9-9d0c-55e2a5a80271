<script setup>
import Background from '../../components/Background.vue'
import {onUnmounted, reactive, ref} from 'vue'
import {Cellphone, Lock} from '@element-plus/icons-vue'
import {ElLoading, ElMessage} from 'element-plus'
import {useRouter} from 'vue-router'
import {resetPassword, sendForgotPasswordCode} from '../../api/forgotPassword/index.js'

const router = useRouter()

// 表单数据
const formData = reactive({
    phone: '',
    verifyCode: '',
    newPassword: '',
    confirmPassword: '',
    uuid: ''
})

// 验证码相关状态
const countdown = ref(0)
const isSending = ref(false)
let timer = null

// 表单验证状态
const errors = reactive({
    phone: '',
    verifyCode: '',
    newPassword: '',
    confirmPassword: ''
})

// 加载状态
const isSubmitting = ref(false)

// 密码正则：必须包含数字、字母、特殊符号，6-20位（与后端保持一致）
const passwordRegex = /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[!@#$%^&*()_+=~`{}[\]|:;<>,.?/]).{6,20}$/

// 手机号正则
const phoneRegex = /^1[3-9]\d{9}$/

// 验证码正则
const codeRegex = /^\d{6}$/

// 表单验证函数
const validatePhone = () => {
    if (!formData.phone) {
        errors.phone = '请输入手机号'
        return false
    }
    if (!phoneRegex.test(formData.phone)) {
        errors.phone = '请输入正确的手机号格式'
        return false
    }
    errors.phone = ''
    return true
}

const validateVerifyCode = () => {
    if (!formData.verifyCode) {
        errors.verifyCode = '请输入验证码'
        return false
    }
    if (!codeRegex.test(formData.verifyCode)) {
        errors.verifyCode = '验证码格式不正确'
        return false
    }
    errors.verifyCode = ''
    return true
}

const validatePassword = () => {
    if (!formData.newPassword) {
        errors.newPassword = '请输入新密码'
        return false
    }
    if (!passwordRegex.test(formData.newPassword)) {
        errors.newPassword = '密码必须包含数字、字母和特殊符号，且为6-20位'
        return false
    }
    errors.newPassword = ''
    return true
}

const validateConfirmPassword = () => {
    if (!formData.confirmPassword) {
        errors.confirmPassword = '请确认新密码'
        return false
    }
    if (formData.newPassword !== formData.confirmPassword) {
        errors.confirmPassword = '两次输入的密码不一致'
        return false
    }
    errors.confirmPassword = ''
    return true
}

// 发送验证码
const sendVerifyCode = async () => {
    if (!validatePhone()) {
        return
    }

    if (countdown.value > 0 || isSending.value) {
        return
    }

    try {
        isSending.value = true

        const response = await sendForgotPasswordCode(formData.phone)

        if (response.code === 200) {
            formData.uuid = response.data
            ElMessage.success('验证码发送成功，请注意查收短信')

            // 开始倒计时
            countdown.value = 60
            timer = setInterval(() => {
                countdown.value--
                if (countdown.value <= 0) {
                    clearInterval(timer)
                    timer = null
                }
            }, 1000)
        }
    } catch (error) {
        console.error('发送验证码失败:', error)
        ElMessage.error(error.response?.data?.msg || error.message || '验证码发送失败，请稍后重试')
    } finally {
        isSending.value = false
    }
}

// 提交表单
const submitForm = async () => {
    // 验证所有字段
    const isPhoneValid = validatePhone()
    const isCodeValid = validateVerifyCode()
    const isPasswordValid = validatePassword()
    const isConfirmPasswordValid = validateConfirmPassword()

    if (!isPhoneValid || !isCodeValid || !isPasswordValid || !isConfirmPasswordValid) {
        return
    }

    if (!formData.uuid) {
        ElMessage.error('请先获取验证码')
        return
    }

    try {
        isSubmitting.value = true

        const loadingInstance = ElLoading.service({
            lock: true,
            text: '正在重置密码...',
            background: 'rgba(0, 0, 0, 0.7)'
        })

        const response = await resetPassword({
            phone: formData.phone,
            code: formData.verifyCode,
            uuid: formData.uuid,
            password: formData.newPassword
        })

        loadingInstance.close()

        if (response.code === 200) {
            ElMessage.success('密码重置成功，请使用新密码登录')

            // 清空表单
            Object.keys(formData).forEach(key => {
                formData[key] = ''
            })
            Object.keys(errors).forEach(key => {
                errors[key] = ''
            })

            // 延迟跳转到登录页
            setTimeout(() => {
                router.push('/login')
            }, 1500)
        } else {
            isSubmitting.value = false
            ElMessage.error(response.msg || '密码重置失败')
        }
    } catch (error) {
        console.error('密码重置失败:', error)
        ElMessage.error(error.response?.data?.msg || error.message || '密码重置失败，请稍后重试')
    } finally {
        isSubmitting.value = false
    }
}

// 清理定时器
onUnmounted(() => {
    if (timer) {
        clearInterval(timer)
        timer = null
    }
})
</script>

<template>
    <Background>
        <div class="forgot-password-container">
            <div class="form-box">
                <h2 class="form-title">忘记密码</h2>

                <!-- 手机号输入 -->
                <div class="input-group">
                    <el-input
                        v-model="formData.phone"
                        class="form-input"
                        maxlength="11"
                        placeholder="请输入手机号"
                        @blur="validatePhone"
                        @input="errors.phone = ''"
                    >
                        <template #prefix>
                            <el-icon>
                                <Cellphone/>
                            </el-icon>
                        </template>
                    </el-input>
                    <div v-if="errors.phone" class="error-message">{{ errors.phone }}</div>
                </div>

                <!-- 验证码输入 -->
                <div class="input-group">
                    <div class="verify-code-group">
                        <el-input
                            v-model="formData.verifyCode"
                            class="form-input code-input"
                            maxlength="6"
                            placeholder="请输入验证码"
                            @blur="validateVerifyCode"
                            @input="errors.verifyCode = ''"
                        />
                        <el-button
                            :disabled="countdown > 0 || isSending || !formData.phone"
                            :loading="isSending"
                            class="send-code-btn"
                            @click="sendVerifyCode"
                        >
                            <span v-if="countdown > 0">{{ countdown }}s后重发</span>
                            <span v-else-if="isSending">发送中...</span>
                            <span v-else>获取验证码</span>
                        </el-button>
                    </div>
                    <div v-if="errors.verifyCode" class="error-message">{{ errors.verifyCode }}</div>
                </div>

                <!-- 新密码输入 -->
                <div class="input-group">
                    <el-input
                        v-model="formData.newPassword"
                        class="form-input"
                        placeholder="请输入新密码（6-20位，包含数字、字母、特殊符号）"
                        show-password
                        type="password"
                        @blur="validatePassword"
                        @input="errors.newPassword = ''"
                    >
                        <template #prefix>
                            <el-icon>
                                <Lock/>
                            </el-icon>
                        </template>
                    </el-input>
                    <div v-if="errors.newPassword" class="error-message">{{ errors.newPassword }}</div>
                </div>

                <!-- 确认密码输入 -->
                <div class="input-group">
                    <el-input
                        v-model="formData.confirmPassword"
                        class="form-input"
                        placeholder="请确认新密码"
                        show-password
                        type="password"
                        @blur="validateConfirmPassword"
                        @input="errors.confirmPassword = ''"
                    >
                        <template #prefix>
                            <el-icon>
                                <Lock/>
                            </el-icon>
                        </template>
                    </el-input>
                    <div v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</div>
                </div>

                <!-- 提交按钮 -->
                <el-button
                    :disabled="isSubmitting"
                    :loading="isSubmitting"
                    class="submit-btn"
                    type="primary"
                    @click="submitForm"
                >
                    <span v-if="isSubmitting">重置中...</span>
                    <span v-else>确认重置密码</span>
                </el-button>

                <!-- 返回登录 -->
                <div class="back-to-login">
                    <router-link class="back-link" to="/login">返回登录</router-link>
                </div>
            </div>
        </div>
    </Background>
</template>

<style lang="scss" scoped>
.forgot-password-container {
    position: relative;
    width: 100%;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    padding: 40px 20px;
    box-sizing: border-box;
}

.form-box {
    width: 100%;
    max-width: 500px;
    background-color: #5170ED;
    border-radius: 8px;
    padding: 40px 30px;

    @media (max-width: 768px) {
        padding: 30px 20px;
        margin: 20px;
    }
}

.form-title {
    text-align: center;
    color: white;
    font-size: 24px;
    margin-bottom: 30px;

    @media (max-width: 768px) {
        font-size: 20px;
        margin-bottom: 24px;
    }
}

.input-group {
    margin-bottom: 20px;

    &:last-of-type {
        margin-bottom: 24px;
    }
}

.form-input {
    width: 100%;

    :deep(.el-input__inner) {
        height: 50px;
        font-size: 16px;
        background: white;
        border: none;
        border-radius: 4px;

        &:focus {
            outline: none;
        }
    }

    &.error :deep(.el-input__inner) {
        border: 1px solid #F56C6C;
    }
}

.error-message {
    color: #F56C6C;
    font-size: 14px;
    margin-top: 6px;
    margin-left: 2px;
}

.verify-code-group {
    display: flex;
    gap: 12px;
    align-items: flex-start;

    .code-input {
        flex: 1;
    }

    .send-code-btn {
        height: 50px;
        min-width: 120px;
        background: #E88205;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 14px;

        &:hover:not(:disabled) {
            background: #d67300;
        }

        &:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        @media (max-width: 768px) {
            min-width: 100px;
            font-size: 12px;
        }
    }
}

.submit-btn {
    width: 100%;
    height: 50px;
    background: #14097A;
    color: white;
    font-size: 16px;
    border-radius: 4px;
    border: none;

    &:hover:not(:disabled) {
        background: #0e075e;
    }

    &:disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    @media (max-width: 768px) {
        height: 48px;
    }
}

.back-to-login {
    text-align: center;
    margin-top: 24px;

    .back-link {
        color: #E6F0FF;
        text-decoration: none;
        font-size: 14px;
        transition: color 0.3s ease;

        &:hover {
            color: white;
            text-decoration: underline;
        }
    }
}

:deep(.el-input__prefix) {
    left: 12px;
    display: flex;
    align-items: center;
}

// 响应式设计
@media (max-width: 768px) {
    .forgot-password-container {
        padding: 20px 10px;
    }

    .form-box {
        margin: 10px;
        padding: 24px 16px;
    }

    .verify-code-group {
        flex-direction: column;
        gap: 8px;

        .send-code-btn {
            width: 100%;
        }
    }
}


</style>
