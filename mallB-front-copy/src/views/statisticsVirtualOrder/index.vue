<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="right-content">
            <div class="filter-container">
                <el-form :model="form" class="filter-form" @submit.prevent="handleSearch">
                    <div class="form-row">
                        <el-form-item label="日期">
                            <el-date-picker
                                v-model="form.date"
                                clearable
                                format="YYYY-MM-DD"
                                placeholder="请选择日期"
                                type="date"
                                value-format="YYYY-MM-DD"
                            />
                        </el-form-item>

                        <div class="button-group">
                            <el-button :loading="loading" type="primary" @click="handleSearch">查询</el-button>
                            <el-button :disabled="loading" @click="handleReset">重置</el-button>
                        </div>
                    </div>
                </el-form>
            </div>

            <!-- 表格区域 -->
            <div v-loading="loading" class="table-container" element-loading-background="rgba(0, 0, 0, 0.1)"
                 element-loading-text="加载中...">
                <el-table
                    :data="data"
                    border
                    style="width: 100%"
                >
                    <el-table-column label="名称" min-width="120" prop="label"/>
                    <el-table-column label="数据" min-width="120" prop="value"/>
                </el-table>
            </div>
        </div>
    </div>
</template>
<script setup>
import {useRouter} from 'vue-router'
import {Session} from '../../utils/storage'
import {ElMessage} from 'element-plus'
import {nextTick, onMounted, reactive, ref} from 'vue'
import {formatDate} from "@/utils/common.js";
import {yesterdayAmount} from "@/api/quantification/index.js";

const loading = ref(false)
const data = ref(false)
const state = reactive({
    buttonList: []
})
const router = useRouter()
// 表单数据
const form = reactive({
    date: formatDate(new Date(), 'yyyy-MM-dd')
})


const handleButtonClick = (item) => {
    router.push(item.component)
}
// 获取数据列表
const getList = async () => {
    loading.value = true
    try {
        const params = {
            date: form.date || undefined,
        }
        const res = await yesterdayAmount(params)
        if (res.code === 200) {
            const arr = []
            for (let key in res.data) {
                arr.push(
                    {
                        label: key,
                        value: res.data[key]
                    }
                )
            }
            data.value = arr
        }
    } catch (error) {
        console.error('获取量化统计数据失败:', error)
        ElMessage.error("获取量化统计数据失败，请稍后重试")
    } finally {
        loading.value = false
    }
}

// 搜索
const handleSearch = () => {
    getList()
}

// 重置
const handleReset = () => {
    form.date = formatDate(new Date(), 'yyyy-MM-dd')
    getList()
}

onMounted(() => {
    nextTick(() => {
        let menuList = Session.getMenu()
        let menuId = Session.get('adminMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
                console.log(state.menuList)
            }
        }
        getList()
    })
})

</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow-y: auto;
}

.filter-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #3A58CF;
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 20px;

    .filter-title {
        font-size: 24px;
        font-weight: bold;
        color: white;
    }

    .search-item {
        margin-left: auto;
        margin-bottom: 0;

        :deep(.el-input-group__append) {
            background-color: #3A58CF;
            border: none;

            .el-button {
                color: white;
            }
        }
    }
}

.filter-form {
    .form-row {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;

        .el-form-item {
            margin-right: 20px;
            margin-bottom: 0;
            min-width: 200px;

            :deep(.el-form-item__label) {
                padding-bottom: 0;
                font-weight: normal;
            }
        }

        .time-separator {
            margin: 0 10px;
            color: #666;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-left: auto;
        }
    }
}

.summary-info {
    padding: 10px 0;
    color: #666;
    font-size: 14px;
    display: flex;
    gap: 20px;
}

.table-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    flex: 1;

    :deep(.el-table) {
        font-size: 14px;

        th {
            background-color: #f5f7fa;
            color: #333;
            font-weight: bold;
        }
    }
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}
</style>
