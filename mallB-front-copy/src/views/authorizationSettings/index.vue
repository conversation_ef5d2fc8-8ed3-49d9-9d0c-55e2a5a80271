<template>

    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>

        <div class="right-top">

            <div class="right7">
                <el-row style="margin-left: 30px">
                    <el-radio-group v-model="radio9">
                        <el-radio size="large" value="1">开启</el-radio>
                        <el-radio size="large" value="2">关闭</el-radio>
                    </el-radio-group>
                </el-row>
                <el-row
                    style="
              height: 40px;
              line-height: 40px;
              text-align: left;
              border-top: none;
            "
                >
                    <el-col>
              <span class="evolution-value" style="padding-left: 30px"
              >所合作企业区域的设置</span
              >
                        <hr class="underline"/>
                    </el-col>
                    <el-row style="margin-left: 50px; margin-top: 5px">
                        <span style="margin-right: 50px">企业名称</span>
                        <el-form-item style="margin-right: 5px">
                            <el-input
                                v-model="message11"
                                placeholder="请输入你想要添加的企业名称"
                            />
                        </el-form-item>
                    </el-row>

                    <el-col>
                        <el-row style="margin-left: 50px; margin-top: 5px">
                            <span style="margin-right: 50px">企业ID</span>
                            <el-form-item style="margin-right: 5px">
                                <el-input
                                    v-model="message12"
                                    placeholder="请输入你想要添加的企业名称"
                                    style="width: 200px"
                                />
                            </el-form-item>
                        </el-row>
                    </el-col>
                    <div class="address-authorization-system">
                        <!-- 时间范围选择器 -->
                        <div class="time-range-selector">
                            <el-date-picker
                                v-model="timeRange"
                                end-placeholder="结束日期时间"
                                format="YYYY-MM-DD HH:mm"
                                range-separator="至"
                                start-placeholder="开始日期时间"
                                style="width: 400px"
                                type="datetimerange"
                                value-format="YYYY-MM-DD HH:mm"
                            />
                        </div>

                        <!-- 五级地址联动选择器 -->
                        <div class="address-selector">
                            <el-select
                                v-model="selectedProvince"
                                clearable
                                placeholder="请选择省份"
                                @change="handleProvinceChange"
                            >
                                <el-option
                                    v-for="province in provinces"
                                    :key="province.value"
                                    :label="province.label"
                                    :value="province.value"
                                />
                            </el-select>

                            <el-select
                                v-model="selectedCity"
                                :disabled="!selectedProvince"
                                clearable
                                placeholder="请选择城市"
                                @change="handleCityChange"
                            >
                                <el-option
                                    v-for="city in cities"
                                    :key="city.value"
                                    :label="city.label"
                                    :value="city.value"
                                />
                            </el-select>

                            <el-select
                                v-model="selectedDistrict"
                                :disabled="!selectedCity"
                                clearable
                                placeholder="请选择区县"
                                @change="handleDistrictChange"
                            >
                                <el-option
                                    v-for="district in districts"
                                    :key="district.value"
                                    :label="district.label"
                                    :value="district.value"
                                />
                            </el-select>

                            <el-select
                                v-model="selectedStreet"
                                :disabled="!selectedDistrict"
                                clearable
                                placeholder="请选择街道"
                            >
                                <el-option
                                    v-for="street in streets"
                                    :key="street.value"
                                    :label="street.label"
                                    :value="street.value"
                                />
                            </el-select>


                        </div>

                        <!-- 授权按钮 -->
                        <div class="authorization-actions1">
                            <el-button
                                :disabled="!canAuthorize"
                                class="power"
                                type="primary"
                                @click="handleAuthorization"
                            >
                                授权
                            </el-button>
                        </div>
                        <div class="power-b">
                            <el-button :class="{ active: isActive }" @click="power">B授权</el-button>
                            <el-button :class="{ checked: isChecked }" @click="power1">C授权</el-button>
                        </div>
                    </div>
                    <div class="right5" style="height:350px">
                        <el-row style="margin-left: 30px">
                            <el-radio-group v-model="radio9">
                                <el-radio size="large" value="1">开启</el-radio>
                                <el-radio size="large" value="2">关闭</el-radio>
                            </el-radio-group>
                        </el-row>
                        <el-row
                            style="
              height: 40px;
              line-height: 40px;
              text-align: left;
              border-top: none;
            "
                        >

                            <span class="province">省</span>

                            <el-input
                                v-model="message13"
                                class="rate1"
                                placeholder="请输入省份"
                            />
                            <span class="percent">%</span>

                            <el-row>
                                <span class="province">市</span>

                                <el-input
                                    v-model="message14"
                                    class="rate1"
                                    placeholder="请输入市"
                                />
                                <span class="percent">%</span>

                            </el-row>
                            <el-row>
                                <span class="province">县、区</span>

                                <el-input
                                    v-model="message15"
                                    class="rate1"
                                    placeholder="请输入县"
                                />
                                <span class="percent">%</span>

                            </el-row>
                            <el-row>
                                <span class="province">镇、街</span>

                                <el-input
                                    v-model="message16"
                                    class="rate1"
                                    placeholder="请输入镇"
                                />
                                <span class="percent">%</span>

                            </el-row>
                        </el-row>
                        <el-button class="save-address" type="primary" @click="submit"
                        >保存
                        </el-button
                        >
                    </div>
                </el-row>
            </div>
        </div>
    </div>

</template>
<script lang="ts" setup>
import {computed, getCurrentInstance, onMounted, ref, watch,} from "vue";
import {deleteNewEnterprise} from '@/api/systemSet'
import {useRouter} from "vue-router";
import {Session} from "@/utils/storage";

const enterpriseQuantityData = ref([])
const router = useRouter();
const inputValue1 = ref(null);
const enterpriseInputValue = ref(null);
const inputValue = ref("");
const switchValue = ref("1")//第二个开关
// 初始化开关状态，优先从 localStorage 读取
const customConstantsSwitch = ref(localStorage.getItem('customConstantsSwitch') || "0")
const enterpriseDataSwitch = ref(localStorage.getItem('enterpriseDataSwitch') || "0")
const quantityDataSwitch = ref(localStorage.getItem('quantityDataSwitch') || "0")
const state = reactive({
    buttonList: []
})
const handleButtonClick = (item) => {
    router.push(item.component)
}
// 当开关变化时自动保存到 localStorage
watch(customConstantsSwitch, (newVal) => {
    localStorage.setItem('customConstantsSwitch', newVal)
})

watch(enterpriseDataSwitch, (newVal) => {
    localStorage.setItem('enterpriseDataSwitch', newVal)
})

watch(quantityDataSwitch, (newVal) => {
    localStorage.setItem('quantityDataSwitch', newVal)
})
const radio4 = ref("1");
const radio5 = ref();
const radio6 = ref("1");
const radio7 = ref("1");
const radio8 = ref("1");
const radio9 = ref("1");
const radio10 = ref("1");
const value1 = ref([]);
const value2 = ref([]);
const value3 = ref([]);
const value4 = ref([]);
const message = ref('')
const message1 = ref('')
const message2 = ref('')
const message3 = ref('')
const message4 = ref('')
const message5 = ref('')
const message6 = ref('')
const message7 = ref('')
const message8 = ref('')
const message9 = ref('')
const message10 = ref('')
const message11 = ref('')
const message12 = ref('')
const message13 = ref('')
const message14 = ref('')
const message15 = ref('')
const message16 = ref('')
const displayNumber = ref('')
const tradeAmountTotal = ref(0)
const enteypriseTradeAmountTotal = ref(0)
const resultValue = ref('')
const submit1 = () => {
    if (message.value) {
        // 可以在这里加校验，比如是否为数字等
        displayNumber.value = message.value;
        // ElMessage.success('保存成功');
    } else {
        // ElMessage.warning('请输入数值');
    }
};
const {proxy} = getCurrentInstance()!;
const tableData1 = ref([]);
const tableData = ref([]);
const options = ref([
    {
        value: "Option1",
        province: "广东省",
        city: "揭阳市",
        district: "惠来县",
        street: "周田镇",
    },
    {
        value: "Option2",
        province: "北京市",
        city: "北京市",
        district: "东城区",
        street: "景山街道",
    },
    {
        value: "Option3",
        province: "北京市",
        city: "北京市",
        district: "东城区",
        street: "景山街道",
    },
    {
        value: "Option4",
        province: "北京市",
        city: "北京市",
        district: "东城区",
        street: "景山街道",
    },
    {
        value: "Option5",
        province: "北京市",
        city: "北京市",
        district: "东城区",
        street: "景山街道",
    },
]);
const provinceOptions = ref([
    {value: "gd", label: "广东省"},
    {value: "zj", label: "浙江省"},
    // 其他省份...
]);
// 原始数据
const allOptions = {
    provinces: [
        {value: "gd", label: "广东省"},
        {value: "zj", label: "浙江省"},
    ],
    cities: {
        gd: [
            {value: "gz", label: "广州市"},
            {value: "sz", label: "深圳市"},
        ],
        zj: [
            {value: "hz", label: "杭州市"},
            {value: "nb", label: "宁波市"},
        ],
    },
    districts: {
        gz: [
            {value: "th", label: "天河区"},
            {value: "yx", label: "越秀区"},
        ],
        sz: [
            {value: "ft", label: "福田区"},
            {value: "ns", label: "南山区"},
        ],
    },
    towns: {
        th: [
            {value: "th1", label: "天河南街道"},
            {value: "th2", label: "林和街道"},
        ],
        yx: [
            {value: "yx1", label: "北京街道"},
            {value: "yx2", label: "人民街道"},
        ],
    },
};

const isVisible = ref(true); // 初始设置为 true，即默认显示
const isSelected = ref(false);

// 定义一个方法来切换 isVisible 的值
function toggleDisplay() {
    isVisible.value = true; // 切换 isVisible 的值
}

function closeDisplay() {
    isVisible.value = false;
}

const isFormVisible = ref(true);
const openForm = () => {
    isFormVisible.value = !isFormVisible.value;
};
//删除自定义名称
const handleDelete = (index) => {
    tableData.value.splice(index, 1);
};
//删除企业名称
const handleDelete1 = (id) => {
    var data = {
        enterpriseId: id
    }
    deleteNewEnterprise(data).then(() => {
        getCooperateEnterprise()
        getEnterTranctionData()
    })
};

//点击授权成功
function submitPower() {
    alert("授权成功！！");
}

//一旦授权成功不再授权

// 响应式数据
const timeRange = ref([]);
const selectedProvince = ref("");
const selectedCity = ref("");
const selectedDistrict = ref("");
const selectedStreet = ref("");
const selectedCommunity = ref("");
const authorizedList = ref([]);


// 计算属性
const provinces = computed(() => allOptions.provinces);

const cities = computed(() => {
    if (!selectedProvince.value) return [];
    return allOptions.cities[selectedProvince.value] || [];
});

const districts = computed(() => {
    if (!selectedCity.value) return [];
    return allOptions.districts[selectedCity.value] || [];
});

const streets = computed(() => {
    if (!selectedDistrict.value) return [];
    return allOptions.towns[selectedDistrict.value] || [];
});

const canAuthorize = computed(() => {
    // 必须选择时间范围和至少省份
    return timeRange.value?.length === 2 && selectedProvince.value;
});

// 方法
const handleProvinceChange = () => {
    selectedCity.value = "";
    selectedDistrict.value = "";
    selectedStreet.value = "";
    selectedCommunity.value = "";
};

const handleCityChange = () => {
    selectedDistrict.value = "";
    selectedStreet.value = "";
    selectedCommunity.value = "";
};

const handleDistrictChange = () => {
    selectedStreet.value = "";
    selectedCommunity.value = "";
};

const handleAuthorization = () => {
    if (!canAuthorize.value) return;

    // 获取各层级地址名称
    const provinceName =
        provinces.value.find((p) => p.value === selectedProvince.value)?.label ||
        "";
    const cityName =
        cities.value.find((c) => c.value === selectedCity.value)?.label || "";
    const districtName =
        districts.value.find((d) => d.value === selectedDistrict.value)?.label ||
        "";
    const streetName =
        streets.value.find((s) => s.value === selectedStreet.value)?.label || "";

    authorizedList.value.push({
        startTime: timeRange.value[0],
        endTime: timeRange.value[1],
        province: provinceName,
        city: cityName,
        district: districtName,
        street: streetName,
        fullAddress: `${provinceName}${cityName}${districtName}${streetName}`,

    });
    console.log(authorizedList.value);

    // 清空选择
    timeRange.value = [];
    selectedProvince.value = "";
    selectedCity.value = "";
    selectedDistrict.value = "";
    selectedStreet.value = "";
    selectedCommunity.value = "";
};

const handleRevoke = (index) => {
    authorizedList.value.splice(index, 1);
};
const isActive = ref(false)
const power = () => {
    console.log(111);
    isActive.value = !isActive.value

}
const isChecked = ref(false)
const power1 = () => {
    console.log(111);
    isChecked.value = !isChecked.value

}
// 撤销授权
const revokeAuthorization = (index) => {
    authorizedAddresses.value.splice(index, 1);
};
const buttonList = ["系统设置", "系统查询", "状态数据"];


onMounted(() => {
    let menuList = Session.getMenu()
    let menuId = Session.get('adminMenuId');
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index];
        if (element.menuId == menuId) {
            state.buttonList = element.children
        }
    }

});

</script>
<style lang="scss" scoped>
.active {
    background-color: #2a48bf; /* 或者其他你想要的高亮颜色 */
    color: black; /* 确保文字颜色在背景色上清晰可见 */
}

.checked {
    background-color: #2a48bf; /* 或者其他你想要的高亮颜色 */
    color: black; /* 确保文字颜色在背景色上清晰可见 */
}

.number {
    margin-top: 20px;
    margin-left: 50px;
    width: 200px;
    height: 30px;
    border: 1px solid #ccc;
}

.ys {
    height: 50px;
    text-align: left;
    margin-left: -80px;
    //  background-color: #9fa0d3;
    border: none;
}

.right-top {
    // border: 1px solid #2a48bf;
    width: 1200px;
    width: 100%;
    height: 100%;
    //margin: 0 auto;
    border-radius: 10px;
    margin-left: 20px;

}

.right2 {
    height: 300px;
    // background-color: red;
    margin-top: 100px;
    box-shadow: 0px 15px 15px #2a48bf;
    border-radius: 10px;
}

//保存按钮
.save {
    border-radius: 5px;
    margin-left: 450px;
    margin-top: 70px;
}

.save1 {
    border-radius: 5px;
    margin-left: 450px;
    margin-top: 160px;
}

.underline {
    border: none;
    height: 3px; // 线条粗细
    background-color: #000; // 线条颜色
    margin: 10px 0; // 线条与上下内容的间距
    width: 100%; // 横贯整个父容器宽度
}

.save2 {
    border-radius: 5px;
    margin-left: 450px;
    margin-top: 260px;
}

.content {
    width: 100%;
    border-bottom: 1px solid #000;
}

.btn {
    background-color: #fff;
    color: red;
}

.btn-selected {
    background-color: #7dcf80; /* 绿色 */
    color: #fff;
}

.right3 {
    height: 200px;

    span {
        // padding-left: 30px;
    }

    box-shadow: 5px 15px 10px #ccc;
    margin-top: 50px;
    border-radius: 10px;
    box-shadow: 0px 15px 15px #2a48bf;
}

.right4 {
    height: 80px;
    // background-color: red;
    margin-top: 30px;
    border: 1px solid #2a48bf;
    border-radius: 10px;
}

.right5 {
    width: 100%;

    span {
        text-align: center;

    }

    height: 300px;
    box-shadow: 0px 15px 15px #2a48bf;
    margin-top: 20px;
    border-radius: 10px;
}

.right6 {
    height: 380px;

    span {
        // padding-left: 30px;
    }

    box-shadow: 5px 15px 10px #ccc;
    margin-top: 50px;
    border-radius: 10px;
    box-shadow: 0px 15px 15px #2a48bf;
}

.right7 {
    span {
        text-align: center;
        // color: red;
        // padding-left: 30px;
    }

    height: 460px;
    box-shadow: 0px 15px 15px #2a48bf;
    margin-top: 20px;
    border-radius: 10px;
}

//总计
.count {
    height: 30px;
    width: 200px;
    border: 1px solid #ccc;
    margin-top: 15px;
    border-radius: 3px;
    margin-left: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 170px;
}

//量化比
.rate {
    height: 25px;
    width: 200px;
    margin-top: 20px;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 370px;
}

.rate1 {
    height: 25px;
    width: 200px;
    margin-top: 20px;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 210px;
}

.rate2 {
    height: 25px;
    width: 200px;
    margin-top: 10px;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
}

.rate3 {
    height: 25px;
    width: 200px;
    margin-top: 20px;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 85px;
}

.rate4 {
    height: 25px;
    width: 200px;
    margin-top: 30px;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 180px;
}

.rate5 {
    width: 200px;
    margin-left: 20px;
    height: 30px;
    margin-top: 7px;
}

.percent {
    height: 28px;
    width: 30px;
    margin-top: 18px;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 10px;
}

.adress {
    display: flex;
}

.province {
    width: 200px;
    margin-left: -40px;
    margin-top: 10px;
}

.save-address {
    border-radius: 5px;
    margin-left: 450px;

    margin-top: 200px;
}

.address-container {
    margin-left: 50px;
    display: flex;
    flex-direction: column;
    // gap: 16px; /* 控制下拉框之间的间距 */
    max-width: 300px; /* 控制整体宽度 */
    flex: 6;
}

.address-item {
    display: flex;
    flex-direction: column;
    gap: 7px; /* 控制标签和下拉框之间的间距 */
}

.address-item p {
    margin: 0;
    font-size: 16px;
    color: #000;
}

.block {
    margin-left: 50px;
    width: 300px;
}

//B授权选中

/* 响应式调整 */
@media (max-width: 768px) {
    .address-container {
        max-width: 100%;
    }

    .el-select {
        width: 50% !important; /* 覆盖内联样式 */
    }
}

.address-authorization-system {
    padding: 20px;
    max-width: 1400px;
    // margin:  auto;
    margin-left: 30px;
}

.address-selector {
    width: 400px;
    margin-bottom: 20px;
}

//授权按钮
.authorization-actions1 {
    position: relative;
}

.power-b {
    position: absolute;
    left: 711px;
    top: 300px;
}

.power {
    position: absolute;
    left: 160px;
    top: -140px;
    width: 200px;
    margin-left: 500px;
}

.container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;

    .left-buttons {
        width: 235px;
        height: 100%;
        overflow-y: auto;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        flex-shrink: 0;

        :deep(.el-button.data-button) {
            width: 100%;
            height: 60px;
            background-color: #3A58CF;
            color: white;
            font-size: 20px;
            border-radius: 0;
            border: none;
            margin: 0;
            padding: 0;
            display: block;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: background-color 0.3s;

            &:hover {
                background-color: #2a48bf;
            }

            &:first-child {
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }

            &:last-child {
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
                border-bottom: none;
            }
        }
    }

    .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }

    .left {
        // background-color: #14097a;
        width: 168px;
        max-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 4;

        .Text {
            font-size: 40px;
            color: white;
            writing-mode: vertical-rl; /* 如果需要垂直文字 */
            letter-spacing: 10px;
        }
    }

    .el-input__wrapper {
        margin-top: 20px;
    }

    .right {
        margin-left: 107px;
        margin-top: 55px;
        z-index: 4;
        width: calc(100% - 275px); /* 168 + 107 */
        .main-value {
            font-size: 18px;
            font-weight: bold;
            color: #000;
        }

        .evolution-value {
            margin-top: 30px;
            width: 300px;
            text-align: center;
            font-size: 14px;
            // color: #666;
            margin-top: 5px;
        }
    }
}
</style>
