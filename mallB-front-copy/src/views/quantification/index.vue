<template>
    <div class="container">
        <!-- <img class="bg" src="../../images/bigBackground.png" alt="背景图"> -->
        <div class="left">
            <text class="Text">月表</text>
        </div>
        <div class="right">
            <el-row
                style="height: 50px; line-height: 50px; text-align: left;  border-bottom: 1px solid rgb(40 40 40);  ">
                <el-col :span="8">
                    <div class="input-group">
                        <span class="main-value">量化值</span>
                        <el-input v-model="shopInfo.quantificationValue" class="rate" disabled
                                  placeholder="请输入量化值"/>
                        <el-button type="primary" @click="openQuantificationEvolutionModal">进化</el-button>
                    </div>
                </el-col>
                <el-col :span="10">
                    <div class="input-group">
                        <span class="main-value">量化</span>
                        <el-input v-model="shopInfo.userFaith" class="rate" disabled placeholder="请输入量化值"/>
                        <el-button type="primary" @click="openFaithEvolutionModal">进化</el-button>
                    </div>
                </el-col>
            </el-row>
            <div class="chart-header">
                <div class="header-left">
                    <text class="Text">日程量化率走势图</text>
                    <div style="margin-top: 30px; margin-left: 400px">
                        当前月份：{{ selectedMonth }}
                    </div>
                </div>
            </div>
            <div class="content-container">
                <div class="picBox" style="border: 1px solid red">
                    <div class="chartRight" style="float: right">
                    </div>
                    <div v-show="chartData.rates.length > 0" ref="chartDom"
                         style="width: 1300px; height: 500px; margin-left: 10px">

                    </div>
                    <div v-show="chartData.rates.length === 0" style="width: 1300px; height: 500px; margin-left: 10px">
                        无数据
                    </div>
                </div>
                <el-date-picker
                    v-model="selectedMonth"
                    placeholder="选择月份"
                    style="margin-bottom: 20px"
                    type="month"
                    value-format="YYYY-MM"
                />
            </div>
            <div ref="chart"></div>
        </div>

        <!-- 量化值进化弹窗 -->
        <el-dialog
            v-model="quantificationEvolutionVisible"
            :close-on-click-modal="false"
            :show-close="true"
            title="量化值进化"
            width="400px"
        >
            <div class="dialog-content">
                <div class="info-row">
                    <span class="label">当前量化值：</span>
                    <span class="value">{{ shopInfo.quantificationValue }}</span>
                </div>
                <el-form :model="quantificationEvolutionForm" label-position="top">
                    <el-form-item :rules="[{ required: true, message: '请输入转换数量', trigger: 'blur' }]"
                                  label="请输入要进化的量化值数量：">
                        <el-input-number
                            v-model="quantificationEvolutionForm.amount"
                            :max="shopInfo.quantificationValue || 0"
                            :precision="2"
                            style="width: 100%"
                        ></el-input-number>
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="quantificationEvolutionVisible = false">取消</el-button>
                    <el-button :loading="loading" type="primary" @click="confirmQuantificationEvolution">确认进化
                    </el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 量化进化弹窗 -->
        <el-dialog
            v-model="faithEvolutionVisible"
            :close-on-click-modal="false"
            :show-close="true"
            title="量化进化"
            width="400px"
        >
            <div class="dialog-content">
                <div class="info-row">
                    <span class="label">当前量化：</span>
                    <span class="value">{{ shopInfo.userFaith }}</span>
                </div>
                <el-form :model="faithEvolutionForm" label-position="top">
                    <el-form-item :rules="[{ required: true, message: '请输入转换数量', trigger: 'blur' }]"
                                  label="请输入要进化的量化数量：">
                        <el-input-number
                            v-model="faithEvolutionForm.amount"
                            :max="shopInfo.userFaith || 0"
                            :precision="2"
                            style="width: 100%"
                        ></el-input-number>
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="faithEvolutionVisible = false">取消</el-button>
                    <el-button :loading="loading" type="primary" @click="confirmFaithEvolution">确认进化</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script lang="ts" setup>
import dayjs from "dayjs";
import {onMounted, ref, watch} from "vue";
import * as echarts from "echarts";
import {ElMessage} from "element-plus";
import {convertToFaith, convertToGold, getQuantizationRate, getUserQuantizationValue} from '@/api/quantification'
import {formatDate} from "@/utils/common";
import {Session} from "@/utils/storage";

// 商家信息
const shopInfo = ref({
    quantificationValue: 0,
    userFaith: 0
});
const userType = Session.get('userType');
// 量化值进化相关
const quantificationEvolutionVisible = ref(false);
const quantificationEvolutionForm = ref({
    amount: 0
});

// 量化进化相关
const faithEvolutionVisible = ref(false);
const faithEvolutionForm = ref({
    amount: 0
});

const loading = ref(false);

// 打开量化值进化弹窗
const openQuantificationEvolutionModal = () => {
    if (!shopInfo.value.quantificationValue || shopInfo.value.quantificationValue <= 0) {
        ElMessage.warning("当前没有可用的量化值");
        return;
    }
    quantificationEvolutionForm.value.amount = Math.min(100, shopInfo.value.quantificationValue);
    quantificationEvolutionVisible.value = true;
};

// 打开量化进化弹窗
const openFaithEvolutionModal = () => {
    if (!shopInfo.value.userFaith || shopInfo.value.userFaith <= 0) {
        ElMessage.warning("当前没有可用的量化");
        return;
    }
    faithEvolutionForm.value.amount = Math.min(100, shopInfo.value.userFaith);
    faithEvolutionVisible.value = true;
};

// 确认量化值进化
const confirmQuantificationEvolution = async () => {
    if (!quantificationEvolutionForm.value.amount) {
        ElMessage.warning("请输入要进化的量化值数量");
        return;
    }

    if (quantificationEvolutionForm.value.amount > shopInfo.value.quantificationValue) {
        ElMessage.warning("进化数量不能大于当前量化值");
        return;
    }
    if (quantificationEvolutionForm.value.amount < 100) {
        ElMessage.warning("请输入大于等于100的量化值数量");
        return;
    }

    loading.value = true;
    try {
        const res = await convertToFaith(quantificationEvolutionForm.value.amount);
        if (res.code === 200) {
            ElMessage.success("量化值进化成功");
            quantificationEvolutionVisible.value = false;
            // 重新获取商家信息
            await getShopInfoData();
        } else {
            ElMessage.error(res.msg || "进化失败");
        }
    } catch (error) {
        console.error("进化失败:", error);
        ElMessage.error("进化失败，请稍后重试");
    } finally {
        loading.value = false;
    }
};

// 确认量化进化
const confirmFaithEvolution = async () => {
    if (!faithEvolutionForm.value.amount) {
        ElMessage.warning("请输入要进化的量化数量");
        return;
    }

    if (faithEvolutionForm.value.amount > shopInfo.value.userFaith) {
        ElMessage.warning("进化数量不能大于当前量化");
        return;
    }
    if (faithEvolutionForm.value.amount < 100) {
        ElMessage.warning("不足100无法进化");
        return;
    }

    loading.value = true;
    try {
        const res = await convertToGold(faithEvolutionForm.value.amount);
        if (res.code === 200) {
            ElMessage.success("量化进化成功");
            faithEvolutionVisible.value = false;
            // 重新获取商家信息
            await getShopInfoData();
        } else {
            ElMessage.error(res.msg || "进化失败");
        }
    } catch (error) {
        console.error("进化失败:", error);
        ElMessage.error("进化失败，请稍后重试");
    } finally {
        loading.value = false;
    }
};

// DOM 引用
const chartDom = ref<HTMLElement | null>(null);
let myChart: echarts.ECharts | null = null;

// 当前月份，默认是当前月
const currentMonth = ref(dayjs().format("YYYY-MM"));
const selectedMonth = ref(currentMonth.value);
const chartData = ref({
    dates: [],
    rates: []
});

// 获取图表数据
const fetchChartData = async (searchMonth: string) => {
    const data = {
        "searchMonth": searchMonth
    }
    console.log("data", data)
    try {
        const res = await getQuantizationRate(data);
        console.log('接口返回:', res);
        if (res.code === 200) {
            const list = res.data;
            chartData.value = {
                dates: list.map(item => formatDate(item.quantifyDate, "yyyy-MM-dd")),
                rates: list.map(item => {
                    const rate = (item.quantifyRate * 100).toFixed(6);
                    console.log('原始数据:', item.quantifyRate, '处理后:', rate);
                    return rate;
                })
            };
        } else {
            chartData.value = {dates: [], rates: []};
        }
    } catch (err) {
        console.error('请求失败:', err);
        chartData.value = {dates: [], rates: []};
    }
};

// 更新图表数据的方法
const updateChartData = async (searchMonth: string) => {
    if (!searchMonth || !myChart) { // 检查月份和ECharts实例
        if (!myChart) console.error("ECharts instance is not available.");
        ElMessage.warning("请选择有效的月份");
        return;
    }
    console.log('更新图表数据:', searchMonth)
    loading.value = true;
    try {
        await fetchChartData(searchMonth);

        if (chartData.value.dates.length > 0 && chartData.value.rates.length > 0) {
            myChart.setOption({
                xAxis: {data: chartData.value.dates},
                series: [{data: chartData.value.rates}]
            });
        }
    } catch (error) {
        console.error('更新图表失败:', error);

    } finally {
        loading.value = false;
    }
};

// 初始化图表
const initChart = () => {
    if (!chartDom.value) return;

    myChart = echarts.init(chartDom.value);
    console.log('ECharts instance initialized:', myChart); // 确认实例已创建

    const option = {
        tooltip: {
            trigger: "axis",
            formatter: function (params) {
                let result = params[0].name + '<br/>';
                params.forEach(function (item) {
                    result += item.marker + item.seriesName + ': ' + parseFloat(item.value).toFixed(6) + '%<br/>';
                });
                return result;
            }
        },
        xAxis: {
            type: "category",
            data: [], // 初始化为空
            axisLabel: {
                show: true,
                interval: 0, // 强制显示所有标签
            },
            axisTick: {
                alignWithLabel: true,
            },
        },
        yAxis: {
            type: "value",
            axisLabel: {
                formatter: function (value) {
                    return parseFloat(value).toFixed(6) + '%';
                }
            }
        },
        series: [
            {
                name: "量化率",
                type: "line",
                data: [],
                smooth: true,
                itemStyle: {
                    color: "#409EFF",
                },
            },
        ],
        grid: {
            left: "10%",
            right: "10%",
            bottom: "10%",
            top: "10%",
        },
    };

    myChart.setOption(option);

    // 初始化加载数据
    updateChartData(selectedMonth.value);
};

// 获取商家信息
const getShopInfoData = async () => {
    try {
        const res = await getUserQuantizationValue();
        if (res.code === 200 && res.data) {
            shopInfo.value = {
                quantificationValue: res.data.quantificationValue || 0,
                userFaith: res.data.userFaith || 0
            };
        }
    } catch (error) {
        console.error("获取商家信息失败:", error);
    }
};

// 监听月份变化，更新图表
watch(
    () => selectedMonth.value,
    (newMonth) => {
        if (newMonth) {
            updateChartData(newMonth);
        }
    }
);

// 组件挂载时初始化
onMounted(() => {
    initChart();
    updateChartData(selectedMonth.value);
    getShopInfoData();
});
</script>
<style lang="scss" scoped>
/* 我们必须给一个高度/宽度，不然echarts图表无法显示出来 */
.chart {
    height: 500px;
    width: 500px;
    background-color: pink;
}

.chart-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
}

.rate1 {
    height: 25px;
    width: 300px;
    margin-top: 10px;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 30px;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 10px;
    height: 50px;
}

.rate {
    width: 200px;
}

.main-value {
    font-size: 16px;
    font-weight: bold;
    white-space: nowrap;
}

/* 弹窗样式 */
.dialog-content {
    padding: 10px 0;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 14px;
}

.info-row .label {
    color: #606266;
}

.info-row .value {
    font-weight: bold;
    color: #303133;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}

.container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;

    .left-buttons {
        width: 235px;
        height: 100%;
        overflow-y: auto;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

        :deep(.el-button.data-button) {
            width: 100%;
            height: 60px;
            background-color: #3a58cf;
            color: white;
            font-size: 20px;
            border-radius: 0;
            border: none;
            margin: 0;
            padding: 0;
            display: block;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: background-color 0.3s;

            &:hover {
                background-color: #2a48bf;
            }

            &:first-child {
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }

            &:last-child {
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
                border-bottom: none;
            }
        }
    }

    .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }

    .left {
        background-color: #14097a;
        width: 168px;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 4;

        .Text {
            font-size: 40px;
            color: white;
            writing-mode: vertical-rl; /* 如果需要垂直文字 */
            letter-spacing: 10px;
        }
    }

    .right {
        margin-left: 107px;
        margin-top: 55px;
        z-index: 4;
        width: calc(100% - 275px); /* 168 + 107 */
        .main-value {
            font-size: 18px;
            font-weight: bold;
            color: #000;
        }

        .evolution-value {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 27px;

            .header-left {
                .Text {
                    color: #000;
                    font-size: 36px;
                    font-weight: bold;
                }
            }
        }

        .content-container {
            display: flex;
            gap: 30px; /* 控制图表和时间表的间距 */
        }

        .picBox {
            width: 1200px;
            height: 520px;
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .timeTable {
            width: 285px;
            height: 278px; /* 与图表同高 */
            background-color: #86ebe4;
            border-radius: 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding-top: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

            .Time {
                font-size: 25px;
                color: #000;
                font-weight: bold;
                margin-bottom: 20px;
            }
        }
    }

    .chart {
        width: 100px;
        height: 400px;
        background-color: red;
    }

    .chartRight {
    }

    .count {
        margin-left: 20px;
        margin-right: 10px;
        margin-top: 10px;
        display: inline-block;
        height: 25px;
        width: 90px;
        border: 1px solid #ccc;
    }
}
</style>
