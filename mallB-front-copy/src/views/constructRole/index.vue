<template>
    <!--  <HomeBg>-->
    <div class="container">
        <img alt="背景图" class="bg" src="../../images/bigBackground.png">
        <div class="form-container">
            <!-- 每个输入框组 -->
            <div class="input-group">
                <el-input
                    v-model="state.ruleForm.roleName"
                    class="form-input"
                    placeholder="角色名称"
                />
                <span class="reserved-text">显示保留</span>
            </div>

            <div class="input-group">
                <el-input
                    v-model="state.ruleForm.roleKey"
                    class="form-input"
                    placeholder="备注"
                    type="text"
                />
                <span class="reserved-text">显示保留</span>
            </div>

            <!-- 负责授权特殊布局 -->
            <div class="input-group">
                <el-cascader v-model="selectedOptions" :options="state.ruleForm.options" :props="props"
                             clearable placeholder="负责授权" size="large" @change="handleChange"/>
            </div>

            <!-- 保存按钮 -->
            <div class="save-button-container">
                <el-button class="save-button" @click="handleToNormalManager">保存</el-button>
            </div>
        </div>
    </div>
    <!--  </HomeBg>-->
</template>
<script lang="ts" setup>
import {ref} from 'vue'
import {useRouter} from 'vue-router'
import {platformRole} from '../../stores/platformRole'

const router = useRouter();
const selectedOptions = ref([])
const state = reactive({
    ruleForm: {
        roleName: "",
        roleKey: "",
        menuIds: [],
        options: []
    }
})
const props = {multiple: true}

// 菜单数据
const getTreeList = async () => {
    try {
        let result = await platformRole().GetMenuTreeList();
        state.ruleForm.options = result.data
    } finally {

    }
}
const handleChange = (cascaderValue) => {
    const flatValues = cascaderValue.flat();
    state.ruleForm.menuIds = flatValues
}
const handleToNormalManager = async () => {
    // console.log(state.ruleForm,'state.ruleForm')
    state.ruleForm.options = []
    try {
        let result = await platformRole().SaveRole(state.ruleForm);
        console.log(result, 'result')
        // router.push({
        //   path:'/roleList'
        // })
    } finally {

    }
}
onMounted(() => {
    getTreeList()
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }

    .form-container {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        max-width: 1200px;

        .input-group {
            display: flex;
            align-items: center;
            margin-bottom: 61px;
            width: 100%;
            margin-left: 399px;

            .form-input {
                width: 805px;

                :deep(.el-input__wrapper) {
                    height: 53px;
                    font-size: 16px;
                }
            }

            .reserved-text {
                margin-left: 55px;
                color: #FF8D1A;
                font-size: 16px;
                white-space: nowrap;
            }
        }

        // 负责授权特殊布局

        // 单选框容器
        .radio-container {
            width: 100%;
            margin-top: 20px;
            margin-bottom: 40px;

            .radio-group {
                width: 805px;
                margin: 0 auto;

                :deep(.el-radio-group) {
                    display: flex;
                    justify-content: space-between;
                    width: 100%;
                }

                .radio-item {
                    display: flex;
                    align-items: center;

                    :deep(.el-radio) {
                        margin-right: 8px;
                        font-size: 16px;

                        .el-radio__input {
                            .el-radio__inner {
                                border-radius: 2px; // 方形单选框
                                width: 24px;
                                height: 24px;
                            }
                        }
                    }
                }
            }
        }

        // 保存按钮
        .save-button-container {
            width: 805px;
            margin: 0 auto;
            text-align: center;

            .save-button {
                width: 120px;
                height: 40px;
                background-color: #14097A;
                color: white;
                border: none;
                font-size: 16px;
                border-radius: 4px;

                &:hover {
                    background-color: #1a0da0;
                }

                &:active {
                    background-color: #0f0657;
                }
            }
        }
    }
}
</style>
