<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="rightBox">
            <div class="head">
                <span class="title">账号注销审核</span>
            </div>
            <div class="main">
                <!-- 搜索栏 -->
                <div class="search-box">
                    <el-form ref="queryFormRef" :inline="true" :model="state.queryParams">
                        <el-form-item label="账户" prop="username">
                            <el-input v-model="state.queryParams.username" clearable placeholder="请输入账户名称"/>
                        </el-form-item>
                        <el-form-item label="联系电话" prop="phone">
                            <el-input v-model="state.queryParams.phone" clearable placeholder="请输入联系电话"/>
                        </el-form-item>
                        <el-form-item label="审核状态" prop="auditStatus">
                            <el-select v-model="state.queryParams.auditStatus" clearable
                                       placeholder="请选择审核状态" style="width: 120px">
                                <el-option label="待审核" value="0"/>
                                <!--                                <el-option label="已通过" value="1"/>-->
                                <el-option label="已拒绝" value="2"/>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleQuery">查询</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 数据表格 -->
                <el-table v-loading="state.loading" :data="state.auditList" border class="data-table" stripe>
                    <el-table-column align="center" label="序号" type="index" width="180"/>
                    <el-table-column label="账户" prop="username" show-overflow-tooltip width="230"/>
                    <el-table-column align="center" label="账户类型" prop="userType" width="180">
                        <template #default="scope">
                            <el-tag v-if="scope.row.userType === 'CB'" type="warning">代销</el-tag>
                            <el-tag v-else-if="scope.row.userType === 'B'" type="success">商家</el-tag>
                            <el-tag v-else-if="scope.row.userType === 'C'" type="info">个人</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="联系电话" prop="phone" width="160"/>
                    <el-table-column align="center" label="审核状态" prop="auditStatus" width="160">
                        <template #default="scope">
                            <el-tag v-if="scope.row.auditStatus === '0'" type="info">待审核</el-tag>
                            <!--                            <el-tag v-else-if="scope.row.auditStatus === '1'" type="success">已通过</el-tag>-->
                            <el-tag v-else-if="scope.row.auditStatus === '2'" type="danger">已拒绝</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="申请时间" prop="createTime" width="200">
                        <template #default="scope">
                            {{ formatDate(scope.row.createTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" min-width="180">
                        <template #default="scope">
                            <!--                            <el-button link type="primary" @click="handleDetail(scope.row)">查看</el-button>-->
                            <el-button
                                v-if="scope.row.auditStatus === '0'"
                                link
                                type="success"
                                @click="handleAudit(scope.row, '1')"
                            >通过
                            </el-button>
                            <el-button
                                v-if="scope.row.auditStatus === '0'"
                                link
                                type="danger"
                                @click="handleAudit(scope.row, '2')"
                            >拒绝
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination
                        v-if="state.pagination.total > 0"
                        v-model:current-page="state.pagination.currentPage"
                        v-model:page-size="state.pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="state.pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>

        <!-- 详情对话框 -->
        <!--        <el-dialog v-model="state.detailDialog.visible" append-to-body title="商家申请详情" width="800px">-->
        <!--            <el-descriptions :column="2" border>-->
        <!--                <el-descriptions-item label="商家名称">{{ state.detailDialog.form.name }}</el-descriptions-item>-->
        <!--                <el-descriptions-item label="商家类型">-->
        <!--                    <el-tag v-if="state.detailDialog.form.type === '0'" type="warning">代销</el-tag>-->
        <!--                    <el-tag v-else-if="state.detailDialog.form.type === '1'" type="success">企业</el-tag>-->
        <!--                </el-descriptions-item>-->
        <!--                <el-descriptions-item label="联系电话">{{ state.detailDialog.form.phone }}</el-descriptions-item>-->
        <!--                <el-descriptions-item label="企业邮箱">{{ state.detailDialog.form.email }}</el-descriptions-item>-->
        <!--                <el-descriptions-item label="营业时间">{{-->
        <!--                        state.detailDialog.form.businessHours-->
        <!--                    }}-->
        <!--                </el-descriptions-item>-->
        <!--                <el-descriptions-item label="企业法人">{{ state.detailDialog.form.legalPerson }}</el-descriptions-item>-->
        <!--                <el-descriptions-item label="法人电话">{{-->
        <!--                        state.detailDialog.form.legalPersonPhone-->
        <!--                    }}-->
        <!--                </el-descriptions-item>-->
        <!--                <el-descriptions-item label="法人身份证">{{-->
        <!--                        state.detailDialog.form.legalPersonIdCard-->
        <!--                    }}-->
        <!--                </el-descriptions-item>-->
        <!--                <el-descriptions-item label="营业执照号">{{-->
        <!--                        state.detailDialog.form.businessLicenseNumber-->
        <!--                    }}-->
        <!--                </el-descriptions-item>-->
        <!--                <el-descriptions-item label="营业执照有效期">{{-->
        <!--                        state.detailDialog.form.businessLicenseExpireTime-->
        <!--                    }}-->
        <!--                </el-descriptions-item>-->
        <!--                <el-descriptions-item :span="2" label="商家简介">{{-->
        <!--                        state.detailDialog.form.introduction-->
        <!--                    }}-->
        <!--                </el-descriptions-item>-->
        <!--            </el-descriptions>-->

        <!--            <div class="image-container">-->
        <!--                <div v-if="state.detailDialog.form.logo" class="image-item">-->
        <!--                    <p>商家Logo</p>-->
        <!--                    <el-image :preview-src-list="[state.detailDialog.form.logo]" :src="state.detailDialog.form.logo"-->
        <!--                              fit="cover"/>-->
        <!--                </div>-->
        <!--                <div v-if="state.detailDialog.form.businessLicense" class="image-item">-->
        <!--                    <p>营业执照</p>-->
        <!--                    <el-image :preview-src-list="[state.detailDialog.form.businessLicense]"-->
        <!--                              :src="state.detailDialog.form.businessLicense" fit="cover"/>-->
        <!--                </div>-->
        <!--                <div v-if="state.detailDialog.form.idCardFront" class="image-item">-->
        <!--                    <p>身份证正面</p>-->
        <!--                    <el-image :preview-src-list="[state.detailDialog.form.idCardFront]"-->
        <!--                              :src="state.detailDialog.form.idCardFront" fit="cover"/>-->
        <!--                </div>-->
        <!--                <div v-if="state.detailDialog.form.idCardBack" class="image-item">-->
        <!--                    <p>身份证反面</p>-->
        <!--                    <el-image :preview-src-list="[state.detailDialog.form.idCardBack]"-->
        <!--                              :src="state.detailDialog.form.idCardBack" fit="cover"/>-->
        <!--                </div>-->
        <!--                <div v-if="state.detailDialog.form.idCardHand" class="image-item">-->
        <!--                    <p>手持身份证</p>-->
        <!--                    <el-image :preview-src-list="[state.detailDialog.form.idCardHand]"-->
        <!--                              :src="state.detailDialog.form.idCardHand" fit="cover"/>-->
        <!--                </div>-->
        <!--            </div>-->

        <!--            <div v-if="state.detailDialog.form.auditStatus === '0'" class="audit-actions">-->
        <!--                <el-button type="success" @click="handleAudit(state.detailDialog.form, '1')">通过审核</el-button>-->
        <!--                <el-button type="danger" @click="handleAudit(state.detailDialog.form, '2')">拒绝申请</el-button>-->
        <!--            </div>-->
        <!--        </el-dialog>-->

        <!-- 审核对话框 -->
        <el-dialog
            v-model="state.auditDialog.visible"
            :title="state.auditDialog.form.auditStatus === '1' ? '审核通过' : '审核拒绝'"
            append-to-body
            width="500px"
        >
            <el-form ref="auditFormRef" :model="state.auditDialog.form" :rules="state.auditDialog.rules"
                     label-width="100px">
                <el-form-item label="审核备注" prop="auditRemark">
                    <el-input v-model="state.auditDialog.form.auditRemark" :rows="4" placeholder="请输入审核备注"
                              type="textarea"/>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="state.auditDialog.visible = false">取 消</el-button>
                <el-button type="primary" @click="submitAudit">确 定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
// @ts-ignore
import {auditAccount, getAccountAuditList} from '@/api/account/accountAudit'
import type {FormInstance} from 'element-plus'
import {ElMessage, ElMessageBox} from 'element-plus'
import {nextTick, onMounted, reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
// @ts-ignore
import {Session} from "@/utils/storage"

// 定义菜单项接口
interface MenuItem {
    menuName: string;
    component: string;

    [key: string]: any;
}

// 定义商家审核接口
// interface ShopAudit {
//     id: number | string;
//     name: string;
//     type: string;
//     phone: string;
//     email?: string;
//     province?: string;
//     city?: string;
//     district?: string;
//     town?: string;
//     address?: string;
//     businessHours?: string;
//     legalPerson?: string;
//     legalPersonPhone?: string;
//     legalPersonIdCard?: string;
//     businessLicenseNumber?: string;
//     businessLicenseExpireTime?: string;
//     introduction?: string;
//     logo?: string;
//     businessLicense?: string;
//     idCardFront?: string;
//     idCardBack?: string;
//     idCardHand?: string;
//     auditStatus: string;
//     auditRemark?: string;
//     createTime?: string | number;
//
//     [key: string]: any;
// }

// 定义账户注销审核接口
interface UserAccountAudit {
    id: number | string;
    // userId: number | string;
    username: string;
    nickname: string;
    phone: string;
    email?: string;
    userType: string;  // 'C'消费者, 'CB'代销, 'B'商家
    deleteReason?: string;
    auditStatus: string;  // '0'待审核, '1'通过, '2'拒绝
    auditRemark?: string;
    auditorId?: number | string;
    auditTime?: string | number;
    remark?: string;
    delFlag?: string;  // '0'存在, '2'删除
    createTime?: string | number;


    [key: string]: any;
}


// 定义API响应接口
interface ApiResponse<T> {
    code: number;
    msg: string;
    rows?: T[];
    total?: number;
    data?: T;
}

const router = useRouter()
const queryFormRef = ref<FormInstance | null>(null)
const auditFormRef = ref<FormInstance | null>(null)

const state = reactive({
    buttonList: [] as MenuItem[],
    loading: false,
    auditList: [] as UserAccountAudit[],
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        username: '',
        phone: '',
        auditStatus: ''
    },
    pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
    },
    detailDialog: {
        visible: false,
        form: {} as UserAccountAudit
    },
    auditDialog: {
        visible: false,
        form: {
            id: '',
            auditStatus: '',
            auditRemark: ''
        },
        rules: {
            auditRemark: [
                {required: true, message: '请输入审核备注', trigger: 'blur'},
                {min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur'}
            ]
        }
    }
})

// 按钮点击事件
const handleButtonClick = (item: MenuItem) => {
    router.push(item.component)
}

// 格式化日期
const formatDate = (timestamp: string | number): string => {
    if (!timestamp) return '--'
    const date = new Date(timestamp)
    return date.toLocaleString()
}

// 获取完整地址
// const getFullAddress = (row: UserAccountAudit): string => {
//     const address: string[] = []
//     if (row.province) address.push(row.province)
//     if (row.city) address.push(row.city)
//     if (row.district) address.push(row.district)
//     if (row.town) address.push(row.town)
//     if (row.address) address.push(row.address)
//     return address.join(' ')
// }

// 查询商家审核列表
const getList = async () => {
    state.loading = true
    try {
        const params = {
            ...state.queryParams,
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize
        }
        const response = await getAccountAuditList(params) as unknown as ApiResponse<UserAccountAudit>
        state.auditList = response.rows || []
        state.pagination.total = response.total || 0
    } catch (error) {
        console.error('获取审核列表失败', error)
    } finally {
        state.loading = false
    }
}

// 搜索按钮操作
const handleQuery = () => {
    state.pagination.currentPage = 1
    getList()
}

// 重置按钮操作
const resetQuery = () => {
    if (queryFormRef.value) {
        queryFormRef.value.resetFields()
    }
    handleQuery()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
    state.pagination.pageSize = val
    getList()
}

// 分页页码变化
const handleCurrentChange = (val: number) => {
    state.pagination.currentPage = val
    getList()
}

// 查看详情
// const handleDetail = async (row: UserAccountAudit) => {
//     try {
//         state.loading = true
//         const response = await getShopAuditDetail(row.id) as unknown as ApiResponse<UserAccountAudit>
//         if (response.data) {
//             state.detailDialog.form = response.data
//         }
//         state.detailDialog.visible = true
//     } catch (error) {
//         console.error('获取详情失败', error)
//         ElMessage.error('获取详情失败')
//     } finally {
//         state.loading = false
//     }
// }

// 审核操作
const handleAudit = (row: UserAccountAudit, status: string) => {
    state.auditDialog.form = {
        id: String(row.id),
        auditStatus: status,
        auditRemark: ''
    }
    state.auditDialog.visible = true
}

// 提交审核
const submitAudit = async () => {
    if (!auditFormRef.value) return

    await auditFormRef.value.validate(async (valid) => {
        if (valid) {
            try {
                const statusText = state.auditDialog.form.auditStatus === '1' ? '通过' : '拒绝'

                await ElMessageBox.confirm(
                    `确认${statusText}该商家申请吗？`,
                    '提示',
                    {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }
                )

                state.loading = true
                await auditAccount(state.auditDialog.form)
                ElMessage.success(`审核${statusText}成功`)
                state.auditDialog.visible = false
                state.detailDialog.visible = false
                getList()
            } catch (error) {
                console.error('审核操作失败', error)
            } finally {
                state.loading = false
            }
        }
    })
}

// 初始化
onMounted(() => {
    nextTick(() => {
        const menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId')

        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index]
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }

        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index]
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })

    getList()
})
</script>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 200px);
    overflow-y: scroll;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.rightBox {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    margin-left: 240px;
}

.head {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
        font-size: 22px;
        font-weight: bold;
        color: #333;
    }
}

.main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.search-box {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.data-table {
    flex: 1;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 10px 0;
}

.image-container {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
}

.image-item {
    width: 150px;
    margin: 10px;
    text-align: center;
}

.image-item p {
    margin-bottom: 5px;
}

.image-item :deep(.el-image) {
    width: 150px;
    height: 150px;
    border: 1px solid #eee;
}

.audit-actions {
    margin-top: 20px;
    text-align: center;
}

:deep(.el-tag) {
    padding: 0 10px;
}

:deep(.el-descriptions) {
    margin-bottom: 20px;
}

:deep(.el-descriptions-item__label) {
    width: 120px;
    text-align: right;
}
</style>
