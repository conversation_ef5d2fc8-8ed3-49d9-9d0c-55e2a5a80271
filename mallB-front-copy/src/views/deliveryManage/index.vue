<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>

        <div class="right-content">
            <div class="header">
                <span class="deliveryList">配送列表</span>
                <el-button class="newDelivery" @click="handleAddDelivery">
                    新增配送+
                </el-button>
            </div>

            <div class="table-container">
                <el-table
                    :data="deliveryData"
                    border
                    height="691"
                    style="width: 1291px"
                >
                    <el-table-column
                        label="账号"
                        prop="account"
                        sortable
                    >
                        <template #default="{ row }">
                            {{ row.id }}
                        </template>
                    </el-table-column>

                    <el-table-column
                        label="快递公司名称"
                        prop="deliveryName"
                    />

                    <el-table-column
                        label="快递公司编码"
                        prop="deliveryCode"
                    />

                    <el-table-column
                        label="操作"
                    >
                        <template #default="{ row }">
                            <el-button
                                size="small"
                                type="warning"
                                @click="handleEdit(row)"
                            >
                                修改
                            </el-button>
                            <el-button
                                size="small"
                                type="danger"
                                @click="handleDelete(row)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <el-dialog v-model="state.dialogTableVisible" width="800" @close="closeDialog">
            <template #title>
                <div class="dialog-title">
                    <span class="dialog-title-text">{{ state.form.id == null ? "新增" : "修改" }}配送信息</span>
                </div>
            </template>
            <div class="dialog-content">
                <el-form ref="form" :model="state.form" label-width="150px">
                    <el-form-item label="配送公司名称">
                        <el-input v-model="state.form.deliveryName"></el-input>
                    </el-form-item>
                    <el-form-item label="配送公司编码">
                        <el-input v-model="state.form.deliveryCode"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="submit()">保存</el-button>
                        <el-button @click="state.dialogTableVisible = false">取消</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </el-dialog>

    </div>
</template>
<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {ref} from 'vue'
import {ElMessageBox} from 'element-plus'
import {Session} from '../../utils/storage'
import {delivery} from '../../stores/delivery'

const router = useRouter()
// 按钮点击处理
const handleButtonClick = (item) => {
    router.push(item.component)
}

// 配送数据表格
const deliveryData = ref([])
const state = reactive({
    buttonList: [],
    dialogTableVisible: false,
    form: {
        id: null,
        deliveryName: '',
        deliveryCode: ''
    }
})

// 关闭dialog，清除表单数据
const closeDialog = () => {
    state.dialogTableVisible = false
    state.form = {
        id: null,
        deliveryName: '',
        deliveryCode: ''
    }
}
// 新增配送
const handleAddDelivery = () => {
    state.dialogTableVisible = true
}

// 修改配送信息
const handleEdit = (row) => {
    delivery().GetInfo(row.id).then(result => {
        state.form = result.data;
        state.dialogTableVisible = true;
    })
}
const submit = () => {
    if (state.form.id == null) {
        delivery().Add(state.form)
    } else {
        delivery().Update(state.form)
    }
    closeDialog()
    getList()
}

// 删除配送
const handleDelete = (row) => {
    ElMessageBox.confirm(`确定删除 ${row.deliveryName} 的配送信息?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        delivery().Delete(row.id)
        getList()
    })
}
const getList = () => {
    delivery().List().then(result => {
        deliveryData.value = result.rows;
    })
}
onMounted(() => {
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })
    getList()
})
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    max-height: 100vh;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 30px);
    overflow-y: scroll;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.right-content {
    flex: 1;
    padding: 20px;
    margin-left: 240px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .deliveryList {
        font-size: 24px;
        font-weight: bold;
        color: #000;
    }

    .newDelivery {
        width: 150px;
        height: 40px;
        background-color: #3A58CF;
        color: white;
        border: none;
        font-size: 16px;

        &:hover {
            background-color: #2a48bf;
        }
    }
}

.table-container {
    margin-top: 20px;

    :deep(.el-table) {
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

        th {
            background-color: #f5f7fa;
            font-weight: bold;
        }

        .el-button {
            margin: 0 5px;
        }
    }
}
</style>
