<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="main">
            <div class="container">
                <!-- 下面的盒子布局 -->
                <div class="boxes-container">
                    <!-- 每个盒子 -->
                    <div class="box box-clickable">
                        <div class="box-row">
                            <span class="box-title">今日交易</span>
                            <span class="box-badge">{{ state.data.todayOrderCount || "0" }}</span>
                        </div>
                    </div>
                    <div class="box box-clickable">
                        <div class="box-row">
                            <span class="box-title">累计交易</span>
                            <span class="box-badge">{{ state.data.totalOrderCount || "0" }}</span>
                        </div>
                    </div>
                    <div class="box box-clickable">
                        <div class="box-row">
                            <span class="box-title">今日订单（数）</span>
                            <span class="box-badge">{{ state.data.todayOrderNum || "0" }}</span>
                        </div>
                    </div>
                    <div v-if="userType == 2" class="box box-clickable">
                        <div class="box-row">
                            <span class="box-title">粉丝量</span>
                            <span class="box-badge">{{ state.data.fans || "0" }}</span>
                        </div>
                    </div>
                    <div class="box box-clickable" @click="toOrder('0')">
                        <div class="box-row">
                            <span class="box-title">待付款订单</span>
                            <span class="box-badge">{{ state.data.pendingPaymentOrderCount || "0" }}</span>
                        </div>
                    </div>
                    <div class="box box-clickable" @click="toOrder('2')">
                        <div class="box-row">
                            <span class="box-title">待发货订单</span>
                            <span class="box-badge">{{ state.data.pendingDeliveryOrderCount || "0" }}</span>
                        </div>
                    </div>
                    <div v-if="userType != 3"class="box box-clickable" @click="returnOrders()">
                        <div class="box-row">
                            <span class="box-title">退货待处理订单</span>
                            <span class="box-badge">{{ state.data.returnOrderCount || "0" }}</span>
                        </div>
                    </div>
                    <div class="box box-clickable">
                        <div class="box-row" @click="handleSubBoxClick('/quantification')">
                            <span class="box-title">量化率</span>
                            <span class="box-badge">{{ state.data.quantificationRate || "0" }}</span>
                        </div>
                    </div>

                </div>
                <!-- 新增的内容区域 -->
                <div class="content-wrapper">
                    <div class="content-area">
                        <p>这里是内容区域，宽度为 100%，但外层有 padding</p>
                    </div>
                </div>

                <!-- 新增子盒子区域 -->
                <div class="sub-boxes-container">
                    <div v-if="userType == 3" class="sub-box" @click="showCommissionDialog">
                        <span class="box-title">佣金结算</span>
                        <span class="box-badge">{{ state.data.deductionMoney || "0" }}</span>
                    </div>
                    <div v-if="userType == 2" class="sub-box" @click="handleSubBoxClick('/commodity')">
                        添加商品
                    </div>
                    <div v-if="userType == 2" class="sub-box" @click="handleSubBoxClick('/productList')">
                        商品列表
                    </div>
                    <div v-if="userType == 2" class="sub-box" @click="handleSubBoxClick('/technicalDrainage')">
                        <span class="box-title">流量库存</span>
                        <span class="box-badge">{{ state.data.technologyDrainageCount || "0" }}</span>
                    </div>
                    <div v-if="userType == 2" class="sub-box" @click="handleSubBoxClick('/settlePayment')">
                        货款结算
                    </div>
                    <div v-if="userType == 2 || userType == 3" class="sub-box" @click="showDonationDialog">
                        <span class="box-title">平台促销金</span>
                        <span class="box-badge">{{ state.data.platformPromotionGold || "0" }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 平台促销金赠送对话框 -->
        <el-dialog
            v-model="state.donationDialogVisible"
            destroy-on-close
            title="平台促销金赠送"
            width="1200px"
        >
            <div class="donation-container">
                <!-- 赠送表单 -->
                <div class="donation-form">
                    <div class="form-header">
                        <h3>赠送促销金</h3>
                        <div class="balance-info">
                            <span>当前平台促销金：</span>
                            <span class="balance-amount">¥{{ state.data.platformPromotionGold || "0" }}</span>
                        </div>
                    </div>
                    <el-form ref="donationFormRef" :model="state.donationForm" :rules="donationRules"
                             label-width="100px">
                        <el-form-item label="用户手机号" prop="phone">
                            <el-input
                                v-model="state.donationForm.phone"
                                clearable
                                maxlength="11"
                                placeholder="请输入用户手机号码"
                                show-word-limit
                                style="width: 800px"
                            >
                                <template #prefix>
                                    <el-icon>
                                        <Phone/>
                                    </el-icon>
                                </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="赠送金" prop="amount">
                            <el-input-number
                                v-model="state.donationForm.amount"
                                :max="state.data.platformPromotionGold || 0"
                                :precision="2"
                                :step="1"
                                placeholder="请输入赠送金"
                                style="width: 800px"
                                @change="calculateActualAmount"
                            />
                            <div class="amount-tip">赠送后将转化为用户的平台补贴金</div>
                        </el-form-item>

                        <!-- 千六损耗显示 -->
                        <el-form-item label="千六损耗">
                            <el-input
                                :value="formatCurrency(state.calculatedAmounts.lossAmount)"
                                readonly
                                style="width: 800px"
                            >
                                <template #prefix>
                                    <span style="color: #f56c6c;">-</span>
                                </template>
                            </el-input>
                            <div class="amount-tip">按千分之六计算的损耗费用</div>
                        </el-form-item>

                        <!-- 实际到账显示 -->
                        <el-form-item label="实际到账">
                            <div>
                                <el-input
                                    :value="formatCurrency(state.calculatedAmounts.actualAmount)"
                                    class="actual-amount-input"
                                    readonly
                                    style="width: 800px"
                                >
                                    <template #prefix>
                                        <span style="color: #67c23a;">¥</span>
                                    </template>
                                </el-input>
                            </div>
                            <div class="amount-tip success-tip">用户实际收到的补贴金金额</div>
                        </el-form-item>
                        <el-form-item label="操作密码" prop="password">
                            <el-input
                                v-model="state.donationForm.password"
                                placeholder="请输入操作密码"
                                show-password
                                type="password"
                                style="width: 800px"
                            >
                                <template #prefix>
                                    <el-icon>
                                        <Lock/>
                                    </el-icon>
                                </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="备注" prop="remark">
                            <el-input
                                v-model="state.donationForm.remark"
                                :autosize="{ minRows: 3, maxRows: 5 }"
                                maxlength="200"
                                placeholder="请输入备注信息（选填）"
                                show-word-limit
                                type="textarea"
                                style="width: 800px"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button :loading="state.donating" type="primary" @click="submitDonation">确认赠送
                            </el-button>
                            <el-button @click="resetDonationForm">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 赠送记录表格 -->
                <div class="donation-records">
                    <div class="records-header">
                        <h3>赠送记录</h3>
                        <div class="search-box">
                            <el-date-picker
                                v-model="state.beginTime"
                                clearable
                                format="YYYY-MM-DD HH:mm:ss"
                                placeholder="开始时间"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                            >
                            </el-date-picker>
                            <el-date-picker
                                v-model="state.endTime"
                                clearable
                                format="YYYY-MM-DD HH:mm:ss"
                                placeholder="结束时间"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                            >
                            </el-date-picker>
                            <el-input
                                v-model="state.searchPhone"
                                clearable
                                placeholder="搜索手机号"
                                style="width: 200px"
                            >
                            </el-input>
                            <el-button type="success" @click="searchRecords">
                                搜索
                            </el-button>
                            <el-button :loading="state.exporting" type="success" @click="exportRecords">
                                <el-icon>
                                    <Download/>
                                </el-icon>
                                导出
                            </el-button>
                        </div>
                    </div>

                    <el-table
                        v-loading="state.recordsLoading"
                        :data="state.donationRecords"
                        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
                        border
                        empty-text="暂无赠送记录"
                        style="width: 100%"
                    >
                        <el-table-column label="接收用户" prop="receiverPhone">
                            <template #default="scope">
                                <el-tag effect="plain" size="small">{{ scope.row.receiverPhone }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="赠送金" prop="amount">
                            <template #default="scope">
                                <span class="amount">¥{{ scope.row.amount }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="千六损耗">
                            <template #default="scope">
                                <span class="loss-amount">-¥{{ scope.row.lossAmount }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="实际到账">
                            <template #default="scope">
                                <span class="actual-amount">¥{{ scope.row.actualAmount }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="赠送时间" prop="donationTime">
                            <template #default="scope">
                                <el-tooltip effect="light" placement="top">
                                    <span>{{ scope.row.donationTime }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" prop="status">
                            <template #default="scope">
                                <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'" size="small">
                                    {{ scope.row.status === '1' ? '成功' : '失败' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="备注" min-width="150" prop="remark" show-overflow-tooltip/>
                    </el-table>

                    <div class="pagination">
                        <el-pagination
                            v-model:current-page="state.currentPage"
                            v-model:page-size="state.pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            :total="state.total"
                            background
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                </div>
            </div>
        </el-dialog>

        <!-- 佣金结算对话框 -->
        <el-dialog
            v-model="state.commissionDialogVisible"
            destroy-on-close
            width="1200px"
        >
            <div class="donation-container">
                <!-- 赠送表单 -->
                <div class="donation-form">
                    <div class="form-header">
                        <h3>赠送</h3>
                        <div class="balance-info">
                            <span>当前佣金：</span>
                            <span class="balance-amount">¥{{ state.data.deductionMoney || "0" }}</span>
                        </div>
                    </div>
                    <el-form ref="commissionFormRef" :model="state.commissionForm" :rules="commissionRules"
                             label-width="100px">
                        <el-form-item label="用户手机号" prop="phone">
                            <el-input
                                v-model="state.commissionForm.phone"
                                clearable
                                maxlength="11"
                                placeholder="请输入用户手机号码"
                                show-word-limit
                                style="width: 800px"
                            >
                                <template #prefix>
                                    <el-icon>
                                        <Phone/>
                                    </el-icon>
                                </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="赠送金" prop="amount">
                            <el-input-number
                                v-model="state.commissionForm.amount"
                                :max="state.data.deductionMoney || 0"
                                :precision="2"
                                :step="1"
                                placeholder="请输入赠送金"
                                style="width: 800px"
                                @change="calculateActualAmount1"
                            />
                            <div class="amount-tip">赠送后将转化为用户的平台补贴金</div>
                        </el-form-item>
                        <el-form-item label="千六损耗">
                            <el-input
                                :value="formatCurrency(state.calculatedAmounts1.lossAmount)"
                                readonly
                                style="width: 800px"
                            >
                                <template #prefix>
                                    <span style="color: #f56c6c;">-</span>
                                </template>
                            </el-input>
                            <div class="amount-tip">按千分之六计算的损耗费用</div>
                        </el-form-item>

                        <!-- 实际到账显示 -->
                        <el-form-item label="实际到账">
                            <el-input
                                :value="formatCurrency(state.calculatedAmounts1.actualAmount)"
                                class="actual-amount-input"
                                readonly
                                style="width: 800px"
                            >
                                <template #prefix>
                                    <span style="color: #67c23a;">¥</span>
                                </template>
                            </el-input>
                            <div class="amount-tip success-tip">用户实际收到的补贴金金额</div>
                        </el-form-item>
                        <el-form-item label="操作密码" prop="password">
                            <el-input
                                v-model="state.commissionForm.password"
                                placeholder="请输入操作密码"
                                show-password
                                type="password"
                                style="width: 800px"
                            >
                                <template #prefix>
                                    <el-icon>
                                        <Lock/>
                                    </el-icon>
                                </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="备注" prop="remark">
                            <el-input
                                v-model="state.commissionForm.remark"
                                :autosize="{ minRows: 3, maxRows: 5 }"
                                maxlength="200"
                                placeholder="请输入备注信息（选填）"
                                show-word-limit
                                type="textarea"
                                style="width: 800px"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button :loading="state.commissioning" type="primary" @click="submitCommission">
                                确认赠送
                            </el-button>
                            <el-button @click="resetCommissionForm">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 赠送记录表格 -->
                <div class="donation-records">
                    <div class="records-header">
                        <h3>赠送记录</h3>
                        <div class="search-box">
                            <el-date-picker
                                v-model="state.commissionBeginTime"
                                clearable
                                format="YYYY-MM-DD HH:mm:ss"
                                placeholder="开始时间"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                            >
                            </el-date-picker>
                            <el-date-picker
                                v-model="state.commissionEndTime"
                                clearable
                                format="YYYY-MM-DD HH:mm:ss"
                                placeholder="结束时间"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                            >
                            </el-date-picker>
                            <el-input
                                v-model="state.commissionSearchPhone"
                                clearable
                                placeholder="搜索手机号"
                                style="width: 200px"
                            >
                            </el-input>
                            <el-button type="success" @click="searchCommissionRecords">
                                <el-icon>
                                    <Download/>
                                </el-icon>
                                搜索
                            </el-button>
                            <el-button :loading="state.exportingCommission" type="success"
                                       @click="exportCommissionRecords">
                                <el-icon>
                                    <Download/>
                                </el-icon>
                                导出
                            </el-button>
                        </div>
                    </div>

                    <el-table
                        v-loading="state.commissionRecordsLoading"
                        :data="state.commissionRecords"
                        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
                        border
                        empty-text="暂无赠送记录"
                        style="width: 100%"
                    >
                        <el-table-column label="接收用户" prop="receiverPhone">
                            <template #default="scope">
                                <el-tag effect="plain" size="small">{{ scope.row.receiverPhone }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="赠送金" prop="amount">
                            <template #default="scope">
                                <span class="amount">¥{{ scope.row.amount }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="千六损耗">
                            <template #default="scope">
                                <span class="loss-amount">-¥{{ scope.row.lossAmount }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="实际到账">
                            <template #default="scope">
                                <span class="actual-amount">¥{{ scope.row.actualAmount }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="赠送时间" prop="donationTime">
                            <template #default="scope">
                                <el-tooltip effect="light" placement="top">
                                    <span>{{ scope.row.donationTime }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" prop="status">
                            <template #default="scope">
                                <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'" size="small">
                                    {{ scope.row.status === '1' ? '成功' : '失败' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="备注" min-width="150" prop="remark" show-overflow-tooltip/>
                    </el-table>

                    <div class="pagination">
                        <el-pagination
                            v-model:current-page="state.commissionCurrentPage"
                            v-model:page-size="state.commissionPageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            :total="state.commissionTotal"
                            background
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleCommissionSizeChange"
                            @current-change="handleCommissionCurrentChange"
                        />
                    </div>
                </div>
            </div>
        </el-dialog>

    </div>
</template>
<script setup>
import {useRouter} from 'vue-router'
import {Session} from '../../utils/storage'
import {shipping} from '../../stores/shipping.js'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Download, Lock, Phone} from '@element-plus/icons-vue'
import {
    DonateDeductionByDistributor,
    DonatePromotionGold,
    ExportDistributorDonationRecords,
    ExportDonationRecords,
    GetDistributorDonationRecords,
    GetDonationRecords
} from '@/api/shipping/shipping'
import {ref} from 'vue'
import {handleDownload} from '@/utils/common'

const userType = Session.get("userType")
const donationFormRef = ref(null)
const commissionFormRef = ref(null)
const state = reactive({
    data: {},
    buttonList: [],
    // 赠送对话框
    donationDialogVisible: false,
    // 赠送表单
    donationForm: {
        phone: '',
        amount: 1,
        password: '',
        remark: ''
    },
    // 计算金额
    calculatedAmounts: {
        lossAmount: 0,      // 千六损耗
        actualAmount: 0     // 实际到账
    },
    // 代销商赠送计算金额
    calculatedAmounts1: {
        lossAmount: 0,      // 千六损耗
        actualAmount: 0     // 实际到账
    },
    // 赠送记录
    donationRecords: [],
    // 搜索手机号
    beginTime: null,
    endTime: null,
    searchPhone: '',
    // 分页
    currentPage: 1,
    pageSize: 10,
    total: 0,
    // 加载状态
    recordsLoading: false,
    donating: false,
    exporting: false,
    // 导出相关
    exportForm: {
        phone: '',
        dateRange: [],
        status: ''
    },

    // 佣金结算相关
    commissionDialogVisible: false,
    commissionForm: {
        phone: '',
        amount: 1,
        password: '',
        remark: ''
    },
    commissionRecords: [],
    commissionBeginTime: null,
    commissionEndTime: null,
    commissionSearchPhone: '',
    commissionCurrentPage: 1,
    commissionPageSize: 10,
    commissionTotal: 0,
    commissionRecordsLoading: false,
    commissioning: false,
    exportingCommission: false
})

// 表单验证规则
const donationRules = {
    phone: [
        {required: true, message: '请输入用户手机号', trigger: 'blur'},
        {pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur'}
    ],
    amount: [
        {required: true, message: '请输入赠送金', trigger: 'blur'},
        {type: 'number', min: 1, message: '金额必须大于0', trigger: 'blur'}
    ],
    password: [
        {required: true, message: '请输入操作密码', trigger: 'blur'}
    ]
}

// 佣金结算表单验证规则
const commissionRules = {
    phone: [
        {required: true, message: '请输入用户手机号', trigger: 'blur'},
        {pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur'}
    ],
    amount: [
        {required: true, message: '请输入赠送金', trigger: 'blur'},
        {type: 'number', min: 1, message: '金额必须大于0', trigger: 'blur'}
    ],
    password: [
        {required: true, message: '请输入操作密码', trigger: 'blur'}
    ]
}

const router = useRouter()
const handleButtonClick = (item) => {
    router.push(item.component)
}
const getShopHomeInfo = async () => {
    let res = await shipping().GetShopHomeInfo()
    state.data = res.data
}

// 显示赠送对话框
const showDonationDialog = () => {
    state.donationDialogVisible = true
    // 重置表单
    state.donationForm = {
        phone: '',
        amount: 1,
        password: '',
        remark: ''
    }
    // 重置计算金额
    calculateActualAmount()
    // 加载赠送记录
    loadDonationRecords()
}

// 显示佣金结算对话框
const showCommissionDialog = () => {
    state.commissionDialogVisible = true
    // 重置表单
    state.commissionForm = {
        phone: '',
        amount: 1,
        password: '',
        remark: ''
    }
    // 重置计算金额
    calculateActualAmount1()
    // 加载赠送记录
    loadCommissionRecords()
}

// 加载赠送记录
const loadDonationRecords = async () => {
    state.recordsLoading = true
    try {
        const res = await GetDonationRecords({
            beginTime: state.beginTime,
            endTime: state.endTime,
            phone: state.searchPhone,
            pageNum: state.currentPage,
            pageSize: state.pageSize
        })
        if (res.code === 200) {
            state.donationRecords = res.rows || []
            state.total = res.total || 0
        } else {
            ElMessage.error(res.msg || '获取赠送记录失败')
        }
    } catch (error) {
        console.error('获取赠送记录失败:', error)
        ElMessage.error('获取赠送记录失败，请稍后重试')
    } finally {
        state.recordsLoading = false
    }
}

// 加载佣金结算记录
const loadCommissionRecords = async () => {
    state.commissionRecordsLoading = true
    try {
        const res = await GetDistributorDonationRecords({
            beginTime: state.commissionBeginTime,
            endTime: state.commissionEndTime,
            phone: state.commissionSearchPhone,
            pageNum: state.commissionCurrentPage,
            pageSize: state.commissionPageSize
        })
        if (res.code === 200) {
            state.commissionRecords = res.rows || []
            state.commissionTotal = res.total || 0
        } else {
            ElMessage.error(res.msg || '获取佣金结算记录失败')
        }
    } catch (error) {
        console.error('获取佣金结算记录失败:', error)
        ElMessage.error('获取佣金结算记录失败，请稍后重试')
    } finally {
        state.commissionRecordsLoading = false
    }
}

// 搜索记录
const searchRecords = () => {
    state.currentPage = 1
    loadDonationRecords()
}

// 搜索佣金结算记录
const searchCommissionRecords = () => {
    state.commissionCurrentPage = 1
    loadCommissionRecords()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
    state.pageSize = size
    loadDonationRecords()
}

// 处理页码变化
const handleCurrentChange = (page) => {
    state.currentPage = page
    loadDonationRecords()
}

// 处理佣金结算分页大小变化
const handleCommissionSizeChange = (size) => {
    state.commissionPageSize = size
    loadCommissionRecords()
}

// 处理佣金结算页码变化
const handleCommissionCurrentChange = (page) => {
    state.commissionCurrentPage = page
    loadCommissionRecords()
}
// 訂單
const toOrder =(status) => {
    Session.set('homeMenuId', "1930824287180279809")
    router.push('/allOrders?status=' + status)
}
const returnOrders = () => {
    Session.set('homeMenuId', "1930824287180279809")
    router.push('/returnOrders')
}

// 提交赠送表单
const submitDonation = async () => {
    if (!donationFormRef.value) return

    await donationFormRef.value.validate(async (valid) => {
        if (!valid) return

        // 二次确认
        try {
            const confirmMessage = `
                <div style="text-align: left; line-height: 1.6;">
                    <p><strong>赠送详情：</strong></p>
                    <p>接收用户：${state.donationForm.phone}</p>
                    <p>赠送金额：¥${formatCurrency(state.donationForm.amount)}</p>
                    <p style="color: #f56c6c;">千六损耗：-¥${formatCurrency(state.calculatedAmounts.lossAmount)}</p>
                    <p style="color: #67c23a;"><strong>实际到账：¥${formatCurrency(state.calculatedAmounts.actualAmount)}</strong></p>
                    <br/>
                    <p style="color: #909399; font-size: 12px;">确认要进行此次赠送操作吗？</p>
                </div>
            `
            await ElMessageBox.confirm(
                confirmMessage,
                '赠送确认',
                {
                    confirmButtonText: '确认赠送',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                }
            )

            // 发起赠送请求
            state.donating = true
            try {
                const res = await DonatePromotionGold(state.donationForm)
                if (res.code === 200) {
                    ElMessage.success('赠送成功')
                    // 重新加载商家信息
                    getShopHomeInfo()
                    // 重新加载赠送记录
                    loadDonationRecords()
                    // 重置表单
                    resetDonationForm()
                } else {
                    ElMessage.error(res.msg || '赠送失败')
                }
            } catch (error) {
                console.error('赠送失败:', error)
                ElMessage.error('赠送失败，请稍后重试')
            } finally {
                state.donating = false
            }
        } catch {
            // 用户取消操作
        }
    })
}

// 重置表单
const resetDonationForm = () => {
    if (donationFormRef.value) {
        donationFormRef.value.resetFields()
    }
    state.donationForm = {
        phone: '',
        amount: 1,
        password: '',
        remark: ''
    }
    // 重新计算金额
    calculateActualAmount()
    calculateActualAmount1()
}

// 提交佣金结算表单
const submitCommission = async () => {
    if (!commissionFormRef.value) return

    await commissionFormRef.value.validate(async (valid) => {
        if (!valid) return

        // 二次确认
        try {
            const confirmMessage = `
                <div style="text-align: left; line-height: 1.6;">
                    <p><strong>佣金结算详情：</strong></p>
                    <p>接收用户：${state.commissionForm.phone}</p>
                    <p>赠送金额：¥${formatCurrency(state.commissionForm.amount)}</p>
                    <p style="color: #f56c6c;">千六损耗：-¥${formatCurrency(state.calculatedAmounts1.lossAmount)}</p>
                    <p style="color: #67c23a;"><strong>实际到账：¥${formatCurrency(state.calculatedAmounts1.actualAmount)}</strong></p>
                    <br/>
                    <p style="color: #909399; font-size: 12px;">确认要进行此次佣金结算操作吗？</p>
                </div>
            `
            await ElMessageBox.confirm(
                confirmMessage,
                '佣金结算确认',
                {
                    confirmButtonText: '确认赠送',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                }
            )

            // 发起赠送请求
            state.commissioning = true
            try {
                const res = await DonateDeductionByDistributor(state.commissionForm)
                if (res.code === 200) {
                    ElMessage.success('佣金结算成功')
                    // 重新加载商家信息
                    getShopHomeInfo()
                    // 重新加载赠送记录
                    loadCommissionRecords()
                    // 重置表单
                    resetCommissionForm()
                } else {
                    ElMessage.error(res.msg || '佣金结算失败')
                }
            } catch (error) {
                console.error('佣金结算失败:', error)
                ElMessage.error('佣金结算失败，请稍后重试')
            } finally {
                state.commissioning = false
            }
        } catch {
            // 用户取消操作
        }
    })
}

// 重置佣金结算表单
const resetCommissionForm = () => {
    if (commissionFormRef.value) {
        commissionFormRef.value.resetFields()
    }
    state.commissionForm = {
        phone: '',
        amount: 1,
        password: '',
        remark: ''
    }
    // 重新计算金额
    calculateActualAmount1()
}

// 导出记录
const exportRecords = async () => {
    state.exporting = true
    try {
        // 准备导出参数
        const params = {
            phone: state.searchPhone,
        }

        // 调用导出API
        const res = await ExportDonationRecords(params)

        // 处理文件下载
        handleDownload(res, `促销金赠送记录_${new Date().getTime()}.xlsx`)
        ElMessage.success('导出成功')
    } catch (error) {
        // 如果是用户取消操作，不显示错误
        if (error !== 'cancel') {
            console.error('导出失败:', error)
            ElMessage.error('导出失败，请稍后重试')
        }
    } finally {
        state.exporting = false
    }
}

// 导出佣金结算记录
const exportCommissionRecords = async () => {
    state.exportingCommission = true
    try {
        // 准备导出参数
        const params = {
            phone: state.commissionSearchPhone,
        }

        // 调用导出API
        const res = await ExportDistributorDonationRecords(params)

        // 处理文件下载
        handleDownload(res, `佣金结算记录_${new Date().getTime()}.xlsx`)
        ElMessage.success('导出成功')
    } catch (error) {
        // 如果是用户取消操作，不显示错误
        if (error !== 'cancel') {
            console.error('导出失败:', error)
            ElMessage.error('导出失败，请稍后重试')
        }
    } finally {
        state.exportingCommission = false
    }
}

// 计算实际到账金额
const calculateActualAmount = () => {
    const amount = state.donationForm.amount || 0
    // 千六损耗：千分之六 (0.6%)
    const lossRate = 0.006
    const lossAmount = Number((amount * lossRate).toFixed(2))
    const actualAmount = Number((amount - lossAmount).toFixed(2))

    state.calculatedAmounts = {
        lossAmount: lossAmount,
        actualAmount: actualAmount
    }
}

// 计算实际到账金额
const calculateActualAmount1 = () => {
    const amount = state.commissionForm.amount || 0
    // 千六损耗：千分之六 (0.6%)
    const lossRate = 0.006
    const lossAmount = Number((amount * lossRate).toFixed(2))
    const actualAmount = Number((amount - lossAmount).toFixed(2))

    // 只更新计算相关的字段，不要重新赋值整个对象
    state.calculatedAmounts1 = {
        lossAmount: lossAmount,
        actualAmount: actualAmount
    }
}

// 格式化货币显示
const formatCurrency = (amount) => {
    if (amount === null || amount === undefined || isNaN(amount)) {
        return '0.00'
    }
    return Number(amount).toFixed(2)
}

// 计算记录中的损耗金额
const calculateLossAmount = (amount) => {
    if (!amount) return '0.00'
    const lossAmount = Number(amount) * 0.006
    return formatCurrency(lossAmount)
}

// 计算记录中的实际到账金额
const calculateActualAmountForRecord = (amount) => {
    if (!amount) return '0.00'
    const lossAmount = Number(amount) * 0.006
    const actualAmount = Number(amount) - lossAmount
    return formatCurrency(actualAmount)
}

// 格式化时间
const formatTime = (time) => {
    if (!time) return '';
    const date = new Date(time);
    return date.toLocaleString();
}

const handleSubBoxClick = (link) => {
    if (link == '/productList') {
        Session.set('homeMenuId', "1930524136855298049")
    }
    router.push(link)
}

onMounted(() => {
    getShopHomeInfo()
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
                console.log(state.menuList)
            }
        }
    })
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
    width: 100%;
    flex-direction: column;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.middle-container {
    border: 1px solid red;
    flex: 1;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    height: 2000px;
}

.header {
    display: flex;
    align-items: center;
    flex: 1;
    height: 61px;
    padding: 0;
    background-color: #83A4EB;
    margin-left: 235px;

    .putaway {
        width: 172px;
        height: 31px;
        margin-left: 167px;
        font-size: 24px;
        color: #000;
    }

    .soldOut {
        width: 182px;
        height: 33px;
        margin-left: 277px;
        margin-right: 277px;
        font-size: 24px;
        color: #000;
    }

    .allProduct {
        width: 224px;
        height: 42px;
        margin-right: 122px;
        font-size: 24px;
    }
}

.main {
    position: absolute;
    left: 250px;
    right: 0;
    display: flex;
    flex-direction: column;
}

.main-top {
    display: flex;
    justify-content: flex-end; /* 整个盒子靠右对齐 */
    padding: 10px 20px; /* 可根据需要调整内边距 */
    position: fixed;
    top: 50px;
    right: 20px;
    z-index: 9999;
}

.main-top-content {
    display: flex;
    align-items: center; /* 垂直居中 */
    gap: 15px; /* 每个子元素之间的间距 */
    background-color: #f5f7fa;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 盒子容器样式 */
.boxes-container {
    width: 100%;
    height: 220px;
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 每行显示 4 个盒子 */
    gap: 15px; /* 盒子之间的间距 */

}

.form-container {
    background-color: #ffffff;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    max-width: 400px;
    margin: 0 auto;
}

/* 表单组 */
.form-group {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

/* 标签固定宽度，对齐 */
.form-group label {
    display: inline-block;
    width: 80px;
    text-align: right;
    margin-right: 10px;
    font-weight: bold;
}

/* 输入框样式 */
.form-group input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    width: 300px;
}

/* 提交按钮 */
.submit-btn {
    width: 120px;
    padding: 8px 12px;
    background-color: #409EFF;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    display: block;
    margin: 20px auto 0;
}

.submit-btn:hover {
    background-color: #337ecc;
}

.box {
    background-color: #ffffff;
    border: 1px solid #000;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    cursor: default;
    transition: all 0.2s ease-in-out;
}

.box:hover {
    transform: translateY(-3px);
}

/* 可点击盒子的悬停样式 */
.box-clickable {
    background-color: #ecf5ff;
    border-color: #b3d8ff;
    cursor: pointer;
}

/* 标题与数值横向排列 */
.box-row {
    display: flex;
    align-items: center;
    line-height: 50px;
    justify-content: space-between;
    gap: 10px;
}

/* 标题样式 */
.box-title {
    font-size: 18px;
    font-weight: bold;
    white-space: nowrap;
    flex-shrink: 0;
}

/* 数值框样式（徽章风格） */
.box-badge {
    background-color: #f5f7fa;
    color: #333;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    min-width: 60px;
    text-align: center;
    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.05);
}

/* 链接文字样式 */
.box-link-text {
    font-size: 13px;
    color: #409EFF;
    font-weight: bold;
}

.boxes-content {
    width: 100%;
    height: 500px;
    background-color: red;
}

.box-link-text {
    font-size: 13px;
    color: #409EFF;
    font-weight: bold;
}

.content-wrapper {
    border-top: 5px solid #000;
    width: 100%;
    height: 400px;
    //background-color: red;
    margin-top: 20px;
    border-bottom: 5px solid #000;

}

.content-area {
    margin-bottom: 10px;
    //background: green;
    width: 100%;
    height: 380px;
    margin-top: 30px;
}

/* 子盒子容器样式 */
.sub-boxes-container {
    width: 100%;
    height: 220px;
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 每行显示 4 个盒子 */
    gap: 15px; /* 盒子之间的间距 */
    margin-top: 20px;
    margin-bottom: 20px;
}

/* 子盒子样式 */
.sub-box {
    transition: all 0.2s ease-in-out;
    box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    height: 100px;
    line-height: 50px;
    justify-content: space-between;
    gap: 10px;
    background-color: #ecf5ff;
    border-color: #b3d8ff;

    &:hover {
        background-color: #ecf5ff;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
}

.filter-row {
    display: flex;
    align-items: center;
    margin-left: 93px;
    gap: 20px;
}

.product-selector {
    width: 229px;
    height: 52px;
}

.commBox {
    background-color: #CCCCCC;
    width: 281px;
    height: 42px;
    display: flex;
    align-items: center;
    font-size: 24px;
    margin-top: -10px;

    .Text {
        margin-left: 13px;

        &:first-child {
            min-width: 120px;
        }
    }

    input {
        flex: 1;
        border: none;
        background: transparent;
        outline: none;
        font-size: 24px;
    }
}

.filter-row1 {
    display: flex;
    align-items: center;
    margin-left: 40px;
    gap: 20px;
}

.product-selector1 {
    width: 196px;
    height: 52px;
}

.brandBox {
    background-color: #3A58CF;
    width: 145px;
    height: 42px;
    margin-top: -10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;

    .brand {
        font-size: 24px;
        color: #fff;
    }
}

.priceBox {
    margin-left: 113px;
    margin-top: 25px;
    display: flex;
    align-items: center; /* 添加这行确保垂直居中 */

    .pirceRange {
        font-size: 30px;
        line-height: 1; /* 确保文本行高一致 */
    }

    .leftPrice {
        width: 277px;
        height: 32px;
        margin-left: 10px;
        background-color: #ccc;
        vertical-align: middle; /* 添加垂直对齐 */
    }

    .to {
        margin-left: 10px;
        line-height: 1; /* 确保文本行高一致 */
    }

    .rightPrice {
        width: 277px;
        height: 32px;
        margin-left: 10px;
        background-color: #ccc;
        vertical-align: middle; /* 添加垂直对齐 */
    }

    .nullBtn {
        width: 277px;
        height: 32px;
        margin-left: 105px;
        background-color: #fff;
        border: 3px solid #3A58CF;
        vertical-align: middle; /* 添加垂直对齐 */
        line-height: 1; /* 重置按钮行高 */
        padding: 0; /* 移除默认内边距 */
    }
}

.product-type-row {
    display: flex;
    align-items: center;
    margin-left: 113px;
    margin-top: 25px;
    gap: 20px;

    .product-type-label {
        font-size: 30px;
        min-width: 120px;
    }

    .checkbox-container {
        display: flex;
        align-items: center;
        gap: 30px;
        margin-left: 129px;

        :deep(.el-checkbox.square-checkbox) {
            .el-checkbox__inner {
                border-radius: 4px; /* 方形复选框 */
                width: 20px;
                height: 20px;

                &::after {
                    top: 2px;
                    left: 6px;
                }
            }

            .el-checkbox__label {
                font-size: 24px;
                color: #333;
            }
        }
    }

    .export-btn {
        margin-left: 430px;
        width: 120px;
        height: 42px;
        font-size: 20px;
        background-color: #FF8D1A;
        border: none;
        color: #000;

    }
}

.allButton {
    margin-top: 32px;
    margin-left: 303px;

    .button {
        width: 247px;
        height: 59px;
        margin-right: 41px;
        border-radius: 30px;
        border: 2px solid #3A58CF;
        font-size: 30px;

        color: #3A58CF;
    }
}

.table-container {
    margin-top: 20px;
    padding: 0 20px;

    :deep(.custom-header) {
        th {
            background-color: #83A4EB !important;
            color: #000;
            font-weight: bold;
            font-size: 16px;

            .el-checkbox {
                .el-checkbox__inner {
                    border-radius: 4px;
                    width: 16px;
                    height: 16px;
                }

                .el-checkbox__label {
                    font-size: 16px;
                    color: #000;
                }
            }
        }
    }

    :deep(.custom-row) {
        td {
            background-color: #D2E0FB;

            .el-checkbox {
                .el-checkbox__inner {
                    border-radius: 4px;
                    width: 16px;
                    height: 16px;
                }

                .el-checkbox__label {
                    font-size: 14px;
                }
            }
        }

        &:hover td {
            background-color: #b8cdf9 !important;
        }
    }

    :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;

        .el-table__cell {
            padding: 12px 0;
        }
    }
}

// 新增的样式
.donation-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.donation-form {
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eaeaea;
}

.form-header h3 {
    margin: 0;
    color: #3A58CF;
    font-size: 18px;
}

.balance-info {
    font-size: 14px;
    color: #606266;
}

.balance-amount {
    font-weight: bold;
    color: #f56c6c;
    font-size: 16px;
}

.amount-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
    line-height: 1.4;
}

.success-tip {
    color: #67c23a !important;
    font-weight: 500;
}

.actual-amount-input {
    :deep(.el-input__inner) {
        background-color: #f0f9ff;
        border-color: #67c23a;
        color: #67c23a;
        font-weight: bold;
    }
}
:deep(.el-form-item__content){
    display: block;
}
// 千六损耗输入框样式
:deep(.el-form-item) {
    .el-input.is-disabled .el-input__inner {
        background-color: #fef0f0;
        border-color: #f56c6c;
        color: #f56c6c;
    }
}

.donation-records {
    .records-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        h3 {
            margin: 0;
            color: #3A58CF;
            font-size: 18px;
        }

        .search-box {
            display: flex;
            gap: 10px;
            align-items: center;
            width: auto;
        }
    }

    .pagination {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
    }
}

.amount {
    color: #f56c6c;
    font-weight: bold;
}

.loss-amount {
    color: #f56c6c;
    font-weight: bold;
}

.actual-amount {
    color: #67c23a;
    font-weight: bold;
}
</style>
