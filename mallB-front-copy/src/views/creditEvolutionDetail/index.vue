<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {onMounted, reactive, ref} from 'vue'
import {Session} from "@/utils/storage"
import {ElMessage} from 'element-plus'
import {exportFaithRecords, getDateFaithStats, getFaithStatisticsPage} from '@/api/quantification'
import {formatDate} from '@/utils/common'

const router = useRouter()

// 加载状态
const loading = ref(false)
const exportLoading = ref(false)

// 分页参数
const pagination = reactive({
    current: 1,
    size: 10,
    total: 0
})

// 表单数据
const form = reactive({
    phone: '',
    date: formatDate(new Date(), 'yyyy-MM-dd')
})

// 表格数据
const tableData = ref([])

// 当天统计数据
const todayStats = reactive({
    totalAmount: 0,
    loading: false
})

const state = reactive({
    buttonList: []
})

// 获取指定日期统计数据
const getDateStats = async () => {
    todayStats.loading = true
    try {
        const res = await getDateFaithStats(form.date)
        if (res.code === 200 && res.data) {
            todayStats.totalAmount = res.data.totalAmount || 0
        } else {
            todayStats.totalAmount = 0
        }
    } catch (error) {
        console.error('获取统计数据失败:', error)
        todayStats.totalAmount = 0
    } finally {
        todayStats.loading = false
    }
}
// 获取数据列表
const getList = async () => {
    loading.value = true
    try {
        const params = {
            pageNum: pagination.current,
            pageSize: pagination.size,
            date: form.date || undefined,
            phone: form.phone || undefined
        }

        const res = await getFaithStatisticsPage(params)
        if (res.code === 200) {
            tableData.value = res.rows || []
            pagination.total = res.total || 0
        } else {
            ElMessage.warning(res.msg || "获取量化统计数据失败")
        }
    } catch (error) {
        console.error('获取量化统计数据失败:', error)
        ElMessage.error("获取量化统计数据失败，请稍后重试")
    } finally {
        loading.value = false
    }
}

// 搜索
const handleSearch = () => {
    pagination.current = 1
    getList()
    getDateStats() // 同时更新统计数据
}

// 重置
const handleReset = () => {
    form.phone = ''
    form.date = formatDate(new Date(), 'yyyy-MM-dd')
    pagination.current = 1
    getList()
}

// 导出Excel
const handleExport = async () => {
    if (exportLoading.value) return
    exportLoading.value = true
    try {
        const params = {
            date: form.date || undefined,
            phone: form.phone || undefined
        }

        const res = await exportFaithRecords(params)
        const blob = new Blob([res], {type: 'application/vnd.ms-excel'})
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = '量化统计_' + formatDate(new Date(), 'yyyyMMddHHmmss') + '.xlsx'
        link.click()
        URL.revokeObjectURL(link.href)
        ElMessage.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
        ElMessage.error('导出失败')
    } finally {
        exportLoading.value = false
    }
}

// 分页变化
const handlePageChange = (page) => {
    pagination.current = page
    getList()
}

// 每页条数变化
const handleSizeChange = (size) => {
    pagination.size = size
    pagination.current = 1
    getList()
}

const handleButtonClick = (item) => {
    router.push(item.component)
}

// 初始化默认时间为当天
const initDefaultTime = () => {
    const today = new Date()
    form.date = formatDate(today, 'yyyy-MM-dd')
}

onMounted(() => {
    let menuList = Session.getMenu2()
    let menuId = Session.get('homeMenuId')
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index]
        if (element.menuId == menuId) {
            state.buttonList = element.children
        }
    }

    // 初始化默认时间为当天
    initDefaultTime()
    // 获取数据列表
    getList()
    getDateStats() // 同时更新统计数据
})
</script>

<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>

        <div class="right-content">
            <div class="filter-container">
                <div class="filter-header">
                    <span class="filter-title">量化进化明细</span>
                </div>

                <el-form :model="form" class="filter-form" @submit.prevent="handleSearch">
                    <div class="form-row">
                        <el-form-item label="日期">
                            <el-date-picker
                                v-model="form.date"
                                clearable
                                format="YYYY-MM-DD"
                                placeholder="请选择日期"
                                type="date"
                                value-format="YYYY-MM-DD"
                            />
                        </el-form-item>

                        <el-form-item label="手机号">
                            <el-input v-model="form.phone" clearable placeholder="请输入手机号"/>
                        </el-form-item>
                        <el-form-item label="当天统计">
                            <span v-loading="todayStats.loading">{{ todayStats.totalAmount.toFixed(2) }}</span>
                        </el-form-item>

                        <div class="button-group">
                            <el-button :loading="loading" type="primary" @click="handleSearch">查询</el-button>
                            <el-button :disabled="loading" @click="handleReset">重置</el-button>
                            <el-button :loading="exportLoading" type="success" @click="handleExport">导出Excel
                            </el-button>
                        </div>
                    </div>
                </el-form>


            </div>

            <!-- 表格区域 -->
            <div v-loading="loading" class="table-container" element-loading-background="rgba(0, 0, 0, 0.1)"
                 element-loading-text="加载中...">
                <el-table
                    :data="tableData"
                    border
                    style="width: 100%"
                >
                    <el-table-column label="日期" min-width="120" prop="date"/>
                    <el-table-column label="手机号" min-width="120" prop="phone"/>
                    <el-table-column label="公司名称" min-width="180" prop="shopName"/>
                    <el-table-column label="法人姓名" min-width="120" prop="legalPerson"/>
                    <el-table-column label="今日量化进化量" min-width="150" prop="dayFaithAmount">
                        <template #default="{row}">
                            {{ row.dayFaithAmount ? row.dayFaithAmount.toFixed(2) : '0.00' }}
                        </template>
                    </el-table-column>
                    <!--                    <el-table-column prop="totalFaithAmount" label="累计转化量化" min-width="150">-->
                    <!--                        <template #default="{row}">-->
                    <!--                            {{ row.totalFaithAmount ? row.totalFaithAmount.toFixed(2) : '0.00' }}-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.current"
                        v-model:page-size="pagination.size"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handlePageChange"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow-y: auto;
}

.filter-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #3A58CF;
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 20px;

    .filter-title {
        font-size: 24px;
        font-weight: bold;
        color: white;
    }

    .search-item {
        margin-left: auto;
        margin-bottom: 0;

        :deep(.el-input-group__append) {
            background-color: #3A58CF;
            border: none;

            .el-button {
                color: white;
            }
        }
    }
}

.filter-form {
    .form-row {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;

        .el-form-item {
            margin-right: 20px;
            margin-bottom: 0;
            min-width: 200px;

            :deep(.el-form-item__label) {
                padding-bottom: 0;
                font-weight: normal;
            }
        }

        .time-separator {
            margin: 0 10px;
            color: #666;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-left: auto;
        }
    }
}

.summary-info {
    padding: 10px 0;
    color: #666;
    font-size: 14px;
    display: flex;
    gap: 20px;
}

.table-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    flex: 1;

    :deep(.el-table) {
        font-size: 14px;

        th {
            background-color: #f5f7fa;
            color: #333;
            font-weight: bold;
        }
    }
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}
</style>
