<script setup>
import {Setting} from '@element-plus/icons-vue'
import UserForm from '../../components/UserForm.vue'
import {useRouter} from 'vue-router'

const router = useRouter()
const handleToConstructRole = () => {
    router.push('/constructRole')
}
const handleToPlatformManage = () => {
    router.push('/platformManage')
}
const handleToQuantification = () => {
    router.push('/quantification')
}
const handleToSystemSetting = () => {
    router.push('/systemSetting')
}
const handleToSetting = () => {
    router.push('/setting')
}
const handleToTechnicalDrainage = () => {
    router.push('/technicalDrainage')
}
</script>

<template>
    <div class="common-layout">
        <el-container>
            <el-aside class="aside" width="263px">
                <!-- 圆形背景 -->
                <div class="asideTitle">
                    <img class="circle" src="../../images/circle.png">
                </div>

                <!-- 菜单按钮 -->
                <div class="menu-container">
                    <div class="menu-button1" @click="handleToPlatformManage">平台管理</div>
                    <div class="menu-button" @click="handleToTechnicalDrainage">基础管理</div>
                    <div class="menu-button" @click="handleToQuantification">量化率</div>
                    <div class="menu-button" @click="handleToMenuSetting">菜单管理</div>
                    <div class="menu-button" @click="handleToRoleSetting">角色管理</div>
                    <div class="menu-button" @click="handleToSystemSetting">系统设置</div>
                </div>
            </el-aside>

            <el-container>
                <el-header>
                    <div class="header-content">
                        <div class="header-top">
                            <el-icon class="setting-icon" @click="handleToSetting">
                                <Setting/>
                            </el-icon>
                        </div>
                        <div class="header-line"></div>
                        <!-- <img class="header-line" src="../../images/line.png" alt="分割线"> -->
                    </div>
                </el-header>
                <el-main>
                    <div class="background-container">
                        <img alt="背景图" class="background-image" src="../../images/bigBackground.png">
                        <div class="main-content">
                            <div class="top-section">
                                <span class="constructRole" @click="handleToConstructRole">创建角色+</span>
                                <div class="input-group">
                                    <input class="phone-input" placeholder="手机号" type="text">
                                    <input class="empty-input" type="text">
                                </div>
                            </div>

                            <div class="form-grid">
                                <UserForm class="form-item1"></UserForm>
                                <UserForm class="form-item"></UserForm>
                                <UserForm class="form-item1"></UserForm>
                                <UserForm class="form-item"></UserForm>
                            </div>
                        </div>
                    </div>
                </el-main>
            </el-container>
        </el-container>
    </div>
</template>

<style lang="scss" scoped>
.common-layout {
    display: flex;
    max-height: 100vh;

    .aside {
        background: #fff;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2; /* 确保侧边栏在背景图上方 */

        .asideTitle {
            display: flex;
            background-color: #D1D9F7;
            width: 263px;
            height: 204px;
            border-bottom: 5px solid #0525F7;

            .circle {
                margin-left: 30px;
                margin-bottom: 0.5px;
            }
        }

        .menu-container {
            width: 100%;
            padding: 30px;
            margin-top: 86px;
            position: relative;
            z-index: 2;

            .menu-button1, .menu-button {
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                cursor: pointer;
                background-color: #3A5BDE;
                transition: all 0.3s;
            }

            .menu-button1 {
                margin-top: 126px;
            }

            .menu-button {
                margin-top: 63px;

                &:hover {
                    background: #2c4ac7;
                }

                &.active {
                    background: #D1D9F7;
                    color: #333;
                    font-weight: 500;
                }
            }
        }
    }

    .el-header {
        background: #fff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 0;
        display: block;
        height: 122px;
        position: relative;
        z-index: 2; /* 确保头部在背景图上方 */

        .header-content {
            display: flex;
            flex-direction: column;

            .header-top {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                padding: 10px 33px 10px 0;

                .setting-icon {
                    font-size: 20px;
                    color: #606266;
                    cursor: pointer;

                    &:hover {
                        color: #3A5BDE;
                    }
                }
            }

            .header-line {
                width: 100%;
                height: 81px;
                background-color: red;
            }
        }
    }

    .el-main {
        padding: 0;
        position: relative;

        .background-container {
            position: relative;
            width: 100%;
            height: 100%;

            .background-image {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                z-index: 1;
            }

            .main-content {
                position: relative;
                z-index: 2; /* 确保内容在背景图上方 */
                padding: 20px 0;

                .top-section {
                    display: flex;
                    align-items: center;
                    margin-bottom: 30px;

                    .constructRole {
                        font-size: 18px;
                        font-weight: bold;
                        color: #333;
                        white-space: nowrap;
                        padding-left: 66px;
                    }

                    .input-group {
                        display: flex;
                        margin-left: 603px;

                        .phone-input {
                            height: 46px;
                            width: 357px;
                            background-color: #fff;
                            border-radius: 16px;
                            border: 1px solid #dcdfe6;
                            padding: 0 15px;
                            font-size: 14px;
                            margin-top: 13px;

                            &::placeholder {
                                color: #c0c4cc;
                            }

                            &:focus {
                                outline: none;
                                border-color: #3A5BDE;
                            }
                        }

                        .empty-input {
                            margin-left: 49px;
                            height: 46px;
                            width: 133px;
                            border-radius: 16px;
                            border: 1px solid #dcdfe6;
                            padding: 0 15px;
                            font-size: 14px;
                            margin-top: 13px;
                            background-color: #f5f7fa;

                            &:focus {
                                outline: none;
                                border-color: #3A5BDE;
                            }
                        }
                    }
                }

                .form-grid {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 23px 31px;

                    .form-item1 {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                        margin-left: 262px;
                    }

                    .form-item {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                    }
                }
            }
        }
    }
}
</style>
