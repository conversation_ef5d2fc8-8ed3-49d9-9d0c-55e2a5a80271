<template>
    <div class="container">
        <!-- 大背景（全屏） -->
        <Background>
            <!-- 首页背景（固定尺寸） -->
            <div class="home-background-container">
                <img class="home-background" src="../../images/LoginBackground.png"/>
            </div>

            <!-- 系统登录盒子（右侧） -->
            <div class="login-box">
                <h2 class="login-title">系统登录</h2>
                <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" @keyup.enter="submitForm(ruleFormRef)">
                    <el-form-item prop="username">
                        <div class="input-group">
                            <input v-model="ruleForm.username" placeholder="请输入手机号" type="text"/>
                        </div>
                    </el-form-item>
                    <el-form-item prop="password">
                        <div class="input-group password-group">
                            <input v-model="ruleForm.password" placeholder="请输入密码" :type="showPassword ? 'text' : 'password'"/>
                            <span class="eye-icon" @click="togglePasswordVisibility">
                                <svg v-if="!showPassword" viewBox="0 0 24 24" width="24" height="24">
                                    <path fill="currentColor" d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                                </svg>
                                <svg v-else viewBox="0 0 24 24" width="24" height="24">
                                    <path fill="currentColor" d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"/>
                                </svg>
                            </span>
                        </div>
                    </el-form-item>
                    <el-form-item prop="code">
                        <div class="captcha-group">
                            <div class="captcha-input">
                                <input v-model="ruleForm.code" placeholder="验证码" type="text"/>
                            </div>
                            <div class="captcha-code">
                                <img :src="imgSrc" alt="">
                            </div>
                            <a class="refresh-captcha" href="#" @click.prevent="getVerifyCode">看不清？</a>
                        </div>
                    </el-form-item>
                    <div class="remember-forgot">
                        <label class="remember-me">
                            记住账号 <input v-model="rememberPassword" class="square-checkbox" type="checkbox"/>
                        </label>
                        <router-link class="forgot-pwd" to="/forgotPassword"
                        >忘记密码？
                        </router-link>
                    </div>
                    <div :class="{ 'loading': isLoading }" class="login-btn" @click="submitForm(ruleFormRef)">
                        <span v-if="!isLoading">登录</span>
                        <span v-else class="loading-text">登录中...</span>
                    </div>
                </el-form>
            </div>
        </Background>
    </div>
</template>

<script lang="ts" setup>
import {onMounted, reactive, ref} from "vue";
import Background from "../../components/Background.vue";
import {useUserInfoLogin} from "../../stores/login";
import {useRouter} from 'vue-router';
import {generateUUID} from "../../utils/other";
import {Session} from '../../utils/storage';
import type {FormInstance, FormRules} from 'element-plus'

const loading = ref(true);
const error = ref(null);
const isLoading = ref(false);
const fetchUserData = async () => {
    try {
        let response = await useUserInfoLogin().GetUserInfo()
        await getUserMessage(response.data.user)
    } catch (err) {
        error.value = err.message || '获取用户信息失败';
    } finally {
        loading.value = false;
    }
};
const router = useRouter()

//登录表单
interface RuleForm {
    username: string,
    password: string,
    code: Number,
    randomStr: Number
}

const ruleForm = reactive<RuleForm>({
    username: '',
    password: '',
    code: '',
    randomStr: ''
})
//记住账号
const rememberPassword = ref(false)
//密码显示状态
const showPassword = ref(false)
//切换密码显示状态
const togglePasswordVisibility = () => {
    showPassword.value = !showPassword.value
}
onMounted(() => {
    const savedCredentials = localStorage.getItem('savedCredentials')
    if (savedCredentials) {
        const {username} = JSON.parse(savedCredentials)
        ruleForm.username = username
        rememberPassword.value = true
    }
    getVerifyCode()
})
//获取验证码地址，以及赋值
const imgSrc = ref("");

//重新获取验证码
const getVerifyCode = () => {
    ruleForm.randomStr = generateUUID()
    useUserInfoLogin().getCode(ruleForm.randomStr).then(res => {
        if (res.code === 200) {
            imgSrc.value = "data:image/png;base64," + res.data
        }
    })
}
const ruleFormRef = ref<FormInstance>()
const rules = reactive<FormRules<RuleForm>>({
    username: [
        {required: true, message: '请输入手机号码', trigger: 'blur'},
    ],
    password: [
        {required: true, message: '请输入密码', trigger: 'blur'},
    ],
    code: [
        {required: true, message: '请输入验证码', trigger: 'blur'},
    ]
})
const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    if (isLoading.value) return // 如果正在加载中，阻止重复提交

    await formEl.validate((valid, fields) => {
        if (valid) {
            // 如果勾选了记住账号
            if (rememberPassword.value) {
                localStorage.setItem('savedCredentials', JSON.stringify({
                    username: ruleForm.username
                }))
            } else {
                // 不记住则清除保存的凭证
                localStorage.removeItem('savedCredentials')
            }
            onSignIn();
        } else {
            console.log('error submit!', fields)
        }
    })
}
//登录成功之后，获取用户信息
// 账号密码登录
const onSignIn = async () => {
    isLoading.value = true; // 开始加载
    try {
        // 调用登录方法
        let result = await useUserInfoLogin().login(ruleForm);
        const status = result.code
        if (status === 200) {
            // 存储token 信息
            Session.set('token', result.data.token);
            fetchUserData()
        } else if (status === 500) {
            getVerifyCode()
            isLoading.value = false; // 登录失败，结束加载
        } else {
            isLoading.value = false; // 其他错误，结束加载
        }
    } catch (error) {
        console.error("登录失败:", error);
        getVerifyCode();
        isLoading.value = false; // 发生异常，结束加载
    }
};
const getUserMessage = async (user) => {
    try {
        let result = await useUserInfoLogin().getUserMessage(1);
        let result1 = await useUserInfoLogin().getUserMessage(2);
        Session.set('menus1', result.data.menus);
        Session.set('menus2', result1.data.menus);
        Session.set('user', user);

        // 如果是商家
        if (user.isShop == 1) {
            Session.set('userType', '2');
            Session.set('currentMenu', 'platform');
        }// 如果是代销
        if (user.isConsignment == 1) {
            Session.set('userType', '3');
            Session.set('currentMenu', 'platform');
        } else if (user.isShop != 1 && user.isConsignment != 1) {
            Session.set('userType', '1');
            Session.set('currentMenu', 'home');
        }
        router.push({path: '/index'});
    } catch (error) {
        console.error("获取用户信息失败:", error);
    } finally {
        isLoading.value = false; // 无论成功或失败，都结束加载状态
    }
}
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    width: 100vw;
    max-height: 100vh;
    overflow: hidden;

    /* 首页背景容器 */
    .home-background-container {
        position: absolute;
        width: 1023px;
        height: 617px;
        z-index: 2;
        margin-left: 80px;
        margin-top: 261px;
        margin-bottom: 118px;

        .home-background {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }

    /* 系统登录盒子 */
    .login-box {
        position: absolute;
        width: 642px;
        height: 824px;
        left: calc(80px + 1023px + 64px);
        top: 50%;
        transform: translateY(-50%);
        background: #5170ed;
        border-radius: 8px;
        padding: 40px 0;
        box-sizing: border-box;
        z-index: 3;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 138px;
        margin-bottom: 118px; /* 新增底部间距 */

        .login-title {
            text-align: center;
            color: white;
            font-size: 48px;
            width: 300px;
            height: 70px;
            margin: 70px 0 61px 0;
            line-height: 70px;
        }

        .input-group {
            margin-bottom: 25px;
            width: 100%;
            display: flex;
            justify-content: center;

            input {
                width: 465px;
                height: 70px;
                padding: 0 25px;
                font-size: 24px;
                background: #fff;
                color: #000;
                margin: 0 89px;

                &::placeholder {
                    color: #000;
                    font-size: 24px;
                    opacity: 0.8;
                }

                &:focus {
                    border-color: #5170ed;
                    outline: none;
                    box-shadow: 0 0 5px rgba(81, 112, 237, 0.5);
                }
            }

            &.password-group {
                position: relative;

                input {
                    padding-right: 60px; /* 为眼睛图标留出空间 */
                }

                .eye-icon {
                    position: absolute;
                    right: 115px; /* 调整位置以适应输入框的margin */
                    top: 50%;
                    transform: translateY(-50%);
                    cursor: pointer;
                    color: #666;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 30px;
                    height: 30px;
                    transition: color 0.3s;

                    &:hover {
                        color: #5170ed;
                    }

                    svg {
                        width: 24px;
                        height: 24px;
                    }
                }
            }
        }

        //深度修改样式
        :deep(.el-form-item__error) {
            color: var(--el-color-danger);
            font-size: 12px;
            left: 91px;
            line-height: 1;
            padding-top: 0px;
            position: absolute;
            top: 88%;
        }

        .captcha-group {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 89px -10px;

            .captcha-input {
                width: 174px;
                height: 59px;
                margin-right: 15px;

                input {
                    width: 100%;
                    height: 100%;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 0 15px;
                    background: #fff;
                    color: #000;
                    font-size: 24px;
                }
            }

            .captcha-code {
                height: 59px;
                background: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 22px;
                color: #000;
                border-radius: 4px;
                margin: 0 10px;
            }

            .refresh-captcha {
                font-size: 20px;
                color: #000;
                text-decoration: none;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        /* 登录按钮样式 */
        .login-btn {
            width: 465px;
            height: 80px; /* 调整高度为80px */
            background: #14097a;
            color: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
            margin: 0 89px 30px;
            text-decoration: none; /* 添加此行移除链接下划线 */

            &:hover {
                background: #0e075e;
                transform: translateY(-2px);
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            }

            &.loading {
                background: #2f1eb1;
                cursor: not-allowed;
                position: relative;
                overflow: hidden;

                &::after {
                    content: '';
                    position: absolute;
                    width: 100%;
                    height: 4px;
                    background: linear-gradient(to right, transparent, #fff, transparent);
                    bottom: 0;
                    left: -100%;
                    animation: loading 1.5s infinite;
                }
            }

            .loading-text {
                display: flex;
                align-items: center;

                &::after {
                    content: '...';
                    animation: dots 1.5s infinite;
                }
            }
        }

        @keyframes loading {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        @keyframes dots {
            0% {
                content: '.';
            }
            33% {
                content: '..';
            }
            66% {
                content: '...';
            }
            100% {
                content: '.';
            }
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            width: 465px;
            margin: 0 89px 10px;
            align-items: center;

            .remember-me {
                color: white;
                font-size: 20px;
                display: flex;
                align-items: center;

                .square-checkbox {
                    -webkit-appearance: none;
                    -moz-appearance: none;
                    appearance: none;
                    width: 20px;
                    height: 20px;
                    border: 2px solid white;
                    border-radius: 4px;
                    margin-left: 10px;
                    position: relative;
                    cursor: pointer;

                    &:checked::after {
                        content: "✓";
                        position: absolute;
                        color: white;
                        font-size: 16px;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                    }
                }
            }

            .forgot-pwd {
                color: white;
                font-size: 20px;
                text-decoration: none;

                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
}
</style>
