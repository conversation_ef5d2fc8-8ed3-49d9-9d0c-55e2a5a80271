<template>
    <div class="container">
        <!-- 固定在左上角的标题 -->
        <div class="page-title">
            <span>添加广告</span>
        </div>

        <!-- 表单内容区域 -->
        <div v-loading="loading" class="main">
            <div class="form-wrapper">
                <!-- 排序行 -->
                <div class="form-row">
                    <span class="label">排序</span>
                    <el-input
                        v-model="formData.sort"
                        class="form-input"
                        placeholder="请输入排序数字"
                        type="number"
                    />
                </div>

                <!-- 标题行 -->
                <div class="form-row">
                    <span class="label">标题</span>
                    <el-input
                        v-model="formData.title"
                        class="form-input"
                        maxlength="50"
                        placeholder="请输入广告标题"
                        show-word-limit
                    />
                </div>

                <!-- 图片行 -->
                <div class="form-row">
                    <span class="label">广告图片</span>
                    <div class="upload-container">
                        <el-upload
                            :auto-upload="false"
                            :on-change="handleImageChange"
                            :show-file-list="false"
                            action="#"
                            class="avatar-uploader"
                        >
                            <img v-if="imageUrl" :src="imageUrl" class="avatar"/>
                            <div v-else class="upload-placeholder">
                                <el-icon :size="28" class="add-icon">
                                    <Plus/>
                                </el-icon>
                                <span>点击上传图片</span>
                            </div>
                        </el-upload>
                        <div class="upload-tips">
                            建议尺寸：1200 x 400 像素，格式：JPG、PNG，大小不超过2MB
                        </div>
                    </div>
                </div>

                <!-- 广告价格 -->
                <div class="form-row">
                    <span class="label">广告价格</span>
                    <el-input
                        v-model="formData.price"
                        class="form-input"
                        placeholder="请输入广告价格"
                        type="number"
                    >
                        <template #prepend>¥</template>
                    </el-input>
                </div>

                <!-- 广告有效期 -->
                <div class="form-row">
                    <span class="label">有效期</span>
                    <el-date-picker
                        v-model="dateRange"
                        class="date-picker"
                        end-placeholder="结束日期"
                        format="YYYY-MM-DD"
                        range-separator="至"
                        start-placeholder="开始日期"
                        type="daterange"
                        value-format="YYYY-MM-DD"
                    />
                </div>

                <!-- 商家官网链接行 -->
                <div class="form-row">
                    <span class="label">跳转链接</span>
                    <el-input
                        v-model="formData.website"
                        class="form-input"
                        placeholder="请输入广告点击后跳转的链接"
                    />
                </div>

                <!-- 广告描述 -->
                <div class="form-row">
                    <span class="label">广告描述</span>
                    <el-input
                        v-model="formData.description"
                        :rows="4"
                        class="form-input"
                        maxlength="200"
                        placeholder="请输入广告描述内容"
                        show-word-limit
                        type="textarea"
                    />
                </div>

                <!-- 提交按钮 -->
                <div class="form-row submit-row">
                    <el-button class="cancel-btn" @click="goBack">取消</el-button>
                    <el-button
                        :loading="loading"
                        class="submit-btn"
                        type="primary"
                        @click="handleSubmit"
                    >
                        提交
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import {Plus} from '@element-plus/icons-vue'
import {reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage} from 'element-plus'
import {useAdvertisementStore} from '../../stores/advertisement'

const router = useRouter()
const advertisementStore = useAdvertisementStore()
const loading = ref(false)
const imageUrl = ref('')
const dateRange = ref([])

const formData = reactive({
    sort: '',
    title: '',
    image: '',
    price: '',
    website: '',
    description: '',
    startDate: '',
    endDate: ''
})

// 处理图片上传
const handleImageChange = (file) => {
    // 实际项目中应该上传到服务器并获取URL
    // 这里只是模拟
    const reader = new FileReader()
    reader.onload = (e) => {
        imageUrl.value = e.target.result
        formData.image = file.raw
    }
    reader.readAsDataURL(file.raw)
}

// 返回列表页
const goBack = () => {
    router.push('/ad')
}

// 表单验证
const validateForm = () => {
    if (!formData.title) {
        ElMessage.warning('请输入广告标题')
        return false
    }

    if (!imageUrl.value) {
        ElMessage.warning('请上传广告图片')
        return false
    }

    if (!formData.price) {
        ElMessage.warning('请输入广告价格')
        return false
    }

    if (!dateRange.value || dateRange.value.length !== 2) {
        ElMessage.warning('请选择广告有效期')
        return false
    }

    if (!formData.website) {
        ElMessage.warning('请输入跳转链接')
        return false
    }

    return true
}

// 提交表单
const handleSubmit = async () => {
    if (!validateForm()) return

    // 设置日期
    if (dateRange.value && dateRange.value.length === 2) {
        formData.startDate = dateRange.value[0]
        formData.endDate = dateRange.value[1]
    }

    loading.value = true
    try {
        // 调用store方法添加广告
        const result = await advertisementStore.addAdvertisement({
            ...formData,
            status: '0', // 待支付状态
        })

        if (result && result.id) {
            // 跳转到支付页面
            router.push({
                path: '/adPayment',
                query: {id: result.id}
            })
        }
    } finally {
        loading.value = false
    }
}
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    min-height: 100vh;
    padding: 30px;
    background-color: #f5f7fa;
}

.page-title {
    position: absolute;
    top: 30px;
    left: 30px;

    span {
        font-size: 28px;
        font-weight: bold;
        color: #333;
    }
}

.main {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 100px);
    margin-top: 80px;
}

.form-wrapper {
    width: 100%;
    max-width: 700px;
    padding: 40px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.form-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;

    .label {
        width: 150px;
        text-align: right;
        padding-right: 25px;
        font-size: 18px;
        color: #606266;
        flex-shrink: 0;
        margin-top: 10px;
    }

    .form-input {
        flex: 1;
        min-width: 300px;

        :deep(.el-input__inner) {
            height: 45px;
            font-size: 16px;
        }
    }

    .date-picker {
        width: 100%;
    }

    .upload-container {
        flex: 1;

        .avatar-uploader {
            display: block;
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: border-color 0.3s;

            &:hover {
                border-color: #409EFF;
            }

            .avatar {
                width: 100%;
                max-height: 250px;
                object-fit: cover;
                display: block;
            }

            .upload-placeholder {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 150px;
                color: #8c939d;

                .add-icon {
                    margin-bottom: 10px;
                }
            }
        }

        .upload-tips {
            margin-top: 8px;
            font-size: 12px;
            color: #909399;
        }
    }

    &.submit-row {
        justify-content: center;
        margin-top: 40px;
        margin-bottom: 0;
        gap: 20px;
    }
}

.submit-btn, .cancel-btn {
    width: 150px;
    height: 50px;
    font-size: 18px;
    border-radius: 8px;
}
</style>
