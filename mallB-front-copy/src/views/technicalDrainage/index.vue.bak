<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="rightBox">
            <span class="fixed-title">技术引流</span>
            <div class="main">
                <!-- 技术引流行 -->
                <div class="form-row">
                    <div class="input-group">
                        <span class="label">技术引流次</span>
                        <el-form :model="form" class="inline-form">
                            <el-form-item>
                                <el-input-number v-model="state.form.techAmount"
                                                 style="width: 300px"
                                                 class="custom-number-input"
                                                 max="499"
                                                 min="10"
                                                 placeholder="请输入10-499的数字"
                                                 type="number"
                                                 @input="setData()"
                                />
                            </el-form-item>
                        </el-form>
                        <span class="unit">技术引流量1次=100技术分量</span>
                    </div>
                </div>

                <!-- 共计行 -->
                <div class="form-row">
                    <div class="input-group">
                        <span class="label">共计</span>
                        <el-form :model="form" class="inline-form">
                            <el-form-item>
                                <el-input v-model="state.form.total"
                                          disabled
                                          placeholder="0.00"
                                />
                            </el-form-item>
                        </el-form>
                        <span class="unit">元</span>
                    </div>
                </div>

                <!-- 库存行 -->
                <div class="form-row">
                    <div class="input-group">
                        <span class="label">库存</span>
                        <el-form :model="form" class="inline-form">
                            <el-form-item>
                                <el-input v-model="state.form.stock"
                                          disabled
                                          placeholder="0.00"
                                />
                            </el-form-item>
                        </el-form>
                        <span class="unit">个 减库存方式：消耗自动减库存</span>
                    </div>

                </div>

                <!-- 提交按钮区域 -->
                <div class="submit-area">
                    <el-button
                        class="submit-btn"
                        type="primary"
                    >
                        <div class="btn-content">
                            <div @click="handleSubmit">订单生成</div>
                            <div class="protocol-agree">
                                <el-checkbox
                                    v-model="state.form.agreeProtocol"
                                    class="square-checkbox"
                                    label="我已阅读并同意"
                                />
                                <a class="protocol-link" href="/service-agreement" target="_blank">《服务协议》</a>
                            </div>
                        </div>
                    </el-button>
                </div>
                <!--        <div class="bottom">-->
                <!--          &lt;!&ndash; <span class="pay">实付金额:￥50</span> &ndash;&gt;-->
                <!--          <div class="payment-section">-->
                <!--            <button class="sure-btn">订单生成-->
                <!--              <div class="agreement-check">-->
                <!--                <input type="checkbox" id="agreement" v-model="state.agreed">-->
                <!--                <label for="agreement">我已阅读并同意<a href="/service-agreement" class="agreement-link">《服务协议》</a></label>-->
                <!--              </div>-->
                <!--            </button>-->
                <!--          </div>-->
                <!--        </div>-->


                <PaymentDialog
                    v-model:visible="state.dialogTableVisible"
                    :orderData="state.tableData1"
                    @cancel-success="handleCancelSuccess"
                />
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import {onMounted, reactive} from "vue";
import {ElMessage} from 'element-plus';
import {useRouter} from 'vue-router'
import {shipping} from '../../stores/shipping'
import {Session} from '../../utils/storage';
import {configurationData} from "@/api/permissionSetting";
import {orderGeneration} from "@/stores/orderGeneration";
import {jurisdiction} from "@/stores/productSellApply";
import PaymentDialog from '../../components/PaymentDialog.vue'

const handleSubmit = () => {
    if (!state.form.agreeProtocol) {
        ElMessage.warning("请勾选服务协议")
        return
    }
    let data = {
        drainageCount: state.form.techAmount,
    }
    shipping().CreateDrainageOrder(data).then((res) => {
        if (res.code == 200) {
            state.orderNo = res.data
            jurisdiction().GetVirtualOrderDetail(state.orderNo).then(result => {
                state.dialogTableVisible = true
                state.tableData1 = result.data
                state.amount = result.data.amount
            })
        }
    })

}
const router = useRouter()
const handleButtonClick = (item) => {
    router.push(item.component)
}

const state = reactive({
    orderNo: null,
    tableData1: {},
    amount: null,
    dialogTableVisible: false,
    money: null,
    stock: null,
    buttonList: [],
    form: {
        techAmount: null,
        total: null,
        stock: null,
        agreeProtocol: false
    }
})

//取消订单
const dialogAddCategoryReturn = async () => {
    let result = await orderGeneration().GetcancelTheOrder(state.orderNo);
    if (result.code == 200) {
        ElMessage.success("取消成功")
        state.dialogTableVisible = false
    } else {
        ElMessage.error(result.msg)
    }
}
const setData = () => {
    state.form.total = state.form.techAmount * state.money
    state.form.stock = Number(state.stock) + Number(state.form.techAmount)
}
const getDrainageCount = () => {
    shipping().GetDrainageCount().then((res) => {
        console.log("res.data", res.data);
        state.stock = res.data
        state.form.stock = res.data
    })
}
const get5 = () => {
    configurationData(5).then((res) => {
        if (res.code == 200) {
            state.money = res.data.citationValue
        }
    })
}
onMounted(() => {
    getDrainageCount()
    get5()
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })

})

// 取消订单成功的处理函数
const handleCancelSuccess = () => {
    // 重置相关状态或刷新数据
    state.dialogTableVisible = false;
    getDrainageCount()
}
</script>
<style lang="scss" scoped>

.cancelTheOrder {
    margin-top: 20px;
    text-align: center;
    margin-left: 300px;
}

.container {
    position: relative;
    //width: 100%;
    max-height: 100vh;
    display: flex;
    box-sizing: border-box;

}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.rightBox {
    display: flex;
    justify-content: center;

}

.fixed-title {
    margin-left: 20px;
    margin-top: 20px;
    width: 200px;
    height: 100px;
    margin-right: 10px;
    left: 20px;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.main {
    margin-top: 200px;
    background: #fff;
    border-radius: 8px;
    padding: 30px;

}

.form-row {
    margin-bottom: 25px;

    .input-group {
        display: flex;
        align-items: center;
        gap: 10px;

        .label {
            min-width: 80px;
            font-size: 16px;
            color: #333;
        }

        .unit {
            color: #666;
            font-size: 14px;
            white-space: nowrap;
            margin-left: 10px;
        }

        .inline-form {
            .el-form-item {
                margin-bottom: 0;

                .el-input {
                    width: 200px;
                }
            }
        }
    }
}

/* 隐藏数字输入框箭头 */
.custom-number-input {
    :deep(input[type="number"]::-webkit-outer-spin-button),
    :deep(input[type="number"]::-webkit-inner-spin-button) {
        -webkit-appearance: none;
        margin: 0;
    }

    :deep(input[type="number"]) {
        -moz-appearance: textfield;
    }
}

.reduce-method {
    display: flex;
    align-items: center;
    margin-left: auto;
    gap: 15px;

    .method-label {
        font-size: 16px;
        color: #333;
    }

    .square-radio {
        :deep(.el-radio__inner) {
            border-radius: 4px;
        }
    }
}

.submit-area {
    margin-top: 40px;
    text-align: center;

    .submit-btn {
        background-color: #FF8D1A;
        width: 300px;
        height: 80px;
        padding: 10px;

        .btn-content {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .protocol-agree {
                display: flex;
                align-items: center;
                justify-content: center;

                :deep(.el-checkbox__label) {
                    color: #666;
                    font-size: 12px;
                }

                .protocol-link {
                    color: #3A58CF;
                    text-decoration: none;
                    margin-left: 5px;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }
}
</style>
