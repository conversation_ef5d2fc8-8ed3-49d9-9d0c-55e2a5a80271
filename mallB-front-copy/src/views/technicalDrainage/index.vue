<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="rightBox">
            <span class="fixed-title">技术引流</span>
            <div class="main">
                <!-- 技术引流行 -->
                <div class="form-row">
                    <div class="input-group">
                        <span class="label">技术引流次</span>
                        <el-form :model="form" class="inline-form">
                            <el-form-item>
                                <el-input-number
                                    v-model="techAmount"
                                    class="custom-number-input"
                                    max="497"
                                    min="10"
                                    placeholder="请输入10-497的数字"
                                    style="width: 300px"
                                    type="number"
                                />
                            </el-form-item>
                        </el-form>
                        <span class="unit">技术引流量1次=100技术分量</span>
                    </div>
                </div>

                <!-- 共计行 -->
                <div class="form-row">
                    <div class="input-group">
                        <span class="label">共计</span>
                        <el-form :model="form" class="inline-form">
                            <el-form-item>
                                <el-input v-model="state.form.total"
                                          disabled
                                          placeholder="0.00"
                                />
                            </el-form-item>
                        </el-form>
                        <span class="unit">元</span>
                    </div>
                </div>

                <!-- 库存行 -->
                <div class="form-row">
                    <div class="input-group">
                        <span class="label">库存</span>
                        <el-form :model="form" class="inline-form">
                            <el-form-item>
                                <el-input v-model="state.form.stock"
                                          disabled
                                          placeholder="0.00"
                                />
                            </el-form-item>
                        </el-form>
                        <span class="unit">个 减库存方式：消耗自动减库存</span>
                    </div>

                </div>

                <!-- 提交按钮区域 -->
                <!--:disabled="!state.form.agreeProtocol || !state.form.techAmount"-->
                <div class="submit-area">
                    <el-button
                        :disabled="!state.form.agreeProtocol"
                        class="submit-btn"
                        type="primary"
                    >
                        <div class="btn-content">
                            <div @click="handleSubmit">订单生成</div>
                            <div class="protocol-agree">
                                <el-checkbox
                                    v-model="state.form.agreeProtocol"
                                    class="square-checkbox"
                                    label="我已阅读并同意"
                                />
                                <!--<a class="protocol-link" href="/service-agreement" target="_blank">《服务协议》</a>-->
                                <a class="protocol-link" @click.prevent="showAgreementDialog">《服务协议》</a>
                            </div>
                        </div>
                    </el-button>
                </div>
                <!--        <div class="bottom">-->
                <!--          &lt;!&ndash; <span class="pay">实付金额:￥50</span> &ndash;&gt;-->
                <!--          <div class="payment-section">-->
                <!--            <button class="sure-btn">订单生成-->
                <!--              <div class="agreement-check">-->
                <!--                <input type="checkbox" id="agreement" v-model="state.agreed">-->
                <!--                <label for="agreement">我已阅读并同意<a href="/service-agreement" class="agreement-link">《服务协议》</a></label>-->
                <!--              </div>-->
                <!--            </button>-->
                <!--          </div>-->
                <!--        </div>-->


                <PaymentDialog
                    v-model:visible="state.dialogTableVisible"
                    :orderData="state.tableData1"
                    @cancel-success="handleCancelSuccess"
                />

                <!-- 协议内容弹窗 -->
                <el-dialog
                    v-model="state.agreementDialogVisible"
                    class="fixed-dialog"
                    title="服务协议"
                    width="50%"
                >
                    <div class="dialog-content-wrapper">
                        <div v-loading="state.agreementLoading" class="agreement-content">
                            <div v-if="state.agreementHtml" class="content-box" v-html="state.agreementHtml"></div>
                            <el-empty v-else description="暂无协议内容"/>
                        </div>
                    </div>
                    <template #footer>
                        <div class="dialog-footer">
                            <el-button type="primary" @click="agreeAndClose">同意并关闭</el-button>
                        </div>
                    </template>
                </el-dialog>
                <!--                <el-dialog
                                        v-model="state.agreementDialogVisible"
                                        title=""
                                        width="50%"
                                        class="embedded-scroll-dialog"
                                        :lock-scroll="false"
                                >
                                    <div class="dialog-container">
                                        &lt;!&ndash; 头部区域 &ndash;&gt;
                                        <div class="dialog-header">
                                            <h3>服务协议</h3>
                                        </div>

                                        &lt;!&ndash; 内容滚动区域 &ndash;&gt;
                                        <div class="scroll-viewport">
                                            <div class="content-wrapper" ref="contentWrapper">
                                                <div
                                                        class="agreement-content"
                                                        v-html="state.agreementHtml"
                                                        v-loading="state.agreementLoading"
                                                ></div>
                                            </div>
                                        </div>

                                        &lt;!&ndash; 底部按钮 &ndash;&gt;
                                        <div class="dialog-footer">
                                            <el-button type="primary" @click="agreeAndClose">同意并关闭</el-button>
                                        </div>
                                    </div>
                                </el-dialog>-->
            </div>
        </div>
    </div>


</template>
<script lang="ts" setup>
import {nextTick, onMounted, reactive, ref, watch} from "vue";
import {ElMessage} from 'element-plus';
import {useRouter} from 'vue-router'
import {shipping} from '../../stores/shipping'
import {Session} from '../../utils/storage';
import {configurationData} from "@/api/permissionSetting";
import {orderGeneration} from "@/stores/orderGeneration";
import {jurisdiction} from "@/stores/productSellApply";
import PaymentDialog from '../../components/PaymentDialog.vue'
import {getAgreementContent} from '@/api/common/protocolCommon'

const router = useRouter()
const agreementId = '1945323476291399681';

const techAmount = ref(null)

const handleSubmit = () => {
    if (!validateForm()) return;
    if (!state.form.agreeProtocol) {
        ElMessage.warning("请勾选服务协议")
        return
    }
    let data = {
        drainageCount: state.form.techAmount,
    }
    shipping().CreateDrainageOrder(data).then((res) => {
        if (res.code == 200) {
            state.orderNo = res.data
            jurisdiction().GetVirtualOrderDetail(state.orderNo).then(result => {
                state.dialogTableVisible = true
                state.tableData1 = result.data
                state.amount = result.data.amount
            })
        }
    }).catch(error => {
        ElMessage.error(`订单生成失败: ${error.message || '未知错误'}`);
    });

}

// 表单验证
const validateForm = () => {
    if (!state.form.agreeProtocol) {
        ElMessage.warning("请先阅读并同意服务协议");
        return false;
    }

    if (!state.form.techAmount || state.form.techAmount < 10 || state.form.techAmount > 497) {
        ElMessage.warning("请输入10-499之间的有效数字");
        return false;
    }

    return true;
};


const handleButtonClick = (item) => {
    router.push(item.component)
}

const state = reactive({
    orderNo: null,
    tableData1: {},
    amount: null,
    dialogTableVisible: false,
    money: null,
    stock: null,
    buttonList: [],
    agreementDialogVisible: false,
    agreementHtml: '',
    agreementLoading: false,
    form: {
        techAmount: null,
        total: null,
        stock: null,
        agreeProtocol: false
    }
})

// 显示协议弹窗
const showAgreementDialog = async () => {
    state.agreementDialogVisible = true;
    if (!state.agreementHtml) {
        state.agreementLoading = true;
        try {
            const res = await getAgreementContent(agreementId);
            if (res.code === 200) {
                state.agreementHtml = res.data.value;
            } else {
                ElMessage.error(res.msg || '获取协议内容失败');
            }
        } catch (error) {
            ElMessage.error('网络错误，请稍后重试');
        } finally {
            state.agreementLoading = false;
        }
    }
};

// 同意协议并关闭弹窗
const agreeAndClose = () => {
    state.form.agreeProtocol = true;
    state.agreementDialogVisible = false;
};

//取消订单
const dialogAddCategoryReturn = async () => {
    try {
        let result = await orderGeneration().GetcancelTheOrder(state.orderNo);
        if (result.code == 200) {
            ElMessage.success("取消成功")
            state.dialogTableVisible = false
        } else {
            ElMessage.error(result.msg)
        }
    } catch (error) {
        ElMessage.error(`取消订单失败: ${error.message}`);
    }
};
watch(techAmount, (newVal, oldVal) => {
    state.form.techAmount = newVal
    state.form.total = state.form.techAmount * state.money
    state.form.stock = Number(state.stock) + Number(state.form.techAmount)
})
const getDrainageCount = () => {
    shipping().GetDrainageCount().then((res) => {
        console.log("res.data", res.data);
        state.stock = res.data
        state.form.stock = res.data
    })
}
const get5 = () => {
    configurationData(5).then((res) => {
        if (res.code == 200) {
            state.money = res.data.citationValue
        }
    })
}
onMounted(() => {
    getDrainageCount()
    get5()
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })

})

// 取消订单成功的处理函数
const handleCancelSuccess = () => {
    // 重置相关状态或刷新数据
    state.dialogTableVisible = false;
    getDrainageCount()
}
</script>
<style lang="scss" scoped>

.cancelTheOrder {
    margin-top: 20px;
    text-align: center;
    margin-left: 300px;
}

.container {
    position: relative;
    //width: 100%;
    max-height: 100vh;
    display: flex;
    box-sizing: border-box;

}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.rightBox {
    display: flex;
    justify-content: center;

}

.fixed-title {
    margin-left: 20px;
    margin-top: 20px;
    width: 200px;
    height: 100px;
    margin-right: 10px;
    left: 20px;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.main {
    margin-top: 200px;
    background: #fff;
    border-radius: 8px;
    padding: 30px;

}

.form-row {
    margin-bottom: 25px;

    .input-group {
        display: flex;
        align-items: center;
        gap: 10px;

        .label {
            min-width: 80px;
            font-size: 16px;
            color: #333;
        }

        .unit {
            color: #666;
            font-size: 14px;
            white-space: nowrap;
            margin-left: 10px;
        }

        .inline-form {
            .el-form-item {
                margin-bottom: 0;

                .el-input {
                    width: 200px;
                }
            }
        }
    }
}

/* 隐藏数字输入框箭头 */
.custom-number-input {
    :deep(input[type="number"]::-webkit-outer-spin-button),
    :deep(input[type="number"]::-webkit-inner-spin-button) {
        -webkit-appearance: none;
        margin: 0;
    }

    :deep(input[type="number"]) {
        -moz-appearance: textfield;
    }
}

.reduce-method {
    display: flex;
    align-items: center;
    margin-left: auto;
    gap: 15px;

    .method-label {
        font-size: 16px;
        color: #333;
    }

    .square-radio {
        :deep(.el-radio__inner) {
            border-radius: 4px;
        }
    }
}

.submit-area {
    margin-top: 40px;
    text-align: center;

    .submit-btn {
        background-color: #FF8D1A;
        width: 300px;
        height: 80px;
        padding: 10px;

        .btn-content {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .protocol-agree {
                display: flex;
                align-items: center;
                justify-content: center;

                :deep(.el-checkbox__label) {
                    color: #666;
                    font-size: 12px;
                }

                .protocol-link {
                    color: #3A58CF;
                    text-decoration: none;
                    margin-left: 5px;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }
}


/* 优化后的弹窗样式 */
.fixed-dialog {
    /* 弹窗整体样式 */
    .el-dialog {
        display: flex;
        flex-direction: column;
        max-height: 80vh; /* 限制弹窗最大高度 */
        margin-top: 5vh !important; /* 垂直居中 */
        margin-bottom: 5vh !important;

        /* 头部样式 */
        .el-dialog__header {
            padding: 16px 20px;
            flex-shrink: 0; /* 防止被压缩 */
            border-bottom: 1px solid #eee;
        }

        /* 主体区域 */
        .el-dialog__body {
            padding: 0;
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* 禁止外部滚动 */
        }
    }

    /* 内容容器 */
    .dialog-content-wrapper {
        flex: 1;
        min-height: 200px; /* 最小高度保证 */
        display: flex;
        flex-direction: column;
        overflow: hidden; /* 隐藏外部滚动条 */
        background: #f9f9f9;
    }

    /* 可滚动内容区域 */
    .agreement-content {
        flex: 1;
        overflow-y: auto; /* 启用内部滚动 */
        overflow-x: hidden;
        padding: 0; /* 移除了内边距 */

        /* 美化滚动条 - 紧贴右侧 */
        &::-webkit-scrollbar {
            width: 8px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(144, 147, 153, 0.5);
            border-radius: 4px;
            border: 2px solid transparent;
            background-clip: content-box;
        }

        &::-webkit-scrollbar-track {
            background-color: rgba(144, 147, 153, 0.1);
        }

        /* 实际内容容器 */
        .content-box {
            padding: 20px; /* 内容内边距 */
            p {
                margin: 0.8em 0;
                line-height: 1.7;
                color: #333;
                font-size: 14px;

                &:first-child {
                    margin-top: 0;
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    /* 页脚样式 */
    .dialog-footer {
        flex-shrink: 0; /* 防止被压缩 */
        padding: 12px 20px;
        border-top: 1px solid #eee;
        background: #fff;
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .fixed-dialog {
        width: 90% !important;

        .el-dialog {
            max-height: 85vh;
            margin-top: 2vh !important;
            margin-bottom: 2vh !important;
        }

        .agreement-content .content-box {
            padding: 15px;
        }
    }
}

/*.embedded-scroll-dialog {
  !* 禁用浏览器滚动条 *!
  overflow: hidden !important;

  .el-dialog {
    display: flex;
    flex-direction: column;
    max-height: 80vh;
    margin: 5vh auto !important;
    overflow: hidden;

    &__header, &__body {
      padding: 0;
    }
  }

  .dialog-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
  }

  !* 固定头部 *!
  .dialog-header {
    padding: 16px 24px;
    border-bottom: 1px solid #ebeef5;
    flex-shrink: 0;

    h3 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }
  }

  !* 滚动视口 (关键部分) *!
  .scroll-viewport {
    flex: 1;
    position: relative;
    overflow: hidden;
    padding: 0 15px;
  }

  !* 内容容器 *!
  .content-wrapper {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 5px;

    !* 完全隐藏原生滚动条 *!
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(144, 147, 153, .5);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: rgba(144, 147, 153, .1);
    }
  }

  !* 实际内容区域 *!
  .agreement-content {
    padding: 20px 0;
  }

  !* 固定底部 *!
  .dialog-footer {
    padding: 12px 24px;
    border-top: 1px solid #ebeef5;
    flex-shrink: 0;
    text-align: center;
    background: #fff;
  }
}*/


</style>
