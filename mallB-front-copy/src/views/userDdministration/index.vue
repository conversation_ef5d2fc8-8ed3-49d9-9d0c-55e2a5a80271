<template>
    <div class="page-container">
        <!-- 顶部按钮 -->
        <div class="header-buttons">
            <el-button class="create-btn" @click="handleToCreateUser">创建职员+</el-button>
        </div>

        <!-- 搜索区域 -->
        <div class="search-area">
            <el-input
                v-model="searchQuery"
                class="search-input"
                clearable
                placeholder="用户名"
                @keyup.enter="handleSearch"
            />
            <el-input
                v-model="phonenumber"
                class="search-input"
                clearable
                placeholder="电话号码"
                @keyup.enter="handleSearch"
            />
            <el-button class="search-btn" type="primary" @click="handleSearch">搜索</el-button>
            <el-button class="search-btn" type="primary" @click="resetSearch">重置</el-button>
        </div>

        <!-- 表格区域 -->
        <div class="table-wrapper">
            <el-table
                :data="tableData"
                border
                default-expand-all
                row-key="id"
                style="width: 100%; margin-bottom: 20px"
            >
                <el-table-column label="用户名" prop="userName"/>
                <el-table-column label="昵称" prop="nickName"/>
                <el-table-column label="手机号" prop="phonenumber"/>
                <el-table-column label="操作" width="250">
                    <template #default="scope">
                        <el-button v-if="!scope.row.admin" link type="primary" @click="openUpdateDialog(scope.row)">
                            修改
                        </el-button>
                        <el-button v-if="!scope.row.admin" link type="primary" @click="openDelete(scope.row)">删除
                        </el-button>
                        <el-button v-if="!scope.row.admin" link type="primary" @click="handleResetPwd(scope.row)">
                            重置密码
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 固定在右下角的分页 -->
        <div class="fixed-pagination">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
            />
        </div>
    </div>
</template>
<script lang="ts" setup>
import {ref} from 'vue'
import {useRouter} from 'vue-router'
import {platformUser} from '../../stores/platformUser'
import {useMessage} from '../../hooks/message';
import {ElMessage, ElMessageBox} from 'element-plus'

const router = useRouter();
const handleToCreateUser = () => {
    router.push({path: '/CreateUser'})
}
// 表格数据
const tableData = ref([])
// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索
const searchQuery = ref('')
const phonenumber = ref('')
// 搜索功能
const handleSearch = () => {
    getUserList()
}
const resetSearch = () => {
    searchQuery.value = ''
    phonenumber.value = ''
    currentPage.value = 1
    getUserList()
}
//获取用户数据
const getUserList = async () => {
    try {
        let data =
            {
                pageNum: currentPage.value,
                pageSize: pageSize.value,
                userName: searchQuery.value,
                phonenumber: phonenumber.value
            }
        let result = await platformUser().GetUserList(data);
        tableData.value = result.rows
        total.value = result.total
    } finally {

    }
};
//修改用户信息
const openUpdateDialog = (row) => {
    router.push({
        path: '/updateUser',
        query: {
            userId: row.userId
        }
    });
}
/** 重置密码按钮操作 */
const handleResetPwd = (row) => {
    ElMessageBox.prompt('请输入"' + row.nickName + '"的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        inputPattern: /^.{5,20}$/,
        inputErrorMessage: "用户密码长度必须介于 5 和 20 之间"
    }).then(({value}) => {
        const data = {
            userId: row.userId,
            password: value
        }
        platformUser().resetUserPwd(data).then(() => {
            ElMessage.success("修改成功，新密码是：" + value);
        }).catch(error => {
            ElMessage.error(error.message);
        })
    })
}

const openDelete = async (row) => {
    const userId = row.userId
    try {
        let result = await platformUser().deleteUser(userId);
        if (result.code == 200) {
            useMessage().success(result.msg || '系统异常请联系管理员');
        } else {
            useMessage().error(result.msg || '系统异常请联系管理员');
        }
        getUserList()
    } finally {

    }
}
onMounted(() => {
    getUserList()
})
</script>
<style lang="scss" scoped>
.page-container {
    width: 100%;
    max-height: 100vh;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;

    .header-buttons {
        display: flex;
        justify-content: flex-start;
        gap: 20px;
        margin-bottom: 20px;

        .create-btn {
            width: 200px;
            height: 50px;
            background-color: #3A58CF;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
    }

    .search-area {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;

        .search-input {
            flex: 1;

            :deep(.el-input__wrapper) {
                height: 50px;
                font-size: 16px;
            }
        }

        .search-btn {
            width: 100px;
            height: 50px;
            font-size: 16px;
        }
    }

    .table-wrapper {
        flex: 1;
        overflow: auto;
        margin-bottom: 60px; /* 为分页留出空间 */

        :deep(.el-table) {
            font-size: 14px;
        }

        :deep(.el-table__cell) {
            padding: 12px 0;
        }
    }

    .fixed-pagination {
        position: fixed;
        right: 20px;
        bottom: 20px;
        background: white;
        padding: 10px;
        border-radius: 4px;;
        z-index: 10;
    }
}
</style>
