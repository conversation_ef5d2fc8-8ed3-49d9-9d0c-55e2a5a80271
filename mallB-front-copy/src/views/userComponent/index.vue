# 用户分量统计页面
<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="main">
            <div class="main-content">
                <!-- 查询表单 -->
                <div class="search-form">
                    <el-form ref="queryForm" :inline="true" :model="queryParams">
                        <el-form-item label="统计日期">
                            <el-date-picker
                                v-model="queryParams.date"
                                placeholder="选择日期"
                                style="width: 200px"
                                type="date"
                                value-format="YYYY-MM-DD"
                            />
                        </el-form-item>
                        <el-form-item label="用户名">
                            <el-input v-model="queryParams.username" clearable placeholder="请输入用户名"
                                      style="width: 180px"/>
                        </el-form-item>
                        <el-form-item label="手机号">
                            <el-input v-model="queryParams.phone" clearable placeholder="请输入手机号"
                                      style="width: 180px"/>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleQuery">查询</el-button>
                            <el-button @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 统计表格 -->
                <div class="table-section">
                    <div class="table-header">
                        <h3>用户分量统计明细</h3>
                    </div>
                    <el-table
                        v-loading="loading"
                        :data="statisticsList"
                        :header-cell-class-name="'custom-header'"
                        :row-class-name="'custom-row'"
                        border
                        style="width: 100%"
                    >
                        <el-table-column align="center" label="用户名" prop="username"/>
                        <el-table-column align="center" label="昵称" prop="nickname"/>
                        <el-table-column align="center" label="手机号" prop="phone"/>
                        <el-table-column align="center" label="邮箱" prop="email"/>
                        <el-table-column align="center" label="当日获得分量" prop="dayAmount">
                            <template #default="scope">
                                <span :class="{'positive': scope.row.dayAmount > 0}">
                                    {{ scope.row.dayAmount }}
                                </span>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页 -->
                    <div class="pagination-container">
                        <el-pagination
                            v-model:current-page="queryParams.pageNum"
                            v-model:page-size="queryParams.pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            :total="total"
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {nextTick, onMounted, reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {Session} from '../../utils/storage'
import {ElMessage} from 'element-plus'
import {getDailyStatistics} from '../../api/userComponent'

// 路由实例
const router = useRouter()

// 状态数据
const state = reactive({
    buttonList: []
})

// 查询参数
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    date: new Date().toISOString().split('T')[0],
    username: '',
    phone: ''
})

// 数据列表
const statisticsList = ref([])
const total = ref(0)
const loading = ref(false)

// 查询数据
const getList = async () => {
    loading.value = true
    try {
        const res = await getDailyStatistics({
            date: queryParams.date,
            pageNum: queryParams.pageNum,
            pageSize: queryParams.pageSize,
            username: queryParams.username,
            phone: queryParams.phone
        })
        statisticsList.value = res.rows
        total.value = res.total
    } catch (error) {
        console.error('获取数据失败:', error)
        ElMessage.error('获取数据失败')
    } finally {
        loading.value = false
    }
}

// 查询按钮点击
const handleQuery = () => {
    if (!queryParams.date) {
        ElMessage.warning('请选择统计日期')
        return
    }
    queryParams.pageNum = 1
    getList()
}

// 重置按钮点击
const resetQuery = () => {
    queryParams.date = new Date().toISOString().split('T')[0] // 重置为今天
    Object.assign(queryParams, {
        pageNum: 1,
        pageSize: 10,
        username: '',
        phone: ''
    })
    getList()
}

// 分页大小改变
const handleSizeChange = (val) => {
    queryParams.pageSize = val
    getList()
}

// 页码改变
const handleCurrentChange = (val) => {
    queryParams.pageNum = val
    getList()
}

// 左侧按钮点击
const handleButtonClick = (item) => {
    router.push(item.component)
}

// 初始化时加载数据
onMounted(() => {
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId')
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index]
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index]
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
        // 加载数据
        getList()
    })
})
</script>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.main {
    position: absolute;
    left: 235px;
    right: 0;
    padding: 20px;
    background: #f5f7fa;
    min-height: 100%;
    box-sizing: border-box;
}

.main-content {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-form {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fb;
    border-radius: 8px;
}

.table-section {
    .table-header {
        margin-bottom: 20px;

        h3 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
    }
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

:deep(.custom-header) {
    th {
        background-color: #83A4EB !important;
        color: #000;
        font-weight: bold;
        font-size: 16px;
    }
}

:deep(.custom-row) {
    td {
        background-color: #D2E0FB;
    }

    &:hover td {
        background-color: #b8cdf9 !important;
    }
}

.positive {
    color: #67C23A;
}
</style>
