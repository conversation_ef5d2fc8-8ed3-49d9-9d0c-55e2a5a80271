<template>
    <!--  <ManageBg>-->
    <div class="data-container">
        <!-- 左侧按钮列表 -->
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                v-if="state.flag"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>

        <!-- 右侧内容区域 -->
        <div class="right-content">
            <div class="total-display">
                <div v-for="n in 3" :key="n" class="total-item">总数</div>
            </div>
        </div>

        <!-- 独立的分页组件 -->
        <div class="fixed-pagination">
            <el-pagination
                :page-size="10"
                :total="50"
                layout="prev, pager, next"
                small
            >
                <template #prev>
                    <el-button :icon="ArrowLeft"/>
                </template>
                <template #next>
                    <el-button :icon="ArrowRight"/>
                </template>
            </el-pagination>
        </div>
    </div>
    <!--  </ManageBg>-->
</template>
<script lang="ts" setup>
import {Session} from '../../utils/storage'

// const buttonList = [
//   '数据', '订单排行', '商品链接', '平台广告', '店铺广告',
//   '开通权限', '量化率', '平台促销券', '交易统计', '贷款统计',
//   '数据', '兑换券', '退单排行', '交易数据', '补贴金数据'
// ]
const state = reactive({
    ruleForm: {
        parentId: 0
    },
    parentData: [
        {
            id: 0,
            name: '顶级分类',
            children: []
        }
    ],
    type: 2,
    flag: false,
    buttonList: []
});
const router = useRouter()
const handleButtonClick = (item) => {
    console.log(item, 'item')
    router.push(item.component)
}

onMounted(() => {
    console.log("统计页面")
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
        state.flag = true
    })
})
</script>

<style lang="scss" scoped>
.data-container {
    position: relative;
    display: flex;
    height: calc(100vh - 40px);
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    margin-right: 30px;
    height: 100%;
    overflow-y: auto; /* 如果按钮太多可滚动 */

    .data-button {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block; /* 确保按钮独占一行 */
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.right-content {
    width: calc(100% - 265px); /* 减去左侧按钮和间距 */
    height: 100%;
    position: relative;

    .total-display {
        display: flex;
        gap: 20px;
        margin-bottom: 30px;

        .total-item {
            padding: 15px 30px;
            background: #f5f7fa;
            border-radius: 8px;
            font-size: 18px;
        }
    }
}

.fixed-pagination {
    position: fixed;
    right: 20px;
    bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    background: white;
    padding: 10px;
    border-radius: 8px;


    :deep(.btn-prev),
    :deep(.btn-next) {
        width: 32px;
        height: 32px;
    }

    :deep(.el-pager li) {
        width: 32px;
        height: 32px;
        line-height: 32px;
    }
}
</style>
