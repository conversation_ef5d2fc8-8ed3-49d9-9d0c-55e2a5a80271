<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="content-area">
            <div class="headBox">
                <div class="search-content">
                    <el-form
                        :inline="true"
                        :model="searchForm"
                        size="large"
                    >
                        <el-form-item label="商品编号" prop="productId">
                            <el-input
                                v-model="searchForm.productId"
                                clearable
                                placeholder="请输入商品编号"
                                show-word-limit
                            />
                        </el-form-item>
                        <el-form-item label="商品名称" prop="productName">
                            <el-input
                                v-model="searchForm.productName"
                                clearable
                                placeholder="请输入商品名称"
                                show-word-limit
                            />
                        </el-form-item>
                        <el-form-item label="手机号" prop="phone">
                            <el-input
                                v-model="searchForm.phone"
                                clearable
                                placeholder="请输入手机号"
                                show-word-limit
                                @input="handlePhoneInput"
                            />
                        </el-form-item>
                        <el-form-item label="状态" prop="daiXiaoStatus">
                            <el-select
                                v-model="searchForm.daiXiaoStatus"
                                clearable
                                placeholder="请选择状态"
                                style="width: 150px"
                            >
                                <el-option label="全部" value=""/>
                                <el-option label="未代销" value="0"/>
                                <el-option label="已代销" value="1"/>
                            </el-select>
                        </el-form-item>
                        <el-form-item class="search-actions">
                            <el-button
                                :icon="Search"
                                :loading="loading"
                                size="large"
                                type="primary"
                                @click="handleSearch"
                            >
                                搜索
                            </el-button>
                            <el-button
                                :icon="RefreshRight"
                                size="large"
                                @click="handleReset"
                            >
                                重置
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <div class="main">
                <el-table
                    v-loading="loading"
                    :data="state.tableData"
                    empty-text="暂无数据"
                    style="width: 100%"
                >
                    <el-table-column align="center" label="序号" type="index" width="100"/>
                    <el-table-column label="商品编号" prop="productId" show-overflow-tooltip/>
                    <el-table-column label="商品名称" prop="name" show-overflow-tooltip/>
                    <el-table-column label="手机号" prop="phone" show-overflow-tooltip/>
                    <el-table-column align="center" label="商品封面" prop="cover">
                        <template #default="scope">
                            <el-image
                                :preview-src-list="[getImageUrl(scope.row.cover)]"
                                :src="getImageUrl(scope.row.cover)"
                                fit="cover"
                                preview-teleported
                                style="width: 80px; height: 80px; border-radius: 4px;"
                            >
                                <template #error>
                                    <div class="image-slot">
                                        <el-icon>
                                            <Picture/>
                                        </el-icon>
                                    </div>
                                </template>
                            </el-image>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="利润" prop="profit">
                        <template #default="scope">
                            <span class="profit-text">¥{{ formatPrice(scope.row) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="状态" prop="status">
                        <template #default="scope">
                            <el-tag :type="getStatusType(scope.row.daiXiaoStatus)">
                                {{ getStatusText(scope.row.daiXiaoStatus) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" fixed="right" label="操作">
                        <template #default="scope">
                            <el-button
                                v-if="scope.row.daiXiaoStatus != '0'"
                                size="small"
                                type="primary"
                                @click="handleConsignment(scope.row)"
                            >
                                代销
                            </el-button>
                            <el-button
                                v-if="scope.row.daiXiaoStatus == '0'"
                                size="small"
                                type="danger"
                                @click="handleCancelConsignment(scope.row)"
                            >
                                取消代销
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.pageNum"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import {nextTick, onMounted, reactive} from 'vue'
import {useRouter} from 'vue-router'
import {Picture, RefreshRight, Search} from '@element-plus/icons-vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Session} from '../../utils/storage'
import {commoditySales} from '../../stores/commoditySales'
import {COS_URL} from '../../config/baseUrl'

const router = useRouter()

// 响应式数据
const state = reactive({
    buttonList: [],
    tableData: []
})

// 搜索表单
const searchForm = reactive({
    productId: '',
    productName: '',
    phone: '',
    daiXiaoStatus: ''
})

// 分页数据
const pagination = reactive({
    pageNum: 1,
    pageSize: 10,
    total: 0
})

// 加载状态
const loading = ref(false)

// 处理按钮点击
const handleButtonClick = (item) => {
    router.push(item.component)
}

// 获取图片URL
const getImageUrl = (cover: string) => {
    if (!cover) return ''
    return cover.startsWith('http') ? cover : `${COS_URL}/${cover}`
}

// 格式化价格
const formatPrice = (row) => {
    if (row.daixiaoType == 1) {
        return Number(row.daixiaoTypeValue).toFixed(2)
    } else {
        return Number(row.daixiaoTypeValue / 100 * row.price).toFixed(2)
    }
}

// 获取状态类型
const getStatusType = (status: string) => {
    const statusMap = {
        '0': 'success',    // 未代销
        '1': 'info', // 已代销
    }
    return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
    const statusMap = {
        '0': '已代销',
        '1': '未代销'
    }
    return statusMap[status] || '未代销'
}

// 获取商品销售数据
const getCommoditySales = async () => {
    try {
        loading.value = true
        const params = {
            pageSize: pagination.pageSize,
            pageNum: pagination.pageNum,
            ...searchForm,
        }

        // 移除空值
        Object.keys(params).forEach(key => {
            if (params[key] === '' || params[key] === null || params[key] === undefined) {
                delete params[key]
            }
        })

        const result = await commoditySales().GetDaiXiaoProductList(params)

        if (result && result.rows) {
            state.tableData = result.rows
            pagination.total = result.total || 0
        } else {
            state.tableData = []
            pagination.total = 0
        }
    } catch (error) {
        console.error('获取商品销售数据失败:', error)
        ElMessage.error('获取数据失败，请稍后重试')
        state.tableData = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 搜索处理
const handleSearch = () => {
    pagination.pageNum = 1
    getCommoditySales()
}

// 重置搜索
const handleReset = () => {
    Object.assign(searchForm, {
        productId: '',
        productName: '',
        phone: '',
        daiXiaoStatus: ''
    })
    pagination.pageNum = 1
    getCommoditySales()
}

// 手机号输入处理（只允许数字）
const handlePhoneInput = (value: string) => {
    searchForm.phone = value.replace(/[^\d]/g, '')
}

// 分页大小改变
const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.pageNum = 1
    getCommoditySales()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
    pagination.pageNum = page
    getCommoditySales()
}

// 处理代销
const handleConsignment = async (row: any) => {
    try {
        await ElMessageBox.confirm(
            `确定要代销商品"${row.name}"吗？`,
            '确认代销',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        // 这里调用代销API
        const result = await commoditySales().InsertConsignmentProduct(row.productId)
        getCommoditySales() // 刷新数据
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('代销失败，请稍后重试')
        }
    }
}

// 处理取消代销
const handleCancelConsignment = async (row: any) => {
    try {
        await ElMessageBox.confirm(
            `确定要取消代销商品"${row.name}"吗？`,
            '确认取消',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        // 这里调用取消代销API
        const result = await commoditySales().CancelConsignmentProduct(row.productId)
        getCommoditySales() // 刷新数据
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('取消代销失败，请稍后重试')
        }
    }
}

// 初始化菜单
const initMenu = () => {
    nextTick(() => {
        const menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId')

        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index]
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }

        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index]
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })
}

// 组件挂载
onMounted(() => {
    getCommoditySales()
    initMenu()
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 30px);
    overflow-y: scroll;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.content-area {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    margin-left: 240px;
}


.pagination-container {
    padding: 20px;
    display: flex;
    justify-content: center;
    background: #fff;
    border-top: 1px solid #ebeef5;
}

.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    background: #f5f7fa;
    color: #909399;
    border-radius: 4px;
}

.profit-text {
    color: #e6a23c;
    font-weight: 600;
}

// 响应式设计
@media (max-width: 1400px) {
    .headBox {
        width: 100%;
    }

    .main {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }

    .left-buttons {
        position: relative;
        width: 100%;
        height: auto;
        margin-bottom: 20px;

        :deep(.el-button.data-button) {
            display: inline-block;
            width: auto;
            height: 40px;
            margin: 5px;
            border-radius: 4px;
        }
    }

    .content-area {
        margin-left: 0;
        padding: 10px;
    }

    .headBox {
        .search-content {
            .search-area {
                .search-row {
                    flex-direction: column;

                    .search-item {
                        min-width: 100%;

                        .label {
                            min-width: 60px;
                            text-align: left;
                        }
                    }
                }
            }
        }
    }
}
</style>
