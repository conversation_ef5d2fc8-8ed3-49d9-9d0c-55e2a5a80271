<template>
    <!--  <HomeBg>-->
    <div class="container">
        <img alt="背景图" class="bg" src="../../images/bigBackground.png">
        <div class="form-container">
            <el-form :model="state.ruleForm">
                <el-form-item label="菜单名称" label-position="right" label-width="80px" prop="menuName">
                    <div class="input-group">
                        <el-input
                            v-model="state.ruleForm.menuName"
                            class="form-input"
                            placeholder="请输入菜单名称"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="平台类型" label-position="right" label-width="80px" prop="openType">
                    <div class="input-group">
                        <el-radio-group v-model="state.ruleForm.openType">
                            <el-radio label="1">管理端</el-radio>
                            <el-radio label="2">平台管理</el-radio>
                        </el-radio-group>
                    </div>
                </el-form-item>
                <el-form-item label="上级菜单" label-position="right" label-width="80px" prop="parent">
                    <div class="input-group">
                        <el-tree-select
                            v-model="state.ruleForm.parentId"
                            :data="state.parentData"
                            :props="{ checkStrictly: true, value: 'menuId', label: 'menuName', children: 'children'}"
                            :render-after-expand="false"
                            check-strictly
                            style="width: 240px"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="组件类型" label-position="right" label-width="80px" prop="menuType">
                    <div class="input-group">
                        <el-radio-group v-model="state.ruleForm.menuType">
                            <el-radio label="C">目录</el-radio>
                            <el-radio label="M">菜单</el-radio>
                        </el-radio-group>
                    </div>
                </el-form-item>

                <el-form-item label="组件路径" label-position="right" label-width="80px" prop="component">
                    <div class="input-group">
                        <el-input
                            v-model="state.ruleForm.component"
                            class="form-input"
                            placeholder="请输入组件路径"
                        />
                    </div>
                </el-form-item>
            </el-form>
            <div class="save-button-container">
                <el-button class="save-button" @click="handleToNormalManager">保存</el-button>
            </div>
        </div>
    </div>
    <!--  </HomeBg>-->
</template>
<script lang="ts" setup>
import {onMounted, reactive} from 'vue'
import {useRouter} from 'vue-router'
import {platformMenu} from '../../stores/platformMenu'
// import { Session } from './storage';
const router = useRouter();
const state = reactive({
    ruleForm: {
        parentId: 0,
        menuName: "",
        path: "",
        component: "",
        menuType: "C",
        remark: "",
        openType: "1"
    },
    parentData: [
        {
            menuId: 0,
            menuName: '主菜单',
            children: [],
        }
    ]
});

const handleToNormalManager = async () => {
    try {
        let result = await platformMenu().SaveMenu(state.ruleForm); // 调用存储菜单方法
        router.push('/menuManager')
    } finally {
        //alert("系统繁忙请联系管理员");
    }
}
// 创建树
const getTreeList = async () => {
    try {
        let result = await platformMenu().GetTreeList();
        state.parentData[0].children = result.data
    } finally {

    }
}
onMounted(() => {
    getTreeList()
})

</script>
<style lang="scss" scoped>
.container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }

    .form-container {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;

        width: 100%;
        max-width: 1200px;

        .input-group {
            display: flex;
            align-items: center;
            //margin-bottom: 61px;
            width: 100%;
            //margin-left: 0;

            .form-input {
                width: 805px;

                :deep(.el-input__wrapper) {
                    height: 53px;
                    font-size: 16px;
                }
            }

            :deep(.el-select__wrapper) {
                height: 53px;
            }

            .reserved-text {
                margin-left: 55px;
                color: #FF8D1A;
                font-size: 16px;
                white-space: nowrap;
            }
        }

        .save-button-container {
            width: 805px;
            margin: 0 auto;
            text-align: center;

            .save-button {
                width: 120px;
                height: 40px;
                background-color: #14097A;
                color: white;
                border: none;
                font-size: 16px;
                border-radius: 4px;

                &:hover {
                    background-color: #1a0da0;
                }

                &:active {
                    background-color: #0f0657;
                }
            }
        }
    }
}
</style>
