<script setup>
import {useRouter} from 'vue-router'
import {ref} from 'vue'

const router = useRouter()
const handleButtonClick = (item) => {
    if (item === '全部客户') router.push('/allCustomer')
    if (item === '资格申请') router.push('/eligibilityApply')
    if (item === '广告') router.push('/ad')
    if (item === '技术引流') router.push('/allCustomertechnicalDrainage')
}

const buttonList = [
    '全部客户', '资格申请', '广告', '技术引流'
]

const form = ref({
    techAmount: '',
    total: '0.00',
    stock: '0.00',
    reduceMethod: 'daily',
    agreeProtocol: false
})

const showIp = ref(null)

const toggleIp = (index) => {
    showIp.value = showIp.value === index ? null : index
}
</script>

<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item }}
            </el-button>
        </div>
        <div class="rightBox">
            <span class="fixed-title">技术引流</span>
            <div class="main">
                <!-- 技术引流行 -->
                <div class="form-row">
                    <div class="input-group">
                        <span class="label">技术引流次</span>
                        <el-form :model="form" class="inline-form">
                            <el-form-item>
                                <el-input
                                    v-model="form.techAmount"
                                    class="custom-number-input"
                                    max="499"
                                    min="10"
                                    placeholder="请输入10-499的数字"
                                    type="number"
                                />
                            </el-form-item>
                        </el-form>
                        <span class="unit">次</span>
                        <span class="unit-right">技术引流1次、100技术分量</span>
                    </div>
                </div>

                <!-- 共计行 -->
                <div class="form-row">
                    <div class="input-group">
                        <span class="label">共计</span>
                        <el-form :model="form" class="inline-form">
                            <el-form-item>
                                <el-input
                                    v-model="form.total"
                                    placeholder="0.00"
                                />
                            </el-form-item>
                        </el-form>
                        <span class="unit">元</span>
                    </div>
                </div>

                <!-- 库存行 -->
                <div class="form-row">
                    <div class="input-group">
                        <span class="label">库存</span>
                        <el-form :model="form" class="inline-form">
                            <el-form-item>
                                <el-input v-model="form.stock"/>
                            </el-form-item>
                        </el-form>
                        <span class="unit">次</span>
                        <span class="unit-right">减库存方式：消耗自动减库存</span>
                    </div>
                </div>
                <!-- 提交按钮区域 -->
                <div class="submit-area">
                    <el-button
                        class="submit-btn"
                        type="primary"
                        @click="handleSubmit"
                    >
                        <div class="btn-content">
                            <div>确认协议并支付</div>
                            <div class="protocol-agree">
                                <el-checkbox
                                    v-model="form.agreeProtocol"
                                    class="square-checkbox"
                                    label="我已阅读并同意"
                                />
                                <a class="protocol-link" href="/service-agreement" target="_blank">《服务协议》</a>
                            </div>
                        </div>
                    </el-button>
                </div>
            </div>
        </div>
    </div>
    <!--  </ManageBg>-->
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.rightBox {
    display: flex;
    justify-content: center;
}

.fixed-title {
    margin-left: 20px;
    margin-top: 20px;
    width: 200px;
    height: 100px;
    margin-right: 10px;
    left: 20px;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.main {
    margin-top: 200px;
    background: #fff;
    border-radius: 8px;
    padding: 30px;
}

.right-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.header {
    height: 76px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #3A58CF;
    margin-bottom: 20px;
    border-radius: 8px;

    .title {
        font-size: 30px;
        color: white;
        font-weight: bold;
    }
}

.search-form {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.form-row {
    margin-bottom: 25px;

    .input-group {
        display: flex;
        align-items: center;
        gap: 10px;

        .label {
            min-width: 80px;
            font-size: 16px;
            color: #333;
        }

        .unit {
            color: #666;
            font-size: 14px;
            white-space: nowrap;
            margin-left: 10px;
        }

        .unit-right {
            color: #666;
            font-size: 14px;
            white-space: nowrap;
            margin-left: 100px;
        }

        .inline-form {
            .el-form-item {
                margin-bottom: 0;

                .el-input {
                    width: 200px;
                }
            }
        }
    }
}

.form-input {
    width: 200px;
    margin-right: 15px;
    margin-bottom: 10px;
}

.date-picker {
    width: 180px;
    margin-right: 15px;
}

.to-text {
    margin: 0 10px;
    color: #606266;
}

.search-btn {
    margin-left: 15px;
}

.data-table {
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-table__cell) {
        padding: 12px 0;
    }
}

/* 隐藏数字输入框箭头 */
.custom-number-input {
    :deep(input[type="number"]::-webkit-outer-spin-button),
    :deep(input[type="number"]::-webkit-inner-spin-button) {
        -webkit-appearance: none;
        margin: 0;
    }

    :deep(input[type="number"]) {
        -moz-appearance: textfield;
    }
}

.reduce-method {
    display: flex;
    align-items: center;
    margin-left: auto;
    gap: 15px;

    .method-label {
        font-size: 16px;
        color: #333;
    }

    .square-radio {
        :deep(.el-radio__inner) {
            border-radius: 4px;
        }
    }
}

.submit-area {
    margin-top: 40px;
    text-align: center;

    .submit-btn {
        width: 300px;
        height: 80px;
        padding: 10px;

        .btn-content {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .protocol-agree {
                display: flex;
                align-items: center;
                justify-content: center;

                :deep(.el-checkbox__label) {
                    color: #666;
                    font-size: 12px;
                }

                .protocol-link {
                    color: #3A58CF;
                    text-decoration: none;
                    margin-left: 5px;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }
}
</style>
