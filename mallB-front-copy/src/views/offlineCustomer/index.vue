<template>
    <div class="container">
        <div class="rightBox">
            <div class="head">
                <span class="title">客户管理</span>
                <el-button type="primary" @click="openRecordsDialog">查看记录</el-button>
            </div>
            <div class="main">
                <div class="search-box">
                    <el-form ref="searchFormRef" :inline="true" :model="state.searchForm">
                        <el-form-item label="企业信用号/手机号">
                            <el-input
                                v-model="state.searchForm.socialCreditCodeOrPhone"
                                class="search-input"
                                clearable
                                placeholder="请输入企业信用号或手机号"
                            />
                        </el-form-item>
                        <el-form-item label="权限级别">
                            <el-button-group class="level-buttons">
                                <el-button
                                    :type="state.searchForm.jurisdiction === '' ? 'primary' : ''"
                                    @click="state.searchForm.jurisdiction = ''"
                                >
                                    全部
                                </el-button>
                                <el-button
                                    :type="state.searchForm.jurisdiction === '1' ? 'primary' : ''"
                                    @click="state.searchForm.jurisdiction = '1'"
                                >
                                    权限一
                                </el-button>
                                <el-button
                                    :type="state.searchForm.jurisdiction === '2' ? 'primary' : ''"
                                    @click="state.searchForm.jurisdiction = '2'"
                                >
                                    权限二
                                </el-button>
                                <el-button
                                    :type="state.searchForm.jurisdiction === '3' ? 'primary' : ''"
                                    @click="state.searchForm.jurisdiction = '3'"
                                >
                                    权限三
                                </el-button>
                            </el-button-group>
                        </el-form-item>
                        <el-form-item label="注册时间">
                            <el-date-picker
                                v-model="state.searchForm.beginCreateTime"
                                class="date-picker"
                                placeholder="注册时间"
                                type="date"
                            />
                            <span class="to-text">至</span>
                            <el-date-picker
                                v-model="state.searchForm.endCreateTime"
                                class="date-picker"
                                placeholder="结束时间"
                                type="date"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="searchFun">搜索</el-button>
                            <el-button @click="resetSearch">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <el-table
                    v-loading="state.loading"
                    :data="state.tableData"
                    border
                    class="data-table"
                    stripe
                    @sort-change="handleSortChange"
                >
                    <el-table-column label="手机号" min-width="120" prop="phone"/>
                    <!--                    <el-table-column prop="legalPersonIdCard" label="身份证" min-width="180"/>-->
                    <el-table-column label="公司名/姓名" min-width="180" prop="businessName" show-overflow-tooltip/>
                    <el-table-column label="企业信用代号" min-width="160" prop="socialCreditCode"/>
                    <el-table-column label="权限" prop="jurisdiction">
                        <template #default="scope">
                            <el-tag v-if="scope.row.jurisdiction" type="success">
                                权限{{ scope.row.jurisdiction }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="注册时间" min-width="180" prop="createTime">
                        <template #default="scope">
                            {{ formatDate(scope.row.createTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="技术引流次数" min-width="100" prop="drainage"/>
                    <el-table-column label="粉丝量" min-width="80" prop="fans"/>
                    <el-table-column label="状态" min-width="100" prop="status">
                        <template #default="scope">
                            <el-tag :type="scope.row.status == 1 ? 'danger' : 'success'">
                                {{ getStatusText(scope.row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" min-width="220">
                        <template #default="scope">
                            <el-button
                                size="small"
                                type="primary"
                                @click="openRechargeDialog(scope.row)"
                            >
                                引流付费
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页组件 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="state.pagination.currentPage"
                        v-model:page-size="state.pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="state.pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>

        <!-- 对话框 -->
        <el-dialog
            v-model="state.rechargeDialog.visible"
            title="引流付费"
            width="600px"
        >
            <el-form
                ref="rechargeFormRef"
                :model="state.rechargeDialog.form"
                :rules="rechargeRules"
                class="recharge-form"
                label-width="120px"
            >
                <!--                <el-form-item label="商家ID" prop="shopId">-->
                <!--                    <el-input-->
                <!--                        v-model.number="state.rechargeDialog.form.shopId"-->
                <!--                        disabled-->
                <!--                    />-->
                <!--                </el-form-item>-->

                <el-form-item label="商家名称">
                    <el-input
                        style="width: 400px"
                        v-model="state.rechargeDialog.customerName"
                        disabled
                    />
                </el-form-item>

                <el-form-item label="数量" prop="count">
                    <el-input-number
                        style="width: 400px"
                        v-model="state.rechargeDialog.form.count"
                        :min="1"
                        :step="1"
                        controls-position="right"
                    />
                </el-form-item>

                <el-form-item label="操作密码" prop="operationPassword">
                    <el-input
                        style="width: 400px"
                        v-model="state.rechargeDialog.form.operationPassword"
                        placeholder="请输入操作密码"
                        show-password
                        type="password"
                    />
                </el-form-item>

                <el-form-item label="备注" prop="remark">
                    <el-input
                        style="width: 400px"
                        v-model="state.rechargeDialog.form.remark"
                        :rows="3"
                        placeholder="请输入备注信息"
                        type="textarea"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="state.rechargeDialog.visible = false">取消</el-button>
                    <el-button :loading="state.rechargeDialog.loading" type="primary" @click="submitRecharge">
                        确认
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 记录对话框 -->
        <el-dialog
            v-model="state.recordsDialog.visible"
            title="记录"
            width="1400px"
        >
            <div class="search-box">
                <el-form :inline="true" :model="state.recordsDialog.searchForm">
                    <el-form-item label="商家名称">
                        <el-input
                            v-model="state.recordsDialog.searchForm.shopName"
                            clearable
                            placeholder="请输入商家名称"
                        />
                    </el-form-item>
                    <el-form-item label="商家名称">
                        <el-input
                            v-model="state.recordsDialog.searchForm.shopPhone"
                            clearable
                            placeholder="请输入电话号码"
                        />
                    </el-form-item>
                    <el-form-item label="操作人">
                        <el-input
                            v-model="state.recordsDialog.searchForm.operatorName"
                            clearable
                            placeholder="请输入操作人"
                        />
                    </el-form-item>
                    <el-form-item label="时间">
                        <el-date-picker
                            v-model="state.recordsDialog.searchForm.beginCreateTime"
                            class="date-picker"
                            placeholder="开始时间"
                            type="datetime"
                            value-format="YYYY-MM-DD HH:mm:ss"
                        />
                        <span class="to-text">至</span>
                        <el-date-picker
                            v-model="state.recordsDialog.searchForm.endCreateTime"
                            class="date-picker"
                            placeholder="结束时间"
                            type="datetime"
                            value-format="YYYY-MM-DD HH:mm:ss"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="searchRecords">搜索</el-button>
                        <el-button @click="resetRecordsSearch">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <el-table
                v-loading="state.recordsDialog.loading"
                :data="state.recordsDialog.tableData"
                border
                stripe
            >
                <el-table-column label="商家名称" min-width="120" prop="shopName"/>
                <el-table-column label="电话号码" min-width="120" prop="shopPhone"/>
                <el-table-column label="数量" prop="count" width="100"/>
                <el-table-column label="操作技术引流费用" prop="citationValueMoney" width="150"/>
                <el-table-column label="库存前" prop="beforeCount" width="100"/>
                <el-table-column label="库存后" prop="afterCount" width="100"/>
                <el-table-column label="操作人" prop="operatorName" width="120"/>
                <el-table-column label="时间" min-width="120" prop="createTime"/>
                <el-table-column label="备注" min-width="150" prop="remark" show-overflow-tooltip/>
                <el-table-column label="状态" prop="status" width="80">
                    <template #default="scope">
                        <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
                            {{ scope.row.status === '0' ? '成功' : '失败' }}
                        </el-tag>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页组件 -->
            <div class="pagination-container">
                <el-pagination
                    v-model:current-page="state.recordsDialog.pagination.currentPage"
                    v-model:page-size="state.recordsDialog.pagination.pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="state.recordsDialog.pagination.total"
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleRecordsSizeChange"
                    @current-change="handleRecordsCurrentChange"
                />
            </div>
        </el-dialog>
    </div>
</template>
<script setup>
import {useRouter} from 'vue-router';
import {onMounted, reactive, ref} from 'vue';
import {allShop} from '../../stores/allShop';
import {offlineCustomer} from '../../stores/offlineCustomer';
import {ElMessage, ElMessageBox} from 'element-plus';

const router = useRouter();
const searchFormRef = ref(null);
const rechargeFormRef = ref(null);

const state = reactive({
    buttonList: [],
    loading: false,
    tableData: [],
    searchForm: {
        socialCreditCodeOrPhone: '',
        businessName: '',
        jurisdiction: '',
        status: '',
        dateRange: null,
        beginCreateTime: null,
        endCreateTime: null,
        sortField: '',
        sortOrder: ''
    },
    pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
    },
    rechargeDialog: {
        visible: false,
        loading: false,
        customerName: '',
        form: {
            shopId: null,
            count: 1,
            operationPassword: '',
            remark: ''
        }
    },
    recordsDialog: {
        visible: false,
        loading: false,
        tableData: [],
        searchForm: {
            shopId: null,
            shopName: '',
            operatorId: null,
            operatorName: '',
            status: ''
        },
        pagination: {
            currentPage: 1,
            pageSize: 10,
            total: 0
        }
    }
});

const rechargeRules = {
    shopId: [
        {required: true, message: '请选择商家', trigger: 'blur'},
        {type: 'number', message: '商家ID必须为数字', trigger: 'blur'}
    ],
    count: [
        {required: true, message: '请输入数量', trigger: 'blur'},
        {type: 'number', message: '数量必须为数字', trigger: 'blur'},
    ],
    operationPassword: [
        {required: true, message: '请输入操作密码', trigger: 'blur'},
        {min: 6, message: '操作密码长度不能少于6个字符', trigger: 'blur'}
    ]
}

const getStatusText = (status) => {
    const texts = {
        '0': '正常',
        '1': '禁用',
        '2': '删除',
        '3': '失效',
        '4': '睡眠',
        '5': '无效',
        '6': '退单'
    }
    return texts[status] || ''
}
//禁用/启用
const disableEnableFun = async (row, status) => {
    try {
        await ElMessageBox.confirm(
            `确定要${status === 1 ? '禁用' : '启用'}该客户吗？`,
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        state.loading = true
        let form = {
            id: row.id,
            status: status
        }
        let result = await allShop().DisableEnableFun(form);
        if (result.code == 500) {
            ElMessage.error(result.msg)
        } else {
            ElMessage.success(`${status === 1 ? '禁用' : '启用'}成功`)
            getShopList()
        }
    } catch (error) {
        console.log('操作取消')
    } finally {
        state.loading = false
    }
}
const getShopList = async () => {
    state.loading = true
    try {
        let time1 = new Date(state.searchForm.beginCreateTime).getTime()
        let time2 = new Date(state.searchForm.endCreateTime).getTime()
        state.searchForm.beginCreateTime = time1
        state.searchForm.endCreateTime = time2
        // 处理分页参数
        const params = {
            ...state.searchForm,
            pageNum: state.pagination.currentPage,
            pageSize: state.pagination.pageSize,
            type: 1
        }

        let result = await allShop().GetShopList(params);
        console.log("result", result);
        state.tableData = result.rows
        state.pagination.total = result.total || 0
    } catch (error) {
        ElMessage.error('获取数据失败')
        console.error(error)
    } finally {
        state.loading = false
    }
}
const searchFun = async () => {
    let time1 = new Date(state.searchForm.beginCreateTime).getTime()
    let time2 = new Date(state.searchForm.endCreateTime).getTime()
    state.searchForm.beginCreateTime = time1
    state.searchForm.endCreateTime = time2
    state.pagination.currentPage = 1 // 重置到第一页
    getShopList()
}
const resetSearch = () => {
    searchFormRef.value?.resetFields()
    state.searchForm = {
        socialCreditCodeOrPhone: '',
        businessName: '',
        jurisdiction: '',
        status: '',
        dateRange: null,
        beginCreateTime: null,
        endCreateTime: null,
        sortField: '',
        sortOrder: ''
    }
    state.pagination.currentPage = 1
    getShopList()
}
const openRechargeDialog = (row) => {
    state.rechargeDialog.customerName = row.businessName || row.name || '';
    state.rechargeDialog.form.shopId = row.id;
    state.rechargeDialog.form.count = 1;
    state.rechargeDialog.form.operationPassword = '';
    state.rechargeDialog.form.remark = '';
    state.rechargeDialog.visible = true;
}
const submitRecharge = () => {
    rechargeFormRef.value?.validate(async (valid) => {
        if (valid) {
            try {
                state.rechargeDialog.loading = true;
                const res = await offlineCustomer().RechargeDrainage(state.rechargeDialog.form);

                if (res.code === 200) {
                    state.rechargeDialog.visible = false;
                    ElMessage.success(res.msg);
                    // 刷新列表
                    getShopList();
                }
            } catch (error) {
                ElMessage.error(error.message || '系统错误');
            } finally {
                state.rechargeDialog.loading = false;
            }
        }
    });
}
const handleSortChange = (column) => {
    if (column.prop && column.order) {
        state.searchForm.sortField = column.prop
        state.searchForm.sortOrder = column.order === 'ascending' ? 'asc' : 'desc'
    } else {
        state.searchForm.sortField = ''
        state.searchForm.sortOrder = ''
    }
    getShopList()
}
const handleCurrentChange = (currentPage) => {
    state.pagination.currentPage = currentPage
    getShopList()
}
const handleSizeChange = (pageSize) => {
    state.pagination.pageSize = pageSize
    getShopList()
}
// 格式化日期显示
const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
};

// 获取完整地址
const getFullAddress = (row) => {
    if (!row) return '';
    const address = [];
    if (row.province) address.push(row.province);
    if (row.city) address.push(row.city);
    if (row.district) address.push(row.district);
    if (row.town) address.push(row.town);
    if (row.address) address.push(row.address);
    return address.join(' ');
};

/**
 * 打开记录对话框
 */
const openRecordsDialog = () => {
    state.recordsDialog.visible = true;
    getDrainageRecords();
};

/**
 * 获取记录
 */
const getDrainageRecords = async () => {
    state.recordsDialog.loading = true;
    try {
        const params = {
            ...state.recordsDialog.searchForm,
            pageNum: state.recordsDialog.pagination.currentPage,
            pageSize: state.recordsDialog.pagination.pageSize
        };

        const res = await offlineCustomer().GetDrainageRecords(params);
        state.recordsDialog.tableData = res.rows || [];
        state.recordsDialog.pagination.total = res.total || 0;
    } catch (error) {
        ElMessage.error('获取记录失败');
        console.error(error);
    } finally {
        state.recordsDialog.loading = false;
    }
};

/**
 * 搜索记录
 */
const searchRecords = () => {
    state.recordsDialog.pagination.currentPage = 1;
    getDrainageRecords();
};

/**
 * 重置记录搜索条件
 */
const resetRecordsSearch = () => {
    state.recordsDialog.searchForm = {
        shopId: null,
        shopPhone: '',
        shopName: '',
        operatorId: null,
        operatorName: '',
        beginCreateTime: '',
        endCreateTime: '',
        status: ''
    };
    state.recordsDialog.pagination.currentPage = 1;
    getDrainageRecords();
};

/**
 * 处理记录分页大小变化
 */
const handleRecordsSizeChange = (pageSize) => {
    state.recordsDialog.pagination.pageSize = pageSize;
    getDrainageRecords();
};

/**
 * 处理记录分页当前页变化
 */
const handleRecordsCurrentChange = (currentPage) => {
    state.recordsDialog.pagination.currentPage = currentPage;
    getDrainageRecords();
};

onMounted(() => {
    getShopList()
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.rightBox {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.head {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
        font-size: 22px;
        font-weight: bold;
        color: #333;
    }
}

.main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.search-box {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.search-input {
    width: 200px;
    margin-right: 15px;
}

.image-container {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;
}

.image-item {
    width: 150px;
    margin: 10px;
    text-align: center;
}

.image-item p {
    margin-bottom: 5px;
}

.image-item :deep(.el-image) {
    width: 150px;
    height: 150px;
    border: 1px solid #eee;
}


.level-buttons {
    margin-right: 15px;
}

.date-picker {
    width: 180px;
}

.to-text {
    margin: 0 10px;
    color: #606266;
}

.search-btn {
    margin-left: 15px;
}

.table-info {
    margin-bottom: 15px;
    font-size: 14px;
    color: #909399;
}

.data-table {
    flex: 1;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.ip-info {
    margin-top: 5px;
    padding: 5px;
    background: #f5f7fa;
    border-radius: 4px;
    font-size: 12px;
    color: #606266;
}

.pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 10px 0;
}

:deep(.el-tag) {
    padding: 0 10px;
}

:deep(.el-descriptions) {
    margin-bottom: 20px;
}

:deep(.el-descriptions-item__label) {
    width: 120px;
    text-align: right;
}
.recharge-form{
    margin-bottom: 50px;
}
.recharge-form :deep(.el-form-item__label) {
    font-weight: 500;
}

.recharge-form :deep(.el-input-number) {
    width: 100%;
}
</style>
