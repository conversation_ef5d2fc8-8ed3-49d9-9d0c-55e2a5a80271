<template>
    <div class="container">
        <!-- 固定在左上角的标题 -->
        <div class="page-title">
            <span>广告支付</span>
        </div>

        <!-- 主要内容区域 -->
        <div v-loading="loading" class="main">
            <div class="payment-wrapper">
                <div class="payment-header">
                    <div class="header-title">广告费用支付</div>
                    <div class="header-desc">支付成功后，您的广告将提交平台审核</div>
                </div>

                <!-- 订单信息 -->
                <div class="order-info">
                    <div class="info-title">广告信息</div>

                    <div class="info-row">
                        <span class="label">广告ID：</span>
                        <span class="value">{{ adDetail.id }}</span>
                    </div>

                    <div class="info-row">
                        <span class="label">广告标题：</span>
                        <span class="value">{{ adDetail.title }}</span>
                    </div>

                    <div class="info-row">
                        <span class="label">广告有效期：</span>
                        <span class="value">{{ adDetail.startDate }} 至 {{ adDetail.endDate }}</span>
                    </div>

                    <div class="info-row">
                        <span class="label">广告图片：</span>
                        <div class="image-preview">
                            <el-image
                                :preview-src-list="[adDetail.image]"
                                :src="adDetail.image"
                                fit="cover"
                                style="width: 200px; height: 100px">
                                <template #error>
                                    <div class="image-error">
                                        <el-icon>
                                            <Picture/>
                                        </el-icon>
                                    </div>
                                </template>
                            </el-image>
                        </div>
                    </div>

                    <div class="info-row">
                        <span class="label">跳转链接：</span>
                        <span class="value link">{{ adDetail.website }}</span>
                    </div>

                    <div class="price-row">
                        <span class="label">应付金额：</span>
                        <span class="price">¥{{ adDetail.price }}</span>
                    </div>
                </div>

                <!-- 核销方式 -->
                <div class="payment-methods">
                    <div class="methods-title">选择核销方式</div>

                    <div class="methods-list">
                        <div
                            v-for="method in paymentMethods"
                            :key="method.id"
                            :class="['method-item', { active: selectedMethod === method.id }]"
                            @click="selectedMethod = method.id"
                        >
                            <div class="method-icon">
                                <img :alt="method.name" :src="method.icon">
                            </div>
                            <div class="method-name">{{ method.name }}</div>
                            <div class="method-check">
                                <el-icon v-if="selectedMethod === method.id">
                                    <Check/>
                                </el-icon>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="action-buttons">
                    <el-button class="cancel-btn" @click="goBack">取消</el-button>
                    <el-button
                        :disabled="!selectedMethod"
                        :loading="payLoading"
                        class="pay-btn"
                        type="primary"
                        @click="handlePayment"
                    >
                        立即支付
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import {onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Check, Picture} from '@element-plus/icons-vue'
import {useAdvertisementStore} from '../../stores/advertisement'

const route = useRoute()
const router = useRouter()
const advertisementStore = useAdvertisementStore()

// 加载状态
const loading = ref(false)
const payLoading = ref(false)

// 广告ID
const adId = ref('')

// 广告详情
const adDetail = ref({
    id: '',
    title: '',
    image: '',
    price: 0,
    website: '',
    startDate: '',
    endDate: '',
})

// 选中的核销方式
const selectedMethod = ref('')

// 核销方式列表
const paymentMethods = [
    {
        id: 'alipay',
        name: '支付宝支付',
        icon: 'https://img.alicdn.com/imgextra/i4/O1CN01XCiY1B1px9ivHEH2W_!!*************-2-tps-200-200.png'
    },
    {
        id: 'wechat',
        name: '微信支付',
        icon: 'https://res.wx.qq.com/a/wx_fed/assets/res/NTI4MWU5.png'
    },
    {
        id: 'bank',
        name: '银行卡支付',
        icon: 'https://img.alicdn.com/imgextra/i1/O1CN01KWVPkp1Q2yDEHi6Bq_!!*************-2-tps-200-200.png'
    }
]

// 获取广告详情
const getAdDetail = async () => {
    loading.value = true
    try {
        const data = await advertisementStore.getAdvertisementDetail(adId.value)
        if (data) {
            adDetail.value = data
        } else {
            goBack()
        }
    } finally {
        loading.value = false
    }
}

// 返回列表页
const goBack = () => {
    router.push('/ad')
}

// 处理支付
const handlePayment = async () => {
    if (!selectedMethod.value) {
        ElMessage.warning('请选择核销方式')
        return
    }

    payLoading.value = true
    try {
        // 创建广告支付订单
        const orderNo = await advertisementStore.createAdPayOrder(adId.value)

        if (orderNo) {
            // 显示支付二维码对话框
            ElMessageBox.alert(
                `<div class="qrcode-container">
           <p>请使用手机APP扫描二维码进行支付</p>
           <div class="qrcode">
             <img src="https://via.placeholder.com/200x200?text=支付二维码" alt="支付二维码">
           </div>
           <p class="order-no">订单号：${orderNo}</p>
         </div>`,
                '扫码支付',
                {
                    dangerouslyUseHTMLString: true,
                    showCancelButton: true,
                    confirmButtonText: '已完成支付',
                    cancelButtonText: '取消支付',
                    callback: async (action) => {
                        if (action === 'confirm') {
                            // 检查支付状态
                            const paySuccess = await advertisementStore.checkAdPayStatus(orderNo)
                            if (paySuccess) {
                                ElMessage.success('支付成功，广告状态已更新')
                                router.push('/ad')
                            } else {
                                ElMessage.warning('未检测到支付完成，请稍后再试')
                            }
                        } else {
                            // 取消支付
                            await advertisementStore.cancelOrder(orderNo)
                        }
                    }
                }
            )
        }
    } catch (error) {
        console.error('支付过程出错:', error)
        ElMessage.error('支付发生错误，请稍后再试')
    } finally {
        payLoading.value = false
    }
}

onMounted(() => {
    // 获取路由参数中的广告ID
    adId.value = route.query.id

    if (!adId.value) {
        ElMessage.error('广告ID不能为空')
        goBack()
        return
    }

    // 获取广告详情
    getAdDetail()
})
</script>

<style lang="scss" scoped>
.container {
    position: relative;
    min-height: 100vh;
    padding: 30px;
    background-color: #f5f7fa;
}

.page-title {
    position: absolute;
    top: 30px;
    left: 30px;

    span {
        font-size: 28px;
        font-weight: bold;
        color: #333;
    }
}

.main {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 100px);
    margin-top: 80px;
}

.payment-wrapper {
    width: 100%;
    max-width: 800px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.payment-header {
    background-color: #3A58CF;
    color: white;
    padding: 25px 30px;

    .header-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 8px;
    }

    .header-desc {
        font-size: 14px;
        opacity: 0.8;
    }
}

.order-info {
    padding: 30px;
    border-bottom: 1px solid #ebeef5;

    .info-title {
        font-size: 20px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 20px;
    }

    .info-row {
        display: flex;
        margin-bottom: 15px;
        align-items: flex-start;

        .label {
            width: 120px;
            color: #909399;
            flex-shrink: 0;
        }

        .value {
            color: #303133;
            flex: 1;
            word-break: break-all;

            &.link {
                color: #3A58CF;
                text-decoration: underline;
            }
        }

        .image-preview {
            margin-top: 5px;

            .image-error {
                width: 200px;
                height: 100px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #f5f7fa;
                color: #909399;
                font-size: 24px;
            }
        }
    }

    .price-row {
        display: flex;
        align-items: center;
        margin-top: 30px;
        border-top: 1px dashed #ebeef5;
        padding-top: 20px;

        .label {
            font-size: 16px;
            color: #303133;
            font-weight: bold;
        }

        .price {
            font-size: 24px;
            color: #f56c6c;
            font-weight: bold;
            margin-left: 10px;
        }
    }
}

.payment-methods {
    padding: 30px;

    .methods-title {
        font-size: 20px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 20px;
    }

    .methods-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 15px;

        .method-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 1px solid #ebeef5;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
                border-color: #3A58CF;
            }

            &.active {
                border-color: #3A58CF;
                background-color: #f0f5ff;
            }

            .method-icon {
                width: 40px;
                height: 40px;
                margin-right: 12px;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }

            .method-name {
                flex: 1;
                font-size: 16px;
            }

            .method-check {
                width: 20px;
                height: 20px;
                color: #3A58CF;
            }
        }
    }
}

.action-buttons {
    padding: 20px 30px;
    display: flex;
    justify-content: center;
    gap: 20px;
    border-top: 1px solid #ebeef5;

    .cancel-btn, .pay-btn {
        width: 150px;
        height: 50px;
        font-size: 18px;
        border-radius: 8px;
    }
}

@media (max-width: 768px) {
    .payment-wrapper {
        margin: 0 15px;
    }

    .order-info .info-row {
        flex-direction: column;

        .label {
            margin-bottom: 5px;
        }
    }

    .methods-list {
        grid-template-columns: 1fr !important;
    }
}
</style>
