<script setup>
import {useRouter} from 'vue-router'

const router = useRouter()
const handleButtonClick = (item) => {
    if (item === '商品列表') router.push('/productList')
    if (item === '发布商品') router.push('/commodity')
    if (item === '商品分类') router.push('/productCategory')
    if (item === '商品已审核') {
        router.push('/commodityAlreadyReview')
    }
    if (item === '品牌管理') router.push('/brandManage')
    if (item === '配送管理') router.push('./deliveryManage')
    if (item === '评论管理') router.push('./commentManage')
    if (item === '退货地址') router.push('./returnAddress')
    if (item === '商品链接') router.push('./productLink')
    if (item === '商品链接生成') router.push('./buildProductLink')
    if (item === '商品链接导入') router.push('./productLinkImport')
    if (item === '商品代销申请') router.push('./productSellApply')
}

const buttonList = [
    '发布商品', '商品列表', '商品分类', '商品已审核', '品牌管理', '配送管理',
    '评论管理', '退货地址', '商品链接', '商品链接生成',
    '商品链接导入', '商品代销申请'
]
</script>

<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item }}
            </el-button>
        </div>
        <div class="right-content">
            <div class="headBox">
                <span class="text">商品链接导入</span>
                <input class="input" placeholder="" type="text">
            </div>
            <div class="bottomBox">
                <span class="note">备注</span>
                <input class="input" placeholder="" type="text">
            </div>
            <button class="submit-btn">提交</button>
        </div>
    </div>
    <!--  </ManageBg>-->
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 600px;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.right-content {
    flex: 1;
    padding: 38px 45px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.headBox {
    width: 100%;
    height: 500px;
    border: 3px solid #3A58CF;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .text {
        font-size: 36px;
        color: #000;
    }

    .input {
        width: 864px;
        height: 406px;
        background-color: #D2E0FB;
        border: none;
        padding: 10px;
    }
}

.bottomBox {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .note {
        font-size: 36px;
        color: #000;
    }

    .input {
        width: 864px;
        height: 84px;
        background-color: #D2E0FB;
        border: none;
        padding: 10px;
    }
}

.submit-btn {
    width: 154px;
    height: 73px;
    background-color: #14097A;
    color: white;
    font-size: 24px;
    border: none;
    border-radius: 16px;
    margin: 0 auto;
    cursor: pointer;
    transition: background-color 0.3s;
}
</style>
