<template>
    <!--  <!--  <HomeBg>-->-->
    <div class="page-container">
        <!-- 顶部按钮 -->
        <div class="header-buttons">
            <el-button class="create-btn" @click="handleToRole">创建角色+</el-button>
        </div>

        <!-- 搜索区域 -->
        <div class="search-area">
            <el-input
                v-model="searchQuery"
                class="search-input"
                clearable
                placeholder="角色名称"
                @keyup.enter="handleSearch"
            />
            <el-button class="search-btn" type="primary" @click="handleSearch">搜索</el-button>
        </div>

        <!-- 表格区域 -->
        <div class="table-wrapper">
            <el-table
                :data="state.tableData"
                border
                default-expand-all
                row-key="id"
                style="width: 100%; margin-bottom: 20px"
            >
                <el-table-column label="角色名称" prop="roleName"/>
                <el-table-column label="备注" prop="roleKey"/>
                <el-table-column label="组件路径" prop="address"/>
                <el-table-column label="操作" width="180">
                    <template #default="scope">
                        <el-button link type="primary" @click="openUpdateDialog(scope.row)">修改</el-button>
                        <el-button link type="primary" @click="openPasswordDialog">新增</el-button>
                        <el-button link type="primary" @click="openPasswordDelete">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 固定在右下角的分页 -->
        <div class="fixed-pagination">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
            />
        </div>
    </div>
    <!--  </HomeBg>-->
</template>
<script lang="ts" setup>
import {ref} from 'vue'
import {useRouter} from 'vue-router'
import {platformRole} from '../../stores/platformRole'

const router = useRouter();
const handleToRole = () => {
    router.push({
        path: '/constructRole'
    })
}
const state = reactive({
    tableData: []
})
// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 搜索
const searchQuery = ref('')

// 搜索功能
const handleSearch = () => {
    console.log('搜索内容:', searchQuery.value)
    // 这里可以添加实际的搜索逻辑
}
// 修改角色
const openUpdateDialog = (row) => {
    console.log(row, 'row')
    router.push({
        path: '/updateRole',
        query: {
            roleId: row.roleId
        }
    });
}
// 创建树
const getTreeList = async () => {
    try {
        let result = await platformRole().GetRoleList();
        state.tableData = result.rows
    } finally {

    }
}
onMounted(() => {
    getTreeList()
})

</script>
<style lang="scss" scoped>
.page-container {
    width: 100%;
    max-height: 100vh;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;

    .header-buttons {
        display: flex;
        justify-content: flex-start;
        gap: 20px;
        margin-bottom: 20px;

        .create-btn {
            width: 200px;
            height: 50px;
            background-color: #3A58CF;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
    }

    .search-area {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;

        .search-input {
            flex: 1;

            :deep(.el-input__wrapper) {
                height: 50px;
                font-size: 16px;
            }
        }

        .search-btn {
            width: 100px;
            height: 50px;
            font-size: 16px;
        }
    }

    .table-wrapper {
        flex: 1;
        overflow: auto;
        margin-bottom: 60px; /* 为分页留出空间 */

        :deep(.el-table) {
            font-size: 14px;
        }

        :deep(.el-table__cell) {
            padding: 12px 0;
        }
    }

    .fixed-pagination {
        position: fixed;
        right: 20px;
        bottom: 20px;
        background: white;
        padding: 10px;
        border-radius: 4px;
        z-index: 10;
    }
}
</style>
