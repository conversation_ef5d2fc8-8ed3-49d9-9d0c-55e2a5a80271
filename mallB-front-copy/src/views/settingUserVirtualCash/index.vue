<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div v-loading="settingUserVirtualCash.loading" class="main">
            <!-- 商家搜索部分 -->
            <div class="search-section">
                <el-form :inline="true" class="search-form">
                    <el-form-item label="电话">
                        <el-input v-model="settingUserVirtualCash.phone" placeholder="请输入电话号码"></el-input>
                    </el-form-item>
                    <el-form-item label="用户类型">
                        <el-select v-model="settingUserVirtualCash.searchType" placeholder="请选择用户类型"
                                   style="width: 150px">
                            <el-option label="用户" value="1"></el-option>
                            <el-option label="商家" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="searchMerchant">搜索</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 商家信息展示 -->
            <div v-if="settingUserVirtualCash.userInfo" class="merchant-info">
                <h3>用户信息</h3>
                <el-descriptions :column="3" border>
                    <el-descriptions-item label="名称">{{ settingUserVirtualCash.userInfo.name }}</el-descriptions-item>
                    <el-descriptions-item label="联系电话">{{
                            settingUserVirtualCash.userInfo.phone
                        }}
                    </el-descriptions-item>
                    <el-descriptions-item label="当前余额">¥{{
                            settingUserVirtualCash.userInfo.balance
                        }}
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 冻结货款表单 -->
                <div class="freeze-section">
                    <el-form ref="freezeFormRef" :model="freezeForm" label-width="120px">
                        <el-form-item :rules="[{ required: true, message: '请输入扣除余额' }]" label="扣除余额"
                                      prop="amount">
                            <el-input v-model.number="freezeForm.amount" placeholder="请输入要扣除的余额"></el-input>
                        </el-form-item>
                        <el-form-item :rules="[{ required: true, message: '请输入扣除原因' }]" label="扣除原因"
                                      prop="reason">
                            <el-input v-model="freezeForm.reason" placeholder="请输入扣除原因"
                                      type="textarea"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="freezeAmount">扣除</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <!-- 冻结记录表格 -->
            <div class="freeze-record-section">
                <h3>货款冻结记录</h3>

                <!-- 冻结记录搜索表单 -->
                <div class="record-search-section">
                    <el-form :inline="true" :model="recordSearchForm" class="record-search-form">
                        <el-form-item label="商家名称">
                            <el-input v-model="recordSearchForm.userName" clearable
                                      placeholder="请输入商家名称"></el-input>
                        </el-form-item>
                        <el-form-item label="商家电话">
                            <el-input v-model="recordSearchForm.phone" clearable
                                      placeholder="请输入商家电话"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="searchRecords">搜索</el-button>
                            <el-button @click="resetSearch">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <el-table v-loading="settingUserVirtualCash.loading" :data="settingUserVirtualCash.freezeRecords" border
                          style="width: 100%">
                    <el-table-column label="记录ID" prop="id"></el-table-column>
                    <el-table-column label="商家名称" prop="userName"></el-table-column>
                    <el-table-column label="电话号码" prop="phone"></el-table-column>
                    <el-table-column label="扣除金额" prop="freezeAmount">
                        <template #default="scope">¥{{ scope.row.amount }}</template>
                    </el-table-column>
                    <el-table-column label="扣除原因" prop="reason"></el-table-column>
                    <el-table-column label="扣除时间" prop="createTime" width="180"></el-table-column>
                </el-table>

                <!-- 分页组件 -->
                <div class="pagination-section">
                    <el-pagination
                        v-model:current-page="settingUserVirtualCash.pagination.pageNum"
                        v-model:page-size="settingUserVirtualCash.pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="settingUserVirtualCash.pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import {useRouter} from 'vue-router'
import {Session} from '../../utils/storage'
import {nextTick, onMounted, reactive, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {settingUserVirtualCashStore} from '../../stores/settingUserVirtualCash'

const state = reactive({
    buttonList: []
})

// 冻结表单
const freezeForm = reactive({
    amount: '',
    reason: ''
})

// 冻结记录搜索表单
const recordSearchForm = reactive({
    shopName: '',
    shopPhone: '',
    status: ''
})

const freezeFormRef = ref(null)

const router = useRouter()
const settingUserVirtualCash = settingUserVirtualCashStore()

const handleButtonClick = (item) => {
    router.push(item.component)
}

// 搜索商家
const searchMerchant = async () => {
    if (!settingUserVirtualCash.phone) {
        ElMessage.warning('请输入电话号码')
        return
    }
    const data = {
        phone: settingUserVirtualCash.phone,
        type: settingUserVirtualCash.searchType
    }

    await settingUserVirtualCash.searchMerchantByPhone(data)
}

// 冻结货款
const freezeAmount = () => {
    if (!settingUserVirtualCash.userInfo) {
        ElMessage.warning('请先搜索')
        return
    }

    freezeFormRef.value.validate(async (valid) => {
        if (valid) {
            // 调用后端API冻结货款
            ElMessageBox.confirm(
                `确定要冻结该商家的 ${freezeForm.amount} 元货款吗？`,
                '确认冻结',
                {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }
            ).then(async () => {
                // 调用store方法冻结货款
                const success = await settingUserVirtualCash.freezeAmount({
                    userId: settingUserVirtualCash.userInfo.id,
                    amount: freezeForm.amount,
                    reason: freezeForm.reason,
                    type: settingUserVirtualCash.searchType
                })

                if (success) {
                    searchMerchant()
                    // 重置表单
                    freezeForm.amount = ''
                    freezeForm.reason = ''
                }
            }).catch(() => {
                // 用户取消操作
            })
        }
    })
}

// 搜索冻结记录
const searchRecords = async () => {
    // 设置搜索条件
    settingUserVirtualCash.setSearchParams(recordSearchForm)
    // 重置到第一页
    settingUserVirtualCash.setPagination(1)
    // 执行搜索
    await settingUserVirtualCash.getFreezeRecords()
}

// 重置搜索
const resetSearch = async () => {
    // 重置搜索表单
    recordSearchForm.shopName = ''
    recordSearchForm.shopPhone = ''
    recordSearchForm.status = ''

    // 重置store中的搜索条件
    settingUserVirtualCash.resetSearchParams()

    // 重新加载数据
    await settingUserVirtualCash.getFreezeRecords()
}

// 分页大小改变
const handleSizeChange = async (newSize) => {
    settingUserVirtualCash.setPagination(settingUserVirtualCash.pagination.pageNum, newSize)
    await settingUserVirtualCash.getFreezeRecords()
}

// 当前页改变
const handleCurrentChange = async (newPage) => {
    settingUserVirtualCash.setPagination(newPage)
    await settingUserVirtualCash.getFreezeRecords()
}

onMounted(() => {
    // 初始化加载冻结记录
    settingUserVirtualCash.getFreezeRecords()

    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.main {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-section {
    padding: 20px;
    background-color: #f5f7fa;
    border-radius: 8px;
    margin-bottom: 20px;

    :deep(.el-form-item) {
        margin-bottom: 0;
    }

    :deep(.el-form-item__label) {
        font-size: 16px;
        color: #333;
    }

    :deep(.el-input__inner) {
        height: 40px;
        font-size: 14px;
    }

    :deep(.el-button) {
        height: 40px;
        padding: 0 30px;
    }
}

.merchant-info {
    padding: 20px;
    background-color: #f0f5ff;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 5px solid #3A58CF;

    h3 {
        margin-top: 0;
        color: #3A58CF;
        font-size: 20px;
        margin-bottom: 15px;
    }

    :deep(.el-descriptions__label) {
        font-weight: bold;
        width: 120px !important;
    }

    :deep(.el-descriptions__cell) {
        font-size: 16px;
    }
}

.freeze-section {
    margin-top: 10px;
    padding-top: 20px;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #ebeef5;

    h3 {
        margin-top: 0;
        color: #3A58CF;
        font-size: 18px;
    }

    :deep(.el-form-item) {
        margin-bottom: 20px;
    }

    :deep(.el-form-item__label) {
        font-size: 14px;
        color: #333;
    }

    :deep(.el-textarea__inner) {
        min-height: 80px;
    }

    :deep(.el-button) {
        padding: 12px 30px;
        font-size: 16px;
    }
}

.freeze-record-section {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #ebeef5;

    h3 {
        margin-top: 0;
        color: #3A58CF;
        font-size: 20px;
        margin-bottom: 20px;
    }
}

.record-search-section {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;

    .record-search-form {
        :deep(.el-form-item) {
            margin-bottom: 0;
            margin-right: 20px;
        }

        :deep(.el-form-item__label) {
            font-size: 14px;
            color: #333;
        }

        :deep(.el-input) {
            width: 200px;
        }

        :deep(.el-select) {
            width: 150px;
        }

        :deep(.el-button) {
            height: 32px;
            padding: 0 20px;
        }
    }
}

.pagination-section {
    margin-top: 20px;
    text-align: right;

    :deep(.el-pagination) {
        justify-content: right;
    }
}

// 响应式优化
@media (max-width: 1200px) {
    .container {
        flex-direction: column;
        height: auto;
    }

    .left-buttons {
        width: 100%;
        height: auto;
        margin: 10px;
        display: flex;
        flex-wrap: wrap;

        :deep(.el-button.data-button) {
            width: calc(50% - 10px);
            margin: 5px;
            height: 50px;
            font-size: 16px;
        }
    }

    .main {
        margin: 10px;
    }
}

@media (max-width: 768px) {
    .main {
        padding: 15px;
    }

    .search-section,
    .merchant-info,
    .freeze-section,
    .freeze-record-section {
        padding: 15px;
    }

    .left-buttons :deep(.el-button.data-button) {
        width: 100%;
        font-size: 18px;
    }

    :deep(.el-descriptions) {
        display: block;
    }

    :deep(.el-descriptions__body) {
        overflow-x: auto;
    }
}
</style>
