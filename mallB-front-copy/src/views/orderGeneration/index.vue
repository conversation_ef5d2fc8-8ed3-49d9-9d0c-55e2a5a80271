<template>
    <div class="order-generation-container">
        <!-- 订单生成 -->
        <div class="content-wrapper">
            <div class="page-header">
                <h2 class="page-title">
                    功能订单
                </h2>
            </div>

            <!-- 搜索表单 -->
            <div class="search-form-container">
                <el-form :inline="true" :model="searchForm" class="search-form">
                    <el-form-item label="费用类型">
                        <el-input v-model="searchForm.productName" clearable placeholder="请输入费用类型"/>
                    </el-form-item>
                    <el-form-item label="电话号码">
                        <el-input v-model="searchForm.phone" clearable placeholder="请输入电话号码"/>
                    </el-form-item>
                    <el-form-item label="公司名称">
                        <el-input v-model="searchForm.userName" clearable placeholder="请输入公司名称"/>
                    </el-form-item>
                    <el-form-item label="订单编码">
                        <el-input v-model="searchForm.orderNo" clearable placeholder="请输入订单编码"/>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select v-model="searchForm.status" class="status-select" clearable placeholder="请选择状态">
                            <el-option label="待支付" value="0"/>
                            <el-option label="支付中" value="1"/>
                            <el-option label="成功" value="2"/>
                            <el-option label="已取消" value="3"/>
                            <el-option label="已过期" value="4"/>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="时间">
                        <el-date-picker
                            v-model="searchForm.startTime"
                            class="date-picker"
                            placeholder="开始时间"
                            type="datetime"
                            value-format="YYYY-MM-DD HH:mm:ss"
                        />
                        <span style="margin: 0 10px;color: #606266;">至</span>
                        <el-date-picker
                            v-model="searchForm.endTime"
                            class="date-picker"
                            placeholder="结束时间"
                            type="datetime"
                            value-format="YYYY-MM-DD HH:mm:ss"
                        />
                    </el-form-item>
                    <el-form-item class="search-buttons">
                        <el-button :loading="loading" type="primary" @click="handleSearch">搜索</el-button>
                        <el-button @click="resetSearch">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 订单表格 -->
            <div class="table-container">
                <el-table
                    v-loading="loading"
                    :data="state.tableData1"
                    border
                    class="order-table"
                    highlight-current-row
                    stripe
                    style="width: 100%"
                >
                    <el-table-column label="费用类型" min-width="120" prop="productName"></el-table-column>
                    <el-table-column label="电话号码" min-width="120" prop="phone"></el-table-column>
                    <el-table-column label="名称" min-width="120" prop="userName"></el-table-column>
                    <el-table-column label="金额" min-width="100" prop="amount">
                        <template #default="scope">
                            <span class="amount">¥{{ scope.row.amount - scope.row.serviceFee }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="第三方支付手续费" min-width="100" prop="serviceFee">
                        <template #default="scope">
                            <span class="amount">¥{{ scope.row.serviceFee }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="订单编码" min-width="180" prop="orderNo"></el-table-column>
                    <el-table-column align="center" label="核销方式" prop="status" width="140">
                        <template #default="scope">
                            <el-tag class="status-tag" effect="light" type="success">
                                <span v-if="scope.row.payType=='1'">微信</span>
                                <span v-if="scope.row.payType=='2' || scope.row.payType=='4'">支付宝</span>
                                <span v-if="scope.row.payType=='3'">补贴金</span>
                                <span v-if="scope.row.payType=='5'">平台促销金</span>
                                <span v-else>-</span>
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="状态" prop="status" width="100">
                        <template #default="scope">
                            <el-tag :type="getStatusType(scope.row.status)" class="status-tag" effect="light">
                                <span v-if="scope.row.status=='0'">待支付</span>
                                <span v-if="scope.row.status=='1'">支付中</span>
                                <span v-if="scope.row.status=='2'">成功</span>
                                <span v-if="scope.row.status=='3'">已取消</span>
                                <span v-if="scope.row.status=='4'">已过期</span>
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="时间" min-width="100" prop="updateTime"></el-table-column>
                    <el-table-column label="备注" min-width="150" prop="remark"></el-table-column>
                    <el-table-column align="center" fixed="right" label="操作" width="180">
                        <template #default="scope">
                            <div class="action-buttons">
                                <el-button
                                    v-if="scope.row.status=='0' && (userType == 2 || userType == 3)"
                                    class="action-button"
                                    size="small"
                                    type="primary"
                                    @click="handleClick(scope.row)"
                                >去支付
                                </el-button>
                                <el-button
                                    v-if="scope.row.status=='0' && (userType == 2 || userType == 3)"
                                    class="action-button"
                                    size="small"
                                    type="warning"
                                    @click="cancelOrder(scope.row)"
                                >取消订单
                                </el-button>
                                <el-button
                                    v-if="(scope.row.status=='3' || scope.row.status=='2') && (userType == 2 || userType == 3)"
                                    class="action-button"
                                    size="small"
                                    type="danger"
                                    @click="deleteOrder(scope.row)"
                                >删除订单
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页组件 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.pageNum"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        background
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>

        <!-- 虚拟订单支付对话框 -->
        <PaymentDialog
            v-model:visible="paymentDialogVisible"
            :orderData="currentOrder"
            @cancel-success="handlePaymentSuccess"
        />
    </div>
</template>
<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {onMounted, reactive, ref} from 'vue'
import {orderGeneration} from '../../stores/orderGeneration'
import PaymentDialog from '../../components/PaymentDialog.vue'
import {Session} from "@/utils/storage";

const state = reactive({
    tableData1: [],
    buttonList: [],
    orderNo: ""
})

// 添加加载状态
const loading = ref(false)
// 用户类型 (1:平台管理员, 2:商家)
const userType = ref(Session.get('userType'))

// 分页参数
const pagination = reactive({
    pageNum: 1,
    pageSize: 10,
    total: 0
})

// 搜索表单
const searchForm = reactive({
    startTime: '',
    endTime: '',
    phone: "",
    userName: '',
    productName: '',
    orderNo: '',
    status: ''
})

//新增分类数据弹窗
const dialogAddCategory = ref(false)
const router = useRouter()
const handleButtonClick = (item) => {
    router.push(item.component)
}

// 支付相关
const paymentDialogVisible = ref(false)
const currentOrder = ref({})

// 处理支付按钮点击
const handleClick = (row) => {
    currentOrder.value = row
    paymentDialogVisible.value = true
}

// 处理支付成功
const handlePaymentSuccess = () => {
    getList() // 刷新订单列表
}

// 分页处理
const handleSizeChange = (size) => {
    pagination.pageSize = size
    getList()
}

const handleCurrentChange = (page) => {
    pagination.pageNum = page
    getList()
}

//取消订单
const cancelOrder = async (row) => {
    loading.value = true
    try {
        let result = await orderGeneration().GetcancelTheOrder(row.orderNo);
        getList()
    } catch (error) {
        console.error('取消订单失败:', error)
    } finally {
        loading.value = false
    }
}
const deleteOrder = async (row) => {
    loading.value = true
    try {
        let result = await orderGeneration().DeleteOrder(row.orderNo);
        await getList()
    } catch (error) {
        console.error('取消订单失败:', error)
    } finally {
        loading.value = false
    }
}

// 处理搜索
const handleSearch = () => {
    pagination.pageNum = 1
    getList()
}

// 重置搜索
const resetSearch = () => {
    searchForm.startTime = ''
    searchForm.endTime = ''
    searchForm.phone = ""
    searchForm.userName = ''
    searchForm.productName = ''
    searchForm.orderNo = ''
    searchForm.status = ''
    pagination.pageNum = 1
    getList()
}

// 获取状态对应的类型
const getStatusType = (status: string) => {
    switch (status) {
        case '0':
            return 'warning'  // 待支付
        case '1':
            return 'primary'  // 支付中
        case '2':
            return 'success'  // 已支付
        case '3':
            return 'info'     // 已取消
        case '4':
            return 'danger'   // 已过期
        default:
            return 'info'
    }
}

const getList = async () => {
    loading.value = true
    try {
        let data = {
            pageNum: pagination.pageNum,
            pageSize: pagination.pageSize,
            startTime: searchForm.startTime || undefined,
            endTime: searchForm.endTime || undefined,
            phone: searchForm.phone || undefined,
            userName: searchForm.userName || undefined,
            productName: searchForm.productName || undefined,
            orderNo: searchForm.orderNo || undefined,
            status: searchForm.status || undefined
        }
        let result: any = await orderGeneration().GetLoginVirtualOrder(data);
        state.tableData1 = result.data.rows
        pagination.total = result.data.total
    } catch (error) {
        console.error('获取订单列表失败:', error)
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    getList()
    // nextTick(() => {
    //     let menuList = Session.getMenu2()
    //     let menuId = Session.get('homeMenuId');
    //     if(menuId == null){
    //       for (let index = 0; index < menuList.length; index++) {
    //           const element = menuList[index];
    //           if(element.openType == 2){
    //             if(menuId == null){
    //                 menuId = element.menuId
    //             }
    //           }
    //       }
    //     }
    //     for(let index = 0; index < menuList.length; index++) {
    //         const element = menuList[index];
    //         if(element.menuId == menuId){
    //             state.buttonList = element.children
    //         }
    //     }
    // })
})
</script>
<style lang="scss" scoped>
.order-generation-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f5f7fa;
    padding: 20px;
    box-sizing: border-box;
}

.content-wrapper {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.page-header {
    margin-bottom: 24px;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 16px;
}

.page-title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin: 0;
}

.search-form-container {
    margin-bottom: 24px;
    background-color: #f9fafc;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #ebeef5;
}

.search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    :deep(.el-form-item) {
        margin-bottom: 10px;
        margin-right: 16px;
    }

    :deep(.el-input__wrapper) {
        width: 220px;
    }
}

.status-select {
    width: 220px;
}

.search-buttons {
    margin-left: auto;

    :deep(.el-button) {
        margin-left: 8px;
    }
}

.table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.order-table {
    flex: 1;

    :deep(.el-table__header) {
        th {
            background-color: #f5f7fa;
            color: #606266;
            font-weight: 600;
            height: 50px;
        }
    }

    :deep(.el-table__row) {
        height: 55px;
    }
}

.amount {
    font-weight: 600;
    color: #f56c6c;
}

.status-tag {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
}

.action-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
}

.action-button {
    min-width: 80px;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .search-form {
        :deep(.el-form-item) {
            margin-right: 0;
            width: 100%;
        }

        :deep(.el-input__wrapper) {
            width: 100%;
        }
    }

    .status-select {
        width: 100%;
    }

    .search-buttons {
        margin-left: 0;
        width: 100%;
        display: flex;
        justify-content: flex-end;
    }
}
</style>
