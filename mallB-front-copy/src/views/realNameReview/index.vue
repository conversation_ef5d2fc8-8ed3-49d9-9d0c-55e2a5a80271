<script setup>
import {useRouter} from 'vue-router'
import {ref} from 'vue'
import {Search} from '@element-plus/icons-vue'

const router = useRouter()
const handleButtonClick = (item) => {
    if (item === '系统通知') router.push('/customerServer')
    if (item === '申请通知') router.push('/404')
    if (item === '商品通知') router.push('/productNote')
    if (item === '订单通知') router.push('/ordersNote')
    if (item === '结算通知') router.push('/settlementNote')
    if (item === '退款通知') router.push('/refundNote')
    if (item === '商家入驻通知') router.push('/businessSettledNote')
    if (item === '实名申请') router.push('/realNameReview')
    if (item === '账号注销') router.push('/logout')
    if (item === '商品审核') router.push('/commodityReview')
}

const buttonList = [
    '全部消息', '系统通知', '申请通知', '商品通知', '订单通知',
    '结算通知', '退款通知', '商家入驻通知', '实名申请', '账号注销', '商品审核'
]

// 表单数据
const form = ref({
    startTime: '',
    endTime: '',
    status: ''
})

// 驳回理由对话框
const rejectDialogVisible = ref(false)
const currentRejectItem = ref(null)
const rejectReason = ref('')

// 表格数据
const tableData = ref([
    {
        id: '1001',
        companyCode: 'COMP2023001',
        businessLicense: '营业执照A',
        applyTime: '2023-11-01 14:30',
        status: '待审核',
        processStatus: '未处理'
    },
    {
        id: '1002',
        companyCode: 'COMP2023002',
        businessLicense: '营业执照B',
        applyTime: '2023-11-02 09:15',
        status: '待审核',
        processStatus: '未处理'
    },
    {
        id: '1003',
        companyCode: 'COMP2023003',
        businessLicense: '营业执照C',
        applyTime: '2023-11-03 16:45',
        status: '待审核',
        processStatus: '未处理'
    }
])

const handleSearch = () => {
    console.log('搜索条件:', form.value)
    // 这里应该调用API获取筛选后的数据
}

// 通过审核
const approveItem = (row) => {
    row.status = '已通过'
    row.processStatus = '已处理'
    ElMessage.success('审核已通过')
}

// 打开驳回对话框
const openRejectDialog = (row) => {
    currentRejectItem.value = row
    rejectReason.value = ''
    rejectDialogVisible.value = true
}

// 提交驳回
const submitReject = () => {
    if (!rejectReason.value) {
        ElMessage.warning('请输入驳回理由')
        return
    }

    currentRejectItem.value.status = '已驳回'
    currentRejectItem.value.processStatus = '已处理'
    currentRejectItem.value.rejectReason = rejectReason.value

    ElMessage.success('已驳回申请')
    rejectDialogVisible.value = false
}
</script>

<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item }}
            </el-button>
        </div>
        <div class="rightBox">
            <div class="head">
                <el-form :model="form" class="filter-form">
                    <!-- 第一行：时间范围 -->
                    <div class="form-row">
                        <el-form-item label="开始时间">
                            <el-date-picker
                                v-model="form.startTime"
                                placeholder="选择开始时间"
                                type="datetime"
                            />
                        </el-form-item>
                        <span class="time-separator">至</span>
                        <el-form-item label="结束时间">
                            <el-date-picker
                                v-model="form.endTime"
                                placeholder="选择结束时间"
                                type="datetime"
                            />
                        </el-form-item>
                    </div>

                    <!-- 第二行：状态筛选 -->
                    <div class="form-row">
                        <el-form-item label="状态">
                            <el-select v-model="form.status" placeholder="请选择状态">
                                <el-option label="全部" value=""></el-option>
                                <el-option label="待审核" value="pending"></el-option>
                                <el-option label="已通过" value="approved"></el-option>
                                <el-option label="已驳回" value="rejected"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-button
                            :icon="Search"
                            type="primary"
                            @click="handleSearch"
                        >
                            搜索
                        </el-button>
                    </div>
                </el-form>
            </div>

            <div class="main">
                <el-table :data="tableData" border style="width: 100%">
                    <el-table-column label="手机号" prop="companyCode" width="150"/>
                    <el-table-column label="身份证号" prop="businessLicense" width="300"/>
                    <el-table-column label="姓名" prop="applyTime" width="220"/>
                    <el-table-column label="注册时间" prop="processStatus" width="250">
                        <template #default="{ row }">
                            <el-tag :type="row.processStatus === '已处理' ? 'success' : 'warning'">
                                {{ row.processStatus }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="审核状态" prop="status" width="150">
                        <template #default="{ row }">
                            <el-tag
                                :type="row.status === '已通过' ? 'success' : row.status === '已驳回' ? 'danger' : 'info'">
                                {{ row.status }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200">
                        <template #default="{ row }">
                            <el-button
                                :disabled="row.processStatus === '已处理'"
                                size="small"
                                type="success"
                                @click="approveItem(row)"
                            >
                                通过
                            </el-button>
                            <el-button
                                :disabled="row.processStatus === '已处理'"
                                size="small"
                                type="danger"
                                @click="openRejectDialog(row)"
                            >
                                驳回
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>

    <!-- 驳回理由对话框 -->
    <el-dialog v-model="rejectDialogVisible" title="输入驳回理由" width="500px">
        <el-input
            v-model="rejectReason"
            :rows="4"
            placeholder="请输入驳回理由"
            type="textarea"
        />
        <template #footer>
            <el-button @click="rejectDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitReject">确定</el-button>
        </template>
    </el-dialog>
    <!--  </ManageBg>-->
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.rightBox {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .head {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid #3A58CF;

        .filter-form {
            .form-row {
                display: flex;
                align-items: center;
                margin-bottom: 20px;

                .el-form-item {
                    margin-right: 20px;
                    margin-bottom: 0;
                }

                .time-separator {
                    margin: 0 10px;
                    color: #666;
                }
            }
        }
    }

    .main {
        flex: 1;
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        overflow: hidden;

        :deep(.el-table) {
            height: 100%;

            th {
                background-color: #f5f7fa;
                font-weight: bold;
            }

            .el-table__cell {
                padding: 12px 0;
            }
        }
    }
}

.el-button + .el-button {
    margin-left: 10px;
}
</style>
