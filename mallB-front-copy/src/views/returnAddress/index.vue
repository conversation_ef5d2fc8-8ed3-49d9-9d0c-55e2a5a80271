<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="content-area">
            <!-- <div class="page-header">
                <div class="page-title">
                    <h2>退货地址管理</h2>
            </div>
            </div> -->

            <div v-loading="loading" class="main">
                <div class="table-container">
                    <div class="table-header">
                        <div class="table-title">退货地址列表</div>
                        <div class="header-right">
                            <div v-if="addressList.length > 0" class="address-count">
                                共 <span class="count-number">{{ pagination.total }}</span> 条记录
                            </div>
                            <el-button :icon="Plus" type="primary" @click="openAddressDialog()">添加退货地址</el-button>
                        </div>
                    </div>

                    <!-- 搜索区域 -->
                    <div class="search-area">
                        <div class="search-row">
                            <div class="search-item">
                                <span class="label">联系人</span>
                                <el-input v-model="searchForm.receiverName" class="input" clearable
                                          placeholder="请输入联系人姓名"></el-input>
                            </div>
                            <div class="search-item">
                                <span class="label">联系电话</span>
                                <el-input v-model="searchForm.receiverPhone" class="input" clearable
                                          placeholder="请输入联系电话"></el-input>
                            </div>
                            <el-select v-model="searchForm.isDefault" class="status-select" clearable
                                       placeholder="地址类型">
                                <el-option label="全部地址" value=""></el-option>
                                <el-option label="默认地址" value="1"></el-option>
                                <el-option label="普通地址" value="0"></el-option>
                            </el-select>
                        </div>
                        <div class="search-button">
                            <el-button :icon="Search" type="primary" @click="handleSearch">搜索</el-button>
                            <el-button :icon="RefreshRight" @click="handleReset">重置</el-button>
                        </div>
                    </div>

                    <el-table :data="addressList" border highlight-current-row stripe style="width: 100%">
                        <el-table-column align="center" label="#" type="index" width="60"/>
                        <el-table-column align="center" label="联系人" prop="receiverName" width="120"/>
                        <el-table-column align="center" label="联系电话" prop="receiverPhone" width="140"/>
                        <el-table-column label="地址" min-width="300">
                            <template #default="scope">
                                {{ getFullAddress(scope.row) }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="默认地址" width="120">
                            <template #default="scope">
                                <el-tag v-if="scope.row.isDefault === '1'" effect="dark" type="success">默认</el-tag>
                                <el-tag v-else effect="plain" type="info">普通</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column align="left" label="操作" width="240">
                            <template #default="scope">
                                <el-button
                                    size="small"
                                    type="primary"
                                    @click="openAddressDialog(scope.row)"
                                >编辑
                                </el-button>
                                <el-button
                                    size="small"
                                    type="danger"
                                    @click="handleDelete(scope.row.id)"
                                >删除
                                </el-button>
                                <el-button
                                    v-if="scope.row.isDefault !== '1'"
                                    size="small"
                                    type="success"
                                    @click="handleSetDefault(scope.row.id)"
                                >设为默认
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 分页组件 -->
                    <div v-if="addressList.length > 0" class="pagination-container">
                        <el-pagination
                            v-model:current-page="pagination.pageNum"
                            v-model:page-size="pagination.pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            :total="pagination.total"
                            background
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        ></el-pagination>
                    </div>

                    <div v-if="addressList.length === 0" class="empty-block">
                        <el-empty :image-size="120" description="暂无退货地址">
                            <template #description>
                                <p>您还没有添加任何退货地址</p>
                            </template>
                        </el-empty>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 退货地址弹窗 -->
    <el-dialog
        v-model="addressDialogVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :title="isEdit ? '编辑退货地址' : '添加退货地址'"
        destroy-on-close
        width="800px"
    >
        <div v-loading="formLoading" class="form-container">
            <el-form
                ref="addressFormRef"
                :model="addressForm"
                :rules="rules"
                class="address-form"
                label-width="100px">

                <el-form-item label="联系人" prop="receiverName">
                    <el-input v-model="addressForm.receiverName" maxlength="20" placeholder="请输入联系人姓名"
                              show-word-limit></el-input>
                </el-form-item>

                <el-form-item label="联系电话" prop="receiverPhone">
                    <el-input v-model="addressForm.receiverPhone" maxlength="11"
                              placeholder="请输入联系电话"></el-input>
                </el-form-item>

                <el-form-item label="所在地区" prop="area" required>
                    <div class="area-selects">
                        <!-- 省份选择 -->
                        <el-select
                            v-model="provinceSelected"
                            class="address-select"
                            filterable
                            placeholder="请选择省份"
                            @change="handleProvinceChange">
                            <el-option
                                v-for="item in provinceOptions"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>

                        <!-- 城市选择 -->
                        <el-select
                            v-model="citySelected"
                            :disabled="!provinceSelected"
                            class="address-select"
                            filterable
                            placeholder="请选择城市"
                            @change="handleCityChange">
                            <el-option
                                v-for="item in cityOptions"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>

                        <!-- 区县选择 -->
                        <el-select
                            v-model="districtSelected"
                            :disabled="!citySelected"
                            class="address-select"
                            filterable
                            placeholder="请选择区/县"
                            @change="handleDistrictChange">
                            <el-option
                                v-for="item in districtOptions"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>

                        <!-- 街道选择 -->
                        <el-select
                            v-model="townSelected"
                            :disabled="!districtSelected"
                            class="address-select"
                            filterable
                            placeholder="请选择街道"
                            @change="handleTownChange">
                            <el-option
                                v-for="item in townOptions"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code">
                            </el-option>
                        </el-select>
                    </div>
                </el-form-item>

                <el-form-item label="详细地址" prop="detailAddress">
                    <el-input
                        v-model="addressForm.detailAddress"
                        maxlength="200"
                        placeholder="请输入详细地址信息，如道路、门牌号、小区、楼栋号、单元等"
                        rows="3"
                        show-word-limit
                        type="textarea"></el-input>
                </el-form-item>

                <el-form-item label="设为默认" prop="isDefault">
                    <el-switch v-model="addressForm.isDefault" active-value="1" inactive-value="0"></el-switch>
                    <span class="form-tip">设为默认后，将作为首选退货地址</span>
                </el-form-item>
            </el-form>
        </div>
        <template #footer>
                <span class="dialog-footer">
                    <el-button @click="addressDialogVisible = false">取消</el-button>
                    <el-button :loading="formLoading" type="primary" @click="submitForm">保存</el-button>
                </span>
        </template>
    </el-dialog>
</template>
<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {computed, nextTick, onMounted, reactive, ref} from 'vue'
import {Plus, RefreshRight, Search} from '@element-plus/icons-vue'
import {Session} from '../../utils/storage'
import {ElMessage, ElMessageBox} from 'element-plus'
import {useReturnAddressStore} from '../../stores/returnAddress'
import {useAreaStore} from '../../stores/area'

const router = useRouter()
const returnAddressStore = useReturnAddressStore()
const areaStore = useAreaStore()

const state = reactive({
    buttonList: [] as any[]
})

// 搜索表单
const searchForm = reactive({
    receiverName: '',
    receiverPhone: '',
    isDefault: ''
})

// 获取地址列表
const addressList = computed(() => returnAddressStore.addressList)

// 分页信息
const pagination = computed(() => returnAddressStore.pagination)

// 加载状态
const loading = computed(() => returnAddressStore.loading)

// 左侧菜单点击
const handleButtonClick = (item) => {
    router.push(item.component)
}

// 搜索
const handleSearch = async () => {
    returnAddressStore.setSearchParams({
        receiverName: searchForm.receiverName,
        receiverPhone: searchForm.receiverPhone,
        isDefault: searchForm.isDefault
    })
    pagination.value.pageNum = 1
    await returnAddressStore.getAddressList()
}

// 重置搜索
const handleReset = () => {
    Object.assign(searchForm, {
        receiverName: '',
        receiverPhone: '',
        isDefault: ''
    })
    returnAddressStore.resetSearchParams()
    returnAddressStore.getAddressList()
}

// 分页大小变更
const handleSizeChange = (size) => {
    pagination.value.pageSize = size
    returnAddressStore.getAddressList()
}

// 页码变更
const handleCurrentChange = (page) => {
    pagination.value.pageNum = page
    returnAddressStore.getAddressList()
}

// 获取完整地址
const getFullAddress = (address) => {
    if (!address) return ''

    let fullAddress = ''
    if (address.provinceName) fullAddress += address.provinceName
    if (address.cityName) fullAddress += ' ' + address.cityName
    if (address.districtName) fullAddress += ' ' + address.districtName
    if (address.detailAddress) fullAddress += ' ' + address.detailAddress

    return fullAddress
}

// 表单数据
const addressForm = reactive({
    id: null,
    receiverName: '',
    receiverPhone: '',
    provinceCode: '',
    provinceName: '',
    cityCode: '',
    cityName: '',
    districtCode: '',
    districtName: '',
    townCode: '',
    townName: '',
    detailAddress: '',
    postCode: '',
    isDefault: '0',
    status: '0',
    addressRemark: ''
})

// 表单验证规则
const rules = {
    receiverName: [
        {required: true, message: '请输入联系人姓名', trigger: 'blur'},
        {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
    ],
    receiverPhone: [
        {required: true, message: '请输入联系电话', trigger: 'blur'},
        {pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur'}
    ],
    detailAddress: [
        {required: true, message: '请输入详细地址', trigger: 'blur'},
        {min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur'}
    ]
}

// 地址选择器相关
const provinceOptions = ref<any[]>([])
const cityOptions = ref<any[]>([])
const districtOptions = ref<any[]>([])
const townOptions = ref<any[]>([])
const provinceSelected = ref('')
const citySelected = ref('')
const districtSelected = ref('')
const townSelected = ref('')

// 弹窗相关
const addressDialogVisible = ref(false)
const formLoading = ref(false)
const addressFormRef = ref(null)
const isEdit = ref(false)

// 打开地址弹窗
const openAddressDialog = async (address?) => {
    resetForm()

    if (address && address.id) {
        isEdit.value = true
        await getAddressDetail(address.id)
    } else {
        isEdit.value = false
    }

    // 加载省份数据
    await loadProvinceData()

    addressDialogVisible.value = true
}

// 重置表单
const resetForm = () => {
    // 重置表单数据
    Object.assign(addressForm, {
        id: null,
        receiverName: '',
        receiverPhone: '',
        provinceCode: '',
        provinceName: '',
        cityCode: '',
        cityName: '',
        districtCode: '',
        districtName: '',
        townCode: '',
        townName: '',
        detailAddress: '',
        postCode: '',
        isDefault: '0',
        status: '0',
        addressRemark: ''
    })

    // 重置选择器
    provinceSelected.value = ''
    citySelected.value = ''
    districtSelected.value = ''
    townSelected.value = ''

    // 清空选项
    provinceOptions.value = []
    cityOptions.value = []
    districtOptions.value = []
    townOptions.value = []
}

// 加载省份数据
const loadProvinceData = async () => {
    try {
        const provinces = await areaStore.getProvinces()
        provinceOptions.value = provinces || []
    } catch (error) {
        ElMessage.error('加载省份数据失败')
    }
}

// 加载城市数据
const loadCityData = async (provinceCode) => {
    if (!provinceCode) return
    try {
        const cities = await areaStore.getCities(provinceCode)
        cityOptions.value = cities || []
    } catch (error) {
        ElMessage.error('加载城市数据失败')
    }
}

// 加载区县数据
const loadDistrictData = async (cityCode) => {
    if (!cityCode) return
    try {
        const districts = await areaStore.getDistricts(cityCode)
        districtOptions.value = districts || []
    } catch (error) {
        ElMessage.error('加载区县数据失败')
    }
}

// 加载街道数据
const loadTownData = async (districtCode) => {
    if (!districtCode) return
    try {
        const towns = await areaStore.getTowns(districtCode)
        townOptions.value = towns || []
    } catch (error) {
        ElMessage.error('加载街道数据失败')
    }
}

// 省份变更处理
const handleProvinceChange = async (provinceCode) => {
    // 清空城市、区县和街道
    citySelected.value = ''
    districtSelected.value = ''
    townSelected.value = ''
    cityOptions.value = []
    districtOptions.value = []
    townOptions.value = []

    if (provinceCode) {
        // 查找选中的省份名称
        const selectedProvince = provinceOptions.value.find(p => Number(p.code) === Number(provinceCode))

        // 更新省份代码和名称
        addressForm.provinceCode = provinceCode
        addressForm.provinceName = selectedProvince ? selectedProvince.name : ''

        // 清空城市、区县和街道代码及名称
        addressForm.cityCode = ''
        addressForm.cityName = ''
        addressForm.districtCode = ''
        addressForm.districtName = ''
        addressForm.townCode = ''
        addressForm.townName = ''

        // 加载城市数据
        await loadCityData(provinceCode)
    }
}

// 城市变更处理
const handleCityChange = async (cityCode) => {
    // 清空区县和街道
    districtSelected.value = ''
    townSelected.value = ''
    districtOptions.value = []
    townOptions.value = []

    if (cityCode) {
        // 查找选中的城市名称
        const selectedCity = cityOptions.value.find(c => Number(c.code) === Number(cityCode))

        // 更新城市代码和名称
        addressForm.cityCode = cityCode
        addressForm.cityName = selectedCity ? selectedCity.name : ''

        // 清空区县和街道代码及名称
        addressForm.districtCode = ''
        addressForm.districtName = ''
        addressForm.townCode = ''
        addressForm.townName = ''

        // 加载区县数据
        await loadDistrictData(cityCode)
    }
}

// 区县变更处理
const handleDistrictChange = async (districtCode) => {
    // 清空街道
    townSelected.value = ''
    townOptions.value = []

    if (districtCode) {
        // 查找选中的区县名称
        const selectedDistrict = districtOptions.value.find(d => Number(d.code) === Number(districtCode))

        // 更新区县代码和名称
        addressForm.districtCode = districtCode
        addressForm.districtName = selectedDistrict ? selectedDistrict.name : ''
        await loadTownData(districtCode)
    }
}

// 街道变更处理
const handleTownChange = async (townCode) => {
    if (townCode) {
        // 查找选中的街道名称
        const selectedTown = townOptions.value.find(t => Number(t.code) === Number(townCode))
        console.log("selectedTown", selectedTown)

        // 更新街道代码和名称
        addressForm.townCode = townCode
        addressForm.townName = selectedTown ? selectedTown.name : ''
    }
}

// 获取地址详情
const getAddressDetail = async (id) => {
    formLoading.value = true
    try {
        const data = await returnAddressStore.getAddressDetail(id)
        if (data) {
            // 填充表单数据
            Object.assign(addressForm, data)

            // 设置选择器的值
            provinceSelected.value = data.provinceCode

            // 加载并设置城市数据
            await loadCityData(data.provinceCode)
            citySelected.value = data.cityCode

            // 加载并设置区县数据
            await loadDistrictData(data.cityCode)
            districtSelected.value = data.districtCode

            // 加载并设置街道数据
            await loadTownData(data.districtCode)
            townSelected.value = data.townCode
        }
    } finally {
        formLoading.value = false
    }
}

// 设置默认地址
const handleSetDefault = async (id) => {
    ElMessageBox.confirm('确认将此地址设为默认退货地址？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
    }).then(async () => {
        const success = await returnAddressStore.setDefaultAddress(id)
        if (success) {
            returnAddressStore.getAddressList()
        }
    }).catch(() => {
    })
}

// 删除地址
const handleDelete = (id) => {
    ElMessageBox.confirm('确认删除此退货地址？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const success = await returnAddressStore.deleteAddress(id)
        if (success) {
            returnAddressStore.getAddressList()
        }
    }).catch(() => {
    })
}

// 提交表单
const submitForm = async () => {
    if (!addressForm.provinceCode || !addressForm.cityCode || !addressForm.districtCode) {
        ElMessage.warning('请选择完整的地区信息')
        return
    }

    formLoading.value = true
    try {
        let success
        if (isEdit.value) {
            success = await returnAddressStore.updateAddress(addressForm)
        } else {
            success = await returnAddressStore.addAddress(addressForm)
        }

        if (success) {
            addressDialogVisible.value = false
            returnAddressStore.getAddressList()
        }
    } finally {
        formLoading.value = false
    }
}

onMounted(() => {
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId')
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index]
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index]
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })

    // 加载退货地址列表
    returnAddressStore.getAddressList()
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 30px);
    overflow-y: scroll;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.content-area {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    margin-left: 260px;
    width: calc(100% - 280px);
}

.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;

    .page-title {
        h2 {
            font-size: 24px;
            margin: 0 0 8px 0;
            color: #303133;
        }
    }
}

.main {
    flex: 1;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.table-container {
    width: 100%;
    height: 100%;

    .table-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .table-title {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
        position: relative;
        padding-left: 12px;

        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background-color: #3A58CF;
            border-radius: 2px;
        }
    }

    .header-right {
        display: flex;
        align-items: center;
        gap: 15px;

        .el-button {
            padding: 10px 20px;
            font-weight: 500;
        }
    }

    .address-count {
        font-size: 14px;
        color: #606266;

        .count-number {
            font-weight: bold;
            color: #3A58CF;
        }
    }
}

.search-area {
    margin-bottom: 20px;
    padding: 18px;
    background-color: #f5f7fa;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .search-row {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }

    .search-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        margin-bottom: 10px;
    }

    .label {
        margin-right: 10px;
        font-size: 14px;
        white-space: nowrap;
        color: #606266;
    }

    .input {
        width: 180px;
    }

    .status-select {
        width: 150px;
    }

    .search-button {
        display: flex;
        gap: 12px;
    }
}

.pagination-container {
    margin-top: 25px;
    display: flex;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
}

.empty-block {
    margin-top: 60px;
    text-align: center;
    padding: 40px 0;

    p {
        color: #909399;
        margin: 8px 0 20px;
    }

    .el-button {
        margin-top: 15px;
        padding: 12px 25px;
    }
}

:deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 10px;

    th {
        background-color: #f5f7fa !important;
        color: #333;
        font-weight: bold;
        height: 50px;
    }

    .el-button {
        margin-right: 8px;

        &:last-child {
            margin-right: 0;
        }
    }

    .el-table__row:hover {
        background-color: #f0f5ff !important;
    }
}

.form-container {
    padding: 10px;
}

.address-form {
    max-width: 100%;

    :deep(.el-form-item__label) {
        font-weight: 500;
    }

    .form-tip {
        margin-left: 10px;
        font-size: 12px;
        color: #909399;
    }
}

.area-selects {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.address-select {
    width: 130px;
}

:deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;

    .el-dialog__header {
        background-color: #f5f7fa;
        margin: 0;
        padding: 15px 20px;
        border-bottom: 1px solid #ebeef5;
    }

    .el-dialog__title {
        font-weight: bold;
        color: #303133;
    }

    .el-dialog__body {
        padding: 25px 20px;
    }

    .el-dialog__footer {
        border-top: 1px solid #ebeef5;
        padding: 15px 20px;
    }
}

@media (max-width: 768px) {
    .area-selects {
        flex-direction: column;
    }

    .address-select {
        width: 100%;
    }
}
</style>
