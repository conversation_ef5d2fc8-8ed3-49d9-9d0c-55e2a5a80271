<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="right-content">
            <!-- 顶部标题和全局开关 -->

            <!-- 主体内容 -->
            <div class="main-content">
                <!-- 扫码注册行 -->
                <div class="setting-row">
                    <span class="setting-label">扫码注册关系链自动生成</span>
                    <el-radio-group v-model="form.scanStatus" class="toggle-group">
                        <el-radio class="square-radio" label="open">
                            <span class="toggle-label">开启</span>
                        </el-radio>
                        <el-radio class="square-radio" label="close">
                            <span class="toggle-label">关闭</span>
                        </el-radio>
                    </el-radio-group>
                </div>

                <!-- 邀请码行 -->
                <div class="setting-row">
                    <span class="setting-label">输入邀请码生成关系链</span>
                    <el-radio-group v-model="form.inviteCodeStatus" class="toggle-group">
                        <el-radio class="square-radio" label="open">
                            <span class="toggle-label">开启</span>
                        </el-radio>
                        <el-radio class="square-radio" label="close">
                            <span class="toggle-label">关闭</span>
                        </el-radio>
                    </el-radio-group>
                </div>
                <div class="setting-row">
                    <el-button type="primary" @click="setRelation">
                        保存
                    </el-button>
                </div>
            </div>
            <!-- 表格区域 -->
            <div class="table-section">
                <div class="table-header">
                    <el-form :inline="true" class="search-form">
                        <el-form-item label="电话号码">
                            <el-input v-model="searchForm.phone" clearable placeholder="请输入电话号码"/>
                        </el-form-item>
                        <el-form-item label="邀请人">
                            <el-input v-model="searchForm.inviter" clearable placeholder="请输入邀请人"/>
                        </el-form-item>
                        <el-form-item label="权限级别">
                            <el-button-group class="level-buttons">
                                <el-button
                                    :type="searchForm.jurisdiction === '' ? 'primary' : ''"
                                    @click="searchForm.jurisdiction = ''"
                                >
                                    全部
                                </el-button>
                                <el-button
                                    :type="searchForm.jurisdiction === '1' ? 'primary' : ''"
                                    @click="searchForm.jurisdiction = '1'"
                                >
                                    权限一
                                </el-button>
                                <el-button
                                    :type="searchForm.jurisdiction === '2' ? 'primary' : ''"
                                    @click="searchForm.jurisdiction = '2'"
                                >
                                    权限二
                                </el-button>
                                <el-button
                                    :type="searchForm.jurisdiction === '3' ? 'primary' : ''"
                                    @click="searchForm.jurisdiction = '3'"
                                >
                                    权限三
                                </el-button>
                            </el-button-group>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="handleSearch">搜索</el-button>
                            <el-button @click="resetSearch">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                <el-table
                    v-loading="loading"
                    :data="tableData"
                    border
                    style="width: 100%"
                    table-layout="fixed"
                >
                    <el-table-column align="center" fixed label="电话号码" prop="phone" width="120"/>
                    <el-table-column align="center" label="邀请人" prop="parentNusername" width="120"/>
                    <el-table-column align="center" label="状态" width="80">
                        <template #default="scope">
                            <el-tag :type="getStatusType(scope.row.status)">
                                {{ getStatusText(scope.row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="注册时间" width="150">
                        <template #default="scope">
                            {{ formatDate(scope.row.createTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="权限" width="100">
                        <template #default="scope">
                            {{ getPermissionText(scope.row.jurisdiction) }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="名字" prop="username" width="120"/>
                    <el-table-column align="center" label="粉丝量" prop="fans" width="80"/>
                    <el-table-column align="center" label="达标状态" width="100">
                        <template #default="scope">
                            <el-tag v-if="scope.row.flag != null" :type="getFlagType(scope.row.flag)">
                                {{ getFlagText(scope.row.flag) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="上限额度" prop="deductionMoneyLimit" width="100"/>
                    <el-table-column align="center" label="地址" min-width="180" show-overflow-tooltip>
                        <template #default="scope">
                            {{ getFullAddress(scope.row) }}
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="定位" prop="loginAddress" width="120"/>
                    <el-table-column align="center" fixed="right" label="操作" width="150">
                        <template #default="scope">
                            <el-button
                                v-if="scope.row.status == '1'"
                                size="small"
                                type="primary"
                                @click="handleChangeStatus(scope.row, '0')"
                            >
                                启用
                            </el-button>
                            <el-button
                                v-if="scope.row.status == '0' || scope.row.status == '4'"
                                size="small"
                                type="danger"
                                @click="handleChangeStatus(scope.row, '1')"
                            >
                                禁用
                            </el-button>
<!--                            <el-button-->
<!--                                v-if="scope.row.status == '0'"-->
<!--                                size="small"-->
<!--                                type="danger"-->
<!--                                @click="handleChangeStatus(scope.row, '1')"-->
<!--                            >-->
<!--                                正常-->
<!--                            </el-button>-->

                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.currentPage"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import {onMounted, reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {Session} from '../../utils/storage'
import {ElMessage, ElMessageBox} from 'element-plus'
import {
    changeUserStatus,
    getRelationChainList,
    getRelationChainSettings,
    updateRelationChainSettings
} from '../../api/relationshipChain'

const router = useRouter()
const state = reactive({
    buttonList: []
})
const handleButtonClick = (item) => {
    router.push(item.component)
}
const form = ref({
    scanStatus: 'open',  // 扫码注册状态
    inviteCodeStatus: 'open', // 邀请码状态
})

// 表格相关
const loading = ref(false)
const tableData = ref([])
const searchForm = reactive({
    jurisdiction: '',
    phone: '',
    inviter: '',
    status: ''
})

// 分页
const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
})

// 获取关系链数据
const getRelationChainData = async () => {
    try {
        loading.value = true
        // 使用API服务获取数据
        const response = await getRelationChainList({
            pageNum: pagination.currentPage,
            pageSize: pagination.pageSize,
            phone: searchForm.phone || undefined,
            inviter: searchForm.inviter || undefined,
            status: searchForm.status || undefined,
            jurisdiction: searchForm.jurisdiction || undefined
        })
        console.log("response", response)
        if (response.code === 200) {
            tableData.value = response.rows || []
            pagination.total = response.total || 0
        }
    } catch (error) {
        console.error('获取关系链数据出错:', error)
        ElMessage.error('获取关系链数据出错')
    } finally {
        loading.value = false
    }
}

// 搜索
const handleSearch = () => {
    pagination.currentPage = 1
    getRelationChainData()
}

// 重置搜索
const resetSearch = () => {
    searchForm.id = ''
    searchForm.phone = ''
    searchForm.inviter = ''
    searchForm.status = ''
    searchForm.jurisdiction = ''
    pagination.currentPage = 1
    getRelationChainData()
}

// 分页处理
const handleSizeChange = (size) => {
    pagination.pageSize = size
    pagination.currentPage = 1
    getRelationChainData()
}

const handleCurrentChange = (page) => {
    pagination.currentPage = page
    getRelationChainData()
}

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    }).replace(/\//g, '-')
}

// 获取状态类型和文本
const getStatusType = (status) => {
    const types = {
        '0': 'success',
        '1': 'danger',
        '2': 'info',
        '3': 'warning',
        '4': 'warning',
        '5': 'danger'
    }
    return types[status] || 'info'
}

const getStatusText = (status) => {
    const texts = {
        '0': '正常',
        '1': '禁用',
        '2': '删除',
        '3': '失效',
        '4': '睡眠',
        '5': '无效',
        '6': '退单'
    }
    return texts[status] || ''
}

// 获取权限文本
const getPermissionText = (jurisdiction) => {
    if (!jurisdiction) return ''
    const permissions = {
        '1': '权限一',
        '2': '权限二',
        '3': '权限三'
    }
    return permissions[jurisdiction] || '未知权限'
}

// 获取达标状态
const getFlagType = (flag) => {
    const types = {
        0: 'danger',
        1: 'success',
        2: 'warning'
    }
    return types[flag] || 'info'
}

const getFlagText = (flag) => {
    const texts = {
        0: '未达标',
        1: '达标1',
        2: '达标2'
    }
    return texts[flag] || null
}

// 获取完整地址
const getFullAddress = (row) => {
    const parts = []
    if (row.province) parts.push(row.province)
    if (row.city) parts.push(row.city)
    if (row.district) parts.push(row.district)
    if (row.town) parts.push(row.town)
    if (row.address) parts.push(row.address)
    return parts.join(' ') || '-'
}

// 修改用户状态
const handleChangeStatus = (row, status) => {
    const statusText = status === '0' ? '启用' : '禁用'
    ElMessageBox.confirm(`确定要${statusText}该用户吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            ``
            // 使用API服务修改用户状态
            const response = await changeUserStatus({
                userId: row.userId,
                status: status
            })

            if (response.code === 200) {
                ElMessage.success(`${statusText}成功`)
                // 重新加载数据
                getRelationChainData()
            }
        } catch (error) {
            console.error(`${statusText}用户失败:`, error)
            ElMessage.error(`${statusText}失败`)
        }
    }).catch(() => {
        // 取消操作
    })
}

const getRelation = async () => {
    const res = await getRelationChainSettings()
    console.log("res", res)
    form.value = res.data
}

const setRelation = async () => {
    let data = {"configValue": form.value}
    const res = await updateRelationChainSettings(data)
    if (res.code === 200) {
        ElMessage.success('保存成功')
        getRelation()
    }
}

onMounted(() => {
    let menuList = Session.getMenu()
    let menuId = Session.get('adminMenuId');
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index];
        if (element.menuId == menuId) {
            state.buttonList = element.children
        }
    }

    // 获取关系链数据
    getRelationChainData()
    // 扫码注册关系链自动生成
    getRelation()
})
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    width: 100%;
    height: 100vh;
    box-sizing: border-box;
    overflow: hidden; /* 防止容器溢出 */
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;

    flex-shrink: 0;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.search {
    margin-top: 20px;
}

.right-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止右侧内容溢出 */
}

.header-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.fixed-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-right: 30px;
}

.global-toggle {
    display: flex;
    align-items: center;
}

.divider {
    height: 1px;
    background-color: #ebeef5;
    margin: 0 auto 20px;
    width: 90%;
}

.main-content {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
}

.setting-row {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;

    &:last-child {
        margin-bottom: 0;
    }
}

.setting-label {
    width: 200px;
    font-size: 18px;
    color: #333;
    text-align: right;
    margin-right: 20px;
}

.toggle-group {
    display: flex;
    align-items: center;
}

/* 方形单选框样式 */
:deep(.square-radio) {
    .el-radio__inner {
        border-radius: 4px;
        width: 16px;
        height: 16px;

        &::after {
            width: 8px;
            height: 8px;
            border-radius: 2px;
        }
    }
}

.toggle-label {
    margin-left: 8px;
    font-size: 16px;
}

.search-form {
    display: flex;
    align-items: center;

    .id-input {
        margin-top: 20px;
        width: 200px;
        margin-right: 10px;
    }
}

.table-section {
    margin-top: 20px;
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止内容溢出 */
    max-width: 100%; /* 确保不超过父容器宽度 */
    min-height: 0; /* 确保flex子元素可以正确收缩 */
}

.table-header {
    margin-bottom: 10px;
}

/* 表格样式调整 */
:deep(.el-table) {
    min-height: 0; /* 确保表格可以正确收缩 */

    /* 设置表格容器宽度和滚动 */
    .el-table__body-wrapper {
        overflow-x: auto !important; /* 强制横向滚动 */
    }

    .el-table__header-wrapper th {
        background-color: #f5f7fa;
        color: #333;
        font-weight: bold;
    }

    .el-button {
        padding: 8px 15px;
    }

    .el-button--danger {
        background-color: #f56c6c;
        border: none;

        &:hover {
            background-color: #e65d5d;
        }
    }
}

.pagination-container {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
}
</style>
