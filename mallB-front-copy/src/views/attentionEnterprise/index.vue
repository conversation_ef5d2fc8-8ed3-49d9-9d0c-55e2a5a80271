<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="main">
            <div class="page-header">
                <h2 class="page-title">关注企业管理</h2>
            </div>
            <div class="content-container">
                <!-- 搜索表单 -->
                <div class="search-form-container">
                    <el-form :inline="true" :model="searchForm" class="search-form">
                        <el-form-item label="店铺名称">
                            <el-input v-model="searchForm.shopName" clearable placeholder="请输入店铺名称"/>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="searchForm.status" clearable placeholder="请选择状态"
                                       style="width: 120px">
                                <el-option label="关注中" value="0"/>
                                <el-option label="已取消" value="1"/>
                            </el-select>
                        </el-form-item>
                        <el-form-item class="search-buttons">
                            <el-button :loading="loading" type="primary" @click="getList">搜索</el-button>
                            <el-button @click="resetSearch">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 表格 -->
                <el-table
                    v-loading="loading"
                    :data="tableData"
                    border
                    stripe
                    style="width: 100%"
                >
                    <el-table-column label="店铺名称" prop="shopName"/>
                    <el-table-column label="店铺Logo" prop="shopLogo">
                        <template #default="scope">
                            <el-image
                                v-if="scope.row.shopLogo"
                                :src="getImageUrl(scope.row.shopLogo)"
                                fit="cover"
                                style="width: 50px; height: 50px; border-radius: 4px;"
                            />
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="关注时间" prop="createTime">
                        <template #default="scope">
                            {{ formatDate(scope.row.createTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" prop="status">
                        <template #default="scope">
                            <el-tag :type="scope.row.status === '0' ? 'success' : 'info'">
                                {{ scope.row.status === '0' ? '关注中' : '已取消' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column fixed="right" label="操作">
                        <template #default="scope">
                            <el-button
                                v-if="scope.row.status === '0'"
                                :loading="scope.row.cancelling"
                                size="small"
                                type="danger"
                                @click="handleCancelFocus(scope.row)"
                            >
                                取消关注
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.current"
                        v-model:page-size="pagination.size"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import {useRouter} from 'vue-router'
import {Session} from '../../utils/storage'
import {nextTick, onMounted, reactive, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {getImageUrl} from "@/utils/common.js";
import {cancelUserFocus, getUserFocusPage} from "@/api/shopFocus"

const router = useRouter()

// 响应式数据
const state = reactive({
    buttonList: []
})

const tableData = ref([])
const loading = ref(false)
const pagination = reactive({
    current: 1,
    size: 10,
    total: 0
})


// 搜索表单
const searchForm = reactive({
    shopName: '',
    status: ''
})

// 重置搜索
const resetSearch = () => {
    searchForm.shopName = ''
    searchForm.status = ''
    pagination.pageNum = 1
    getList()
}

// 左侧菜单点击
const handleButtonClick = (item) => {
    router.push(item.component)
}

// 获取列表数据
const getList = async () => {
    loading.value = true
    try {
        let data = {
            ...searchForm,
            pageNum: pagination.current,
            pageSize: pagination.size,
        }
        const res = await getUserFocusPage(data)
        if (res.code === 200) {
            tableData.value = res.rows || []
            pagination.total = res.total || 0
        } else {
            ElMessage.error(res.msg || '获取数据失败')
        }
    } catch (error) {
        console.error('获取关注列表失败:', error)
        ElMessage.error('获取数据失败')
    } finally {
        loading.value = false
    }
}

// 分页大小改变
const handleSizeChange = (size) => {
    pagination.size = size
    pagination.current = 1
    getList()
}

// 当前页改变
const handleCurrentChange = (current) => {
    pagination.current = current
    getList()
}

// 取消关注
const handleCancelFocus = async (row) => {
    try {
        await ElMessageBox.confirm(
            `确定要取消用户对店铺"${row.shopName}"的关注吗？`,
            '确认取消关注',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        // 设置单行loading状态
        row.cancelling = true

        const res = await cancelUserFocus(row.id)
        if (res.code === 200) {
            ElMessage.success('取消关注成功')
            // 刷新列表
            getList()
        }
    } catch (error) {
        if (error !== 'cancel') {
            console.error('取消关注失败:', error)
            ElMessage.error('取消关注失败')
        }
    } finally {
        row.cancelling = false
    }
}

// 格式化日期
const formatDate = (dateStr) => {
    if (!dateStr) return '-'
    return new Date(dateStr).toLocaleString('zh-CN')
}

// 组件挂载
onMounted(() => {
    nextTick(() => {
        // 初始化左侧菜单
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }

        // 获取列表数据
        getList()
    })
})

</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.middle-container {
    border: 1px solid red;
    flex: 1;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    height: 2000px;
}

.header {
    display: flex;
    align-items: center;
    flex: 1;
    height: 61px;
    padding: 0;
    background-color: #83A4EB;
    margin-left: 235px;

    .putaway {
        width: 172px;
        height: 31px;
        margin-left: 167px;
        font-size: 24px;
        color: #000;
    }

    .soldOut {
        width: 182px;
        height: 33px;
        margin-left: 277px;
        margin-right: 277px;
        font-size: 24px;
        color: #000;
    }

    .allProduct {
        width: 224px;
        height: 42px;
        margin-right: 122px;
        font-size: 24px;
        color: #000;
    }
}

.main {
    flex: 1;
    padding: 20px;
}

.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;

    .page-title {
        font-size: 22px;
        font-weight: 600;
        color: #303133;
        margin: 0;
    }

    .header-actions {
        display: flex;
        gap: 10px;
    }
}

.content-container {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.pagination-container {
    display: flex;
    justify-content: right;
    margin-top: 20px;
}

.text-gray {
    color: #909399;
}

.filter-row {
    display: flex;
    align-items: center;
    margin-left: 93px;
    gap: 20px;
}

.product-selector {
    width: 229px;
    height: 52px;
}

.commBox {
    background-color: #CCCCCC;
    width: 281px;
    height: 42px;
    display: flex;
    align-items: center;
    font-size: 24px;
    margin-top: -10px;

    .Text {
        margin-left: 13px;

        &:first-child {
            min-width: 120px;
        }
    }

    input {
        flex: 1;
        border: none;
        background: transparent;
        outline: none;
        font-size: 24px;
    }
}

.filter-row1 {
    display: flex;
    align-items: center;
    margin-left: 40px;
    gap: 20px;
}

.product-selector1 {
    width: 196px;
    height: 52px;
}

.brandBox {
    background-color: #3A58CF;
    width: 145px;
    height: 42px;
    margin-top: -10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;

    .brand {
        font-size: 24px;
        color: #fff;
    }
}

.priceBox {
    margin-left: 113px;
    margin-top: 25px;
    display: flex;
    align-items: center; /* 添加这行确保垂直居中 */

    .pirceRange {
        font-size: 30px;
        line-height: 1; /* 确保文本行高一致 */
    }

    .leftPrice {
        width: 277px;
        height: 32px;
        margin-left: 10px;
        background-color: #ccc;
        vertical-align: middle; /* 添加垂直对齐 */
    }

    .to {
        margin-left: 10px;
        line-height: 1; /* 确保文本行高一致 */
    }

    .rightPrice {
        width: 277px;
        height: 32px;
        margin-left: 10px;
        background-color: #ccc;
        vertical-align: middle; /* 添加垂直对齐 */
    }

    .nullBtn {
        width: 277px;
        height: 32px;
        margin-left: 105px;
        background-color: #fff;
        border: 3px solid #3A58CF;
        vertical-align: middle; /* 添加垂直对齐 */
        line-height: 1; /* 重置按钮行高 */
        padding: 0; /* 移除默认内边距 */
    }
}

.product-type-row {
    display: flex;
    align-items: center;
    margin-left: 113px;
    margin-top: 25px;
    gap: 20px;

    .product-type-label {
        font-size: 30px;
        min-width: 120px;
    }

    .checkbox-container {
        display: flex;
        align-items: center;
        gap: 30px;
        margin-left: 129px;

        :deep(.el-checkbox.square-checkbox) {
            .el-checkbox__inner {
                border-radius: 4px; /* 方形复选框 */
                width: 20px;
                height: 20px;

                &::after {
                    top: 2px;
                    left: 6px;
                }
            }

            .el-checkbox__label {
                font-size: 24px;
                color: #333;
            }
        }
    }

    .export-btn {
        margin-left: 430px;
        width: 120px;
        height: 42px;
        font-size: 20px;
        background-color: #FF8D1A;
        border: none;
        color: #000;

    }
}

.allButton {
    margin-top: 32px;
    margin-left: 303px;

    .button {
        width: 247px;
        height: 59px;
        margin-right: 41px;
        border-radius: 30px;
        border: 2px solid #3A58CF;
        font-size: 30px;

        color: #3A58CF;
    }
}

.table-container {
    margin-top: 20px;
    padding: 0 20px;

    :deep(.custom-header) {
        th {
            background-color: #83A4EB !important;
            color: #000;
            font-weight: bold;
            font-size: 16px;

            .el-checkbox {
                .el-checkbox__inner {
                    border-radius: 4px;
                    width: 16px;
                    height: 16px;
                }

                .el-checkbox__label {
                    font-size: 16px;
                    color: #000;
                }
            }
        }
    }

    :deep(.custom-row) {
        td {
            background-color: #D2E0FB;

            .el-checkbox {
                .el-checkbox__inner {
                    border-radius: 4px;
                    width: 16px;
                    height: 16px;
                }

                .el-checkbox__label {
                    font-size: 14px;
                }
            }
        }

        &:hover td {
            background-color: #b8cdf9 !important;
        }
    }

    :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;

        .el-table__cell {
            padding: 12px 0;
        }
    }
}
</style>
