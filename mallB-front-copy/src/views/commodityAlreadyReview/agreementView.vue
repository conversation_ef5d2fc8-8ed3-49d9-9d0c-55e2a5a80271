<template>
    <div class="agreement-view-container">
        <div class="agreement-header">
            <h1 class="agreement-title">{{ agreementTitle }}</h1>
        </div>

        <div v-loading="loading" class="agreement-content">
            <div v-if="agreement && agreement.value" class="content-box">
                <div class="agreement-text" v-html="safeHtml"></div>
            </div>
            <div v-else-if="!loading" class="no-agreement">
                <el-empty :image-size="200" description="未找到相关协议内容">
                    <template #description>
                        <p>{{ errorMessage || '未找到相关协议内容' }}</p>
                    </template>
                    <el-button type="primary" @click="goBack">返回</el-button>
                    <el-button v-if="loadFailed" @click="retryLoad">重试</el-button>
                </el-empty>
            </div>
        </div>
    </div>
</template>

<script setup>
import {computed, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {platformAgreement} from '../../stores/platformAgreement'

const router = useRouter()
const route = useRoute()

// 状态定义
const loading = ref(true)
const agreement = ref(null)
const agreementTitle = ref('平台协议')
const errorMessage = ref('')
const loadFailed = ref(false)

// 安全处理HTML内容，防止null值导致错误
const safeHtml = computed(() => {
    if (!agreement.value || !agreement.value.value) return '';

    try {
        // 返回协议内容，如果是HTML格式就直接返回
        return agreement.value.value;
    } catch (error) {
        console.error('处理HTML内容错误:', error);
        return '<p>内容显示错误，请刷新页面重试</p>';
    }
})

// 获取协议内容
const getAgreementByTitle = async (title) => {
    loading.value = true
    loadFailed.value = false

    try {
        // 找到匹配的协议，获取详细内容
        const detailResult = await platformAgreement().GetInfoByTitle(title)
        if (detailResult.code === 200 && detailResult.data) {
            agreement.value = detailResult.data
            agreementTitle.value = detailResult.data.name || '平台协议'
        } else {
            errorMessage.value = '无法加载协议详情'
            agreement.value = null
            loadFailed.value = true
        }
    } catch (error) {
        console.error('获取协议失败:', error)
        errorMessage.value = '获取协议发生错误，请稍后重试'
        agreement.value = null
        loadFailed.value = true
    } finally {
        loading.value = false
    }
}

// 重试加载
const retryLoad = () => {
    const title = route.query.title
    if (title) {
        getAgreementByTitle(title)
    } else {
        errorMessage.value = '未指定协议标题，请返回并重试'
    }
}

// 返回上一页
const goBack = () => {
    router.go(-1)
}

// 组件加载时获取协议内容
onMounted(() => {
    // 从URL参数获取协议标题
    const title = route.query.title
    if (title) {
        getAgreementByTitle(title)
    } else {
        // 没有提供标题参数，尝试获取默认协议
        loading.value = false
        errorMessage.value = '未指定协议标题，请返回并重试'
    }
})
</script>

<style lang="scss" scoped>
.agreement-view-container {
    padding: 30px;
    max-width: 1200px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    min-height: 90vh;
}

.agreement-header {
    display: flex;
    flex-direction: column;
    //margin-bottom: 20px;
    position: relative;

    .back-button {
        //margin-top: 20px;
        text-align: center;
    }

    .agreement-title {
        text-align: center;
        font-size: 24px;
        margin: 20px 0;
        color: #333;
    }
}

.agreement-content {
    padding: 20px 0;

    .content-box {
        background-color: #f9f9f9;
        padding: 30px;
        border-radius: 8px;
        border: 1px solid #eaeaea;
    }

    .agreement-text {
        font-size: 16px;
        line-height: 1.8;
        color: #333;

        /* 富文本内容样式调整 */
        :deep(p) {
            margin-bottom: 16px;
        }

        :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }

        :deep(ul), :deep(ol) {
            padding-left: 2em;
            margin-bottom: 16px;
        }

        :deep(img) {
            max-width: 100%;
            height: auto;
        }

        :deep(table) {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 16px;

            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }

            th {
                background-color: #f2f2f2;
            }
        }
    }
}

.no-agreement {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;

    p {
        margin-top: 20px;
        color: #909399;
        font-size: 16px;
    }
}
</style>
