<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="content-area">

            <div class="filter-section">
                <el-form :model="state.queryParams" inline>
                    <el-form-item label="商品名称">
                        <el-input v-model="state.queryParams.productName" clearable placeholder="请输入商品名称"/>
                    </el-form-item>

                    <el-form-item label="价格区间">
                        <div class="price-range">
                            <el-input-number v-model="state.queryParams.minPrice" :min="0" :precision="2"
                                             placeholder="最低价"/>
                            <span class="price-separator">至</span>
                            <el-input-number v-model="state.queryParams.maxPrice" :min="0" :precision="2"
                                             placeholder="最高价"/>
                        </div>
                    </el-form-item>

                    <el-form-item label="商品类型">
                        <el-checkbox-group v-model="state.queryParams.productType">
                            <el-checkbox label="new">新品</el-checkbox>
                            <el-checkbox label="hot">热卖</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>

                    <el-form-item>
                        <el-button type="primary" @click="handleQuery">查询</el-button>
                        <el-button @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <div class="table-section">
                <el-table
                    v-loading="state.loading"
                    :data="state.tableData"
                    border
                    stripe
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                >
                    <el-table-column type="selection" width="55"/>
                    <el-table-column label="商品信息" min-width="300">
                        <template #default="scope">
                            <div class="product-info">
                                <el-image
                                    :src="getImageUrl(scope.row.cover)"
                                    class="product-image"
                                    fit="cover"
                                />
                                <div class="product-details">
                                    <div class="product-name">{{ scope.row.name }}</div>
                                    <div class="product-sku">商品编码: {{ scope.row.productSn }}</div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="价格" prop="price" width="120">
                        <template #default="scope">
                            <span class="price">¥{{ scope.row.price }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="库存" prop="stock" width="100"/>
                    <el-table-column label="销量" prop="sales" width="100"/>
                    <el-table-column label="状态" width="100">
                        <template #default="scope">
                            <el-tag :type="scope.row.status == 1 ? 'success' : 'info'">
                                {{ scope.row.status == 1 ? '上架中' : '已下架' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="审核状态" width="120">
                        <template #default="scope">
                            <el-tag :type="getAuditStatusType(scope.row.auditStatus)">
                                {{ getAuditStatusText(scope.row.auditStatus) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                </el-table>

                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="state.queryParams.pageNum"
                        v-model:page-size="state.queryParams.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="state.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>
    </div>

    <!-- 驳回理由对话框 -->
    <el-dialog v-model="dialogFormVisible" title="驳回理由" width="500">
        <el-form :model="state.form">
            <el-form-item label="驳回理由" label-width="">
                <el-input v-model="state.form.auditRemake" :rows="4" placeholder="请输入驳回理由" type="textarea"/>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="returnFun">取消</el-button>
                <el-button type="primary" @click="confirm">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {productList} from '../../stores/productList'
import {commodityReview} from '../../stores/commodityReview'
import {nextTick, onMounted, reactive, ref} from 'vue'
import {Session} from '../../utils/storage'
import {ElMessage, ElMessageBox} from 'element-plus'
import {getImageUrl} from "@/utils/common";

const dialogFormVisible = ref(false)

const state = reactive({
    loading: false,
    tableData: [],
    total: 0,
    selectedRows: [],
    currentStatus: '',
    statusCount: {
        total: 0,
        onSale: 0,
        offSale: 0
    },
    form: {
        id: "",           // 驳回id值
        auditStatus: "",  // 驳回状态
        auditRemake: "",  // 驳回理由
    },
    buttonList: [],
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        auditStatus: 1,  // 默认已审核
        productName: '',
        categoryId: null,
        minPrice: null,
        maxPrice: null,
        status: '',
        productType: [],
        keyword: ''
    },
    categoryOptions: []
})

const router = useRouter()

const handleButtonClick = (item) => {
    router.push(item.component)
}

const getAuditStatusType = (status) => {
    switch (status) {
        case "1":
            return 'success'
        case "2":
            return 'danger'
        case "0":
            return 'info'
        default:
            return 'info'
    }
}

const getAuditStatusText = (status) => {
    switch (status) {
        case "1":
            return '已通过'
        case "2":
            return '已驳回'
        case "0":
            return '待审核'
        default:
            return '未知'
    }
}

const getProductList = async () => {
    state.loading = true
    try {
        const result = await productList().GetProductList({
            ...state.queryParams,
            auditStatus: 1  // 固定查询已审核的商品
        })
        state.tableData = result.rows || []
        state.total = result.total || 0

        // 获取各状态数量
        const totalResult = await productList().GetProductList({auditStatus: 1})
        const onSaleResult = await productList().GetProductList({auditStatus: 1, publishStatus: 1})
        const offSaleResult = await productList().GetProductList({auditStatus: 1, publishStatus: 0})

        state.statusCount = {
            total: totalResult.total || 0,
            onSale: onSaleResult.total || 0,
            offSale: offSaleResult.total || 0
        }
    } catch (error) {
        console.error('获取商品列表失败', error)
        ElMessage.error('获取商品列表失败')
    } finally {
        state.loading = false
    }
}

const handleStatusChange = (value) => {
    state.queryParams.publishStatus = value
    state.queryParams.pageNum = 1
    getProductList()
}

const handleQuery = () => {
    state.queryParams.pageNum = 1
    getProductList()
}

const resetQuery = () => {
    state.queryParams = {
        pageNum: 1,
        pageSize: 10,
        auditStatus: 1,
        productName: '',
        categoryId: null,
        minPrice: null,
        maxPrice: null,
        status: '',
        productType: [],
        keyword: ''
    }
    getProductList()
}

const handleExport = async () => {
    try {
        await productList().ExportProductList(state.queryParams)
        ElMessage.success('导出成功')
    } catch (error) {
        ElMessage.error('导出失败')
    }
}

const handleSelectionChange = (selection) => {
    state.selectedRows = selection
}

const handlePublish = async (row) => {
    try {
        await productList().GetSale(row.id)
        ElMessage.success('商品上架成功')
        getProductList()
    } catch (error) {
        ElMessage.error('商品上架失败')
    }
}

const handleUnpublish = async (row) => {
    try {
        await productList().Getshelves(row.id)
        ElMessage.success('商品下架成功')
        getProductList()
    } catch (error) {
        ElMessage.error('商品下架失败')
    }
}

const handleDelete = (row) => {
    ElMessageBox.confirm('确定要删除该商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            await productList().Getdelete(row.id)
            ElMessage.success('删除成功')
            getProductList()
        } catch (error) {
            ElMessage.error('删除失败')
        }
    }).catch(() => {
    })
}

const handleBatchPublish = () => {
    if (state.selectedRows.length === 0) {
        ElMessage.warning('请至少选择一个商品')
        return
    }

    ElMessageBox.confirm('确定要批量上架选中的商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            const ids = state.selectedRows.map(item => item.id)
            await productList().GetSales(ids)
            ElMessage.success('批量上架成功')
            getProductList()
        } catch (error) {
            ElMessage.error('批量上架失败')
        }
    }).catch(() => {
    })
}

const handleBatchUnpublish = () => {
    if (state.selectedRows.length === 0) {
        ElMessage.warning('请至少选择一个商品')
        return
    }

    ElMessageBox.confirm('确定要批量下架选中的商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            const ids = state.selectedRows.map(item => item.id)
            await productList().Batchgetshelves(ids)
            ElMessage.success('批量下架成功')
            getProductList()
        } catch (error) {
            ElMessage.error('批量下架失败')
        }
    }).catch(() => {
    })
}

const handleBatchDelete = () => {
    if (state.selectedRows.length === 0) {
        ElMessage.warning('请至少选择一个商品')
        return
    }

    ElMessageBox.confirm('确定要批量删除选中的商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            const ids = state.selectedRows.map(item => item.id).join(',')
            await productList().Getdelete(ids)
            ElMessage.success('批量删除成功')
            getProductList()
        } catch (error) {
            ElMessage.error('批量删除失败')
        }
    }).catch(() => {
    })
}

const handleSizeChange = (val) => {
    state.queryParams.pageSize = val
    getProductList()
}

const handleCurrentChange = (val) => {
    state.queryParams.pageNum = val
    getProductList()
}

const returnFun = () => {
    dialogFormVisible.value = false
    state.form.auditRemake = ''
}

const confirm = async () => {
    try {
        await commodityReview().GetThroughList(state.form)
        ElMessage.success('操作成功')
        getProductList()
        dialogFormVisible.value = false
    } catch (error) {
        ElMessage.error('操作失败')
    }
}

onMounted(() => {
    getProductList()
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })
})
</script>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    height: 100vh;
    box-sizing: border-box;
    overflow: hidden;
}

.left-buttons {
    width: 218px;
    height: calc(100vh - 30px);
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.content-area {
    margin-left: 218px;
    width: calc(100% - 218px);
    padding: 20px;
    box-sizing: border-box;
    overflow-x: hidden;
}

.page-header {
    background-color: #fff;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h2 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }
}

.filter-section {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.price-range {
    display: flex;
    align-items: center;
}

.price-separator {
    margin: 0 10px;
}

.table-section {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.table-toolbar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    .left-operations {
        display: flex;
        gap: 10px;
    }

    .right-operations {
        width: 300px;
    }
}

.product-info {
    display: flex;
    align-items: center;
}

.product-image {
    width: 60px;
    height: 60px;
    margin-right: 10px;
    border-radius: 4px;
    object-fit: cover;
}

.product-details {
    display: flex;
    flex-direction: column;
}

.product-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.product-sku {
    font-size: 12px;
    color: #999;
}

.price {
    color: #ff6b00;
    font-weight: bold;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}
</style>
