<script setup>
import {useRouter} from 'vue-router'
import {ref} from 'vue'
import {Search} from '@element-plus/icons-vue'

const router = useRouter()
const handleButtonClick = (item) => {
    if (item === '系统通知') router.push('/customerServer')
    if (item === '申请通知') router.push('/404')
    if (item === '商品通知') router.push('/productNote')
    if (item === '订单通知') router.push('/ordersNote')
    if (item === '结算通知') router.push('/settlementNote')
    if (item === '退款通知') router.push('/refundNote')
    if (item === '商家入驻通知') router.push('/businessSettledNote')
    if (item === '实名申请') router.push('/realNameReview')
    if (item === '账号注销') router.push('/logout')
    if (item === '商品审核') router.push('/commodityReview')
}

const buttonList = [
    '全部消息', '系统通知', '申请通知', '商品通知', '订单通知',
    '结算通知', '退款通知', '商家入驻通知', '实名申请', '账号注销', '商品审核'
]

// 表单数据
const form = ref({
    startTime: '',
    endTime: '',
    status: ''
})

// 对话框控制
const messageDialogVisible = ref(false)
const rejectDialogVisible = ref(false)
const currentItem = ref(null)
const merchantMessage = ref('')
const rejectReason = ref('')

// 表格数据
const tableData = ref([
    {
        id: '1001',
        amount: '¥199.00',
        phone: '***********',
        time: '2023-11-01 14:30',
        status: '待审核',
        processStatus: '未处理'
    },
    {
        id: '1002',
        amount: '¥299.00',
        phone: '13800138002',
        time: '2023-11-02 09:15',
        status: '待审核',
        processStatus: '未处理'
    },
    {
        id: '1003',
        amount: '¥399.00',
        phone: '13800138003',
        time: '2023-11-03 16:45',
        status: '待审核',
        processStatus: '未处理'
    }
])

const handleSearch = () => {
    console.log('搜索条件:', form.value)
    // 这里应该调用API获取筛选后的数据
}

// 通过结算
const approveSettlement = (row) => {
    row.status = '已通过'
    row.processStatus = '已处理'
    ElMessage.success('结算申请已通过')
}

// 打开驳回对话框
const openRejectDialog = (row) => {
    currentItem.value = row
    rejectReason.value = ''
    rejectDialogVisible.value = true
}

// 提交驳回
const submitReject = () => {
    if (!rejectReason.value) {
        ElMessage.warning('请输入驳回理由')
        return
    }

    currentItem.value.status = '已驳回'
    currentItem.value.processStatus = '已处理'
    currentItem.value.rejectReason = rejectReason.value
    ElMessage.success('结算申请已驳回')
    rejectDialogVisible.value = false
}

// 打开留言对话框
const openMessageDialog = (row) => {
    currentItem.value = row
    merchantMessage.value = ''
    messageDialogVisible.value = true
}

// 提交留言
const submitMessage = () => {
    if (!merchantMessage.value) {
        ElMessage.warning('请输入留言内容')
        return
    }

    currentItem.value.merchantMessage = merchantMessage.value
    ElMessage.success('留言已发送')
    messageDialogVisible.value = false
}
</script>

<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item }}
            </el-button>
        </div>
        <div class="rightBox">
            <div class="head">
                <el-form :model="form" class="filter-form">
                    <!-- 第一行：时间范围 -->
                    <div class="form-row">
                        <el-form-item label="开始时间">
                            <el-date-picker
                                v-model="form.startTime"
                                placeholder="选择开始时间"
                                type="datetime"
                            />
                        </el-form-item>
                        <span class="time-separator">至</span>
                        <el-form-item label="结束时间">
                            <el-date-picker
                                v-model="form.endTime"
                                placeholder="选择结束时间"
                                type="datetime"
                            />
                        </el-form-item>
                    </div>

                    <!-- 第二行：状态筛选 -->
                    <div class="form-row">
                        <el-form-item label="状态">
                            <el-select v-model="form.status" placeholder="请选择状态">
                                <el-option label="全部" value=""></el-option>
                                <el-option label="待审核" value="pending"></el-option>
                                <el-option label="已通过" value="approved"></el-option>
                                <el-option label="已驳回" value="rejected"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-button
                            :icon="Search"
                            type="primary"
                            @click="handleSearch"
                        >
                            搜索
                        </el-button>
                    </div>
                </el-form>
            </div>

            <div class="main">
                <el-table :data="tableData" border style="width: 100%">
                    <el-table-column label="ID号" prop="id" width="120"/>
                    <el-table-column label="结算金额" prop="amount" width="120"/>
                    <el-table-column label="手机号" prop="phone" width="150"/>
                    <el-table-column label="结算时间" prop="time" width="180"/>
                    <el-table-column label="审核状态" prop="status" width="120">
                        <template #default="{ row }">
                            <el-tag
                                :type="row.status === '已通过' ? 'success' : row.status === '已驳回' ? 'danger' : 'warning'">
                                {{ row.status }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="处理状态" prop="processStatus" width="120">
                        <template #default="{ row }">
                            <el-tag :type="row.processStatus === '已处理' ? 'success' : 'info'">
                                {{ row.processStatus }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="250">
                        <template #default="{ row }">
                            <el-button
                                :disabled="row.processStatus === '已处理'"
                                size="small"
                                type="success"
                                @click="approveSettlement(row)"
                            >
                                通过
                            </el-button>
                            <el-button
                                :disabled="row.processStatus === '已处理'"
                                size="small"
                                type="danger"
                                @click="openRejectDialog(row)"
                            >
                                驳回
                            </el-button>
                            <el-button
                                size="small"
                                type="primary"
                                @click="openMessageDialog(row)"
                            >
                                留言
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>

    <!-- 驳回对话框 -->
    <el-dialog v-model="rejectDialogVisible" title="驳回结算申请" width="500px">
        <el-input
            v-model="rejectReason"
            :rows="4"
            placeholder="请输入驳回理由"
            type="textarea"
        />
        <template #footer>
            <el-button @click="rejectDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitReject">确认驳回</el-button>
        </template>
    </el-dialog>

    <!-- 商家留言对话框 -->
    <el-dialog v-model="messageDialogVisible" title="给商家留言" width="500px">
        <el-input
            v-model="merchantMessage"
            :rows="4"
            placeholder="请输入给商家的留言内容"
            type="textarea"
        />
        <template #footer>
            <el-button @click="messageDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitMessage">发送</el-button>
        </template>
    </el-dialog>
    <!--  </ManageBg>-->
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.rightBox {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .head {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid #3A58CF;

        .filter-form {
            .form-row {
                display: flex;
                align-items: center;
                margin-bottom: 20px;

                .el-form-item {
                    margin-right: 20px;
                    margin-bottom: 0;
                }

                .time-separator {
                    margin: 0 10px;
                    color: #666;
                }
            }
        }
    }

    .main {
        flex: 1;
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        overflow: hidden;

        :deep(.el-table) {
            height: 100%;

            th {
                background-color: #f5f7fa;
                font-weight: bold;
            }

            .el-table__cell {
                padding: 12px 0;
            }
        }
    }
}

.el-button + .el-button {
    margin-left: 10px;
}
</style>
