<script setup>
import {useRouter} from 'vue-router'
import {ref} from 'vue'
// 导入账号注销
import {deleteAccount, getAgreementContent, sendDeleteAccountSms} from '@/api/logout/UserAccount'
import {Warning} from '@element-plus/icons-vue'
// 添加 ElMessage 和 ElMessageBox 的导入
import {ElMessage, ElMessageBox} from 'element-plus'

const router = useRouter()

// 表单数据
const form = ref({
    password: '',
    verificationCode: '',
    agreeProtocol: false
})


// 协议弹窗控制
const protocolDialogVisible = ref(false)
const protocolContent = ref('')
const protocolTitle = ref('')
const agreementId = '1945322439271444481' // 协议ID，可根据需要改为动态获取

// 阅读完协议后，滚动到内容底部
const contentElement = ref(null)
const isAtBottom = ref(false)

// 监听滚动事件
const handleScroll = () => {
    if (contentElement.value) {
        const {scrollTop, scrollHeight, clientHeight} = contentElement.value
        isAtBottom.value = scrollHeight - scrollTop <= clientHeight + 10 // 10px容差
    }
}


// 获取协议内容
const fetchProtocolContent = async () => {
    try {
        const res = await getAgreementContent(agreementId)
        if (res.code === 200) {
            protocolTitle.value = res.data.name
            protocolContent.value = res.data.value
            protocolDialogVisible.value = true

            // 等待DOM更新后添加监听
            nextTick(() => {
                if (contentElement.value) {
                    contentElement.value.addEventListener('scroll', handleScroll)
                    // 初始检查位置
                    handleScroll()
                }
            })
        } else {
            ElMessage.error(res.msg || '获取协议内容失败')
        }
    } catch (error) {
        ElMessage.error('网络错误，获取协议失败')
        console.error('获取协议失败:', error)
    }
}

// 关闭前检查是否已滚动到底部
const handleCloseDialog = () => {
    if (isAtBottom.value) {
        // 滚动到底部后自动勾选协议
        form.value.agreeProtocol = true
        protocolDialogVisible.value = false
    } else {
        ElMessage.warning('请阅读完整协议内容后继续')
    }
}

// 清除监听
onBeforeUnmount(() => {
    if (contentElement.value) {
        contentElement.value.removeEventListener('scroll', handleScroll)
    }
})


/**
 * 验证码倒计时
 */
const countdown = ref(0)
const isSending = ref(false)
let timer = null


// 清除定时器
onBeforeUnmount(() => {
    if (timer) clearInterval(timer)
})

// 发送验证码
const sendCode = async () => {
    console.log('发送验证码')
    // 实际项目中这里调用发送验证码API
    if (countdown.value > 0 || isSending.value) return

    try {
        isSending.value = true
        const res = await sendDeleteAccountSms()
        countdown.value = 60
        timer = setInterval(() => {
            countdown.value--
            if (countdown.value <= 0) {
                clearInterval(timer)
            }
        }, 1000)
        if (res.code != 200){
            return
        }
        ElMessage.success('验证码发送成功')
    } catch (error) {
        console.error('发送验证码失败:', error)
        ElMessage.error(error.message || '发送验证码失败')
    } finally {
        isSending.value = false
    }
}

// 提交注销
const isLoading = ref(false)
const submitDelete = async () => {
    console.log('提交注销', form.value)
    // 实际项目中这里调用注销账号API
    try {
        // 表单验证
        if (!form.value.password) {
            ElMessage.error('请输入密码')
            return
        }
        if (!form.value.verificationCode) {
            ElMessage.error('请输入验证码')
            return
        }

        if (!form.value.agreeProtocol) {  // 新增协议勾选验证
            ElMessage.error('请先阅读并同意账号注销协议')
            return
        }

        // 二次确认
        await ElMessageBox.confirm(
            '确定要注销账号吗？此操作不可逆，所有数据将被永久删除',
            '确认注销',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        isLoading.value = true
        const params = {
            password: form.value.password,
            code: form.value.verificationCode
        }
        const res = await deleteAccount(params)
        // ElMessage.success('账号注销成功')
        if (res.code === 200) {
            ElMessage.success('账号注销正在处理中，请耐心等待')
            router.push('/login')
        } else {
            ElMessage.error(res.msg || '账号注销失败')
        }
        router.push('/login')
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error(error.message || '注销失败')
        }
    } finally {
        isLoading.value = false
    }
}
</script>

<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="rightBox">
            <div class="delete-account-container">
                <h2 class="delete-title">账号注销</h2>

                <!-- 警告提示 -->
                <div class="warning-box">
                    <h4><i class="el-icon-warning"></i> 重要提示</h4>
                    <ul>
                        <li>账号注销后将无法恢复</li>
                        <li>所有个人数据和历史记录将被永久删除</li>
                        <li>未使用的权益将自动失效</li>
                    </ul>
                </div>

                <!-- 注销表单 -->
                <el-form :model="form" class="delete-form">
                    <el-form-item label="登录密码" prop="password">
                        <el-input
                            v-model="form.password"
                            autocomplete="off"
                            placeholder="请输入当前账号密码"
                            show-password
                            type="password"
                        />
                    </el-form-item>

                    <el-form-item label="验证码" prop="verificationCode">
                        <div class="code-input">
                            <el-input
                                v-model="form.verificationCode"
                                clearable
                                maxlength="6"
                                placeholder="请输入6位短信验证码"
                            />
                            <el-button
                                :disabled="countdown > 0 || isSending"
                                :plain="countdown <= 0"
                                :style="countdown > 0 ? {
                                    'background-color': '#f5f7fa',
                                    'border-color': '#dcdfe6',
                                    'color': '#c0c4cc',
                                    'cursor': 'not-allowed'
                                } : {}"
                                :type="countdown > 0 ? '' : 'primary'"
                                class="send-code-btn"
                                @click="sendCode"
                            >
                                {{ countdown > 0 ? `${countdown}秒后重试` : '获取验证码' }}
                            </el-button>
                        </div>
                    </el-form-item>

                    <div class="action-buttons">
                        <el-button
                            :disabled="!form.agreeProtocol"
                            :loading="isLoading"
                            class="confirm-delete"
                            type="danger"
                            @click="submitDelete"
                        >
                            确认注销

                        </el-button>
                        <el-button
                            class="cancel-btn"
                            @click="router.push('/')"
                        >
                            取消
                        </el-button>
                    </div>
                </el-form>
                <!-- 换多行 -->
                <!--                <div class="btn-content">
                                    <div @click="handleSubmit">账号注销协议</div>
                                    <div class="protocol-agree">
                                        <el-checkbox
                                            v-model="form.agreeProtocol"
                                            class="square-checkbox"
                                            label="我已阅读并同意"
                                        />
                                        <a class="protocol-link" href="/service-agreement" target="_blank">《服务协议》</a>
                                    </div>
                                </div>-->
                <!--                <div class="protocol-section">
                                    &lt;!&ndash; 标题优化：最小化但保留功能 &ndash;&gt;
                                    <div class="protocol-header" @click="handleSubmit">
                                        <span class="protocol-title">账号注销协议</span>
                                        <i class="el-icon-arrow-down"></i>
                                    </div>

                                    &lt;!&ndash; 协议区域优化：提升视觉层次 &ndash;&gt;
                                    <div class="protocol-agree">
                                        <el-checkbox
                                                v-model="form.agreeProtocol"
                                                id="agreeProtocol"
                                                class="enhanced-checkbox"
                                                label="我已阅读并同意"
                                        />
                                        <router-link
                                                class="protocol-link"
                                                to="/service-agreement"
                                                target="_blank"
                                        >
                                            <span class="link-text">《服务协议》</span>
                                            <i class="el-icon-document"></i>
                                        </router-link>
                                    </div>
                                </div>-->
                <!-- 协议区域 -->
                <div class="protocol-section">
                    <div class="protocol-agree">
                        <el-checkbox
                            v-model="form.agreeProtocol"
                            class="enhanced-checkbox"
                            label="我已阅读并同意"
                        />
                        <a
                            class="protocol-link"
                            @click="fetchProtocolContent"
                            @click.stop=""
                        >
                            <span class="link-text">《账号注销协议》</span>
                            <i class="el-icon-document"></i>
                        </a>
                    </div>
                    <div v-if="!form.agreeProtocol" class="protocol-warning">
                        <el-icon>
                            <Warning/>
                        </el-icon>
                        <span>必须同意协议才能继续</span>
                    </div>
                </div>

            </div>
        </div>
    </div>


    <!-- 协议弹窗 -->
    <!--    <el-dialog
                v-model="protocolDialogVisible"
                :title="protocolTitle"
                width="70%"
                top="5vh"
                custom-class="protocol-dialog"
        >
            <div class="protocol-content" v-html="protocolContent"></div>
            <template #footer>
                <el-button type="primary" @click="protocolDialogVisible = false">我已阅读</el-button>
            </template>
        </el-dialog>-->
    <!--    <el-dialog
                v-model="protocolDialogVisible"
                :title="protocolTitle"
                width="70%"
                top="5vh"
                custom-class="protocol-dialog"
        >
            <div class="protocol-content-wrapper">
                <div
                        class="protocol-content"
                        v-html="protocolContent"
                        ref="contentElement"
                ></div>
            </div>
            <template #footer>
                <el-button
                        type="primary"
                        @click="protocolDialogVisible = false"
                        class="confirm-read-btn"
                >
                    我已阅读
                </el-button>
            </template>
        </el-dialog>-->
    <el-dialog
        v-model="protocolDialogVisible"
        :before-close="handleCloseDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        :title="protocolTitle"
        custom-class="protocol-dialog"
        top="5vh"
        width="70%"
    >
        <div class="protocol-content-wrapper">
            <div
                ref="contentElement"
                class="protocol-content"
                v-html="protocolContent"
            ></div>

            <!-- 底部提示 -->
            <div v-if="!isAtBottom" class="scroll-hint">
                <el-icon>
                    <ArrowDown/>
                </el-icon>
                <span>继续向下滚动阅读完整协议</span>
            </div>
        </div>

        <template #footer>
            <el-button
                :disabled="!isAtBottom"
                class="confirm-read-btn"
                type="primary"
                @click="handleCloseDialog"
            >
                {{ isAtBottom ? '我已阅读完整协议' : '请阅读完整协议' }}
            </el-button>
        </template>
    </el-dialog>
    <!--  </ManageBg>-->
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.rightBox {
    flex: 1;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.delete-account-container {
    width: 100%;
    max-width: 600px;
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.delete-title {
    text-align: center;
    color: #3A58CF;
    margin-bottom: 30px;
    font-size: 24px;
}

.warning-box {
    background-color: #fff6f6;
    border-left: 4px solid #f56c6c;
    padding: 15px;
    margin-bottom: 30px;

    h4 {
        color: #f56c6c;
        margin-bottom: 10px;
        display: flex;
        align-items: center;

        i {
            margin-right: 8px;
        }
    }

    ul {
        padding-left: 20px;
        margin: 0;
        color: #666;

        li {
            margin-bottom: 5px;
        }
    }
}

.delete-form {
    :deep(.el-form-item__label) {
        font-weight: bold;
        color: #333;
    }

    .el-input {
        width: 100%;
    }
}

.code-input {
    display: flex;
    gap: 10px;

    .send-code-btn {
        width: 120px;
        flex-shrink: 0;
    }
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;

    .confirm-delete {
        width: 200px;
        height: 45px;
        font-size: 16px;
    }

    .cancel-btn {
        width: 200px;
        height: 45px;
        font-size: 16px;
        border: 1px solid #dcdfe6;
    }
}

// 协议样式
.protocol-section {
    background-color: #F8F6F3;
    padding: 8px;
    border-radius: 4px;
    margin-top: 8px;
    border: 1px solid #EDE9E1;
}

.protocol-header {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #5A4A42;
    font-size: 11px;
    font-weight: 500; /* 改为中等字重 */
    background-color: #F0ECE5;
    padding: 4px 6px;
    border-radius: 3px;
    cursor: pointer;
    margin-bottom: 4px;
}

.protocol-title {
    margin-right: 4px;
}

.protocol-agree {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 0;
    background-color: #FDFDFB;
    border-radius: 3px;
    border: 1px solid #F0ECE5;
}

.enhanced-checkbox :deep(.el-checkbox__label) {
    font-size: 12px;
    color: #5A4A42;
    font-weight: 500;
}

.protocol-link {
    display: flex;
    align-items: center;
    color: #7D6652;
    font-size: 12px;
    font-weight: 500;
    margin-left: 8px;
    text-decoration: none;
    border-bottom: 1px dashed #7D6652;
}

.link-text {
    margin-right: 4px;
}

.el-icon-document {
    font-size: 14px;
    color: #7D6652;
}


// 协议弹窗样式
.protocol-dialog {
    // 确保弹窗有清晰的渲染上下文
    transform: translateZ(0); // 触发GPU加速
    backface-visibility: hidden;

    // 弹窗基础样式
    border-radius: 8px;

    :deep(.el-dialog) {
        // 防止模糊的核心样式
        //image-rendering: -webkit-optimize-contrast;
        text-rendering: geometricPrecision;
    }

    :deep(.el-dialog__header) {
        border-bottom: 1px solid #f0f0f0;
        padding: 15px 20px;
        margin-right: 0;
    }

    :deep(.el-dialog__body) {
        padding: 0;
    }

    :deep(.el-dialog__footer) {
        border-top: 1px solid #f0f0f0;
        padding: 15px 20px;
    }
}

.protocol-content-wrapper {
    // 新增以下属性
    will-change: transform; // 优化渲染性能
    contain: content; // 限制浏览器重绘范围

    max-height: 60vh;
    overflow-y: auto;
    padding: 0 20px;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background-color: rgba(144, 147, 153, 0.3);
        border-radius: 3px;

        &:hover {
            background-color: rgba(144, 147, 153, 0.5);
        }
    }

    &::-webkit-scrollbar-track {
        background-color: #f5f5f5;
    }
}

.protocol-content {

    table {
        border-collapse: separate; // 替代默认的collapse
        border-spacing: 0;

        td, th {
            padding: 8px 12px;
            background-clip: padding-box; // 防止背景渗透
        }
    }

    // 针对代码块的优化
    pre, code {
        font-family: SFMono-Regular, Consolas, monospace;
        font-size: 13px;
    }

    // 新增以下样式
    text-rendering: optimizeLegibility; // 优化文本渲染
    -webkit-font-smoothing: antialiased; // macOS字体抗锯齿
    -moz-osx-font-smoothing: grayscale; // Windows字体平滑

    // 确保基础样式正确
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    padding: 15px 0;

    h1, h2, h3, h4 {
        color: #3A58CF;
        margin: 20px 0 15px;
    }

    p {
        margin-bottom: 15px;
        text-align: justify;
    }

    ul, ol {
        padding-left: 2em;
        margin-bottom: 15px;

        li {
            margin-bottom: 8px;
        }
    }

    // 修复HTML内容样式冲突
    * {
        max-width: 100%;
        word-wrap: break-word;
    }

    img {
        max-width: 100%;
        height: auto;
    }
}

.confirm-read-btn {
    min-width: 120px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .protocol-dialog {
        width: 90% !important;

        .protocol-content-wrapper {
            max-height: 50vh;
            padding: 0 10px;
        }
    }
}

// 协议阅读完毕样式
.scroll-hint {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    color: #909399;
    font-size: 12px;
    position: sticky;
    bottom: 0;
    background: linear-gradient(to top, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.6));

    .el-icon {
        margin-bottom: 5px;
        animation: bounce 1.5s infinite;
    }
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

.confirm-read-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

// 注销按钮
.protocol-warning {
    display: flex;
    justify-content: center; /* 水平居中 */
    align-items: center;
    margin-top: 8px; /* 调整与上方内容的间距 */
    padding: 4px 0; /* 增加内边距 */
    color: #f56c6c;
    font-size: 12px;
    background-color: #fff6f6; /* 浅红色背景增强提示 */
    border-radius: 4px;
    //border-left: 3px solid #f56c6c; /* 左侧红线强调 */

    .el-icon {
        margin-right: 5px;
        font-size: 14px; /* 图标稍大 */
    }

    span {
        line-height: 1; /* 确保文字垂直居中 */
    }
}

.confirm-delete:disabled {
    background-color: #f5f5f5;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
}


</style>
