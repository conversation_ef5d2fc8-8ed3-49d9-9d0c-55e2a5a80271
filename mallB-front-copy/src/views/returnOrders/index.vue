<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="right-content">
            <div class="filter-section">
                <div class="title">
                    <span>订单筛选</span>
                </div>
                <div class="filter-form">
                    <el-form :model="state.filterForm">
                        <div class="form-row">
                            <el-input
                                v-model="state.filterForm.orderId"
                                class="filter-input"
                                placeholder="订单编码"
                            />
                            <el-input
                                v-model="state.filterForm.refundSn"
                                class="filter-input"
                                placeholder="退款编号"
                            />
                            <el-input
                                v-model="state.filterForm.buyerMobile"
                                class="filter-input"
                                placeholder="申请人电话"
                            />
                        </div>
                        <div class="form-row">
                            <el-button
                                class="search-btn"
                                type="primary"
                                @click="getProductList()"
                            >
                                查询
                            </el-button>
                        </div>
                    </el-form>
                </div>
            </div>
            <el-table :data="state.tableData" class="order-table">
                <el-table-column
                    header-class-name="table-header"
                    label="订单号"
                    prop="orderId"
                    width="180"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="申请时间"
                    prop="applyTime"
                    width="180"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="申请人电话"
                    prop="buyerMobile"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="退款金额"
                    prop="refundAmount"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="申请理由"
                    prop="buyerReason"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="退款编号"
                    prop="refundSn"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="退款状态"
                    prop="returnMoneySts"
                >
                    <template #default="scope">
                        <el-button v-if="scope.row.returnMoneySts=='1'" link size="small" type="success">退款申请中
                        </el-button>
                        <el-button v-if="scope.row.returnMoneySts=='2'" link size="small" type="success">卖家处理退款
                        </el-button>
                        <el-button v-if="scope.row.returnMoneySts=='3'" link size="small" type="success">退款成功
                        </el-button>
                        <el-button v-if="scope.row.returnMoneySts=='4'" link size="small" type="success">客户撤回退款申请
                        </el-button>
                        <el-button v-if="scope.row.returnMoneySts=='5'" link size="small" type="success">商家拒绝
                        </el-button>
                        <el-button v-if="scope.row.returnMoneySts=='-1'" link size="small" type="success">退款关闭
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="250">
                    <template #default="scope">
                        <el-button v-if="scope.row.returnMoneySts=='1'" size="small" type="info" @click="viewOrderDetail(scope.row)">
                            订单详情
                        </el-button>
                        <el-button v-if="userType == 2" v-show="scope.row.returnMoneySts=='1'" size="small"
                                   type="warning"
                                   @click="updateAmount(scope.row)">修改退款金额
                        </el-button>
                        <el-button v-if="userType == 2" v-show="scope.row.returnMoneySts=='1'" size="small"
                                   type="warning"
                                   @click="agreeFun(scope.row)">
                            同意
                        </el-button>
                        <el-button v-if="userType == 2" v-show="scope.row.returnMoneySts=='1'" size="small"
                                   type="danger"
                                   @click="refuseFun(scope.row)">
                            拒绝
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 订单详情对话框 -->
        <el-dialog v-model="state.orderDetailVisible" title="订单详情" width="1000px" @close="closeOrderDetail">
            <div v-loading="orderDetailLoading" class="order-detail-content">
                <div v-if="state.orderDetail" class="order-info">
                    <!-- 基本信息 -->
                    <div class="info-section">
                        <h3>基本信息</h3>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">订单编号：</span>
                                    <span class="value">{{ state.orderDetail.orderNo }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">订单状态：</span>
                                    <el-tag :type="getStatusType(state.orderDetail.status)" effect="light">
                                        {{ getStatusText(state.orderDetail.status) }}
                                    </el-tag>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">创建时间：</span>
                                    <span class="value">{{ state.orderDetail.createTime }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">支付时间：</span>
                                    <span class="value">{{ state.orderDetail.payTime || '-' }}</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 店铺信息 -->
                    <div class="info-section">
                        <h3>店铺信息</h3>
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <div class="info-item">
                                    <span class="label">店铺名称：</span>
                                    <span class="value">{{ state.orderDetail.shopName || '-' }}</span>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="info-item">
                                    <span class="label">店铺电话：</span>
                                    <span class="value">{{ state.orderDetail.shopPhone || '-' }}</span>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="info-item shop-logo-item">
                                    <span class="label">店铺Logo：</span>
                                    <el-image
                                        v-if="getImageUrl(state.orderDetail.shopLogo)"
                                        :src="getImageUrl(state.orderDetail.shopLogo)"
                                        class="shop-logo"
                                        fit="cover"
                                    />
                                    <span v-else class="value">-</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 收货信息 -->
                    <div class="info-section">
                        <h3>收货信息</h3>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">收货人：</span>
                                    <span class="value">{{ state.orderDetail.receiverName }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">联系电话：</span>
                                    <span class="value">{{ state.orderDetail.receiverPhone }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">收货方式：</span>
                                    <el-tag :type="state.orderDetail.receiverType == 1 ? 'success' : 'primary'" effect="light">
                                        {{ state.orderDetail.receiverType == 1 ? '自提' : '配送' }}
                                    </el-tag>
                                </div>
                            </el-col>
                            <el-col :span="24">
                                <div class="info-item">
                                    <span class="label">收货地址：</span>
                                    <span class="value">{{ getFullAddress(state.orderDetail) }}</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 商品信息 -->
                    <div class="info-section">
                        <h3>商品信息</h3>
                        <el-table :data="state.orderDetail.orderItems" border stripe>
                            <el-table-column label="商品图片" width="120">
                                <template #default="scope">
                                    <el-image
                                        v-if="getImageUrl(scope.row.productImage)"
                                        :src="getImageUrl(scope.row.productImage)"
                                        class="product-image"
                                        fit="cover"
                                    />
                                    <div v-else class="no-image">暂无图片</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="商品名称" prop="productName" show-overflow-tooltip/>
                            <el-table-column label="规格" prop="skuSpec" width="120"/>
                            <el-table-column label="单价" width="100">
                                <template #default="scope">
                                    ¥{{ scope.row.productPrice }}
                                </template>
                            </el-table-column>
                            <el-table-column label="数量" prop="quantity" width="80"/>
                            <el-table-column label="小计" width="100">
                                <template #default="scope">
                                    ¥{{ scope.row.totalAmount }}
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>

                    <!-- 费用信息 -->
                    <div class="info-section">
                        <h3>费用信息</h3>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">商品总额：</span>
                                    <span class="value amount">¥{{ state.orderDetail.totalAmount }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">补贴金：</span>
                                    <span class="value deduction-amount">¥{{ state.orderDetail.deductionAmount || '0.00' }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">实付金额：</span>
                                    <span class="value amount final-amount">¥{{ state.orderDetail.payAmount }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">支付方式：</span>
                                    <el-tag v-if="state.orderDetail.payType == 1" effect="plain" type="success">微信支付</el-tag>
                                    <el-tag v-else-if="state.orderDetail.payType == 2 || state.orderDetail.payType == 4" effect="plain" type="primary">支付宝支付</el-tag>
                                    <el-tag v-else-if="state.orderDetail.payType == 3" effect="plain" type="warning">用户补贴金</el-tag>
                                    <el-tag v-else-if="state.orderDetail.payType == 5" effect="plain" type="info">商家平台兑换金</el-tag>
                                    <span v-else>-</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 物流信息 -->
                    <div v-if="state.orderDetail.deliveryName" class="info-section">
                        <h3>物流信息</h3>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">快递公司：</span>
                                    <span class="value">{{ state.orderDetail.deliveryName }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">快递单号：</span>
                                    <span class="value">{{ state.orderDetail.deliveryNumber }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">发货时间：</span>
                                    <span class="value">{{ state.orderDetail.deliveryTime || '-' }}</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="closeOrderDetail">关闭</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
    <!--  </ManageBg>-->
</template>
<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {returnOrders} from '../../stores/returnOrders'
import {allOrders} from '../../stores/allOrders'
import {ElMessage, ElMessageBox} from "element-plus";
import {Session} from "@/utils/storage.js";
import {getImageUrl} from "@/utils/common";

const router = useRouter()
// 用户类型 (1:平台管理员, 2:商家)
const userType = ref(Session.get('userType'))
const state = reactive({
    buttonList: [],
    dialogTableVisible: false,
    orderDetailVisible: false,
    orderDetail: null,
    tableData: [],
    filterForm: {
        orderId: '',
        refundSn: '',
        buyerMobile: ''
    },
    detailsForm: {
        receiverName: '',
        receiverAddress: '',
        receiverPhone: '',
        payType: '',
        payTime: '',
        deliveryTime: ''
    }
})

const orderDetailLoading = ref(false)
const handleButtonClick = (item) => {
    router.push(item.component)
}

const getOrderRefundList = async () => {
    let result = await returnOrders().GetOrderRefundList();
    state.tableData = result.rows;
}
const getProductList = async () => {
    let result = await returnOrders().GetOrderRefundList(state.filterForm);
    state.tableData = result.rows;
}
// 查看订单详情
const viewOrderDetail = async (row) => {
    orderDetailLoading.value = true
    state.orderDetailVisible = true
    try {
        // 使用订单详情API获取完整信息
        const result = await allOrders().GetOrderDetail(row.orderNo)
        state.orderDetail = result.data
    } catch (error) {
        console.error('获取订单详情失败:', error)
        ElMessage.error('获取订单详情失败')
        state.orderDetailVisible = false
    } finally {
        orderDetailLoading.value = false
    }
}

// 关闭订单详情对话框
const closeOrderDetail = () => {
    state.orderDetailVisible = false
    state.orderDetail = null
}

// 获取订单状态类型
const getStatusType = (status) => {
    switch (status) {
        case '0':
            return 'warning'  // 待支付
        case '1':
            return 'primary'  // 支付中
        case '2':
            return 'primary'  // 待发货
        case '3':
            return 'warning'  // 待收货
        case '4':
            return 'success'  // 已完成
        case '5':
            return 'danger'   // 已取消
        case '6':
            return 'info'     // 已退款
        default:
            return 'info'
    }
}

// 获取状态文本
const getStatusText = (status) => {
    switch (status) {
        case '0':
            return '待支付'
        case '1':
            return '支付中'
        case '2':
            return '待发货'
        case '3':
            return '待收货'
        case '4':
            return '已完成'
        case '5':
            return '已取消'
        case '6':
            return '已退款'
        default:
            return '未知状态'
    }
}

// 获取完整地址
const getFullAddress = (order) => {
    if (!order) return '-'
    const parts = [
        order.receiverProvince,
        order.receiverCity,
        order.receiverDistrict,
        order.receiverAddress
    ].filter(part => part && part.trim())
    return parts.length > 0 ? parts.join(' ') : '-'
}

const lookFun = async (row) => {
    let refundId = row.refundId
    let result = await returnOrders().GetOrderRefund(refundId);
    let data = result.data.order
    state.detailsForm.receiverName = data.receiverName
    state.detailsForm.receiverAddress = data.receiverAddress
    state.detailsForm.receiverPhone = data.receiverPhone
    state.detailsForm.payType = data.payType
    state.detailsForm.payTime = data.payTime
    state.detailsForm.deliveryTime = data.deliveryTime
    state.dialogTableVisible = true
}
const agreeFun = async (row) => {
    let refundId = row.refundId
    let result = await returnOrders().GetagreeFun(refundId);
    getOrderRefundList()
    // if(result.code==500){
    //   console.log(result.code,'result.code')
    // }
}
const refuseFun = async (row) => {
    let refundId = row.refundId
    let result = await returnOrders().GetagreeFun(refundId);
    getOrderRefundList()
    // if(result.code==500){
    //   console.log(result.code,'result.code')
    // }
}
const updateAmount = async (row) => {
    ElMessageBox.prompt('请输入的新的退款金额', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        inputValidator: (_value) => {
            let value = parseFloat(_value);
            if (value < row.refundAmount) {
                // 正则表达式 可以整数，最多是两位小数
                let pattern = /^[0-9]+(\.[0-9]{1,2})?$/;
                if (!pattern.test(value)) {
                    return ("请输入正确的金额");
                } else {
                    return (true);
                }
            } else {
                return ("新的退款金额不能大于之前的退款金额");
            }
        },
    }).then(({value}) => {
        let refundId = row.refundId
        let data = {
            refundAmount: value
        }
        returnOrders().UpdateAmount(refundId, data).then((res) => {
            if (res.code == 200) {
                ElMessage.success("修改成功");
            } else {
                ElMessage.error(res.msg);
            }
        }).catch((error) => {
            ElMessage.error(error.message);
        }).finally(() => {
            getOrderRefundList()
        });
    })


}
onMounted(() => {
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })
    getOrderRefundList()
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.right-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.filter-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.title {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
}

.filter-form {
    .form-row {
        display: flex;
        margin-bottom: 15px;
        gap: 15px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.filter-input {
    flex: 1;
}

.search-btn {
    width: 120px;
}

.order-table {
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-table__header) {
        .table-header {
            background-color: #3A58CF;
            color: white;
        }
    }

    :deep(.el-table__cell) {
        padding: 12px 0;
    }
}

// 订单详情对话框样式
.order-detail-content {
    .info-section {
        margin-bottom: 24px;

        h3 {
            margin: 0 0 16px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #e6f7ff;
            color: #1890ff;
            font-size: 16px;
            font-weight: 600;
        }

        .info-item {
            margin-bottom: 12px;
            display: flex;
            align-items: center;

            .label {
                font-weight: 500;
                color: #666;
                min-width: 100px;
                margin-right: 8px;
            }

            .value {
                color: #333;

                &.amount {
                    color: #f56c6c;
                    font-weight: 600;
                }

                &.deduction-amount {
                    color: #67c23a;
                    font-weight: 600;
                }

                &.final-amount {
                    font-size: 16px;
                    font-weight: 700;
                }
            }
        }

        .shop-logo-item {
            .shop-logo {
                width: 40px;
                height: 40px;
                border-radius: 4px;
                border: 1px solid #e6e6e6;
            }
        }
    }

    .product-image {
        width: 50px;
        height: 50px;
        border-radius: 4px;
    }

    .no-image {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        color: #999;
        font-size: 12px;
        border-radius: 4px;
    }
}
</style>
