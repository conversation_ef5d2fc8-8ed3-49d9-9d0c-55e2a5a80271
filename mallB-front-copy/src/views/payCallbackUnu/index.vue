<script setup>
import {useRouter} from 'vue-router'
import {ref} from 'vue'

const router = useRouter()
const handleButtonClick = (item) => {
    if (item === '全部订单') router.push('/allOrders')
    if (item === '支付异常') router.push('/unusualPay')
    if (item === '支付回调异常') router.push('/payCallbackUnu')
    if (item === '退货订单') router.push('/returnOrders')
    if (item === '已退款') router.push('/refunded')
    if (item === '发货') router.push('/shipping')
}

const buttonList = [
    '全部订单', '支付异常', '支付回调异常', '退货订单',
    '已退款', '发货', '发货单号'
]

// 筛选表单数据
const filterForm = ref({
    IDNumber: '',
    originShipment: '',
    orderNumber: '',
    addressee: '',
    shippingTime: '',
    trackingNumber: '',
    payCallbackUnu: '',
    receiverPhone: ''
})

// 表格数据
const tableData = ref([
    {
        IDNumber: '20030305',
        originShipment: '广东省广州市天河区车坡街道车佩大厦222',
        orderNumber: '13542163654',
        addressee: '武小姐',
        shippingTime: '2024-2-9',
        trackingNumber: '1547416513454',
        payCallbackUnu: '异常',
        receiverPhone: '134154564642'
    },
    {
        IDNumber: '20251103',
        originShipment: '北京市二环街道大成路102',
        orderNumber: '795454646',
        addressee: '希先生',
        shippingTime: '2022-2-9',
        trackingNumber: '744857411125',
        payCallbackUnu: '正常',
        receiverPhone: '74475488712'
    },
])
</script>

<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item }}
            </el-button>
        </div>

        <div class="right-content">
            <div class="filter-section">
                <div class="title">
                    <span>订单筛选</span>
                </div>

                <div class="filter-form">
                    <el-form :model="filterForm">
                        <div class="form-row">
                            <el-input
                                v-model="filterForm.userPhone"
                                class="filter-input"
                                placeholder="用户手机号"
                            />
                            <el-input
                                v-model="filterForm.sender"
                                class="filter-input"
                                placeholder="发件人"
                            />
                            <el-input
                                v-model="filterForm.receiverPhone"
                                class="filter-input"
                                placeholder="收件手机号"
                            />
                        </div>

                        <div class="form-row">
                            <el-input
                                v-model="filterForm.shippingAddress"
                                class="filter-input"
                                placeholder="发货地"
                            />
                            <el-input
                                v-model="filterForm.trackingNumber"
                                class="filter-input"
                                placeholder="快递号"
                            />
                            <el-input
                                v-model="filterForm.productName"
                                class="filter-input"
                                placeholder="商品名称"
                            />
                        </div>

                        <div class="form-row">
                            <el-input
                                v-model="filterForm.orderNumber"
                                class="filter-input"
                                placeholder="订单号"
                            />
                            <el-date-picker
                                v-model="filterForm.shippingTime"
                                class="filter-input"
                                placeholder="发货时间"
                                type="datetime"
                            />
                            <el-button
                                class="search-btn"
                                type="primary"
                            >
                                查询
                            </el-button>
                        </div>
                    </el-form>

                </div>
            </div>

            <el-table :data="tableData" class="order-table">
                <el-table-column
                    header-class-name="table-header"
                    label="ID号"
                    prop="IDNumber"
                    width="100"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="发货地"
                    prop="originShipment"
                    width="350"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="订单号"
                    prop="orderNumber"
                    width="150"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="收件人"
                    prop="addressee"
                    width="120"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="快递号"
                    prop="trackingNumber"
                    width="150"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="发货时间"
                    prop="shippingTime"
                    width="150"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="支付回调异常"
                    prop="payCallbackUnu"
                    width="120"
                />
                <el-table-column
                    header-class-name="table-header"
                    label="收件手机号"
                    prop="receiverPhone"
                    width="200"
                />


            </el-table>
        </div>
    </div>
    <!--  </ManageBg>-->
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.right-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.filter-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.title {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
}

.filter-form {
    .form-row {
        display: flex;
        margin-bottom: 15px;
        gap: 15px;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.filter-input {
    flex: 1;
}

.search-btn {
    width: 120px;
}

.order-table {
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-table__header) {
        .table-header {
            background-color: #3A58CF;
            color: white;
        }
    }

    :deep(.el-table__cell) {
        padding: 12px 0;
    }
}
</style>
