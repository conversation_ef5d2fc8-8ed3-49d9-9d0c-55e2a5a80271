<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {onMounted, reactive, ref} from 'vue'
import {Refresh} from '@element-plus/icons-vue'
import {Session} from "@/utils/storage";

// 导入结算记录
import {exportSettlementRecord, listSettlementRecord} from '@/api/settlementRecord/SettlementRecord'
import {ElMessage} from "element-plus";
import {formatDate} from "../../utils/common";

const router = useRouter()


// 查询参数
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    // phone: '',
    legalPersonPhone: '', // 法人手机号
    accountName: '',  // 账户名
    // settlementType: '', // 结算方式
    status: '', // 状态
    beginTime: '',
    endTime: '',
    //  shopName: '' // 商家名称
})


// 重置按钮 处理
const resetQuery = () => {
    Object.assign(queryParams, {
        pageNum: 1,
        pageSize: 10,
        // phone: '',
        legalPersonPhone: '', // 法人手机号
        accountName: '',  // 账户名
        // settlementType: '', // 结算方式
        status: '', // 状态
        beginTime: '',
        endTime: '',
        // shopName: '' // 商家名称
    })
    beginTime.value = null // 清空开始时间选择器
    endTime.value = null  // 清空结束时间选择器
    // total.value = 0 // 清空总条数
    // settlementRecordList.value = [] // 清空列表
    getList() // 可选：触发列表刷新
}

// 结算记录数据列表
const settlementRecordList = ref<any[]>([])
const total = ref(0)

// 加载状态
const loading = ref(false)


// 获取结算记录列表
const getList = async () => {
    loading.value = true
    try {
        const res = await listSettlementRecord(queryParams)
        settlementRecordList.value = res.rows || []
        pagination.total = res.total
    } catch (error) {
        console.error('获取商家结算记录列表失败:', error)
        ElMessage.error('获取商家结算记录列表失败')
    } finally {
        loading.value = false
    }
}


// 分页参数
const pagination = reactive({
    pageNum: 1,
    pageSize: 10,
    total: 0
})
// 分页处理
const handleSizeChange = (size) => {
    // pagination.pageSize = size
    queryParams.pageSize = size
    getList()
}
const handleCurrentChange = (page) => {
    // pagination.pageNum = page
    queryParams.pageNum = page
    getList()
}


// 时间选择器变化处理
const beginTime = ref<Date | null>(null)
const endTime = ref<Date | null>(null)
const handleBeginDateChange = (value: Date | null) => {
    if (value instanceof Date && !isNaN(value.getTime())) {
        queryParams.beginTime = formatDate(value)
    } else {
        queryParams.beginTime = ''
    }
}

const handleEndDateChange = (value: Date | null) => {
    if (value instanceof Date && !isNaN(value.getTime())) {
        queryParams.endTime = formatDate(value)
    } else {
        queryParams.endTime = ''
    }
}


// 导出Excel
const handleExport = () => {
    exportSettlementRecord(queryParams)
        .then(response => {
            const blob = new Blob([response])
            const downloadUrl = URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = downloadUrl
            link.setAttribute('download', `结算记录_${new Date().getTime()}.xlsx`)
            document.body.appendChild(link)
            link.click()
            link.remove()
            ElMessage.success('导出成功')
        })
        .catch(() => {
            ElMessage.error('导出失败')
        })
}


// 状态映射表
const statusMap = {
    '0': {label: '待审核', type: 'info'},
    '1': {label: '审核通过', type: 'success'},
    '2': {label: '审核拒绝', type: 'danger'},
    '3': {label: '已打款', type: 'warning'},
    '4': {label: '已完成', type: 'primary'},
    default: {label: '--', type: 'info'}
}

// 获取状态标签信息
const getStatusLabel = (status) => {
    return statusMap[status] || statusMap.default
}


/*---------------------------------- 分割线 -----------------------------------------*/

const state = reactive({
    form: {},
    buttonList: [],
})
const handleButtonClick = (item) => {
    router.push(item.component)
}
onMounted(() => {

    // getList()

    let menuList = Session.getMenu2()
    let menuId = Session.get('homeMenuId');
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index];
        if (element.menuId == menuId) {
            state.buttonList = element.children
        }
    }
})

getList()

</script>

<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>

        <div class="right-content">
            <div class="filter-container">
                <div class="filter-header">
                    <span class="filter-title">结算记录管理</span>
                </div>
                <el-form :model="queryParams" class="filter-form">
                    <!-- 第一行：手机号 -->
                    <div class="form-row">


                        <el-form-item label="账号">
                            <el-input v-model="queryParams.accountName" clearable placeholder="收款账户名"/>
                        </el-form-item>

                        <el-form-item label="手机号">
                            <el-input v-model="queryParams.legalPersonPhone" clearable placeholder="企业法人手机号"/>
                        </el-form-item>

                        <el-form-item label="结算方式">
                            <el-select v-model="queryParams.settlementType" placeholder="请选择">
                                <el-option label="银行卡(对公)" value="1"></el-option>
                                <el-option label="微信" value="2"></el-option>
                                <el-option label="支付宝" value="3"></el-option>
                            </el-select>
                        </el-form-item>


                        <el-form-item label="状态">
                            <el-select v-model="queryParams.status" placeholder="请选择">
                                <el-option label="全部" value=""></el-option>
                                <el-option label="待审核" value="0"></el-option>
                                <el-option label="审核通过" value="1"></el-option>
                                <el-option label="审核拒绝" value="2"></el-option>
                                <el-option label="已打款" value="3"></el-option>
                                <el-option label="已完成" value="4"></el-option>
                            </el-select>
                        </el-form-item>

                        <!-- 时间范围选择 -->
                        <el-form-item label="时间范围">
                            <div class="date-range-container">
                                <el-date-picker
                                    v-model="beginTime"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    placeholder="选择开始日期时间"
                                    type="datetime"
                                    @change="handleBeginDateChange"
                                />
                                <span class="time-separator">至</span>
                                <el-date-picker
                                    v-model="endTime"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    placeholder="选择结束日期时间"
                                    type="datetime"
                                    @change="handleEndDateChange"
                                />
                            </div>
                        </el-form-item>


                        <el-button class="search-btn" type="primary" @click="getList">
                            查询
                        </el-button>
                        <el-button class="export-btn" @click="handleExport">
                            导出Excel
                        </el-button>
                        <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
                    </div>
                </el-form>
                <!-- 分隔线 -->
                <el-divider/>
            </div>

            <!-- 表格区域 -->
            <div class="table-container">
                <el-table v-loading="loading" :data="settlementRecordList" border style="width: 100%">
                    <el-table-column label="结算时间" prop="settlementTime" width="160"/>
                    <el-table-column label="账号" prop="accountName" width="150"/>
                    <el-table-column label="手机号" prop="legalPersonPhone" width="150"/>
                    <el-table-column label="结算单号" prop="settlementNo" width="180"/>
                    <el-table-column label="结算卡号" prop="accountNo" width="180"/>
                    <el-table-column label="公司名称" prop="businessName" width="160"/>
                    <el-table-column label="结算方式" prop="settlementTypeLabel" width="120">
                        <span>{{ "对公" }}</span>
                    </el-table-column>
                    <el-table-column label="货款金额" prop="actualAmount" width="120"/>
                    <el-table-column label="状态" min-width="120" prop="status">
                        <template #default="{ row }">
                            <el-tag :type="getStatusLabel(row.status).type" size="small">
                                {{ getStatusLabel(row.status).label }}
                            </el-tag>
                        </template>
                    </el-table-column>


                </el-table>

                <!-- 分页组件 -->
                <div v-if="settlementRecordList.length > 0" class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.pageNum"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        background
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>

            </div>

        </div>
    </div>
    <!--  </ManageBg>-->
</template>


<style lang="scss" scoped>


.truncate-with-title {
    display: inline-block;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}


.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 30px);
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.right-content {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    max-width: calc(100% - 250px);
}

.filter-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #3A58CF;
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 20px;

    .filter-title {
        font-size: 24px;
        font-weight: bold;
        color: white;
    }
}

.filter-form {
    .form-row {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;

        .el-form-item {
            margin-right: 30px;
            margin-bottom: 10px;
            min-width: 180px;

            :deep(.el-form-item__label) {
                padding-bottom: 0;
                font-weight: normal;
            }

            :deep(.el-input) {
                width: 180px;
            }

            :deep(.el-select) {
                width: 180px;
            }

            :deep(.el-date-editor) {
                width: 180px;
            }
        }

        .time-separator {
            margin: 0 10px;
            color: #666;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-left: auto;
        }
    }
}

.table-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    flex: 1;
    overflow-x: auto;

    :deep(.el-table) {
        font-size: 13px;

        th {
            background-color: #f5f7fa;
            color: #333;
            font-weight: bold;
            padding: 8px 0;
        }

        td {
            padding: 6px 0;
        }
    }
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 5px;

    div {
        line-height: 1.4;
    }
}

.deduction-info {
    display: flex;
    flex-direction: column;
    gap: 5px;

    .deduction-item {
        display: flex;
        align-items: center;
        line-height: 1.4;

        .label {
            width: 80px;
            color: #606266;
        }

        .value {
            font-weight: 500;

            &.total {
                color: #409EFF;
            }

            &.used {
                color: #F56C6C;
            }

            &.remain {
                color: #67C23A;
            }
        }
    }
}


// 导出按钮样式

.export-btn {
    margin-left: 20px;
    background: linear-gradient(135deg, #4CAF50, #66BB6A);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
    }

    &:active {
        transform: scale(0.98);
    }

    .el-icon {
        font-size: 16px;
    }
}


// 分页组件样式
.pagination-container {
    display: flex;
    justify-content: flex-end; // 右对齐
    align-items: center;
    margin-top: 20px;
    gap: 15px; // 按钮间距更清晰
}

.pagination-container .el-pagination {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 8px 12px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.pagination-container .el-pagination:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 150, 255, 0.2);
}

// 时间选择器样式
.date-range-container {
    display: flex;
    align-items: center;
    gap: 10px; // 控制两个选择器之间的间距
}

.time-separator {
    margin: 0 5px;
    color: #666;
    font-weight: normal;
}


</style>
