<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {reactive, ref} from 'vue'
import {Search} from '@element-plus/icons-vue'
import {Session} from "@/utils/storage";

const router = useRouter()

// 表单数据
const form = reactive({
    accountId: '',
    phone: '',
    settlementMethod: '',
    startTime: '',
    endTime: '',
    searchText: ''
})

// 表格数据
const tableData = ref([
    {
        id: '**********',
        time: '2025-11-4 16:30',
        account: 'user001',
        accountNum: "444444444444444444444",
        companyName: '游戏公司',
        name: '李四',
        todayVoucher: '8200',
        total: '500.00'
    },
    // 可以添加更多数据...
])

// 状态按钮
const statusButtons = [
    {label: '全部记录', value: 'all'},
    {label: '待审核', value: 'pending'},
    {label: '待打款', value: 'waiting'},
    {label: '已打款', value: 'paid'},
    {label: '已驳回', value: 'rejected'}
]

const activeStatus = ref('all')

const onExport = () => {
    console.log('导出Excel')
}

const handleSearch = () => {
    console.log('搜索操作')
}

const handleShelf = (row: any) => {
    console.log('上架操作:', row)
}

const handleUnshelf = (row: any) => {
    console.log('下架操作:', row)
}


const state = reactive({
    form: {},
    buttonList: []
})
const handleButtonClick = (item) => {
    router.push(item.component)
}
onMounted(() => {
    let menuList = Session.getMenu2()
    let menuId = Session.get('homeMenuId');
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index];
        if (element.menuId == menuId) {
            state.buttonList = element.children
        }
    }
})
</script>

<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>

        <div class="right-content">
            <div class="filter-container">
                <!-- 状态按钮行 -->
                <div class="status-buttons">
                    <el-button
                        v-for="button in statusButtons"
                        :key="button.value"
                        :type="activeStatus === button.value ? 'primary' : ''"
                        @click="activeStatus = button.value"
                    >
                        {{ button.label }}
                    </el-button>
                </div>

                <!-- 表单筛选区域 -->
                <el-form :model="form" class="filter-form">
                    <!-- 账号ID行 -->
                    <div class="form-row">
                        <span class="form-label">账号ID</span>
                        <el-form-item>
                            <el-input v-model="form.accountId" placeholder="企业号"/>
                        </el-form-item>
                    </div>

                    <!-- 手机号行 -->
                    <div class="form-row">
                        <span class="form-label">手机号</span>
                        <el-form-item>
                            <el-input v-model="form.phone"/>
                        </el-form-item>
                    </div>

                    <!-- 结算方式行 -->
                    <div class="form-row">
                        <span class="form-label">结算方式</span>
                        <el-form-item>
                            <el-input v-model="form.settlementMethod"/>
                        </el-form-item>
                    </div>

                    <!-- 结算时间行 -->
                    <div class="form-row">
                        <span class="form-label">结算时间</span>
                        <el-form-item>
                            <el-date-picker
                                v-model="form.startTime"
                                placeholder="开始时间"
                                type="datetime"
                            />
                        </el-form-item>
                        <span class="time-separator">至</span>
                        <el-form-item>
                            <el-date-picker
                                v-model="form.endTime"
                                placeholder="结束时间"
                                type="datetime"
                            />
                        </el-form-item>
                    </div>

                    <!-- 操作按钮行 -->
                    <div class="action-buttons">
                        <el-button type="primary" @click="handleSearch">
                            <el-icon>
                                <Search/>
                            </el-icon>
                            <span>搜索</span>
                        </el-button>
                        <el-button type="warning" @click="onExport">
                            导出
                        </el-button>
                    </div>
                </el-form>
            </div>

            <!-- 表格区域 -->
            <div class="table-container">
                <el-table :data="tableData" border style="width: 100%">
                    <el-table-column label="账号ID" prop="id" width="100"/>
                    <el-table-column label="结算时间" prop="time" width="100"/>
                    <el-table-column label="结算单号" prop="account" width="100"/>
                    <el-table-column label="结算卡号" prop="accountNum" width="200"/>
                    <el-table-column label="公司名称/姓名" width="300">
                        <template #default="{row}">
                            <div>{{ row.companyName }}</div>
                            <div>{{ row.name }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="结算方式" prop="todayVoucher" width="200"/>
                    <el-table-column label="货款/补贴金" prop="total" width="200"/>
                    <el-table-column label="操作" width="180">
                        <template #default="{row}">
                            <el-button class="shelf-btn" @click="handleShelf(row)">
                                上架
                            </el-button>
                            <el-button class="unshelf-btn" @click="handleUnshelf(row)">
                                下架
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>
    <!--  </ManageBg>-->
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.filter-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;

    .status-buttons {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;

        .el-button {
            flex: 1;
        }
    }

    .filter-form {
        .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;

            .form-label {
                width: 80px;
                text-align: right;
                margin-right: 10px;
                font-size: 14px;
                color: #606266;
            }

            .el-form-item {
                margin-bottom: 0;
                flex: 1;
            }

            .el-form-item1 {
                margin-bottom: 0;

            }

            .time-separator {
                margin: 0 10px;
                color: #606266;
            }
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;

            .el-button {
                width: 120px;
            }
        }
    }
}

.table-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    flex: 1;

    :deep(.el-table) {
        font-size: 14px;

        th {
            background-color: #f5f7fa;
            color: #333;
            font-weight: bold;
        }

        .cell {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
    }
}

:deep(.shelf-btn) {
    border-radius: 4px;
    padding: 8px 16px;
    transition: all 0.3s;
    background-color: pink;

    &:hover {
        transform: translateY(-1px);
    }
}

:deep(.unshelf-btn) {
    border-radius: 4px;
    padding: 8px 16px;
    transition: all 0.3s;
    margin-left: 0px;
    background-color: cyan;

    &:hover {
        transform: translateY(-1px);
    }
}
</style>
