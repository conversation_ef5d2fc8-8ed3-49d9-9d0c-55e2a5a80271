<template>
    <!--  <HomeBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="right-content">
            <span class="fixed-title">权限设置</span>
            <div class="main-content">
                <!-- 开启/关闭单选框 -->
                <div class="toggle-section">
                    <el-radio-group v-model="state.form1.permissionOpen">
                        <el-radio label="open" size="large">
                            <span class="toggle-label">开启</span>
                        </el-radio>
                        <el-radio label="close" size="large">
                            <span class="toggle-label">关闭</span>
                        </el-radio>
                    </el-radio-group>
                </div>
                <!-- 代销权限按钮区域 -->
<!--                <div class="permission-buttons">-->
<!--                    <div class="permission-item">-->
<!--                        权限一上架代销数量-->
<!--                        <el-input v-model="state.form1.authorityOne" placeholder="代销权限一" type="number"></el-input>-->
<!--                    </div>-->
<!--                    <div class="arrow">→</div>-->
<!--                    <div class="permission-item">-->
<!--                        权限二上架代销数量-->
<!--                        <el-input v-model="state.form1.authorityTwo" placeholder="代销权限二" type="number"></el-input>-->
<!--                    </div>-->
<!--                    <div class="arrow">→</div>-->
<!--                    <div class="permission-item">-->
<!--                        权限三上架代销数量-->
<!--                        <el-input v-model="state.form1.authorityThree" placeholder="代销权限三"-->
<!--                                  type="number"></el-input>-->
<!--                    </div>-->
<!--                </div>-->
                <!-- 权限价格表单区域 -->
                <div class="price-form">
                    <div class="price-row">
                        <span class="price-label">权限一原价：</span>
                        <el-input v-model="state.form1.permission1.originalPrice" class="price-input"/>
                        <span class="price-unit">元/月</span>

                        <span class="price-label" style="width: 50px">优惠：</span>
                        <el-input v-model="state.form1.permission1.discountPrice" class="price-input"/>
                        <span class="price-unit">折扣/月</span>

                        <span class="price-label" style="width: 160px">权限一上架代销数量：</span>
                        <el-input v-model="state.form1.authorityOne" class="price-input"/>
                    </div>
                    <div class="down-arrow">↓</div>
                    <div class="price-row">
                        <span class="price-label">权限二原价：</span>
                        <el-input v-model="state.form1.permission2.originalPrice" class="price-input"/>
                        <span class="price-unit">元/月</span>

                        <span class="price-label" style="width: 50px">优惠：</span>
                        <el-input v-model="state.form1.permission2.discountPrice" class="price-input"/>
                        <span class="price-unit">折扣/月</span>

                        <span class="price-label" style="width: 160px">权限二上架代销数量：</span>
                        <el-input v-model="state.form1.authorityTwo" class="price-input"/>
                    </div>
                    <div class="down-arrow">↓</div>
                    <div class="price-row">
                        <span class="price-label">权限三原价：</span>
                        <el-input v-model="state.form1.permission3.originalPrice" class="price-input"/>
                        <span class="price-unit">元/月</span>

                        <span class="price-label" style="width: 50px">优惠：</span>
                        <el-input v-model="state.form1.permission3.discountPrice" class="price-input"/>
                        <span class="price-unit">折扣/月</span>

                        <span class="price-label" style="width: 160px">权限三上架代销数量：</span>
                        <el-input v-model="state.form1.authorityThree" class="price-input"/>
                    </div>
                    <div style="text-align: center">
                        <el-button style="margin-top: 10px;" type="success" @click="Save">保存</el-button>
                    </div>
                </div>
                <el-divider/>
                <!-- 代销 -->
                <!-- 开启/关闭单选框 -->
                <div class="toggle-section">
                    <el-radio-group v-model="state.form2.permissionOpen">
                        <el-radio label="open" size="large">
                            <span class="toggle-label">开启</span>
                        </el-radio>
                        <el-radio label="close" size="large">
                            <span class="toggle-label">关闭</span>
                        </el-radio>
                    </el-radio-group>
                </div>
                <div class="price-row">
                    <span class="price-label">开启代销权限：</span>
                    <el-input v-model="state.form2.originalPrice" class="price-input"/>
                    <span class="price-unit">￥/月</span>
                    <span class="price-label">优惠：</span>
                    <el-input v-model="state.form2.discountPrice" class="price-input"/>
                    <span class="price-unit">折扣/月</span>
                </div>
                <div style="text-align: center">
                    <el-button style="margin-top: 10px;" type="success" @click="SaveTwo">保存</el-button>
                </div>
                <el-divider/>
                <!-- 自动延续 -->
                <!-- 开启/关闭单选框 -->
                <div class="toggle-section">
                    <el-radio-group v-model="state.form3.permissionOpen">
                        <el-radio label="open" size="large">
                            <span class="toggle-label">开启</span>
                        </el-radio>
                        <el-radio label="close" size="large">
                            <span class="toggle-label">关闭</span>
                        </el-radio>
                    </el-radio-group>
                </div>
                <div class="price-row">
                    <span class="price-label">自动延续：</span>
                    <el-input v-model="state.form3.renewal" class="price-input"/>
                    <span class="price-unit">￥/月</span>
                    <span class="price-label">优惠：</span>
                    <el-input v-model="state.form3.offer" class="price-input"/>
                    <span class="price-unit">折扣/月</span>
                </div>
                <div style="text-align: center">
                    <el-button style="margin-top: 10px;" type="success" @click="SaveThree">保存</el-button>
                </div>
            </div>
        </div>
    </div>
    <!--  </HomeBg>-->
</template>
<script lang="ts" setup>
import {onMounted} from 'vue'
import {useRouter} from 'vue-router'
import {permissionSetting} from '../../stores/permissionSetting'
import {ElMessage} from 'element-plus'
import {Session} from '../../utils/storage'

const router = useRouter()
const state = reactive({
    form1: {
        permissionOpen: 'open', // 开启/关闭状态
        permission1: {
            originalPrice: '',
            discountPrice: ''
        },
        permission2: {
            originalPrice: '',
            discountPrice: ''
        },
        permission3: {
            originalPrice: '',
            discountPrice: ''
        },
        configValue: "1"
    },
    form2: {
        permissionOpen: 'open',
        configValue: "1",
        originalPrice: '',
        discountPrice: ''
    },
    form3: {
        permissionOpen: 'open',
        configValue: "1",
        renewal: "",
        offer: ""
    },
    buttonList: []
})
const handleButtonClick = (item) => {
    router.push(item.component)
}
const Save = async () => {
    let data = {"configValue": state.form1}
    let result = await permissionSetting().GetThroughList(data, 1);
    if (result.code == 200) {
        ElMessage.success('配置成功')
    } else {
        ElMessage.error('配置失败，请重新输入')
    }
}
const SaveTwo = async () => {
    let data = {"configValue": state.form2}
    let result = await permissionSetting().GetThroughList(data, 2);
    if (result.code == 200) {
        ElMessage.success('配置成功')
    } else {
        ElMessage.error('配置失败，请重新输入')
    }
}
const SaveThree = async () => {
    let data = {"configValue": state.form3}
    let result = await permissionSetting().GetThroughList(data, 3);
    if (result.code == 200) {
        ElMessage.success('配置成功')
    } else {
        ElMessage.error('配置失败，请重新输入')
    }
}
const getDataConfig = async () => {
    let result = await permissionSetting().GetThroughList(data, 3);
}
const getConfigurationData1 = async () => {
    let result = await permissionSetting().ConfigurationData(1);
    if (result.data != null) {
        state.form1 = result.data
    }
}
const getConfigurationData2 = async () => {
    let result = await permissionSetting().ConfigurationData(2);
    if (result.data != null) {
        state.form2 = result.data
    }
}
const getConfigurationData3 = async () => {
    let result = await permissionSetting().ConfigurationData(3);
    if (result.data != null) {
        state.form3 = result.data
    }
}
onMounted(() => {
    getConfigurationData1()
    getConfigurationData2()
    getConfigurationData3()
    let menuList = Session.getMenu()
    let menuId = Session.get('adminMenuId');
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index];
        if (element.menuId == menuId) {
            state.buttonList = element.children
        }
    }
})
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    width: 100%;
    max-height: 100vh;
    box-sizing: border-box;
    position: relative;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    position: relative;
    z-index: 1;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.right-content {
    flex: 1;

    position: relative;
}

.fixed-title {
    position: absolute;
    left: 20px;
    top: 20px;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    z-index: 2;
}

.main-content {
    margin-left: 225px;
    width: calc(100% - 40px);
    height: 600px;
    margin-top: 60px;
    max-width: 940px;
    background: #fff;
    border-radius: 8px;
    padding: 30px;

}

.toggle-section {
    margin-bottom: 30px;

    :deep(.el-radio) {
        margin-right: 30px;

        .el-radio__label {
            font-size: 16px;
        }
    }
}

.permission-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40px;
}

.permission-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.permission-btn {
    width: 120px;
    height: 50px;
    margin-bottom: 10px;
    background-color: #3A58CF;
    border: none;

    &:hover {
        background-color: #2a48bf;
    }
}

.product-desc {
    font-size: 14px;
    color: #666;
}

.arrow {
    font-size: 24px;
    color: #999;
    margin: 0 30px;
    // padding-top: 20px;
}

.price-form {
    width: 100%;
}

.price-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.price-label {
    width: 120px;
    font-size: 16px;
    color: #333;
}

.price-input {
    width: 120px;
    margin: 0 10px;
}

.price-unit {
    width: 60px;
    font-size: 14px;
    color: #666;
}

.down-arrow {
    margin-left: 36px;
    margin-bottom: 12px;
}
</style>
