<template>
    <!--  <HomeBg>-->
    <div class="container">
        <img alt="背景图" class="bg" src="../../images/bigBackground.png">
        <div class="form-container">
            <h2 class="form-title">创建新用户</h2>
            <UserForm
                :isEdit="false"
                :loading="state.loading"
                :menu-options="state.options"
                @cancel="goBack"
                @submit="handleSubmit"
            />
        </div>
    </div>
    <!--  </HomeBg>-->
</template>
<script lang="ts" setup>
import {onMounted, reactive} from 'vue'
import {useRouter} from 'vue-router'
import {platformUser} from '../../stores/platformUser'
import {useMessage} from '../../hooks/message';
import UserForm from '../../components/UserForm.vue'

const router = useRouter();

const state = reactive({
    loading: false,
    options: []
});

// 处理表单提交
const handleSubmit = async (formData) => {
    try {
        state.loading = true;
        console.log('提交的表单数据:', JSON.stringify(formData));

        const result: any = await platformUser().SaveUser(formData);
        if (result?.code === 200) {
            useMessage().success('创建用户成功');
            router.push('/userDdministration');
        } else {
            useMessage().error(result?.msg || '创建用户失败');
        }
    } catch (error) {
        console.error('错误详情:', error);
        useMessage().error('系统异常，请联系管理员');
    } finally {
        state.loading = false;
    }
};

// 返回用户管理页面
const goBack = () => {
    router.push('/userDdministration');
};

// 获取菜单树数据
const getMenuTreeList = async () => {
    try {
        state.loading = true;
        const result: any = await platformUser().GetMenuTreeList();
        if (result?.data) {
            state.options = result.data;
        }
    } catch (error) {
        useMessage().error('获取授权数据失败');
        console.error(error);
    } finally {
        state.loading = false;
    }
};

onMounted(() => {
    getMenuTreeList();
});
</script>
<style lang="scss" scoped>
.el-form-item__label {
    font-weight: 500;
}

.container {
    position: relative;
    width: 100%;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px 0;
}

.bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
}

.form-container {
    position: relative;
    z-index: 2;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    width: 800px;
    max-width: 90%;
}

.form-title {
    margin-bottom: 30px;
    text-align: center;
    color: #14097A;
    font-size: 24px;
}

.input-group {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
}

.form-input {
    width: 100%;

    :deep(.el-input__wrapper) {
        height: 45px;
        font-size: 15px;
    }
}

.form-buttons {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    gap: 15px;
}

.save-button {
    background-color: #14097A;

    &:hover {
        background-color: #1a0da0;
    }

    &:active {
        background-color: #0f0657;
    }
}

:deep(.el-cascader) {
    width: 100%;
}
</style>
