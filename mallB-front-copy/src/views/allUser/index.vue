<script setup>
import {nextTick, onMounted, reactive, ref} from 'vue'
import {getUserDetail, getUserList, resetUserPassword, updateUserStatus} from '../../api/user'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Session} from "@/utils/storage.js";
import {useRouter} from 'vue-router'

const buttonList = ref([])
// 表格数据
const tableData = ref([])
const total = ref(0)
const loading = ref(false)

// 用户详情弹窗
const userDetailDialog = ref(false)
const userDetail = ref({})
const userDetailLoading = ref(false)

// 查询参数
const queryParams = reactive({
    username: '',
    nickname: '',
    phone: '',
    status: '',
    startTime: '',
    endTime: '',
    pageNum: 1,
    pageSize: 10
})

// 用户状态字典
const userStatusOptions = [
    {value: '0', label: '正常'},
    {value: '1', label: '禁用'},
    {value: '3', label: '失效'},
    {value: '4', label: '睡眠'},
    {value: '5', label: '无效'}
]

// 用户类型字典
const userTypeOptions = [
    {value: 'C', label: '消费者'},
    {value: 'CB', label: '代销'},
    {value: 'B', label: '商家'}
]

// 获取用户列表
const getList = async () => {
    loading.value = true
    try {
        const res = await getUserList(queryParams)
        if (res.code === 200) {
            tableData.value = res.rows
            total.value = res.total
        } else {
            ElMessage.error(res.msg || '获取用户列表失败')
        }
    } catch (error) {
        console.error('获取用户列表失败:', error)
        ElMessage.error('获取用户列表失败')
    } finally {
        loading.value = false
    }
}

// 重置查询
const resetQuery = () => {
    queryParams.username = ''
    queryParams.nickname = ''
    queryParams.phone = ''
    queryParams.status = ''
    queryParams.startTime = ''
    queryParams.endTime = ''
    queryParams.pageNum = 1
    getList()
}

// 搜索
const handleSearch = () => {
    queryParams.pageNum = 1
    getList()
}

// 查看用户详情
const handleView = async (userId) => {
    userDetailLoading.value = true
    userDetailDialog.value = true
    try {
        const res = await getUserDetail(userId)
        if (res.code === 200) {
            userDetail.value = res.data
        } else {
            ElMessage.error(res.msg || '获取用户详情失败')
        }
    } catch (error) {
        console.error('获取用户详情失败:', error)
        ElMessage.error('获取用户详情失败')
    } finally {
        userDetailLoading.value = false
    }
}

// 修改用户状态
const handleStatusChange = async (row) => {
    const newStatus = row.status === '0' ? '1' : '0'
    const statusText = newStatus === '0' ? '启用' : '禁用'

    ElMessageBox.confirm(`确认要${statusText}该用户吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            const res = await updateUserStatus(row.userId, newStatus)
            if (res.code === 200) {
                ElMessage.success(`${statusText}成功`)
                getList() // 刷新列表
            } else {
                ElMessage.error(res.msg || `${statusText}失败`)
            }
        } catch (error) {
            console.error(`${statusText}失败:`, error)
            ElMessage.error(`${statusText}失败`)
        }
    }).catch(() => {
    })
}

// 重置密码
const handleResetPwd = (userId) => {
    ElMessageBox.confirm('确认重置该用户的密码吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        try {
            const res = await resetUserPassword(userId)
            if (res.code === 200) {
                ElMessage.success('重置密码成功')
            } else {
                ElMessage.error(res.msg || '重置密码失败')
            }
        } catch (error) {
            console.error('重置密码失败:', error)
            ElMessage.error('重置密码失败')
        }
    }).catch(() => {
    })
}

// 处理分页变化
const handleSizeChange = (val) => {
    queryParams.pageSize = val
    getList()
}

const handleCurrentChange = (val) => {
    queryParams.pageNum = val
    getList()
}

// 格式化状态
const formatStatus = (status) => {
    const statusMap = {
        '0': '正常',
        '1': '禁用',
        '2': '删除',
        '3': '失效',
        '4': '睡眠',
        '5': '无效'
    }
    return statusMap[status] || status
}

// 格式化用户类型
const formatUserType = (type) => {
    const typeMap = {
        'C': '消费者',
        'CB': '代销',
        'B': '商家'
    }
    return typeMap[type] || type
}
const router = useRouter()
const handleButtonClick = (item) => {
    router.push(item.component)
}

onMounted(() => {
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                buttonList.value = element.children
            }
        }
    })
    getList()
})
</script>

<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="rightBox">
            <div class="head">
                <span class="search">商城用户管理</span>
            </div>
            <div class="main">
                <!-- 搜索区域 -->
                <div class="search-box">
                    <div class="search-row">
                        <el-input
                            v-model="queryParams.username"
                            class="search-input"
                            clearable
                            placeholder="用户名"
                            @keyup.enter="handleSearch"
                        />
                        <el-input
                            v-model="queryParams.nickname"
                            class="search-input"
                            clearable
                            placeholder="昵称"
                            @keyup.enter="handleSearch"
                        />
                        <el-input
                            v-model="queryParams.phone"
                            class="search-input"
                            clearable
                            placeholder="手机号"
                            @keyup.enter="handleSearch"
                        />
                        <el-select v-model="queryParams.status" class="search-input" clearable placeholder="状态">
                            <el-option
                                v-for="dict in userStatusOptions"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            />
                        </el-select>
                    </div>
                    <div class="search-row">
                        <el-date-picker
                            v-model="queryParams.startTime"
                            class="date-picker"
                            placeholder="注册开始时间"
                            type="datetime"
                            value-format="YYYY-MM-DD HH:mm:ss"
                        />
                        <span class="to-text">至</span>
                        <el-date-picker
                            v-model="queryParams.endTime"
                            class="date-picker"
                            placeholder="注册结束时间"
                            type="datetime"
                            value-format="YYYY-MM-DD HH:mm:ss"
                        />
                        <el-button class="search-btn" type="primary" @click="handleSearch">搜索</el-button>
                        <el-button class="search-btn" @click="resetQuery">重置</el-button>
                    </div>
                </div>

                <!-- 用户表格 -->
                <el-table
                    v-loading="loading"
                    :data="tableData"
                    border
                    class="data-table"
                    stripe
                >
                    <!--          <el-table-column prop="userId" label="用户ID"  />-->
                    <el-table-column label="用户名" prop="username"/>
                    <el-table-column label="昵称" prop="nickname"/>
                    <el-table-column label="手机号" prop="phone"/>
                    <el-table-column label="邮箱" prop="email"/>
                    <!--          <el-table-column prop="userType" label="用户类型">-->
                    <!--            <template #default="scope">-->
                    <!--              {{ formatUserType(scope.row.userType) }}-->
                    <!--            </template>-->
                    <!--          </el-table-column>-->
                    <el-table-column label="补贴金" prop="deductionMoney"/>
                    <el-table-column label="分量" prop="component"/>
                    <el-table-column label="状态" prop="status">
                        <template #default="scope">
                            {{ formatStatus(scope.row.status) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="注册时间" prop="createTime"/>
                    <el-table-column fixed="right" label="操作" width="200">
                        <template #default="scope">
                            <el-button link type="primary" @click="handleView(scope.row.userId)">详情</el-button>
                            <el-button
                                v-if="scope.row.status === '0' || scope.row.status === '1'"
                                :type="scope.row.status === '0' ? 'danger' : 'success'"
                                link
                                @click="handleStatusChange(scope.row)"
                            >
                                {{ scope.row.status === '0' ? '禁用' : '启用' }}
                            </el-button>
<!--                            <el-button link type="warning" @click="handleResetPwd(scope.row.userId)">重置密码-->
<!--                            </el-button>-->
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <div class="pagination">
                    <el-pagination
                        v-model:current-page="queryParams.pageNum"
                        v-model:page-size="queryParams.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>

        <!-- 用户详情弹窗 -->
        <el-dialog
            v-model="userDetailDialog"
            append-to-body
            title="用户详情"
            width="600px"
        >
            <el-descriptions v-loading="userDetailLoading" :column="2" border>
                <!--        <el-descriptions-item label="用户ID">{{ userDetail.userId }}</el-descriptions-item>-->
                <el-descriptions-item label="用户名">{{ userDetail.username }}</el-descriptions-item>
                <el-descriptions-item label="昵称">{{ userDetail.nickname }}</el-descriptions-item>
                <el-descriptions-item label="手机号">{{ userDetail.phone }}</el-descriptions-item>
                <el-descriptions-item label="邮箱">{{ userDetail.email }}</el-descriptions-item>
                <!--        <el-descriptions-item label="用户类型">{{ formatUserType(userDetail.userType) }}</el-descriptions-item>-->
                <el-descriptions-item label="补贴金">{{ userDetail.deductionMoney }}</el-descriptions-item>
                <el-descriptions-item label="补贴金上限">{{ userDetail.deductionMoneyLimit }}</el-descriptions-item>
                <el-descriptions-item label="状态">{{ formatStatus(userDetail.status) }}</el-descriptions-item>
                <el-descriptions-item label="注册时间">{{ userDetail.createTime }}</el-descriptions-item>
                <!--        <el-descriptions-item label="邀请码">{{ userDetail.InvitationCode }}</el-descriptions-item>-->
                <el-descriptions-item label="绑定邀请码">{{ userDetail.bindingInvitationCode }}</el-descriptions-item>
            </el-descriptions>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="userDetailDialog = false">关闭</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}


.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-right: 20px;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.rightBox {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.head {
    margin-bottom: 20px;

    .search {
        font-size: 24px;
        font-weight: bold;
        color: #333;
    }
}

.main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.search-box {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.search-row {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    &:last-child {
        margin-bottom: 0;
    }
}

.search-input {
    width: 200px;
    margin-right: 15px;
}

.date-picker {
    width: 180px;
}

.to-text {
    margin: 0 10px;
    color: #606266;
}

.search-btn {
    margin-left: 15px;
}

.table-info {
    margin-bottom: 15px;
    font-size: 14px;
    color: #606266;
}

.data-table {
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

.ip-info {
    margin-top: 5px;
    padding: 5px;
    background: #f5f7fa;
    border-radius: 4px;
    font-size: 12px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}
</style>
