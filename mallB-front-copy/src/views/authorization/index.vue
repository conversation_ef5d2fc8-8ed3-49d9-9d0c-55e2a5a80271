<template>
    <!--    <HomeBg>-->
    <div class="page-container">
        <!-- 侧边栏 -->
        <PowerSider></PowerSider>
        <!-- 右边内容 -->

        <div class="main-content">
            <!-- 您的主内容区域 -->
            <div class="content">11</div>
        </div>
    </div>
    <!--    </HomeBg>-->
</template>
<script lang="ts" setup>
import PowerSider from "../../components/PowerSider.vue";
import {computed, reactive, ref} from "vue";
import {ElMessage} from "element-plus";
import {useRouter} from "vue-router";

const radio1 = ref('1')
const router = useRouter();
const handleToMenu = () => {
    router.push({
        path: "/constructMenu",
    });
};
//添加数据
const inputValue = ref("");
const inputValue1 = ref("");


const state = reactive({
    displayList: [
        {content: '内容1', time: '2025-05-29'},
        {content: '内容2', time: '2025-05-30'},
    ]
});
// 搜索条件
const searchId = ref("");
const startDate = ref("");
const endDate = ref("");

// 格式化日期显示
const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
};
// 删除方法
const displayList = ref([]);

function handleDelete(index) {
    displayList.value.splice(index, 1);
}


// 过滤后的数据
const filteredData = computed(() => {
    return originalData.value.filter((item) => {
        // 工号筛选
        const idMatch = searchId.value === "" || item.id.includes(searchId.value);

        // 时间范围筛选
        let dateMatch = true;
        if (startDate.value || endDate.value) {
            const joinDate = new Date(item.joinDate);
            const start = startDate.value ? new Date(startDate.value) : null;
            const end = endDate.value ? new Date(endDate.value) : null;

            if (start && joinDate < start) dateMatch = false;
            if (end && joinDate > end) dateMatch = false;
        }

        return idMatch && dateMatch;
    });
});
const addItem = () => {
    if (!inputValue.value.trim()) {
        ElMessage.warning("请输入内容");
        return;
    }

    displayList.value.push({
        content: inputValue.value,
        time: new Date().toLocaleString(),
    });

    inputValue.value = ""; // 清空输入框
    ElMessage.success("添加成功");
};

const removeItem = (index) => {
    displayList.value.splice(index, 1);
    ElMessage.success("删除成功");
};
// 表格数据
// const tableData = ref([]);
// 分页
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(100);

// 搜索功能
const handleSearch = async () => {
    try {
        // let result=await platformMenu().GetMenuList();   // 调用登录方法
        //tableData.value =result.data
    } finally {
    }
};
//

const handleSearchs = () => {
    router.push({
        path: "/updateMenu",
        query: {
            id: "123",
            name: "john",
        },
    });
};
const handleButtonClick = (item) => {
    // if(item==="基础设置") router.push("/roleList")
    if (item === "系统设置") router.push("/systemSet");
    if (item === "系统查询") router.push("/systemQuery");
};

const buttonList = ["中南惠企业"];
const buttonList1 = ["交易数据明细", "关系链", "量化数"];
const searchForm = ref({
    startTime: "",
    endTime: "",
    dailyData: "",
    remainder: "",
    remainderCount: "",
    Dailymeasure: "",
});
// 导出Excel
const exportToExcel = () => {
    alert("导出成功");
};
</script>
<style lang="scss" scoped>
.page-container {
    // position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    margin: 0;
    // border: 1px solid;
    .content {
        width: 100px;
        width: 100px;
        background-color: red;
    }


}
</style>
