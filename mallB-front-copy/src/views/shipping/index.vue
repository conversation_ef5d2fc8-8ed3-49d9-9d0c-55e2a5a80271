<template>
    <div class="shipping-container">
        <div class="shipping-card">
            <div class="card-header">
                <h2 class="title">订单发货</h2>
            </div>
            <!-- 订单信息 -->
            <div v-if="orderInfo" class="order-info">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="订单编号">{{ orderInfo.orderNo }}</el-descriptions-item>
                    <el-descriptions-item label="订单状态">
                        <el-tag :type="orderInfo.status === '2' ? 'warning' : 'info'">
                            {{ orderInfo.status === '2' ? '待发货' : getOrderStatus(orderInfo.status) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="收货人">{{ orderInfo.receiverName }}</el-descriptions-item>
                    <el-descriptions-item label="联系电话">{{ orderInfo.receiverPhone }}</el-descriptions-item>
                    <el-descriptions-item label="商品名称">{{ orderInfo.orderName }}</el-descriptions-item>
                    <el-descriptions-item label="商品数量">{{ orderInfo.totalQuantity }}</el-descriptions-item>
                    <el-descriptions-item label="快递方式">{{
                            orderInfo.receiverType == 1 ? "快递配送" : "自提"
                        }}
                    </el-descriptions-item>
                    <el-descriptions-item label="支付金额">{{ orderInfo.payAmount }}</el-descriptions-item>
                    <el-descriptions-item :span="2" label="收货地址">
                        {{ getFullAddress(orderInfo) }}
                    </el-descriptions-item>
                </el-descriptions>
            </div>

            <!-- 发货表单 -->
            <div class="shipment-form">
                <el-form ref="shipmentFormRef" :model="state.form" :rules="rules" label-position="top">
                    <el-form-item label="配送方式" prop="receiverType">
                        <el-select
                            v-model="state.form.receiverType"
                            class="form-select"
                            clearable
                            filterable
                            placeholder="配送方式"
                        >
                            <el-option :value="1" label="快递配送"/>
                            <el-option :value="2" label="无需配送"/>
                        </el-select>
                    </el-form-item>

                    <el-form-item v-if="state.form.receiverType == 1" label="快递公司" prop="deliveryId">
                        <el-select
                            v-model="state.form.deliveryId"
                            :loading="loading.companies"
                            class="form-select"
                            clearable
                            filterable
                            placeholder="请选择快递公司"
                        >
                            <el-option
                                v-for="item in state.deliveryCompanyList"
                                :key="item.id"
                                :label="item.deliveryName"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item v-if="state.form.receiverType == 1" label="快递单号" prop="deliveryNumber">
                        <el-input
                            v-model="state.form.deliveryNumber"
                            class="form-input"
                            clearable
                            placeholder="请输入快递单号"
                        />
                    </el-form-item>

                    <el-form-item>
                        <div class="form-buttons">
                            <el-button @click="handleCancel">取消</el-button>
                            <el-button :loading="loading.submit" type="primary" @click="handleSubmit">
                                <el-icon>
                                    <Van/>
                                </el-icon>
                                确认发货
                            </el-button>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import {useRoute, useRouter} from 'vue-router'
import {onMounted, reactive, ref} from 'vue'
import {shipping} from '../../stores/shipping'
import {allOrders} from '../../stores/allOrders'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Van} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const shipmentFormRef = ref()

// 订单信息
const orderInfo = ref<any>(null)

// 加载状态
const loading = reactive({
    companies: false,
    submit: false,
    orderInfo: false
})

// 表单验证规则
const rules = {
    receiverType: [
        {required: true, message: '请选择配送方式', trigger: 'change'}
    ],
    deliveryId: [
        {required: true, message: '请选择快递公司', trigger: 'change'}
    ],
    deliveryNumber: [
        {required: true, message: '请输入快递单号', trigger: 'blur'},
        {min: 5, message: '快递单号长度不能小于5个字符', trigger: 'blur'}
    ]
}

const state = reactive({
    deliveryCompanyList: [] as any[],
    form: {
        orderNo: route.query.orderNo as string,
        receiverType: 1,
        deliveryId: '',
        deliveryNumber: ''
    }
})

// 获取订单状态文本
const getOrderStatus = (status: string) => {
    const statusMap: Record<string, string> = {
        '0': '待支付',
        '1': '支付中',
        '2': '待发货',
        '3': '待收货',
        '4': '已完成',
        '5': '已取消',
        '6': '已退款'
    }
    return statusMap[status] || '未知状态'
}

// 获取完整地址
const getFullAddress = (order: any) => {
    if (!order) return ''
    const province = order.receiverProvince || ''
    const city = order.receiverCity || ''
    const district = order.receiverDistrict || ''
    const address = order.receiverAddress || ''
    return `${province}${city}${district}${address}`
}

// 获取订单详情
const getOrderInfo = async () => {
    if (!state.form.orderNo) {
        ElMessage.error('订单编号不能为空')
        return
    }

    loading.orderInfo = true
    try {
        const result = await allOrders().GetOrderDetail(state.form.orderNo)
        if (result && result.code === 200) {
            orderInfo.value = result.data

            // 检查订单状态是否为待发货
            if (orderInfo.value && orderInfo.value.status !== '2') {
                ElMessage.warning('当前订单状态不是待发货状态，无法进行发货操作')
            }
        } else {
            ElMessage.error(result?.msg || '获取订单信息失败')
        }
    } catch (error) {
        console.error('获取订单信息失败:', error)
        ElMessage.error('获取订单信息失败')
    } finally {
        loading.orderInfo = false
    }
}

// 获取快递公司列表
const getDeliveryCompanies = async () => {
    loading.companies = true
    try {
        const result = await shipping().DeliveryCompany({})
        if (result && result.rows) {
            state.deliveryCompanyList = result.rows
        } else {
            ElMessage.error('获取快递公司列表失败')
        }
    } catch (error) {
        console.error('获取快递公司列表失败:', error)
        ElMessage.error('获取快递公司列表失败')
    } finally {
        loading.companies = false
    }
}

// 提交发货
const handleSubmit = async () => {
    if (!shipmentFormRef.value) return

    shipmentFormRef.value.validate(async (valid: boolean) => {
        if (!valid) return

        // 确认发货
        ElMessageBox.confirm(
            '确认为该订单发货吗？',
            '发货确认',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
            }
        ).then(async () => {
            loading.submit = true
            try {
                const result = await shipping().ShipmentOder(state.form)
                if (result && result.code === 200) {
                    ElMessage.success('订单发货成功')
                    router.push('/allOrders')
                }
            } catch (error) {
                console.error('订单发货失败:', error)
                ElMessage.error('订单发货失败')
            } finally {
                loading.submit = false
            }
        }).catch(() => {
            // 用户取消操作
        })
    })
}

// 取消发货
const handleCancel = () => {
    ElMessageBox.confirm(
        '确认取消发货操作吗？',
        '取消确认',
        {
            confirmButtonText: '确认',
            cancelButtonText: '返回',
            type: 'info',
        }
    ).then(() => {
        router.push('/allOrders')
    }).catch(() => {
        // 用户取消操作
    })
}

onMounted(() => {
    if (!state.form.orderNo) {
        ElMessage.error('订单编号不能为空')
        router.push('/allOrders')
        return
    }

    getDeliveryCompanies()
    getOrderInfo()
})
</script>

<style lang="scss" scoped>
.shipping-container {
    padding: 20px;
    min-height: 100%;
    background-color: #f5f7fa;
    display: flex;
    justify-content: center;
}

.shipping-card {
    width: 100%;
    max-width: 800px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.card-header {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 20px;
    margin-bottom: 20px;

    .title {
        font-size: 20px;
        font-weight: 600;
        color: #303133;
        margin: 0;
    }
}

.order-info {
    margin-bottom: 30px;

    :deep(.el-descriptions__label) {
        width: 120px;
        font-weight: bold;
    }
}

.shipment-form {
    .form-select,
    .form-input {
        width: 100%;
    }

    :deep(.el-form-item__label) {
        font-weight: bold;
    }
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}
</style>
