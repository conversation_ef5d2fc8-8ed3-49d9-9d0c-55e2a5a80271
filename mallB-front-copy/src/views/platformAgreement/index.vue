<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="right-content">
            <!-- 固定标题 - 参考技术引流样式 -->
            <div class="main-content">
                <div style="width: 340px">
                    <div class="add-protocol">
                        <el-input
                            v-model="state.inputValue"
                            class="protocol-input"
                            placeholder="自定义协议标题"
                            style="width: 200px"
                        />
                        <el-button class="add-btn" size="small" type="primary" @click="add()">添加</el-button>
                    </div>
                    <div class="grid-warp">
                        <el-scrollbar height="600">
                            <div class="grid-content-list">
                                <el-row :gutter="40" style="display: flex">
                                    <el-col v-for="(item,index) in dataList" :key="index" :span="24" class="big-col">
                                        <div class="scrollbar-demo-item">
                                            <el-button type="text" @click="getInfo(item)">{{ item.name }}</el-button>
                                            <span class="delete" @click="deleteById(item)">X</span>
                                        </div>
                                    </el-col>
                                </el-row>
                            </div>
                        </el-scrollbar>
                    </div>
                </div>
                <div>
                    <div class="upload-container">
                        <div class="text-content" @copy.prevent>
                            <div class="editor-container">
                                <Editor
                                    ref="editorRef"
                                    v-model="state.form.value"
                                    :height="500"
                                    @error="handleEditorError"
                                    @update:modelValue="handleEditorChange"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="save">
                        <el-button type="primary" @click="submit()">保存</el-button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import {nextTick, onMounted, reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {platformAgreement} from '../../stores/platformAgreement'
import {Session} from "@/utils/storage";
import {ElMessage, ElMessageBox} from "element-plus";
import Editor from '@/components/Editor.vue';

// 左侧菜单
const router = useRouter()
const state = reactive({
    buttonList: [],
    inputValue: '',
    form: {
        id: '',
        name: '',
        value: ''
    }
})
const handleButtonClick = (item) => {
    router.push(item.component)
}

const dataList = ref([])

const deleteById = (row) => {
    ElMessageBox.confirm(`确定删除 ${row.name} 的协议信息?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        deleteBid(row.id)
    })
}
const deleteBid = async (id) => {
    const result = await platformAgreement().Delete(id)
    if (result.code == 200) {
        ElMessage.success("删除成功");
        getList();
    } else {
        ElMessage.error(result.msg);
    }
    clean()
}

const getInfo = async (item) => {
    const result = await platformAgreement().GetInfo(item.id)
    if (result.code == 200) {
        state.form = result.data;
    } else {
        ElMessage.error(result.msg);
    }
}
const submit = async () => {
    try {
        if (!state.form.id || !state.form.name) {
            ElMessage.warning('请先选择一个协议');
            return;
        }

        const result = await platformAgreement().Update(state.form)
        if (result.code == 200) {
            ElMessage.success("修改成功");
            clean();
            getList();
        } else {
            ElMessage.error(result.msg || '保存失败');
        }
    } catch (error) {
        console.error('保存协议错误:', error);
        ElMessage.error('保存协议失败，请重试');
    }
}

const add = async () => {
    let data = {
        name: state.inputValue
    }
    const result = await platformAgreement().Add(data)
    if (result.code == 200) {
        ElMessage.success("添加成功");
        clean();
        getList();
    }
}
const clean = () => {
    state.inputValue = "";
    state.form.value = null;
}
const getList = () => {
    platformAgreement().List().then((result) => {
        dataList.value = result.rows;
    })
}

// 添加编辑器内容变更处理函数
const handleEditorChange = (content) => {
    // 确保不会传递null值给state
    state.form.value = content || '';
}

// 添加编辑器错误处理函数
const handleEditorError = (error) => {
    console.error('编辑器错误:', error);
    ElMessage.error('编辑器出现错误，请刷新页面重试');
}

onMounted(() => {
    nextTick(() => {
        let menuList = Session.getMenu()
        let menuId = Session.get('adminMenuId');
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
        getList();
    })
})
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    width: 100%;
    max-height: 100vh;
    box-sizing: border-box;
    position: relative;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    position: relative;
    z-index: 1;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.right-content {
    flex: 1;
    padding: 20px 20px 20px 0px;
    position: relative;
}

/* 固定标题样式 - 参考技术引流 */
.fixed-title {
    position: absolute;
    margin-left: 20px;
    top: 20px;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    z-index: 2;
}

.main-content {
    display: flex;
    width: 100%;
    background: #fff;
    border-radius: 8px;
    padding: 30px;
}

.cascader-selector {
    width: 200px;
    margin-bottom: 20px;
}

.protocol {
    width: 100%;
    background-color: yellow;
}

.protocol-form {
    width: 50%;
    margin-bottom: 20px;
    border: 1px solid red;
}

.dataList {
    width: 50%;
    background-color: yellowgreen;
}

.protocol-input {
    width: 100%;
}

.upload-container {
    margin-left: 10px;
    width: 100%;
    height: calc(100% - 120px);
}

.add-btn {
    background-color: #3A58CF;
    border: none;
    margin-left: 10px;

    &:hover {
        background-color: #2a48bf;
    }
}

.upload-btn {
    background-color: #3A58CF;
    border: none;

    &:hover {
        background-color: #2a48bf;
    }
}

.text-content {
    height: calc(100% - 60px);
}

.protocol-textarea {
    width: 100%;
    height: 100%;
}

.editor-container {
    height: 100%;
    min-height: 300px;
    border-radius: 4px;
}

.grid-warp {
    margin-top: 20px;
}

.grid-content-list {
    overflow: scroll;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }
}

.big-col {
    margin-right: 10px;
}

.delete {
    vertical-align: middle;
    margin-left: 10px;
    cursor: pointer;
}

.scrollbar-demo-item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    line-height: 40px;
    margin: 5px;
    text-align: center;
    border-radius: 4px;
    background: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
    position: relative;

    .delete {
        position: absolute;
        right: 10px;
        font-size: 12px;
    }
}

.add-protocol {
    margin-left: 10px;
}

.save {
    margin-top: 30px;
    text-align: center;
}
</style>
