<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="main">
            <!-- 搜索表单 -->
            <div class="search-form">
                <el-form ref="queryRef" :inline="true" :model="queryParams" label-width="80px">
                    <el-form-item label="真实姓名" prop="realName">
                        <el-input
                            v-model="queryParams.realName"
                            clearable
                            placeholder="请输入真实姓名"
                            style="width: 200px"
                        />
                    </el-form-item>
                    <el-form-item label="身份证号" prop="idCard">
                        <el-input
                            v-model="queryParams.idCard"
                            clearable
                            placeholder="请输入身份证号"
                            style="width: 200px"
                        />
                    </el-form-item>
                    <el-form-item label="审核状态" prop="status">
                        <el-select v-model="queryParams.status" clearable placeholder="请选择审核状态"
                                   style="width: 200px">
                            <el-option label="审核中" value="0"/>
                            <el-option label="审核通过" value="1"/>
                            <el-option label="审核拒绝" value="2"/>
                            <el-option label="重新提交" value="3"/>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 数据表格 -->
            <div class="table-container">
                <el-table v-loading="loading" :data="realNameList" @selection-change="handleSelectionChange">
                    <!--                    <el-table-column type="selection" width="55" align="center" />-->
                    <el-table-column align="center" label="用户名" prop="username"/>
                    <el-table-column align="center" label="昵称" prop="nickname"/>
                    <el-table-column align="center" label="手机号" prop="phone"/>
                    <el-table-column align="center" label="真实姓名" prop="realName"/>
                    <el-table-column :show-overflow-tooltip="true" align="center" label="身份证号" prop="idCard"/>
                    <el-table-column align="center" label="审核状态" prop="status">
                        <template #default="scope">
                            <el-tag
                                :type="scope.row.status === '1' ? 'success' : scope.row.status === '2' ? 'danger' : 'warning'"
                            >
                                {{ scope.row.statusText }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column align="center" label="申请时间" prop="createTime" width="180"/>
                    <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
                        <template #default="scope">
                            <el-button
                                v-if="scope.row.status === '0'"
                                icon="View"
                                type="primary"
                                @click="handleView(scope.row)"
                            >
                                审核
                            </el-button>
                            <el-button
                                v-else
                                icon="View"
                                type="info"
                                @click="handleView(scope.row)"
                            >
                                查看
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <el-pagination
                    v-show="total > 0"
                    :current-page="queryParams.pageNum"
                    :page-size="queryParams.pageSize"
                    :page-sizes="[10, 20, 30, 50]"
                    :total="total"
                    layout="total, sizes, prev, pager, next, jumper"
                    style="float: right"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>

        <!-- 审核对话框 -->
        <el-dialog v-model="open" :title="dialogTitle" append-to-body width="800px">
            <div v-if="currentRealName">
                <!-- 用户基本信息 -->
                <el-descriptions :column="2" border title="用户基本信息">
                    <el-descriptions-item label="用户名">{{ currentRealName.username }}</el-descriptions-item>
                    <el-descriptions-item label="昵称">{{ currentRealName.nickname }}</el-descriptions-item>
                    <el-descriptions-item label="手机号">{{ currentRealName.phone }}</el-descriptions-item>
                    <el-descriptions-item label="申请时间">{{ currentRealName.createTime }}</el-descriptions-item>
                </el-descriptions>

                <!-- 实名信息 -->
                <el-descriptions :column="2" border style="margin-top: 20px" title="实名认证信息">
                    <el-descriptions-item label="真实姓名">{{ currentRealName.realName }}</el-descriptions-item>
                    <el-descriptions-item label="身份证号">{{ currentRealName.idCard }}</el-descriptions-item>
                    <el-descriptions-item label="省份">{{ currentRealName.province }}</el-descriptions-item>
                    <el-descriptions-item label="城市">{{ currentRealName.city }}</el-descriptions-item>
                    <el-descriptions-item label="区域">{{ currentRealName.district }}</el-descriptions-item>
                    <el-descriptions-item label="街道">{{ currentRealName.town }}</el-descriptions-item>
                    <el-descriptions-item :span="2" label="详细地址">{{
                            currentRealName.address
                        }}
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 证件照片 -->
                <div style="margin-top: 20px">
                    <h4>证件照片</h4>
                    <div style="display: flex; gap: 20px; margin-top: 10px">
                        <div>
                            <p>身份证正面</p>
                            <el-image
                                v-if="currentRealName.idCardFront"
                                :preview-src-list="[getImageUrl(currentRealName.idCardFront)]"
                                :src="getImageUrl(currentRealName.idCardFront)"
                                fit="cover"
                                style="width: 200px; height: 120px"
                            />
                            <div v-else
                                 style="width: 200px; height: 120px; background: #f5f5f5; display: flex; align-items: center; justify-content: center">
                                暂无图片
                            </div>
                        </div>
                        <div>
                            <p>身份证反面</p>
                            <el-image
                                v-if="currentRealName.idCardBack"
                                :preview-src-list="[getImageUrl(currentRealName.idCardBack)]"
                                :src="getImageUrl(currentRealName.idCardBack)"
                                fit="cover"
                                style="width: 200px; height: 120px"
                            />
                            <div v-else
                                 style="width: 200px; height: 120px; background: #f5f5f5; display: flex; align-items: center; justify-content: center">
                                暂无图片
                            </div>
                        </div>
                        <div>
                            <p>手持身份证</p>
                            <el-image
                                v-if="currentRealName.idCardHand"
                                :preview-src-list="[getImageUrl(currentRealName.idCardHand)]"
                                :src="getImageUrl(currentRealName.idCardHand)"
                                fit="cover"
                                style="width: 200px; height: 120px"
                            />
                            <div v-else
                                 style="width: 200px; height: 120px; background: #f5f5f5; display: flex; align-items: center; justify-content: center">
                                暂无图片
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 审核表单 -->
                <div v-if="currentRealName.status === '0'" style="margin-top: 20px">
                    <h4>审核操作</h4>
                    <el-form ref="auditRef" :model="auditForm" label-width="80px">
                        <el-form-item :rules="[{ required: true, message: '请选择审核结果', trigger: 'change' }]" label="审核结果"
                                      prop="status">
                            <el-radio-group v-model="auditForm.status">
                                <el-radio label="1">通过</el-radio>
                                <el-radio label="2">拒绝</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="auditForm.status === '2'" :rules="[{ required: true, message: '请填写拒绝原因', trigger: 'blur' }]" label="审核意见"
                                      prop="reason">
                            <el-input
                                v-model="auditForm.reason"
                                :rows="3"
                                placeholder="请填写拒绝原因"
                                type="textarea"
                            />
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 审核历史 -->
                <div v-if="currentRealName.status !== '0'" style="margin-top: 20px">
                    <h4>审核信息</h4>
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="审核状态">
                            <el-tag :type="currentRealName.status === '1' ? 'success' : 'danger'">
                                {{ currentRealName.statusText }}
                            </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="审核时间">{{ currentRealName.auditTime }}</el-descriptions-item>
                        <el-descriptions-item v-if="currentRealName.reason" :span="2" label="审核意见">
                            {{ currentRealName.reason }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancel">取消</el-button>
                    <el-button
                        v-if="currentRealName && currentRealName.status === '0'"
                        type="primary"
                        @click="submitAudit"
                    >
                        确定
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import {nextTick, onMounted, reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {Session} from '../../utils/storage'
import {ElMessage} from 'element-plus'
import {auditUserRealName, getUserRealName, listUserRealName} from '@/api/mall/userRealName'
import {getImageUrl} from "@/utils/common.js";

const router = useRouter()

// 响应式数据
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const showSearch = ref(true)
const total = ref(0)
const realNameList = ref([])
const open = ref(false)
const dialogTitle = ref("")
const currentRealName = ref(null)

// 查询参数
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    realName: null,
    idCard: null,
    status: null
})

// 审核表单
const auditForm = ref({
    id: null,
    status: null,
    reason: null
})

// 左侧按钮状态
const state = reactive({
    buttonList: []
})

// 表单引用
const queryRef = ref()
const auditRef = ref()

/** 查询实名认证列表 */
function getList() {
    loading.value = true
    listUserRealName(queryParams.value).then(response => {
        realNameList.value = response.rows
        total.value = response.total
        loading.value = false
    })
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1
    getList()
}

/** 重置按钮操作 */
function resetQuery() {
    queryRef.value.resetFields()
    handleQuery()
}

/** 分页大小改变 */
function handleSizeChange(newSize) {
    queryParams.value.pageSize = newSize
    getList()
}

/** 当前页改变 */
function handleCurrentChange(newPage) {
    queryParams.value.pageNum = newPage
    getList()
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id)
    single.value = selection.length !== 1
    multiple.value = !selection.length
}

/** 查看/审核按钮操作 */
function handleView(row) {
    loading.value = true
    const realNameId = row.id
    getUserRealName(realNameId).then(response => {
        currentRealName.value = response.data
        auditForm.value = {
            id: realNameId,
            status: null,
            reason: null
        }
        open.value = true
        dialogTitle.value = row.status === '0' ? "实名认证审核" : "实名认证详情"
        loading.value = false
    })
}

/** 提交审核 */
function submitAudit() {
    auditRef.value.validate(valid => {
        if (valid) {
            auditUserRealName(auditForm.value).then(response => {
                ElMessage.success("审核成功")
                open.value = false
                getList()
            })
        }
    })
}

/** 取消按钮 */
function cancel() {
    open.value = false
    auditForm.value = {
        id: null,
        status: null,
        reason: null
    }
}

/** 左侧按钮点击 */
function handleButtonClick(item) {
    router.push(item.component)
}

/** 初始化 */
onMounted(() => {
    getList()

    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId')
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index]
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index]
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    height: 100vh;
    box-sizing: border-box;
    gap: 20px;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.main {
    flex: 1;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 20px;
    overflow-y: auto;
}

.search-form {
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}


.table-container {
    :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;

        .el-table__cell {
            padding: 12px 0;
        }
    }
}

.dialog-footer {
    text-align: right;
}

:deep(.el-descriptions__title) {
    font-weight: bold;
    margin-bottom: 10px;
}

:deep(.el-pagination) {
    margin-top: 20px;
    text-align: center;
}
</style>
