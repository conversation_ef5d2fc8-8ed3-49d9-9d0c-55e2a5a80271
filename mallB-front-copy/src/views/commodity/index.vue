<template>
    <div class="data-container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="middle-container">
            <div class="page-header">
                <div class="breadcrumb">
                    <el-breadcrumb separator="/">
                        <el-breadcrumb-item :to="{ path: '/productList' }">商品管理</el-breadcrumb-item>
                        <el-breadcrumb-item>{{ isEdit ? '编辑商品' : '添加商品' }}</el-breadcrumb-item>
                    </el-breadcrumb>
                </div>
                <h2 class="page-title">{{ isEdit ? '编辑商品' : '添加商品' }}</h2>
            </div>

            <!-- 商品基本信息 -->
            <div class="product-section">
                <div class="section-header">
                    <h3>商品基本信息</h3>
                </div>
                <div class="section-content">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <div class="info-item">
                                <span class="info-label">商品名称:</span>
                                <el-input v-model="state.commodity.name" placeholder="请输入商品名称"/>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="info-item">
                                <span class="info-label">商品单位:</span>
                                <el-input v-model="state.commodity.unit" placeholder="请输入商品单位，如：个、件、箱"/>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="info-item">
                                <span class="info-label">商品类型:</span>
                                <el-radio-group v-model="state.commodity.type">
                                    <el-radio label="0">普通商品</el-radio>
                                    <el-radio label="1">虚拟商品</el-radio>
                                </el-radio-group>
                            </div>
                        </el-col>
                    </el-row>

                    <div class="info-item category-container">
                        <span class="info-label">商品分类:</span>
                        <div class="category-selects">
                            <el-select :disabled="shopDefaultCategoryId" v-model="state.value1" class="category-select" placeholder="一级分类"
                                       @change="handleGetCategory">
                                <el-option
                                    v-for="item in state.levelArray"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                            <el-select :disabled="shopDefaultCategoryId" v-model="state.value2" class="category-select" placeholder="二级分类"
                                       @change="handleGetCategoryTwo">
                                <el-option
                                    v-for="item in state.Level2Array"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                            <el-select :disabled="shopDefaultCategoryId" v-model="state.commodity.categoryId" class="category-select"
                                       placeholder="三级分类">
                                <el-option
                                    v-for="item in state.Level3Array"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                            <el-button type="primary" @click="dialogAddCategoryHandle">添加分类</el-button>
                        </div>
                    </div>

                    <el-form-item style="display: flex;">
                        <div style="display: flex; justify-content: space-between; width: 100%;">
                            <div style="font-size: 14px; color: #86ebe4">
                                第三方支付手续费默认商家承担
                            </div>
                            <div style="font-size: 12px; color: rgb(197, 209, 232); cursor: pointer;"
                                 @click="state.feeBearerDialog = true">
                                更改
                            </div>
                        </div>
                    </el-form-item>
                </div>
            </div>

            <!-- 商品媒体 -->
            <div class="product-section">
                <div class="section-header">
                    <h3>商品媒体</h3>
                </div>
                <div class="section-content">
                    <div class="media-item">
                        <span class="info-label">商品视频:</span>
                        <div class="upload-container">
                            <el-upload
                                :action="UPLOAD_URL"
                                :before-upload="beforeUploadVideo"
                                :file-list="videoFileList"
                                :headers="headers"
                                :on-error="handleVideoError"
                                :on-progress="handleVideoProgress"
                                :on-success="handleVideoSuccess"
                                accept="video/*"
                                class="upload-demo"
                                drag
                            >
                                <el-icon class="el-icon--upload">
                                    <upload-filled/>
                                </el-icon>
                                <div class="el-upload__text">
                                    将文件拖到此处 <em>或点击上传</em>
                                </div>
                                <template #tip>
                                    <div class="el-upload__tip">
                                        请上传MP4、MOV等视频格式文件，且不超过5MB
                                    </div>
                                </template>
                            </el-upload>
                            <div v-if="state.commodity.video && !videoUploading" class="video-preview">
                                <video controls width="300">
                                    <source :src="getVideoUrl(state.commodity.video)" type="video/mp4">
                                    您的浏览器不支持视频播放
                                </video>
                                <el-button class="remove-video-btn" size="small" type="danger" @click="removeVideo">
                                    删除视频
                                </el-button>
                            </div>
                            <el-progress
                                v-if="videoUploading"
                                :percentage="videoUploadPercent"
                                :status="videoUploadStatus"
                                class="video-upload-progress"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <!-- 商品规格 -->
            <div class="product-section">
                <div class="section-header">
                    <h3>商品规格</h3>
                </div>
                <div class="section-content">
                    <div class="upload-product">
                        <el-button type="primary" @click="uploadProductsHandle">添加商品规格</el-button>
                    </div>

                    <div class="product-table">
                        <el-scrollbar height="400px">
                            <el-table
                                :data="state.skuList"
                                border
                                style="width: 100%; margin-top: 10px"
                            >
                                <el-table-column label="规格" prop="name"/>
                                <el-table-column label="图片" prop="image" width="180">
                                    <template #default="scope">
                                        <el-image
                                            v-viewer
                                            :src="getImageUrl(scope.row.image)"
                                            fit="cover"
                                            style="width: 60px; height: 60px; border-radius: 4px; object-fit: cover;"
                                        />
                                    </template>
                                </el-table-column>
                                <el-table-column label="价格" prop="price">
                                    <template #default="scope">
                                        <span class="price-tag">¥{{ scope.row.price }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="库存" prop="stock"/>
                                <el-table-column label="操作">
                                    <template #default="scope">
                                        <el-button
                                            size="small"
                                            type="danger"
                                            @click="handleDelete(scope.$index, scope.row)"
                                        >
                                            删除
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-scrollbar>
                    </div>
                </div>
            </div>

            <!-- 商品属性设置 -->
            <div class="product-attributes">
                <div class="attributes-header">
                    <h3>商品属性设置</h3>
                </div>
                <div class="attributes-content">
                    <el-row :gutter="20">
                        <el-col :span="5">
                            <div class="attribute-item">
                                <span class="attribute-label">是否推荐:</span>
                                <el-radio-group v-model="state.commodity.isRecommend" class="ml-4">
                                    <el-radio label="1" size="large">是</el-radio>
                                    <el-radio label="0" size="large">否</el-radio>
                                </el-radio-group>
                            </div>
                        </el-col>
                        <el-col :span="5">
                            <div class="attribute-item">
                                <span class="attribute-label">是否新品:</span>
                                <el-radio-group v-model="state.commodity.isNew" class="ml-4">
                                    <el-radio label="1" size="large">是</el-radio>
                                    <el-radio label="0" size="large">否</el-radio>
                                </el-radio-group>
                            </div>
                        </el-col>
                        <el-col :span="5">
                            <div class="attribute-item">
                                <span class="attribute-label">是否热销:</span>
                                <el-radio-group v-model="state.commodity.isHot" class="ml-4">
                                    <el-radio label="1" size="large">是</el-radio>
                                    <el-radio label="0" size="large">否</el-radio>
                                </el-radio-group>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="attribute-item">
                                <span class="attribute-label">减库存方式:</span>
                                <el-radio-group v-model="state.commodity.stockType" class="ml-4">
                                    <el-radio label="1" name="stockType" size="large">下单减库存</el-radio>
                                    <el-radio label="2" name="stockType" size="large">付款减库存</el-radio>
                                </el-radio-group>
                            </div>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <!-- 商品详情 -->
            <div class="product-pic-container_detail">
                <span class="product-pic-label1">商品详情:</span>
                <div class="product-editor" style="width: 1000px">
                    <Editor v-model="state.commodity.content"/>
                </div>
            </div>
            <div class="form-actions">
                <el-button v-if="!productId" :loading="state.saveLoading" type="success"  @click="save">保存</el-button>
                <el-button :loading="state.submitLoading" type="primary" @click="submit">{{
                        isEdit ? '保存修改' : '提交'
                    }}
                </el-button>
                <el-button @click="goBack">返回</el-button>
            </div>
            <!-- start 通过支付成功后，显示模块 -->
            <div class="sales-settings">
                <div class="sales-settings-header">
                    <h3>代销设置</h3>
                </div>
                <div class="product-pic-container1">
                    <span class="product-pic-label1">是否代销:</span>
                    <el-radio-group v-model="state.salesForm.isDaixiao" class="ml-4">
                        <el-radio label="1" size="large">是</el-radio>
                        <el-radio label="2" size="large">否</el-radio>
                    </el-radio-group>
                </div>
                <div class="product-pic-container1">
                    <span class="product-pic-label1">代销权限佣金方式:</span>
                    <el-radio-group v-model="state.salesForm.daixiaoType" class="ml-4">
                        <el-radio label="1" size="large" value="1">固定佣金</el-radio>
                        <el-radio label="2" size="large" value="2">佣金比例</el-radio>
                    </el-radio-group>
                    <el-input
                        v-if="state.salesForm.daixiaoType == 1"
                        v-model="state.salesForm.daixiaoTypeValue"
                        placeholder="请输入"
                        size="small"
                        style="width: 120px; margin-left: 20px;">
                    </el-input>
                    <span v-if="state.salesForm.daixiaoType == 1">￥</span>
                    <el-input
                        v-if="state.salesForm.daixiaoType == 2"
                        v-model="state.salesForm.daixiaoTypeValue"
                        placeholder="请输入"
                        size="small"
                        style="width: 120px; margin-left: 20px;">
                    </el-input>
                    <span v-if="state.salesForm.daixiaoType == 2">%</span>
                </div>
                <div class="form-actions">
                    <el-button :disabled="!state.showCategory" type="success" @click="submitFun">保存
                    </el-button>
                </div>
            </div>
        </div>
    </div>

    <el-dialog v-model="state.feeBearerDialog" title="修改第三方支付手续费" width="500">
        <el-radio-group v-model="state.commodity.feeBearer">
            <el-radio label="0">店铺承担</el-radio>
            <el-radio label="1">用户承担</el-radio>
        </el-radio-group>
    </el-dialog>

    <!-- 新增分类对话框 -->
    <el-dialog v-model="dialogAddCategory" title="新增分类" width="500">
        <el-form :model="state.ruleForm" label-width="100px">
            <el-form-item label="分类名称">
                <el-input v-model="state.ruleForm.name" placeholder="请输入分类名称"/>
            </el-form-item>
            <el-form-item label="分类编码">
                <el-input v-model="state.ruleForm.code" placeholder="请输入分类编码"/>
            </el-form-item>
            <el-form-item label="上级分类">
                <el-tree-select
                    v-model="state.ruleForm.parentId"
                    :data="state.parentData"
                    :props="{ checkStrictly: true, value: 'id', label: 'name', children: 'children'}"
                    :render-after-expand="false"
                    check-strictly
                    placeholder="请选择上级分类"
                    style="width: 100%"
                />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogAddCategoryReturn">取消</el-button>
                <el-button type="primary" @click="dialogAddCategoryConfirm">确定</el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 产品上传对话框 -->
    <el-dialog v-model="dialogProductVisible" title="添加商品规格" width="600">
        <el-form :model="state.form" label-width="100px">
            <el-form-item label="商品图片" required>
                <el-upload
                    :action="UPLOAD_URL"
                    :before-upload="beforeUploadImage"
                    :file-list="fileList"
                    :headers="headers"
                    :limit="1"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemove"
                    :on-success="handleSuccessPic"
                    list-type="picture-card"
                >
                    <el-icon>
                        <Plus/>
                    </el-icon>
                    <template #tip>
                        <div class="el-upload__tip">
                            请上传图片文件，且不超过1MB
                        </div>
                    </template>
                </el-upload>
                <el-dialog v-model="dialogVisible" title="预览">
                    <img :src="dialogImageUrl" alt="Preview Image" style="width: 100%;"/>
                </el-dialog>
            </el-form-item>

            <el-form-item label="库存" required>
                <el-input-number v-model="state.form.stock" :min="0" :precision="0"/>
            </el-form-item>

            <el-form-item label="规格类型">
                <el-radio-group v-model="radio" @change="handleChange">
                    <el-radio label="1">同个价格有不同规格</el-radio>
                    <el-radio label="2">不同规格不同价格</el-radio>
                </el-radio-group>
            </el-form-item>

            <div v-if="radio==='1'">
                <el-form-item label="规格">
                    <el-input
                        v-model="inputValue"
                        placeholder="输入后按回车添加规格"
                        @blur="addTag"
                        @keyup.enter="addTag"
                    />
                    <div class="tags-container">
                        <el-tag
                            v-for="(tag, index) in tags"
                            :key="index"
                            class="tag-item"
                            closable
                            @close="removeTag(index)"
                        >
                            {{ tag }}
                        </el-tag>
                    </div>
                </el-form-item>

                <el-form-item label="价格">
                    <el-input-number v-model="state.form.price" :min="0" :precision="2" :step="0.1"/>
                </el-form-item>
            </div>

            <div v-else>
                <el-button plain type="primary" @click="addRow">
                    <el-icon>
                        <Plus/>
                    </el-icon>
                    新增规格
                </el-button>
                <el-table :data="tableData1" style="width: 100%; margin-top: 15px">
                    <el-table-column label="规格" prop="name">
                        <template #default="{ row }">
                            <el-input v-model="row.name" placeholder="请输入规格"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="价格" prop="price">
                        <template #default="{ row }">
                            <el-input-number v-model="row.price" :min="0" :precision="2" :step="0.1"
                                             style="width: 100%"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100">
                        <template #default="{ $index }">
                            <el-button size="small" type="danger" @click="removeRow($index)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogAddProductReturn">取消</el-button>
                <el-button v-if="radio==='1'" type="primary" @click="dialogAddProductConfirm">确定</el-button>
                <el-button v-else type="primary" @click="dialogAddProductConfirm2">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import Editor from '../../components/Editor.vue'
import {nextTick, onMounted, reactive, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {productCategory} from '../../stores/productCategory'
import {releaseProduct} from '../../stores/releaseProduct'
import {commodity} from '../../stores/commodity'
import {Plus, UploadFilled} from '@element-plus/icons-vue'
import type {UploadFile, UploadProgressEvent} from 'element-plus'
import {ElMessage} from 'element-plus'
import {Session} from '../../utils/storage'
import {COS_URL} from '../../config/baseUrl'
import {getImageUrl} from "../../utils/common";

const baseUrl = import.meta.env.VITE_IP;
// 上传相关
const UPLOAD_URL = baseUrl  + '/system/oss/upload'

const router = useRouter()
const route = useRoute()
const isEdit = ref(false)
const productId = ref<string | null>(null)
const shopDefaultCategoryId = ref(null)

interface CommodityState {
    loading: boolean;
    submitLoading: boolean;
    saveLoading: boolean;
    value1: string;
    value2: string;
    value3: string;
    ruleForm: {
        parentId: number;
        name?: string;
        code?: string;
    };
    commodity: {
        type: string;
        id?: string | number;
        content: string;
        categoryId: string;
        name: string;
        video: string;
        images: string;
        unit?: string;
        size: string;
        price: number;
        isRecommend: string;
        isNew: string;
        isHot: string;
        stockType: string;
        skuList: any[];
        stock: number;
        feeBearer: string;
        cover?: string;
    };
    skuList: any[];
    form: {
        name: string;
        url: string;
        size: string;
        price: string | number;
        stock: string | number;
        filePath: string;
    };
    parentData: any[];
    levelArray: any[];
    Level2Array: any[];
    Level3Array: any[];
    buttonList: any[];
    showCategory: boolean;
    salesForm: {
        id: string | number;
        isDaixiao: string;
        daixiaoType: string;
        daixiaoTypeValue: string;
    };
}

const state = reactive<CommodityState>({
    feeBearerDialog: false,
    loading: false,
    submitLoading: false,
    saveLoading: false,
    value1: "",
    value2: "",
    value3: "",
    ruleForm: {
        parentId: 0
    },
    commodity: {
        type: '0',
        content: "",
        categoryId: "",
        name: "",
        video: "",
        images: "",
        size: "",
        price: 0,
        isRecommend: "0",
        isNew: "0",
        isHot: "0",
        stockType: "1",
        skuList: [],
        stock: 0,  //总库存
        feeBearer: "0",
    },
    skuList: [],
    //上传产品表单数据
    form: {
        name: "",
        url: "",
        size: "",
        price: "",
        stock: "",
        filePath: ""
    },
    parentData: [
        {
            id: 0,
            name: '顶级分类',
            children: []
        }
    ],
    levelArray: [],
    Level2Array: [],
    Level3Array: [],
    buttonList: [],
    showCategory: false,
    // 代销佣金方式（1：固定金额，2比例%）
    // 代销佣金的值
    salesForm: {
        id: "",
        isDaixiao: "",
        daixiaoType: "",
        daixiaoTypeValue: 0
    }
})

const inputValue = ref('');
const tags = ref([]);

const addTag = () => {
    if (inputValue.value && !tags.value.includes(inputValue.value)) {
        tags.value.push(inputValue.value);
        inputValue.value = '';
    }
};
const removeTag = (index) => {
    state.skuList.splice(index, 1)
    tags.value.splice(index, 1);
};
const handleButtonClick = (item) => {
    router.push(item.component)
}
const dialogImageUrl = ref('')
const dialogVisible = ref(false)

//删除
const handleRemove = (file) => {
    state.form.url = '';
    state.form.filePath = '';
    fileList.value = [];
}

const handlePictureCardPreview = (file) => {
    dialogImageUrl.value = file.url || getImageUrl(file.response?.data?.filePath);
    dialogVisible.value = true;
}

const handleChange = (value) => {
    dialogAddVisible.value = true;
}
// 提交否代销数据
const submitFun = async () => {
    if (!state.commodity.id) {
        ElMessage.error('请先保存商品信息')
        return
    }
    state.salesForm.id = state.commodity.id;
    const res = await commodity().GetEditDaixiao(state.salesForm);
    if (res.code == 200) {
        ElMessage.success(isEdit.value ? '商品更新成功' : '商品添加成功');
        // 返回商品列表页
        router.push('/productList');
    }
}
const radio = ref('1')
//新增分类数据弹窗
const dialogAddCategory = ref(false)
const dialogAddCategoryHandle = () => {
    dialogAddCategory.value = true;
};
const dialogAddCategoryReturn = () => {
    dialogAddCategory.value = false;
}
const dialogAddCategoryConfirm = async () => {
    let result = await productCategory().SaveCategory(state.ruleForm); // 调用存储分类方法
    if (result.code == 200) {
        ElMessage.success('添加成功!');
        dialogAddCategory.value = false;
    }
    // getCategory()
}
//查询三级分类数据
const GetCategoryByParentId = async () => {
    let result = await releaseProduct().GetCategoryByParentId(0);
    state.levelArray = result.data
}
//切换第一级分类
const handleGetCategory = async (value) => {
    let result = await releaseProduct().GetCategoryByParentId(value);
    state.Level2Array = result.data
}
//切换第二级分类
const handleGetCategoryTwo = async (value) => {
    let result = await releaseProduct().GetCategoryByParentId(value);
    state.Level3Array = result.data
}
//上传视频
const beforeUploadVideo = (file: File) => {
    const isVideo = file.type.startsWith('video/');
    const isLt5M = file.size / 1024 / 1024 < 5;

    if (!isVideo) {
        ElMessage.error('只能上传视频文件!');
        return false;
    }

    if (!isLt5M) {
        ElMessage.error('视频大小不能超过 5MB!');
        return false;
    }

    videoUploading.value = true;
    videoUploadPercent.value = 0;
    videoUploadStatus.value = '';
    return true;
}
const handleVideoProgress = (event: UploadProgressEvent) => {
    videoUploadPercent.value = Math.round(event.percent);
}
const handleVideoSuccess = (response: any, file: UploadFile) => {
    if (response.code === 200 && response.data) {
        state.commodity.video = response.data.filePath;
        videoFileList.value = [{
            name: file.name,
            url: getVideoUrl(response.data.filePath)
        }];
        ElMessage.success('视频上传成功');
    } else {
        ElMessage.error('视频上传失败');
    }
    videoUploading.value = false;
    videoUploadStatus.value = 'success';
}
const handleVideoError = () => {
    ElMessage.error('视频上传失败');
    videoUploading.value = false;
    videoUploadStatus.value = 'exception';
}
const removeVideo = () => {
    state.commodity.video = '';
    videoFileList.value = [];
}
const getVideoUrl = (path: string): string => {
    if (!path) return '';
    if (path.startsWith('http')) return path;
    return COS_URL + '/' + path;
}
//上传图片
const handleSuccessPic = (response: any, file: UploadFile) => {
    if (response.code === 200 && response.data) {
        state.form.url = response.data.filePath;
        state.form.filePath = response.data.filePath;
        state.commodity.images = response.data.filePath

        // 更新文件列表，确保UI显示正确
        fileList.value = [{
            name: file.name,
            url: getImageUrl(response.data.filePath)
        }];
        ElMessage.success('图片上传成功');
    } else {
        ElMessage.error('图片上传失败');
    }
}
//获取分类树数据
const getCategoryTree = async () => {
    let result = await productCategory().GetCategoryTree(); // 调用存储分类列表
    state.parentData[0].children = result.data
}
const getShopDefaultCategory = async () => {
    let result = await productCategory().GetShopDefaultCategory(); // 调用存储分类列表
    if (result.code == 200 && result.data != null){
        shopDefaultCategoryId.value = result.data
        loadCategoryData(result.data);
    }
}
//上传产品弹窗
const dialogProductVisible = ref(false)
const uploadProductsHandle = () => {
    dialogProductVisible.value = true;
};
//新增数据弹窗
const dialogAddVisible = ref(false)
const dialogAddVisibleHandle = () => {
    dialogAddVisible.value = true;
};
const dialogAddProductReturn = () => {
    dialogProductVisible.value = false;
    // 重置表单和上传状态
    state.form = {
        name: "",
        url: "",
        size: "",
        price: "",
        stock: "",
        filePath: ""
    }
    fileList.value = []
}

const dialogAddProductConfirm = () => {
    if (tags.value.length === 0) {
        ElMessage.error('请添加规格')
        return
    }
    if (!state.form.url) {
        ElMessage.error('请上传图片')
        return
    }
    if (!state.form.stock) {
        ElMessage.error('请输入库存')
        return
    }
    if (!state.form.price) {
        ElMessage.error('请输入价格')
        return
    }
    tags.value.forEach(item => {
        state.skuList.push({
            name: item,
            image: state.form.url,
            price: state.form.price,
            stock: state.form.stock,
            filePath: state.form.filePath
        })
    })
    dialogProductVisible.value = false
    // 重置表单和上传状态
    state.form = {
        name: "",
        url: "",
        size: "",
        price: "",
        stock: "",
        filePath: ""
    }
    fileList.value = []
    tags.value = []
    inputValue.value = ''
}

const dialogAddProductConfirm2 = () => {
    if (tableData1.value.length === 0) {
        ElMessage.error('请添加规格')
        return
    }
    if (!state.form.url) {
        ElMessage.error('请上传图片')
        return
    }
    if (!state.form.stock) {
        ElMessage.error('请输入库存')
        return
    }
    tableData1.value.forEach(item => {
        state.skuList.push({
            name: item.name,
            image: state.form.url,
            price: item.price,
            stock: state.form.stock,
            filePath: state.form.filePath
        })
    })
    dialogProductVisible.value = false
    // 重置表单和上传状态
    state.form = {
        name: "",
        url: "",
        size: "",
        price: "",
        stock: "",
        filePath: ""
    }
    fileList.value = []
    tableData1.value = []
}
const tableData1 = ref([
    {name: '', price: ''}
]);

const addRow = () => {
    tableData1.value.push({name: '', price: ''});
};

const removeRow = (index) => {
    tableData1.value.splice(index, 1);
};
const handleDelete = (index) => {
    state.skuList.splice(index, 1)
}

// 返回上一页
const goBack = () => {
    router.push('/productList');
};

// 表单验证
const validateForm = () => {
    if (!state.commodity.name) {
        ElMessage.warning('请输入商品名称');
        return false;
    }

    if (!state.commodity.categoryId) {
        ElMessage.warning('请选择商品分类');
        return false;
    }

    if (!state.commodity.unit) {
        ElMessage.warning('请输入商品单位');
        return false;
    }

    if (state.skuList.length === 0) {
        ElMessage.warning('请添加至少一个商品规格');
        return false;
    }

    if (!state.commodity.content) {
        ElMessage.warning('请填写商品详情');
        return false;
    }

    return true;
};

// 提交表单
const submit = async () => {
    // 表单验证
    if (!validateForm()) {
        return;
    }

    try {
        state.submitLoading = true;

        let stockTotal = 0;
        const list = state.skuList;
        for (let i = 0; i < list.length; i++) {
            stockTotal = stockTotal + list[i].stock;
        }

        state.commodity.stock = stockTotal;
        state.commodity.skuList = state.skuList;

        if (state.skuList.length > 0) {
            state.commodity.cover = state.skuList[0].image;
            state.commodity.price = state.skuList[0].price;
            state.commodity.size = state.skuList[0].name;
        }

        // 确保stockType是字符串类型
        state.commodity.stockType = state.commodity.stockType.toString();

        let result;
        if (isEdit.value) {
            // 更新商品
            result = await commodity().UpdateProduct(state.commodity);
        } else {
            // 新增商品
            result = await productCategory().PublishProduct(state.commodity);
        }

        if (result.code == 200) {
            ElMessage.success(isEdit.value ? '商品更新成功' : '商品添加成功');
            // 返回商品列表页
            router.push('/productList');
        }
    } catch (error) {
        console.error('提交失败:', error);
        ElMessage.error('提交失败，请重试');
    } finally {
        state.submitLoading = false;
    }
};

// 提交表单
const save = async () => {
    // 表单验证
    if (!validateForm()) {
        return;
    }

    try {
        state.saveLoading = true;

        let stockTotal = 0;
        const list = state.skuList;
        for (let i = 0; i < list.length; i++) {
            stockTotal = stockTotal + list[i].stock;
        }

        state.commodity.stock = stockTotal;
        state.commodity.skuList = state.skuList;

        if (state.skuList.length > 0) {
            state.commodity.cover = state.skuList[0].image;
            state.commodity.price = state.skuList[0].price;
            state.commodity.size = state.skuList[0].name;
        }

        // 确保stockType是字符串类型
        state.commodity.stockType = state.commodity.stockType.toString();

        // 新增商品
        let result = await productCategory().PublishProduct(state.commodity);

        if (result.code == 200) {
            ElMessage.success('商品保存成功');
            productId.value = result.data
            loadProductDetail(productId.value)
        }
    } catch (error) {
        console.error('提交失败:', error);
        ElMessage.error('提交失败，请重试');
    } finally {
        state.saveLoading = false;
    }
};

// 加载商品详情
const loadProductDetail = async (id) => {
    try {
        state.loading = true;
        const result = await commodity().GetProductById(id);
        if (result.code === 200 && result.data) {
            const productData = result.data;

            // 填充商品基本信息
            state.commodity.id = productData.id;
            state.commodity.name = productData.name;
            state.commodity.categoryId = productData.categoryId;
            state.commodity.content = productData.content;
            state.commodity.video = productData.video;
            state.commodity.images = productData.images;
            state.commodity.unit = productData.unit;
            state.commodity.price = productData.price;
            state.commodity.isRecommend = productData.isRecommend;
            state.commodity.isNew = productData.isNew;
            state.commodity.isHot = productData.isHot;
            state.commodity.feeBearer = productData.feeBearer;
            state.commodity.type = productData.type;
            // 确保stockType始终是字符串类型
            state.commodity.stockType = productData.stockType ? productData.stockType.toString() : "1";
            state.commodity.stock = productData.stock;

            // 调试输出
            console.log('商品数据:', productData);
            console.log('减库存方式:', state.commodity.stockType, typeof state.commodity.stockType);

            // 填充SKU列表
            if (productData.skuList && productData.skuList.length > 0) {
                state.skuList = productData.skuList;
            }

            // 填充代销信息
            if (productData.isDaixiao) {
                state.salesForm.id = productData.id;
                state.salesForm.isDaixiao = productData.isDaixiao.toString();
                state.salesForm.daixiaoType = productData.daixiaoType ? productData.daixiaoType.toString() : "1";
                state.salesForm.daixiaoTypeValue = productData.daixiaoTypeValue ? productData.daixiaoTypeValue.toString() : "60";
            }

            // 加载分类数据
            await loadCategoryData(productData.categoryId);

            ElMessage.success('商品数据加载成功');
        } else {
            ElMessage.error('商品数据加载失败');
        }
    } catch (error) {
        console.error('加载商品详情失败:', error);
        ElMessage.error('加载商品详情失败');
    } finally {
        state.loading = false;
    }
};

// 加载分类数据
const loadCategoryData = async (categoryId) => {
    // 这里需要根据分类ID加载对应的分类层级
    // 实际实现可能需要查询分类的父级信息
    // 简化处理，仅加载当前分类数据
    try {
        // 先获取商品分类的详细信息
        const categoryResult = await productCategory().GetCategoryById(categoryId);
        if (categoryResult.code === 200 && categoryResult.data) {
            const category = categoryResult.data;

            // 加载一级分类
            await GetCategoryByParentId();

            // 如果是三级分类
            if (category.parentId !== 0) {
                // 获取二级分类信息
                const parentResult = await productCategory().GetCategoryById(category.parentId);
                if (parentResult.code === 200 && parentResult.data) {
                    const parentCategory = parentResult.data;

                    // 如果父分类是二级分类（其父分类是一级分类）
                    if (parentCategory.parentId !== 0) {
                        // 设置一级分类
                        state.value1 = parentCategory.parentId;
                        // 加载二级分类选项
                        await handleGetCategory(state.value1);
                        // 设置二级分类
                        state.value2 = parentCategory.id;
                        // 加载三级分类选项
                        await handleGetCategoryTwo(state.value2);
                        // 设置三级分类
                        state.commodity.categoryId = categoryId;
                    }
                    // 如果父分类是一级分类
                    else {
                        // 设置一级分类
                        state.value1 = parentCategory.id;
                        // 加载二级分类选项
                        await handleGetCategory(state.value1);
                        // 设置二级分类
                        state.value2 = categoryId;
                        // 加载三级分类选项为空
                        await handleGetCategoryTwo(state.value2);
                    }
                }
            }
            // 如果是一级分类
            else {
                state.value1 = categoryId;
                await handleGetCategory(state.value1);
            }
        }
    } catch (error) {
        console.error('加载分类数据失败:', error);
        ElMessage.error('加载分类数据失败');
    }
};

onMounted(() => {
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
        // 检查是否是编辑模式
        if (route.query.id) {
            isEdit.value = true;
            productId.value = route.query.id;
            loadProductDetail(productId.value);
        } else {
            GetCategoryByParentId();
            getCategoryTree();
            getShopDefaultCategory()
        }
        commodity().GetsalesData().then(result => {
            let data = result.data
            console.log("data", data)
            //展示
            if (data != null && data > 0) {
                state.showCategory = true
            }
        })
    })
})

// 上传请求头
const headers = {
    Authorization: `Bearer ${Session.getToken()}`
}

// 添加文件列表状态
const fileList = ref<any[]>([]);
const videoFileList = ref<any[]>([]);
const videoUploading = ref(false);
const videoUploadPercent = ref(0);
const videoUploadStatus = ref('');

// 图片上传前的验证
const beforeUploadImage = (file: File) => {
    const isImage = file.type.startsWith('image/');
    const isLt5M = file.size / 1024 / 1024 < 1;

    if (!isImage) {
        ElMessage.error('只能上传图片文件!');
        return false;
    }

    if (!isLt5M) {
        ElMessage.error('图片大小不能超过 1MB!');
        return false;
    }

    return true;
}

</script>

<style lang="scss" scoped>
.data-container {
    position: relative;
    display: flex;
    box-sizing: border-box;
    background-color: #f5f7fa;
    min-height: 100vh;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 30px);
    overflow-y: scroll;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.middle-container {
    flex: 1;
    position: relative;
    margin-left: 255px;
    padding: 20px;
}

.page-header {
    background-color: #fff;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .breadcrumb {
        margin-bottom: 10px;
    }

    .page-title {
        font-size: 22px;
        font-weight: 600;
        color: #303133;
        margin: 0;
    }
}

.mid {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
}

.commBox {
    background-color: #f5f7fa;
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    font-size: 16px;
    margin-bottom: 20px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    transition: all 0.3s ease;

    &:hover, &:focus-within {
        border-color: #3A58CF;
        box-shadow: 0 0 0 2px rgba(58, 88, 207, 0.1);
    }

    .Text {
        margin-left: 13px;

        &:first-child {
            min-width: 120px;
            font-weight: bold;
        }
    }

    input {
        flex: 1;
        border: none;
        background: transparent;
        outline: none;
        font-size: 16px;
        padding: 0 10px;
        height: 100%;
    }
}

.category-row {
    display: flex;
    margin-bottom: 20px;
    align-items: center;
}

.commBox-small {
    width: 144px;
    height: 50px;
    background-color: #f5f7fa;
    font-size: 16px;
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    border: 1px solid #dcdfe6;

    .Text {
        font-weight: bold;
    }
}

.category-buttons {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;

    .category-select {
        width: 150px;
    }

    .addBtn {
        margin-left: 10px;
        background-color: #3A58CF;
        font-size: 16px;
        padding: 0 20px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            background-color: #2a48bf;
            transform: scale(1.05);
        }

        .addbtn {
            color: #fff;
            border: none;
            background: transparent;
            cursor: pointer;
        }
    }
}

.product-pic-container {
    display: flex;
    align-items: flex-start;
    margin-bottom: 32px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .product-pic-label1 {
        width: 150px;
        font-size: 16px;
        font-weight: bold;
        margin-top: 8px;
    }
}

.product-pic-container_detail {
    display: flex;
    align-items: flex-start;
    margin-bottom: 32px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .product-pic-label1 {
        width: 150px;
        font-size: 16px;
        font-weight: bold;
        margin-top: 8px;
    }

    :deep(.el-textarea) {
        width: 80% !important;
    }
}

.product-pic-container1 {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .product-pic-label1 {
        width: 150px;
        font-size: 16px;
        font-weight: bold;
    }
}

.commodityList {
    margin-bottom: 32px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;

        .el-table__header-wrapper th {
            background-color: #f5f7fa;
            font-weight: bold;
        }

        .el-table__row {
            transition: all 0.3s ease;

            &:hover {
                background-color: #f0f5ff !important;
            }
        }

        .price-tag {
            color: #ff6b6b;
            font-weight: 500;
            font-size: 16px;
        }
    }
}

.product-editor {
    width: 80%;
    min-height: 400px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
    transition: border-color 0.3s;

    &:hover, &:focus-within {
        border-color: #3A58CF;
    }
}

.form-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    margin-bottom: 20px;

    :deep(.el-button) {
        min-width: 120px;
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
    }
}

.sales-settings {
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    margin-top: 30px;
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .sales-settings-header {
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;

        h3 {
            font-size: 18px;
            color: #303133;
            margin: 0;
        }
    }
}

.video-preview {
    margin-left: 20px;
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #f8f8f8;
    transition: all 0.3s ease;

    &:hover {
        transform: scale(1.02);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    video {
        border-radius: 4px;
    }
}

:deep(.el-upload--picture-card) {
    border-radius: 8px;
    border-color: #dcdfe6;
    transition: all 0.3s ease;

    &:hover {
        border-color: #3A58CF;
        transform: scale(1.05);
    }
}

:deep(.el-upload-dragger) {
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
        border-color: #3A58CF;
        transform: scale(1.02);
    }
}

:deep(.el-button) {
    border-radius: 4px;
    transition: all 0.3s ease;
}

:deep(.el-button--primary) {
    background-color: #3A58CF;

    &:hover, &:focus {
        background-color: #2a48bf;
    }
}

:deep(.el-button--success) {
    background-color: #67c23a;

    &:hover, &:focus {
        background-color: #5daf34;
    }
}

:deep(.el-button--danger) {
    &:hover {
        transform: scale(1.05);
    }
}

:deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;

    .el-dialog__header {
        padding: 20px;
        border-bottom: 1px solid #f0f0f0;
        background-color: #f5f7fa;
    }

    .el-dialog__body {
        padding: 30px 20px;
    }

    .el-dialog__footer {
        padding: 15px 20px;
        border-top: 1px solid #f0f0f0;
        background-color: #f5f7fa;
    }
}

.tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .category-buttons {
        flex-wrap: wrap;
    }

    .product-pic-container_detail {
        flex-direction: column;

        .product-pic-label1 {
            margin-bottom: 10px;
        }

        .product-editor {
            width: 100%;
        }
    }
}

@media (max-width: 768px) {
    .left-buttons {
        width: 180px;
    }

    .middle-container {
        margin-left: 200px;
    }

    .commBox {
        flex-direction: column;
        height: auto;
        padding: 10px;
        align-items: flex-start;

        .Text:first-child {
            margin-bottom: 5px;
        }

        input {
            width: 100%;
            padding: 8px 0;
        }
    }

    .product-pic-container,
    .product-pic-container1 {
        flex-direction: column;

        .product-pic-label1 {
            margin-bottom: 10px;
            width: 100%;
        }
    }
}

.product-attributes {
    margin-bottom: 32px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .attributes-header {
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;

        h3 {
            font-size: 18px;
            color: #303133;
            margin: 0;
        }
    }

    .attributes-content {
        padding: 10px 0;
    }

    .attribute-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .attribute-label {
            font-weight: bold;
            margin-right: 10px;
            min-width: 100px;
        }
    }
}

.product-section {
    margin-bottom: 32px;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .section-header {
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;

        h3 {
            font-size: 18px;
            color: #303133;
            margin: 0;
        }
    }

    .section-content {
        padding: 10px 0;
    }

    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .info-label {
            font-weight: bold;
            margin-right: 10px;
            min-width: 100px;
        }

        :deep(.el-input) {
            flex: 1;
        }
    }

    .category-container {
        display: flex;
        align-items: flex-start;

        .category-selects {
            display: flex;
            flex: 1;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }

        .category-select {
            width: 180px;
        }
    }

    .media-item {
        display: flex;
        align-items: flex-start;

        .upload-container {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
    }

    .upload-product {
        margin-bottom: 15px;
    }

    .product-table {
        width: 100%;
    }
}
</style>

