<script lang="ts" setup>
import {onMounted, reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {Refresh, Search} from '@element-plus/icons-vue'
import {Session} from "../../utils/storage"
import {ElMessage} from 'element-plus'
import {listUserDeduction} from '../../api/platformVoucher/userDeduction'
import {formatAmount, formatDate} from '../../utils/common'

const router = useRouter()
const loading = ref(false)
const total = ref(0)
const voucherList = ref<any[]>([])
const ids = ref<number[]>([])

// 查询参数
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    userId: '',
    phone: '',
    username: '',
})

const state = reactive({
    buttonList: []
})

// 获取用户平台抵扣金列表
const getList = async () => {
    loading.value = true
    try {
        const res = await listUserDeduction(queryParams)
        voucherList.value = res.rows || []
        // total.value = res.total
        pagination.total = res.total
    } catch (error) {
        console.error('获取用户平台补贴金列表失败:', error)
        ElMessage.error('获取用户平台补贴金列表失败')
    } finally {
        loading.value = false
    }
}


// 分页参数
const pagination = reactive({
    pageNum: 1,
    pageSize: 10,
    total: 0
})
// 分页处理
const handleSizeChange = (size) => {
    // pagination.pageSize = size
    queryParams.pageSize = size
    getList()
}
const handleCurrentChange = (page) => {
    // pagination.pageNum = page
    queryParams.pageNum = page
    getList()
}


// 查询按钮点击
const handleQuery = () => {
    queryParams.pageNum = 1
    getList()
}

// 重置按钮点击
const resetQuery = () => {
    Object.assign(queryParams, {
        pageNum: 1,
        pageSize: 10,
        userId: '',
        phone: '',
        username: '',
    })
    handleQuery()
}

// 选择项变化
const handleSelectionChange = (selection) => {
    ids.value = selection.map(item => item.userId)
}

// 左侧按钮点击
const handleButtonClick = (item) => {
    router.push(item.component)
}

onMounted(() => {
    let menuList = Session.getMenu2()
    let menuId = Session.get('homeMenuId')
    if (menuId == null) {
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index]
            if (element.openType == 2) {
                if (menuId == null) {
                    menuId = element.menuId
                }
            }
        }
    }
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index]
        if (element.menuId == menuId) {
            state.buttonList = element.children
        }
    }

    // 加载用户平台补贴金信息
    getList()
})
</script>

<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>

        <div class="right-content">
            <div class="filter-container">
                <div class="filter-header">
                    <span class="filter-title">平台补贴金管理</span>
                </div>

                <el-form :model="queryParams" class="filter-form" @submit.prevent>
                    <!-- 查询条件：用户ID、手机号、用户名 -->
                    <div class="form-row">
                        <!--                        <el-form-item label="用户ID">-->
                        <!--                            <el-input-->
                        <!--                                v-model="queryParams.userId"-->
                        <!--                                placeholder="请输入用户ID"-->
                        <!--                                clearable-->
                        <!--                                style="width: 180px"-->
                        <!--                                @keyup.enter="handleQuery"-->
                        <!--                            />-->
                        <!--                        </el-form-item>-->
                        <el-form-item label="手机号">
                            <el-input
                                v-model="queryParams.phone"
                                clearable
                                placeholder="请输入手机号"
                                style="width: 180px"
                                @keyup.enter="handleQuery"
                            />
                        </el-form-item>
                        <el-form-item label="用户名">
                            <el-input
                                v-model="queryParams.username"
                                clearable
                                placeholder="请输入用户名"
                                style="width: 180px"
                                @keyup.enter="handleQuery"
                            />
                        </el-form-item>
                        <div class="form-buttons">
                            <el-button :icon="Search" type="primary" @click="handleQuery">查询</el-button>
                            <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
                        </div>
                    </div>
                </el-form>

                <!--                <div class="filter-title">-->
                <!--                    <el-button type="primary" :icon="Download" @click="handleExport">导出</el-button>-->
                <!--                </div>-->
            </div>

            <div class="table-container">
                <el-table
                    v-loading="loading"
                    :data="voucherList"
                    border
                    size="small"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                >
                    <!--                    <el-table-column type="selection" align="center" />-->
                    <el-table-column label="用户ID" prop="userId" width="180"/>
                    <el-table-column label="用户信息">
                        <template #default="scope">
                            <div class="user-info" style="display: flex">
                                <div>
                                    <span class="info-label">用户名：</span>
                                    <span class="info-value">{{ scope.row.username }}</span>
                                </div>
                                <div>
                                    <span class="info-label">昵称：</span>
                                    <span class="info-value">{{ scope.row.nickname || '-' }}</span>
                                </div>
                                <div>
                                    <span class="info-label">手机号：</span>
                                    <span class="info-value">{{ scope.row.phone }}</span>
                                </div>
                                <div>
                                    <span class="info-label">注册时间：</span>
                                    <span class="info-value">{{ formatDate(scope.row.createTime) }}</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="平台补贴金情况">
                        <template #default="scope">
                            <div class="deduction-info">
                                <div>
                                    <span class="info-label">总额：</span>
                                    <span class="info-value amount-total">{{
                                            formatAmount(scope.row.deductionTotal)
                                        }}</span>
                                </div>
                                <div>
                                    <span class="info-label">已用：</span>
                                    <span class="info-value amount-used">{{
                                            formatAmount(scope.row.deductionUsed)
                                        }}</span>
                                </div>
                                <div>
                                    <span class="info-label">剩余：</span>
                                    <span class="info-value amount-balance">{{
                                            formatAmount(scope.row.deductionBalance)
                                        }}</span>
                                </div>
                                <div>
                                    <span class="info-label">最近使用：</span>
                                    <span class="info-value">{{ formatDate(scope.row.lastUsedTime) || '-' }}</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <!--                    <el-table-column label="操作" width="120" align="center">-->
                    <!--                        <template #default="scope">-->
                    <!--                            <el-button type="primary" link size="small" @click="handleDetail(scope.row)">查看详情</el-button>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                </el-table>

                <!-- 分页组件 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.pageNum"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        background
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 30px);
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.right-content {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    max-width: calc(100% - 250px);
}

.filter-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #3A58CF;
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 20px;

    .filter-title {
        font-size: 24px;
        font-weight: bold;
        color: white;
    }
}

.filter-form {
    .form-row {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;

        .el-form-item {
            margin-right: 20px;
            margin-bottom: 0;
            min-width: 180px;

            :deep(.el-form-item__label) {
                padding-bottom: 0;
                font-weight: normal;
            }

            :deep(.el-input) {
                width: 180px;
            }

            :deep(.el-select) {
                width: 180px;
            }

            :deep(.el-date-editor) {
                width: 180px;
            }
        }

        .time-separator {
            margin: 0 10px;
            color: #666;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-left: auto;
        }
    }
}

.table-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    flex: 1;
    overflow-x: auto;

    :deep(.el-table) {
        font-size: 13px;

        th {
            background-color: #f5f7fa;
            color: #333;
            font-weight: bold;
            padding: 8px 0;
        }

        td {
            padding: 6px 0;
        }
    }
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 5px;

    div {
        line-height: 1.4;
    }
}

.deduction-info {
    display: flex;
    flex-direction: column;
    gap: 5px;

    .deduction-item {
        display: flex;
        align-items: center;
        line-height: 1.4;

        .label {
            width: 80px;
            color: #606266;
        }

        .value {
            font-weight: 500;

            &.total {
                color: #409EFF;
            }

            &.used {
                color: #F56C6C;
            }

            &.remain {
                color: #67C23A;
            }
        }
    }
}

.pagination-container {
    display: flex;
    justify-content: end;
    align-items: center;
    margin-top: 20px;
}
</style>
