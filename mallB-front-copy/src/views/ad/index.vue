<template>
    <div class="ad-container">
        <!-- 顶部操作区 -->
        <div class="operation-area">
            <el-button v-if="userType === '2' || userType === '3'" type="primary" @click="handleAdd">添加广告
            </el-button>
            <div class="search-area">
                <el-input v-model="searchParams.adName" class="search-input" clearable placeholder="广告名称"/>
                <el-select v-model="searchParams.status" class="search-select" clearable placeholder="状态">
                    <el-option label="待审核" value="0"/>
                    <el-option label="待支付" value="1"/>
                    <el-option label="已生效" value="2"/>
                    <el-option label="已拒绝" value="3"/>
                </el-select>
                <el-button type="primary" @click="handleSearch">搜索</el-button>
                <el-button @click="handleReset">重置</el-button>
            </div>
        </div>

        <!-- 广告列表 -->
        <el-table
            v-loading="loading"
            :data="adList"
            border
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChange"
        >
            <el-table-column v-if="userType === 'sys_user'" type="selection" width="55"/>
            <el-table-column label="ID" prop="id"/>
            <el-table-column label="广告内容">
                <template #default="scope">
                    <el-image
                        v-if="scope.row.adType === '1'"
                        :initial-index="0"
                        :preview-src-list="[getImageUrl(scope.row.adUrl)]"
                        :preview-teleported="true"
                        :src="getImageUrl(scope.row.adUrl)"
                        fit="cover"
                        style="width: 80px; height: 80px"
                    />
                    <el-button v-else size="small" @click="previewVideo(getImageUrl(scope.row.adUrl))">预览视频
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="名称" prop="adName"/>
            <el-table-column label="类型" width="80">
                <template #default="scope">
                    {{ scope.row.adTypeText }}
                </template>
            </el-table-column>
            <el-table-column label="广告位置" width="100">
                <template #default="scope">
                    {{ scope.row.typeText || '首页' }}
                </template>
            </el-table-column>
            <el-table-column label="状态">
                <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.status)">
                        {{ scope.row.statusText }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="有效期">
                <template #default="scope">
                    <div v-if="scope.row.startTime && scope.row.endTime">
                        {{ scope.row.startTime }} 至 {{ scope.row.endTime }}
                        <el-tooltip content="有效期由平台设置，不可修改" placement="top">
                            <el-icon class="info-icon">
                                <InfoFilled/>
                            </el-icon>
                        </el-tooltip>
                    </div>
                    <div v-else>
                        暂未设置
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="点击量" prop="clickNumber"/>
            <el-table-column label="创建时间" prop="createTime"/>
            <el-table-column v-if="userType === 'merchant'" label="备注">
                <template #default="scope">
                    <span>{{ scope.row.updateBy }}</span>
                </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作">
                <template #default="scope">
                    <!-- 平台管理员操作 -->
                    <template v-if="userType === '1'">
                        <el-button v-if="scope.row.status === '0'" link type="success"
                                   @click="handleAudit(scope.row, '1')">
                            通过
                        </el-button>
                        <el-button v-if="scope.row.status === '0'" link type="danger"
                                   @click="handleAudit(scope.row, '3')">
                            拒绝
                        </el-button>
                    </template>
                    <!-- 商家操作 -->
                    <template v-else-if="userType === '2' || userType === '3'">
                        <el-button v-if="scope.row.status != '2'" link type="primary" @click="handleEdit(scope.row)">
                            修改
                        </el-button>
                        <el-button v-if="scope.row.status != '2'" link type="danger" @click="handleDelete(scope.row)">
                            删除
                        </el-button>
                        <el-button v-if="scope.row.status === '1'" link type="success" @click="handlePay(scope.row)">
                            生成订单
                        </el-button>
                    </template>
                </template>
            </el-table-column>
        </el-table>

        <!-- 批量审核按钮 -->
        <div v-if="userType === '1' && selectedRows.length > 0" class="batch-actions">
            <el-button type="success" @click="handleBatchAudit('1')">批量通过</el-button>
            <el-button type="danger" @click="handleBatchAudit('3')">批量拒绝</el-button>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
            <el-pagination
                v-model:current-page="pagination.pageNum"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 30, 50]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
        </div>

        <!-- 广告表单弹窗 -->
        <el-dialog
            v-model="dialogVisible"
            :title="formTitle"
            destroy-on-close
            width="600px"
            @close="handleDialogClose"
            @open="handleDialogOpen"
        >
            <!-- 广告表单 -->
            <div class="ad-form-container">
                <el-form
                    ref="formRef"
                    :model="formData"
                    :rules="rules"
                    label-width="100px"
                >
                    <el-form-item label="广告名称" prop="adName">
                        <el-input v-model="formData.adName" placeholder="请输入广告名称"/>
                    </el-form-item>

                    <el-form-item label="广告类型" prop="adType">
                        <el-radio-group v-model="formData.adType">
                            <el-radio label="1">图片</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="广告位置" prop="type">
                        <el-radio-group v-model="formData.type">
                            <el-radio label="0">商城首页</el-radio>
                            <el-radio label="1">分享广告</el-radio>
                            <el-radio label="2">商区广告</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="广告内容" prop="adUrl">
                        <el-upload
                            :action="uploadAction"
                            :before-upload="beforeUpload"
                            :data="uploadData"
                            :headers="uploadHeaders"
                            :on-success="handleUploadSuccess"
                            :show-file-list="false"
                            class="avatar-uploader"
                            name="file"
                        >
                            <template v-if="formData.adUrl">
                                <img
                                    v-if="formData.adType === '1'"
                                    :src="getImageUrl(formData.adUrl)"
                                    class="avatar"
                                    @click.stop="previewImage(getImageUrl(formData.adUrl))"
                                />
                                <video
                                    v-else
                                    :src="getImageUrl(formData.adUrl)"
                                    class="avatar"
                                    controls
                                ></video>
                            </template>
                            <el-icon v-else class="avatar-uploader-icon">
                                <Plus/>
                            </el-icon>
                        </el-upload>
                        <div class="upload-tip">
                            {{
                                formData.adType === '1' ? '支持jpg、png格式，大小不超过2MB' : '支持mp4格式，大小不超过10MB'
                            }}
                        </div>
                    </el-form-item>

                    <el-form-item label="跳转链接" prop="jumpUrl">
                        <el-input v-model="formData.jumpUrl" placeholder="请输入跳转链接"/>
                    </el-form-item>
                </el-form>

                <!-- 图片预览弹窗 -->
                <div v-if="previewVisible">
                    <el-image-viewer
                        :initial-index="0"
                        :teleported="true"
                        :url-list="[previewUrl]"
                        @close="previewVisible = false"
                    />
                </div>
            </div>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 审核对话框 -->
        <el-dialog
            v-model="auditDialogVisible"
            :title="auditStatus === '1' ? '审核通过' : '审核拒绝'"
            width="500px"
        >
            <el-form ref="auditFormRef" :model="auditForm" label-width="100px">
                <el-form-item
                    :rules="[
                        { required: auditStatus === '3', message: '拒绝时必须填写备注', trigger: 'blur' },
                        { max: 200, message: '备注长度不能超过200个字符', trigger: 'blur' }
                    ]"
                    label="审核备注"
                    prop="remark"
                >
                    <el-input
                        v-model="auditForm.remark"
                        :placeholder="auditStatus === '1' ? '通过备注（可选）' : '请输入拒绝理由（必填）'"
                        :rows="4"
                        type="textarea"
                    ></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="auditDialogVisible = false">取 消</el-button>
                    <el-button type="primary" @click="confirmAudit">确 定</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 确认删除弹窗 -->
        <el-dialog
            v-model="deleteDialogVisible"
            title="删除确认"
            width="400px"
        >
            <p>确定要删除该广告吗？此操作不可撤销。</p>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="deleteDialogVisible = false">取 消</el-button>
                    <el-button type="danger" @click="confirmDelete">确 定</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 视频预览弹窗 -->
        <el-dialog
            v-model="videoDialogVisible"
            title="视频预览"
            width="640px"
        >
            <video
                v-if="videoUrl"
                :src="videoUrl"
                controls
                style="width: 100%; max-height: 480px;"
            ></video>
        </el-dialog>

        <!-- 使用支付对话框组件 -->
        <PaymentDialog
            v-model:visible="paymentDialogVisible"
            :orderData="currentOrder"
            @cancel-success="handleCancelSuccess"
        />
    </div>
</template>

<script lang="ts" setup>
import {computed, nextTick, onMounted, reactive, ref} from 'vue'
import {ElImageViewer, ElMessage, ElMessageBox} from 'element-plus'
import {useAdvertisementStore} from '../../stores/advertisement'
import {Advertisement} from '../../api/shop/advertisement'
import {InfoFilled, Plus} from '@element-plus/icons-vue'
import {getImageUrl} from '../../utils/common'
import {Session} from '../../utils/storage'
import PaymentDialog from '../../components/PaymentDialog.vue'

const baseUrl = import.meta.env.VITE_IP;
// 上传相关
const uploadAction = baseUrl  + '/system/oss/upload'

// 广告Store
const adStore = useAdvertisementStore()

// 用户类型 (1:平台管理员, 2:商家)
const userType = ref(Session.get('userType'))

// 表单引用
const formRef = ref()
const auditFormRef = ref()

// 弹窗相关
const dialogVisible = ref(false)
const deleteDialogVisible = ref(false)
const videoDialogVisible = ref(false)
const paymentDialogVisible = ref(false)
const videoUrl = ref('')
const currentId = ref<string | null>(null)
const isEdit = ref(false)
const currentOrderNo = ref('')
const currentOrder = ref({})

// 审核相关
const auditDialogVisible = ref(false)
const auditStatus = ref('1') // 1:通过, 3:拒绝
const auditForm = reactive({
    id: '',
    remark: '',
})
// 选中的行
const selectedRows = ref([])

// 表单标题
const formTitle = computed(() => isEdit.value ? '修改广告' : '添加广告')

// 搜索参数
const searchParams = reactive({
    adName: '',
    status: ''
})

// 表单数据
const formData = reactive({
    id: '',
    adName: '',
    adType: '1',
    adUrl: '',
    jumpUrl: '',
    status: '',
    delFlag: '',
    shopId: null,
    createTime: null,
    startTime: null,
    endTime: null,
    clickNumber: 0,
    type: '0'
})

// 表单验证规则
const rules = {
    adName: [
        {required: true, message: '请输入广告名称', trigger: 'blur'},
        {min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur'}
    ],
    adType: [
        {required: true, message: '请选择广告类型', trigger: 'change'}
    ],
    adUrl: [
        {required: true, message: '请上传广告内容', trigger: 'change'}
    ],
    jumpUrl: [
        {type: 'url', message: '请输入正确的URL格式', trigger: 'blur'}
    ]
}

// 图片预览
const previewVisible = ref(false)
const previewUrl = ref('')

// 预览图片方法
const previewImage = (url) => {
    console.log('预览图片:', url)
    previewUrl.value = url
    previewVisible.value = true
}

// 上传请求头
const uploadHeaders = {
    Authorization: `Bearer ${Session.getToken()}`
}

// 上传额外参数
const uploadData = {
    busiType: 'shop-ad' // 业务类型
}

// 上传成功回调
const handleUploadSuccess = (res) => {
    console.log('上传成功，返回数据:', res)
    // 处理返回的图片URL
    if (res && res.code === 200 && res.data && res.data.filePath) {
        // 更新表单数据
        formData.adUrl = res.data.filePath
        console.log('设置广告URL为:', formData.adUrl)

        // 显示成功消息
        ElMessage.success('上传成功')
    }
}

// 上传前验证
const beforeUpload = (file) => {
    const isImage = formData.adType === '1' && file.type.startsWith('image/')
    const isVideo = formData.adType === '2' && file.type === 'video/mp4'
    const isLt2M = formData.adType === '1' && file.size / 1024 / 1024 < 2
    const isLt10M = formData.adType === '2' && file.size / 1024 / 1024 < 10

    if (formData.adType === '1' && !isImage) {
        ElMessage.error('图片格式不正确!')
        return false
    }

    if (formData.adType === '2' && !isVideo) {
        ElMessage.error('请上传mp4格式视频!')
        return false
    }

    if (formData.adType === '1' && !isLt2M) {
        ElMessage.error('图片大小不能超过 2MB!')
        return false
    }

    if (formData.adType === '2' && !isLt10M) {
        ElMessage.error('视频大小不能超过 10MB!')
        return false
    }

    return true
}

// 计算属性：广告列表
const adList = computed(() => adStore.adList)
const loading = computed(() => adStore.loading)
const pagination = computed(() => adStore.pagination)

// 生命周期钩子
onMounted(() => {
    // 加载广告列表
    loadData()
})

// 加载数据
const loadData = () => {
    console.log('开始加载广告数据')
    adStore.getAdvertisementList()
}

// 视频预览
const previewVideo = (url: string) => {
    videoUrl.value = url
    videoDialogVisible.value = true
}

// 状态Tag类型
const getStatusType = (status: string) => {
    switch (status) {
        case '0':
            return 'warning'   // 待审核
        case '1':
            return 'info'      // 待支付
        case '2':
            return 'success'   // 已生效
        case '3':
            return 'danger'    // 已拒绝
        default:
            return 'info'
    }
}

// 处理搜索
const handleSearch = () => {
    // 设置查询参数
    adStore.setSearchParams(searchParams)

    // 重置页码到第一页
    adStore.pagination.pageNum = 1

    // 查询数据
    loadData()
}

// 重置搜索
const handleReset = () => {
    searchParams.adName = ''
    searchParams.status = ''

    adStore.resetSearchParams()
    loadData()
}

// 处理弹窗打开
const handleDialogOpen = () => {
    console.log('弹窗打开，当前表单数据:', JSON.stringify(formData))
}

// 处理弹窗关闭
const handleDialogClose = () => {

    // 重置编辑状态
    isEdit.value = false

    // 延迟重置表单，确保弹窗完全关闭
    setTimeout(() => {
        // 重置表单数据
        formData.id = ''
        formData.adName = ''
        formData.adType = '1'
        formData.adUrl = ''
        formData.jumpUrl = ''
        formData.clickNumber = 0
        formData.type = '0'

        // 如果表单引用存在，重置表单验证状态
        if (formRef.value) {
            formRef.value.resetFields()
        }

        console.log('弹窗关闭后，表单数据已重置:', JSON.stringify(formData))
    }, 100)
}

// 处理分页大小变化
const handleSizeChange = (val: number) => {
    adStore.pagination.pageSize = val
    loadData()
}

// 处理当前页变化
const handleCurrentChange = (val: number) => {
    adStore.pagination.pageNum = val
    loadData()
}

// 处理添加广告
const handleAdd = () => {
    isEdit.value = false

    // 重置表单数据
    formData.id = ''
    formData.adName = ''
    formData.adType = '1'
    formData.adUrl = ''
    formData.jumpUrl = ''
    formData.clickNumber = 0
    formData.type = '0'

    // 打开弹窗
    dialogVisible.value = true

    // 确保下一个事件循环中表单验证状态被重置
    nextTick(() => {
        if (formRef.value) {
            formRef.value.resetFields()
        }
    })
}

// 处理编辑广告
const handleEdit = (row: Advertisement) => {
    console.log('开始编辑广告, 原始数据:', JSON.stringify(row))

    // 设置编辑状态
    isEdit.value = true

    // 直接设置每个字段
    formData.id = row.id || ''
    formData.adName = row.adName || ''
    formData.adType = row.adType || '1'
    formData.adUrl = row.adUrl || ''
    formData.jumpUrl = row.jumpUrl || ''
    formData.clickNumber = row.clickNumber || 0
    formData.type = row.type || '0'

    console.log('填充表单数据完成，准备打开弹窗:', JSON.stringify(formData))

    // 打开弹窗
    dialogVisible.value = true
}

// 处理删除广告
const handleDelete = (row: Advertisement) => {
    currentId.value = row.id
    deleteDialogVisible.value = true
}

// 确认删除
const confirmDelete = async () => {
    if (!currentId.value) return

    const result = await adStore.deleteAdvertisement(currentId.value)
    if (result) {
        ElMessage.success('删除成功')
        loadData()
    }
    deleteDialogVisible.value = false
    currentId.value = null
}

// 处理审核
const handleAudit = (row: Advertisement, status: string) => {
    // 设置审核信息
    auditForm.id = row.id
    auditForm.remark = ''
    auditStatus.value = status

    // 打开审核对话框
    auditDialogVisible.value = true
}

// 确认审核
const confirmAudit = async () => {
    // 如果是拒绝，必须填写理由
    if (auditStatus.value === '3') {
        if (!auditForm.remark) {
            ElMessage.warning('拒绝时必须填写备注理由')
            return
        }
    }

    // 调用审核API
    const success = await adStore.auditAdvertisement(auditForm.id, auditStatus.value, auditForm.remark)

    if (success) {
        // 关闭对话框
        auditDialogVisible.value = false
        // 重新加载数据
        loadData()
    }
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
    selectedRows.value = selection
}

// 批量审核
const handleBatchAudit = (status: string) => {
    if (selectedRows.value.length === 0) {
        ElMessage.warning('请选择要审核的广告')
        return
    }

    // 拒绝时必须填写备注
    if (status === '3') {
        ElMessageBox.prompt('请输入拒绝理由', '批量拒绝', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'textarea',
            inputValidator: (value) => {
                return value.trim().length > 0
            },
            inputErrorMessage: '理由不能为空'
        }).then(({value}) => {
            // 执行批量审核
            doBatchAudit(status, value)
        }).catch(() => {
            // 用户取消
        })
    } else {
        // 批量通过，可选填备注
        ElMessageBox.prompt('请输入审核备注（可选）', '批量通过', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'textarea',
            inputPlaceholder: '可选填写备注'
        }).then(({value}) => {
            // 执行批量审核
            doBatchAudit(status, value)
        }).catch(() => {
            // 用户取消
        })
    }
}

// 执行批量审核
const doBatchAudit = async (status: string, remark: string) => {
    const ids = selectedRows.value.map(row => row.id)
    const success = await adStore.batchAuditAdvertisement(ids, status, remark)

    if (success) {
        // 重新加载数据
        loadData()
    }
}

// 处理支付广告
const handlePay = async (row: Advertisement) => {
    try {
        // 创建广告支付订单
        const orderNo = await adStore.createAdPayOrder(row.id); // 从响应中获取订单号
        if (orderNo) {
            // 获取订单详情
            const orderDetail = await adStore.getVirtualOrderDetail(orderNo);
            // 显示支付对话框
            paymentDialogVisible.value = true;
            currentOrderNo.value = orderNo;
            currentOrder.value = orderDetail;
        }
    } catch (error) {
        console.error('创建支付订单失败:', error);
    }
}

// 处理取消订单成功事件
const handleCancelSuccess = () => {
    loadData(); // 刷新广告列表
}

// 提交表单
const submitForm = async () => {
    try {
        // 先验证表单
        await formRef.value.validate()

        // 打印当前表单数据，检查是否正确
        console.log('提交前的表单数据:', JSON.stringify(formData))

        // 准备提交数据
        const submitData = {
            id: formData.id,
            adName: formData.adName,
            adType: formData.adType,
            adUrl: formData.adUrl,
            jumpUrl: formData.jumpUrl,
            status: "0", // 默认待审核状态
            delFlag: "0", // 默认未删除
            type: formData.type
        }

        console.log('准备提交的数据:', submitData)

        let result
        if (isEdit.value) {
            // 编辑模式
            result = await adStore.updateAdvertisement(submitData)
        } else {
            // 添加模式
            result = await adStore.addAdvertisement(submitData)
        }

        if (result) {
            ElMessage.success(isEdit.value ? '修改成功' : '添加成功')
            dialogVisible.value = false
            loadData()
        }
    } catch (error) {
        console.error('表单验证或提交失败:', error)
    }
}
</script>

<style>
/* 全局图片预览样式 - 确保预览弹窗总是在最顶层 */
.el-image-viewer__wrapper {
    z-index: 3000 !important;
}
</style>

<style scoped>
.ad-container {
    padding: 20px;
}

.operation-area {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.search-area {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.search-input, .search-select {
    width: 200px;
}

.pagination-container {
    margin-top: 20px;
    text-align: right;
}

.info-icon {
    margin-left: 5px;
    color: #909399;
    font-size: 14px;
    cursor: help;
}

.dialog-footer {
    padding-top: 10px;
    text-align: right;
}

/* 批量操作区域 */
.batch-actions {
    margin: 15px 0;
    display: flex;
    gap: 10px;
}

/* 表单样式 */
.ad-form-container {
    max-width: 100%;
}

.avatar-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 178px;
    height: 178px;
}

.avatar-uploader:hover {
    border-color: #409EFF;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
}

.avatar {
    width: 178px;
    height: 178px;
    display: block;
    object-fit: cover;
}

.upload-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
}
</style>
