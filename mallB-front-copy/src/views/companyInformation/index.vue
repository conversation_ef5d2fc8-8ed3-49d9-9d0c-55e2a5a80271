<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="main">
            <div class="container">
                <!-- 上部商家号和退出按钮 -->
                <!--                <div class="main-top">-->
                <!--                    <div class="main-top-content">-->
                <!--                        <span>商家号</span>-->
                <!--                        <span>190384400333</span>-->
                <!--                        <el-button type="primary">退出</el-button>-->
                <!--                    </div>-->
                <!--                </div>-->
                <div class="main-content">信息</div>
                <div>
                    <div class="from">
                        <div class="form-container">
                            <form>
                                <div class="form-group">
                                    <label for="company">公司名称：</label>
                                    <input id="company" v-model="state.shop.businessName" placeholder="请输入公司名称"
                                           type="text"/>
                                </div>

                                <div class="form-group">
                                    <label for="name">姓　名：</label>
                                    <input id="name" v-model="state.shop.legalPerson" placeholder="请输入姓名"
                                           type="text"/>
                                </div>

                                <div class="form-group">
                                    <label for="phone">手机号：</label>
                                    <input id="phone" v-model="state.shop.legalPersonPhone" placeholder="请输入手机号"
                                           type="text"/>
                                </div>

                                <button class="submit-btn" type="button" @click.prevent="submitQY">提交</button>
                            </form>
                        </div>
                    </div>
                    <!-- 商家信息表单盒子 -->
                    <div class="main-content">商家信息</div>
                    <div class="form-container business-info-form">
                        <el-form :model="businessInfo" label-position="top" label-width="120px">

                            <!-- 第一行：店铺名称 -->
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="店铺名称">
                                        <el-input v-model="state.shop.name" placeholder="请输入店铺名称"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <!-- 第二行：Logo上传 -->
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="Logo上传">
                                        <el-upload
                                            :action="UPLOAD_URL"
                                            :before-upload="beforeAvatarUpload"
                                            :file-list="logoFileList"
                                            :on-remove="handleRemove"
                                            :on-success="handleAvatarSuccess"
                                            list-type="picture-card"
                                        >
                                            <el-icon>
                                                <Plus/>
                                            </el-icon>
                                        </el-upload>
                                        <div class="el-upload__tip">建议尺寸：200x200px，仅支持JPG/PNG格式</div>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <!-- 第三行：四级地址 -->
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="地址">
                                        <!-- 省份选择 -->
                                        <div class="address-selectors">
                                            <el-select
                                                v-model="provinceSelected"
                                                class="address-select"
                                                placeholder="请选择省份"
                                                popper-class="address-dropdown"
                                                @change="handleProvinceChange">
                                                <el-option
                                                    v-for="item in provinceOptions"
                                                    :key="item.code"
                                                    :label="item.name"
                                                    :value="item.code">
                                                    {{ item.name }}
                                                </el-option>
                                            </el-select>

                                            <!-- 城市选择 -->
                                            <el-select
                                                v-model="citySelected"
                                                class="address-select"
                                                placeholder="请选择城市"
                                                popper-class="address-dropdown"
                                                @change="handleCityChange">
                                                <el-option
                                                    v-for="item in cityOptions"
                                                    :key="item.code"
                                                    :label="item.name"
                                                    :value="item.code">
                                                    {{ item.name }}
                                                </el-option>
                                            </el-select>

                                            <!-- 区县选择 -->
                                            <el-select
                                                v-model="districtSelected"
                                                class="address-select"
                                                placeholder="请选择区/县"
                                                popper-class="address-dropdown"
                                                @change="handleDistrictChange">
                                                <el-option
                                                    v-for="item in districtOptions"
                                                    :key="item.code"
                                                    :label="item.name"
                                                    :value="item.code">
                                                    {{ item.name }}
                                                </el-option>
                                            </el-select>

                                            <!-- 街道选择 -->
                                            <el-select
                                                v-model="townSelected"
                                                class="address-select"
                                                placeholder="请选择街道"
                                                popper-class="address-dropdown"
                                                @change="handleTownChange">
                                                <el-option
                                                    v-for="item in townOptions"
                                                    :key="item.code"
                                                    :label="item.name"
                                                    :value="item.code">
                                                    {{ item.name }}
                                                </el-option>
                                            </el-select>
                                        </div>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <!-- 第四行：详细地址 -->
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="详细地址">
                                        <el-input v-model="state.shop.address"
                                                  placeholder="例如：中山路100号华联大厦B座5层"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <!-- 第五行：资质证件上传 -->
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="资质证件上传">
                                        <el-upload
                                            :action="UPLOAD_URL"
                                            :before-upload="beforeAvatarUpload"
                                            :file-list="certFileList"
                                            :on-remove="handleRemove"
                                            :on-success="handleSuccess"
                                            list-type="picture-card"
                                        >
                                            <el-icon>
                                                <Plus/>
                                            </el-icon>
                                        </el-upload>

                                        <div class="el-upload__tip">请上传图片，支持JPG/PNG格式</div>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <!-- 提交按钮 -->
                            <el-row :gutter="20">
                                <el-col :span="24" style="text-align: center;">
                                    <el-button style="width: 120px; margin-top: 20px;" type="primary"
                                               @click.prevent="submitDPXX">
                                        提交
                                    </el-button>
                                </el-col>
                            </el-row>

                        </el-form>
                    </div>
                    <div class="main-content">收款信息(绑定之后，不允许修改，请反复确认)</div>
                    <div class="form-container payment-info-form">
                        <el-form ref="paymentFormRef"
                                 :model="state.shopBankAccount"
                                 :rules="rules"
                                 label-position="top"
                                 label-width="120px"
                        >
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="对公银行账号" prop="bankAccountNumber">
                                        <el-input v-model="state.shopBankAccount.bankAccountNumber"
                                                  placeholder="请输入银行账号"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="公司开户名字" prop="bankAccountName">
                                        <el-input v-model="state.shopBankAccount.bankAccountName"
                                                  placeholder="请输入开户人姓名"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="银行名称" prop="bankName">
                                        <el-input v-model="state.shopBankAccount.bankName"
                                                  placeholder="请输入开户人姓名"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item label="开户支行" prop="bankBranchName">
                                        <el-input v-model="state.shopBankAccount.bankBranchName"
                                                  placeholder="例如：中国工商银行XX分行"/>
                                    </el-form-item>
                                </el-col>
                            </el-row>

<!--                            <el-row :gutter="20">-->
<!--                                <el-col :span="24">-->
<!--                                    <el-form-item label="支付宝账号">-->
<!--                                        <el-input v-model="state.shopBankAccount.alipayAccount"-->
<!--                                                  placeholder="请输入支付宝绑定手机号或邮箱"/>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                            </el-row>-->

<!--                            <el-row :gutter="20">-->
<!--                                <el-col :span="24">-->
<!--                                    <el-form-item label="支付宝姓名">-->
<!--                                        <el-input v-model="state.shopBankAccount.alipayRealName"-->
<!--                                                  placeholder="请输入与支付宝账号匹配的姓名"/>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                            </el-row>-->

<!--                            <el-row :gutter="20">-->
<!--                                <el-col :span="24">-->
<!--                                    <el-form-item label="微信账号">-->
<!--                                        <el-input v-model="state.shopBankAccount.wechatAccount"-->
<!--                                                  placeholder="请输入微信绑定手机号"/>-->
<!--                                    </el-form-item>-->
<!--                                </el-col>-->
<!--                            </el-row>-->

                            <el-row v-if="!state.shopBankAccount.id" :gutter="20">
                                <el-col :span="24" style="text-align: center;">
                                    <el-button style="width: 120px; margin-top: 20px;" type="primary"
                                               @click.prevent="submitShopBankAccount()">
                                        提交
                                    </el-button>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import {useRouter} from 'vue-router'
import {Session} from '@/utils/storage.js'
import {computed, onMounted, reactive, ref} from 'vue'
import {shipping} from "@/stores/shipping.js";
import {useAreaStore} from "@/stores/area";
import {ElMessage} from 'element-plus';
import {Plus} from '@element-plus/icons-vue';
import {COS_URL} from '../../config/baseUrl'

const baseUrl = import.meta.env.VITE_IP;
// 上传相关
const UPLOAD_URL = baseUrl  + '/system/oss/upload'

// Logo文件列表，用于回显
const logoFileList = computed(() => {
    if (state.shop.logo) {
        return [{
            name: 'logo',
            url: `${COS_URL}/${state.shop.logo}`
        }];
    }
    return [];
});

// 资质证件文件列表，用于回显
const certFileList = computed(() => {
    if (state.shop.otherCertificate) {
        return [{
            name: 'certificate',
            url: `${COS_URL}/${state.shop.otherCertificate}`
        }];
    }
    return [];
});

// 省市区街道选项
const provinceOptions = ref([]);
const cityOptions = ref([]);
const districtOptions = ref([]);
const townOptions = ref([]);

// 省市区街道选中值
const provinceSelected = ref('');
const citySelected = ref('');
const districtSelected = ref('');
const townSelected = ref('');

// 上传请求头
const headers = {
    Authorization: `Bearer ${Session.getToken()}`
}

// 获取商家信息
const getShopInfo = async () => {
    try {
        let result = await shipping().GetShopInfo();
        if (!result || !result.data) {
            return;
        }
        state.shop = result.data;
        state.shopBankAccount = state.shop.shopBankAccount || {};
        // 先加载所有省份数据
        await loadProvinceData();

        // 然后初始化地址选择器
        await initAddressSelectors();
    } catch (error) {
        ElMessage.error('获取商家信息失败');
    }
};

// 加载省份数据
const loadProvinceData = async () => {
    try {
        console.log('加载省份数据...');
        const res = await useAreaStore().GetAreasByParentId(0);
        if (res.code === 200 && res.data) {
            provinceOptions.value = res.data;
            console.log('省份数据加载成功, 共', provinceOptions.value.length, '项');
        } else {
            console.error('省份数据返回异常:', res);
        }
    } catch (error) {
        console.error('加载省份数据失败:', error);
    }
};

// 初始化地址选择器
const initAddressSelectors = async () => {
    console.log('初始化地址选择器开始...');
    console.log('当前shop数据:', JSON.stringify({
        provinceCode: state.shop.provinceCode,
        province: state.shop.province,
        cityCode: state.shop.cityCode,
        city: state.shop.city,
        districtCode: state.shop.districtCode,
        district: state.shop.district,
        townCode: state.shop.townCode,
        town: state.shop.town
    }));

    // 转换类型确保是数字
    if (state.shop.provinceCode) {
        state.shop.provinceCode = Number(state.shop.provinceCode);
    }
    if (state.shop.cityCode) {
        state.shop.cityCode = Number(state.shop.cityCode);
    }
    if (state.shop.districtCode) {
        state.shop.districtCode = Number(state.shop.districtCode);
    }
    if (state.shop.townCode) {
        state.shop.townCode = Number(state.shop.townCode);
    }

    try {
        if (state.shop.provinceCode) {
            // 设置省份
            provinceSelected.value = state.shop.provinceCode;
            console.log('设置省份:', provinceSelected.value);

            // 加载城市数据
            await loadCityData(state.shop.provinceCode);

            // 查找并设置省份名称（如果不存在）
            if (!state.shop.province && provinceOptions.value.length > 0) {
                const province = provinceOptions.value.find(p => Number(p.code) === Number(state.shop.provinceCode));
                if (province) {
                    state.shop.province = province.name;
                    console.log('初始化时设置省份名称:', state.shop.province);
                }
            }

            if (state.shop.cityCode) {
                // 设置城市
                citySelected.value = state.shop.cityCode;
                console.log('设置城市:', citySelected.value);

                // 加载区县数据
                await loadDistrictData(state.shop.cityCode);

                // 查找并设置城市名称（如果不存在）
                if (!state.shop.city && cityOptions.value.length > 0) {
                    const city = cityOptions.value.find(c => Number(c.code) === Number(state.shop.cityCode));
                    if (city) {
                        state.shop.city = city.name;
                        console.log('初始化时设置城市名称:', state.shop.city);
                    }
                }

                if (state.shop.districtCode) {
                    // 设置区县
                    districtSelected.value = state.shop.districtCode;
                    console.log('设置区县:', districtSelected.value);

                    // 加载街道数据
                    await loadTownData(state.shop.districtCode);

                    // 查找并设置区县名称（如果不存在）
                    if (!state.shop.district && districtOptions.value.length > 0) {
                        const district = districtOptions.value.find(d => Number(d.code) === Number(state.shop.districtCode));
                        if (district) {
                            state.shop.district = district.name;
                            console.log('初始化时设置区县名称:', state.shop.district);
                        }
                    }

                    if (state.shop.townCode) {
                        // 设置街道
                        townSelected.value = state.shop.townCode;
                        console.log('设置街道:', townSelected.value);

                        // 查找并设置街道名称（如果不存在）
                        if (!state.shop.town && townOptions.value.length > 0) {
                            const town = townOptions.value.find(t => Number(t.code) === Number(state.shop.townCode));
                            if (town) {
                                state.shop.town = town.name;
                                console.log('初始化时设置街道名称:', state.shop.town);
                            }
                        }
                    }
                }
            }
        }
    } catch (error) {
        console.error('初始化地址选择器出错:', error);
    }
};

// 加载城市数据
const loadCityData = async (provinceCode) => {
    try {
        // 确保是数字类型
        if (provinceCode !== null && provinceCode !== undefined) {
            provinceCode = Number(provinceCode);
        }

        console.log('加载城市数据, 省份代码:', provinceCode, '类型:', typeof provinceCode);
        cityOptions.value = []; // 清空已有数据

        const res = await useAreaStore().GetAreasByParentId(provinceCode);
        if (res.code === 200 && res.data) {
            cityOptions.value = res.data;
            console.log('城市数据加载成功, 共', cityOptions.value.length, '项');
        } else {
            console.error('城市数据返回异常:', res);
        }
    } catch (error) {
        console.error('加载城市数据失败:', error);
    }
};

// 加载区县数据
const loadDistrictData = async (cityCode) => {
    try {
        // 确保是数字类型
        if (cityCode !== null && cityCode !== undefined) {
            cityCode = Number(cityCode);
        }

        console.log('加载区县数据, 城市代码:', cityCode, '类型:', typeof cityCode);
        districtOptions.value = []; // 清空已有数据

        const res = await useAreaStore().GetAreasByParentId(cityCode);
        if (res.code === 200 && res.data) {
            districtOptions.value = res.data;
            console.log('区县数据加载成功, 共', districtOptions.value.length, '项');
        } else {
            console.error('区县数据返回异常:', res);
        }
    } catch (error) {
        console.error('加载区县数据失败:', error);
    }
};

// 加载街道数据
const loadTownData = async (districtCode) => {
    try {
        // 确保是数字类型
        if (districtCode !== null && districtCode !== undefined) {
            districtCode = Number(districtCode);
        }

        console.log('加载街道数据, 区县代码:', districtCode, '类型:', typeof districtCode);
        townOptions.value = []; // 清空已有数据

        const res = await useAreaStore().GetAreasByParentId(districtCode);
        if (res.code === 200 && res.data) {
            townOptions.value = res.data;
            console.log('街道数据加载成功, 共', townOptions.value.length, '项');
        } else {
            console.error('街道数据返回异常:', res);
        }
    } catch (error) {
        console.error('加载街道数据失败:', error);
    }
};

// 省份变更处理
const handleProvinceChange = async (provinceCode) => {
    console.log('省份变更为:', provinceCode, '类型:', typeof provinceCode);

    // 确保是数字类型
    if (provinceCode !== null && provinceCode !== undefined) {
        provinceCode = Number(provinceCode);
    }

    // 清空城市和区县
    citySelected.value = '';
    districtSelected.value = '';
    townSelected.value = '';  // 清空街道
    cityOptions.value = [];
    districtOptions.value = [];
    townOptions.value = [];  // 清空街道选项

    if (provinceCode) {
        // 查找选中的省份名称
        const selectedProvince = provinceOptions.value.find(p => Number(p.code) === Number(provinceCode));

        // 更新省份代码和名称
        state.shop.provinceCode = provinceCode;
        state.shop.province = selectedProvince ? selectedProvince.name : '';
        console.log('设置省份名称:', state.shop.province);

        // 清空城市和区县代码及名称
        state.shop.cityCode = '';
        state.shop.city = '';
        state.shop.districtCode = '';
        state.shop.district = '';
        state.shop.townCode = '';  // 清空街道代码
        state.shop.town = '';      // 清空街道名称

        // 加载城市数据
        await loadCityData(provinceCode);
    }
};

// 城市变更处理
const handleCityChange = async (cityCode) => {
    console.log('城市变更为:', cityCode, '类型:', typeof cityCode);

    // 确保是数字类型
    if (cityCode !== null && cityCode !== undefined) {
        cityCode = Number(cityCode);
    }

    // 清空区县
    districtSelected.value = '';
    townSelected.value = '';  // 清空街道
    districtOptions.value = [];
    townOptions.value = [];   // 清空街道选项

    if (cityCode) {
        // 查找选中的城市名称
        const selectedCity = cityOptions.value.find(c => Number(c.code) === Number(cityCode));

        // 更新城市代码和名称
        state.shop.cityCode = cityCode;
        state.shop.city = selectedCity ? selectedCity.name : '';
        console.log('设置城市名称:', state.shop.city);

        // 清空区县代码和名称
        state.shop.districtCode = '';
        state.shop.district = '';
        state.shop.townCode = '';  // 清空街道代码
        state.shop.town = '';      // 清空街道名称

        // 加载区县数据
        await loadDistrictData(cityCode);
    }
};

// 区县变更处理
const handleDistrictChange = async (districtCode) => {
    console.log('区县变更为:', districtCode, '类型:', typeof districtCode);

    // 确保是数字类型
    if (districtCode !== null && districtCode !== undefined) {
        districtCode = Number(districtCode);
    }

    // 清空街道
    townSelected.value = '';
    townOptions.value = [];

    if (districtCode) {
        // 查找选中的区县名称
        const selectedDistrict = districtOptions.value.find(d => Number(d.code) === Number(districtCode));

        // 更新区县代码和名称
        state.shop.districtCode = districtCode;
        state.shop.district = selectedDistrict ? selectedDistrict.name : '';
        console.log('设置区县名称:', state.shop.district);

        // 清空街道代码及名称
        state.shop.townCode = '';
        state.shop.town = '';

        // 加载街道数据
        await loadTownData(districtCode);
    }
};

// 街道变更处理
const handleTownChange = async (townCode) => {
    console.log('街道变更为:', townCode, '类型:', typeof townCode);

    // 确保是数字类型
    if (townCode !== null && townCode !== undefined) {
        townCode = Number(townCode);
    }

    if (townCode) {
        // 查找选中的街道名称
        const selectedTown = townOptions.value.find(t => Number(t.code) === Number(townCode));

        // 更新街道代码和名称
        state.shop.townCode = townCode;
        state.shop.town = selectedTown ? selectedTown.name : '';
        console.log('设置街道名称:', state.shop.town);
    }
};

const state = reactive({
    addressOptions: [],
    buttonList: [],
    shop: {
        addressAll: [] // 初始化地址数组
    },
    shopBankAccount: {}
})
const router = useRouter()
const handleButtonClick = (item) => {
    router.push(item.component)
}

// 添加调试显示
onMounted(() => {
    // 调试: 在控制台打印所有组件的生命周期钩子
    console.log('组件onMounted开始执行');
});

// 表单验证规则
const rules = reactive({
    bankAccountNumber: [
        {required: true, message: '银行账号不能为空', trigger: 'blur'}
    ],
    bankAccountName: [
        {required: true, message: '公司开户名字不能为空', trigger: 'blur'}
    ],
    bankName: [
        {required: true, message: '银行名称不能为空', trigger: 'blur'}
    ],
    bankBranchName: [
        {required: true, message: '开户支行不能为空', trigger: 'blur'}
    ]
});

// 提交企业信息
const submitQY = () => {
    let data = {
        id: state.shop.id,
        businessName: state.shop.businessName,
        legalPerson: state.shop.legalPerson,
        legalPersonPhone: state.shop.legalPersonPhone,
    }
    updateShop(data)
}
// 提交店铺信息
const submitDPXX = () => {
    if (!state.shop.name) {
        ElMessage.error('请输入店铺名称');
        return;
    }

    // 获取地址选择器的当前值
    let provinceCode = provinceSelected.value;
    let cityCode = citySelected.value;
    let districtCode = districtSelected.value;
    let townCode = townSelected.value;

    // 确保所有值都是数字类型
    if (provinceCode) provinceCode = Number(provinceCode);
    if (cityCode) cityCode = Number(cityCode);
    if (districtCode) districtCode = Number(districtCode);
    if (townCode) townCode = Number(townCode);

    // 查找并设置名称
    let provinceName = '';
    let cityName = '';
    let districtName = '';
    let townName = '';

    if (provinceCode && provinceOptions.value.length > 0) {
        const province = provinceOptions.value.find(p => Number(p.code) === provinceCode);
        if (province) {
            provinceName = province.name;
        }
    }

    if (cityCode && cityOptions.value.length > 0) {
        const city = cityOptions.value.find(c => Number(c.code) === cityCode);
        if (city) {
            cityName = city.name;
        }
    }

    if (districtCode && districtOptions.value.length > 0) {
        const district = districtOptions.value.find(d => Number(d.code) === districtCode);
        if (district) {
            districtName = district.name;
        }
    }

    if (townCode && townOptions.value.length > 0) {
        const town = townOptions.value.find(t => Number(t.code) === townCode);
        if (town) {
            townName = town.name;
        }
    }

    // 强制同步选择器值到state.shop
    state.shop.provinceCode = provinceCode;
    state.shop.province = provinceName;
    state.shop.cityCode = cityCode;
    state.shop.city = cityName;
    state.shop.districtCode = districtCode;
    state.shop.district = districtName;
    state.shop.townCode = townCode || '';  // 如果没有选择街道，确保值为空字符串而不是undefined
    state.shop.town = townName || '';      // 如果没有选择街道名称，确保值为空字符串

    if (!state.shop.provinceCode || !state.shop.cityCode || !state.shop.districtCode) {
        ElMessage.error('请选择完整的省市区地址');
        return;
    }

    // 确认最终提交的数据
    let data = {
        id: state.shop.id,
        name: state.shop.name,
        logo: state.shop.logo,
        provinceCode: state.shop.provinceCode,
        province: state.shop.province,
        cityCode: state.shop.cityCode,
        city: state.shop.city,
        districtCode: state.shop.districtCode,
        district: state.shop.district,
        townCode: state.shop.townCode,
        town: state.shop.town,
        address: state.shop.address,
        otherCertificate: state.shop.otherCertificate
    }

    console.log('最终提交的数据:', data);
    updateShop(data)
}
// 银行卡保存
const submitShopBankAccount = async () => {
    let data = state.shopBankAccount
    if (state.shopBankAccount.id) {
        ElMessage.error('不允许进行修改')
        return;
        // let res = await shipping().UpdateShopBankAccount(data)
        // if (res.code == 200) {
        //     ElMessage.success('保存成功')
        // }
    } else {
        let res = await shipping().SaveShopBankAccount(data);
        if (res.code == 200) {
            ElMessage.success('保存成功')
        }
    }
    await getShopInfo()
}

// 修改商家信息
const updateShop = async (data) => {
    let res = await shipping().UpdateShop(data)
    if (res.code == 200) {
        ElMessage.success(res.msg)
        // 成功后刷新数据
        await getShopInfo();
    } else {
        ElMessage.error(res.msg)
    }
}
// 在组件挂载时执行
onMounted(async () => {
    let menuList = Session.getMenu2()
    let menuId = Session.get('homeMenuId');
    if (menuId == null) {
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.openType == 2) {
                if (menuId == null) {
                    menuId = element.menuId
                }
            }
        }
    }
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index];
        if (element.menuId == menuId) {
            state.buttonList = element.children
            console.log(state.menuList)
        }
    }
    // 直接调用获取商家信息函数，内部会处理省份数据加载
    await getShopInfo();
})
// 商家信息表单数据模型
const businessInfo = ref({
    shopName: '',         // 店铺名称
    logo: null,           // Logo文件
    address: [],          // 地址数组 [省, 市, 区, 街道]
    detailAddress: '',    // 详细地址
    certificates: []      // 资质证件文件列表
})
// 上传限制
const beforeAvatarUpload = (rawFile) => {
    console.log(rawFile.type)
    if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png') {
        ElMessage.error('只能上传图片')
        return false
    } else if (rawFile.size / 1024 / 1024 > 2) {
        ElMessage.error('图片最大2MB!')
        return false
    }
    return true
}
//上传LOGO
const handleAvatarSuccess = (response, file) => {
    console.log('Logo上传成功，响应:', response);
    if (response && response.code === 200 && response.data) {
        // 保存返回的文件路径
        state.shop.logo = response.data.filePath;
        console.log('保存的Logo路径:', state.shop.logo);
        // 如果已有URL部分，移除它
        if (state.shop.logo && state.shop.logo.includes(COS_URL + '/')) {
            state.shop.logo = state.shop.logo.replace(COS_URL + '/', '');
        }
    } else {
        console.error('Logo上传响应异常:', response);
    }
}
// 删除LOGO
const handleRemove = (file) => {
    console.log('移除Logo');
    state.shop.logo = null;
}
// 处理资质证件上传
const handleSuccess = (response, file) => {
    console.log('证件上传成功，响应:', response);
    if (response && response.code === 200 && response.data) {
        // 保存返回的文件路径
        state.shop.otherCertificate = response.data.filePath;
        console.log('保存的证件路径:', state.shop.otherCertificate);
        // 如果已有URL部分，移除它
        if (state.shop.otherCertificate && state.shop.otherCertificate.includes(COS_URL + '/')) {
            state.shop.otherCertificate = state.shop.otherCertificate.replace(COS_URL + '/', '');
        }
    } else {
        console.error('证件上传响应异常:', response);
    }
}
// 处理资质证件删除
const beforeRemove = (file) => {
    console.log('移除证件');
    state.shop.otherCertificate = null;
}

</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
    width: 100%;
    flex-direction: column;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.middle-container {
    border: 1px solid red;
    flex: 1;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    height: 2000px;
}

.header {
    display: flex;
    align-items: center;
    flex: 1;
    height: 61px;
    padding: 0;
    background-color: #83A4EB;
    margin-left: 235px;

    .putaway {
        width: 172px;
        height: 31px;
        margin-left: 167px;
        font-size: 24px;
        color: #000;
    }

    .soldOut {
        width: 182px;
        height: 33px;
        margin-left: 277px;
        margin-right: 277px;
        font-size: 24px;
        color: #000;
    }

    .allProduct {
        width: 224px;
        height: 42px;
        margin-right: 122px;
        font-size: 24px;
        color: #000;
    }
}

.main {
    position: absolute;
    left: 250px;
    right: 0;
    display: flex;
    flex-direction: column;
}

.main-top {
    display: flex;
    justify-content: flex-end; /* 整个盒子靠右对齐 */
    padding: 10px 20px; /* 可根据需要调整内边距 */
    position: fixed;
    top: 50px;
    right: 20px;
    z-index: 9999;
}

.main-top-content {
    display: flex;
    align-items: center; /* 垂直居中 */
    gap: 15px; /* 每个子元素之间的间距 */
    background-color: #f5f7fa;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.main-content {
    width: 100%;
    height: 50px;
    font-weight: bold;
    font-size: 20px;
    display: flex;
    line-height: 50px;
    padding-left: 100px;
    border-bottom: 3px solid #000;
}

.from {
    width: 100%;
    border-bottom: 3px solid #000;
}

.form-container {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 30px 0px;
    max-width: 800px;
    margin: 0 auto;

}

/* 表单组 */
.form-group {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

/* 标签固定宽度，对齐 */
.form-group label {
    display: inline-block;
    width: 100px;
    text-align: right;
    margin-right: 10px;
    font-weight: bold;
}

/* 输入框样式 */
.form-group input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    width: 500px;
}

/* 提交按钮 */
.submit-btn {
    width: 120px;
    padding: 8px 12px;
    background-color: #409EFF;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    display: block;
    margin: 20px auto 0;
}

.submit-btn:hover {
    background-color: #337ecc;
}

.filter-row {
    display: flex;
    align-items: center;
    margin-left: 93px;
    gap: 20px;
}

.product-selector {
    width: 229px;
    height: 52px;
}

.business-info-form {

}

:deep(.el-form-item__label) {
    color: #000;
    font-size: 16px;
}

.required-label::before {
    content: '*';
    color: #ff4949; /* 红色星号 */
    margin-right: 4px;
}

.commBox {
    background-color: #CCCCCC;
    width: 281px;
    height: 42px;
    display: flex;
    align-items: center;
    font-size: 24px;
    margin-top: -10px;

    .Text {
        margin-left: 13px;

        &:first-child {
            min-width: 120px;
        }
    }

    input {
        flex: 1;
        border: none;
        background: transparent;
        outline: none;
        font-size: 24px;
    }
}

.filter-row1 {
    display: flex;
    align-items: center;
    margin-left: 40px;
    gap: 20px;
}

.product-selector1 {
    width: 196px;
    height: 52px;
}

.brandBox {
    background-color: #3A58CF;
    width: 145px;
    height: 42px;
    margin-top: -10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;

    .brand {
        font-size: 24px;
        color: #fff;
    }
}

.priceBox {
    margin-left: 113px;
    margin-top: 25px;
    display: flex;
    align-items: center; /* 添加这行确保垂直居中 */

    .pirceRange {
        font-size: 30px;
        line-height: 1; /* 确保文本行高一致 */
    }

    .leftPrice {
        width: 277px;
        height: 32px;
        margin-left: 10px;
        background-color: #ccc;
        vertical-align: middle; /* 添加垂直对齐 */
    }

    .to {
        margin-left: 10px;
        line-height: 1; /* 确保文本行高一致 */
    }

    .rightPrice {
        width: 277px;
        height: 32px;
        margin-left: 10px;
        background-color: #ccc;
        vertical-align: middle; /* 添加垂直对齐 */
    }

    .nullBtn {
        width: 277px;
        height: 32px;
        margin-left: 105px;
        background-color: #fff;
        border: 3px solid #3A58CF;
        vertical-align: middle; /* 添加垂直对齐 */
        line-height: 1; /* 重置按钮行高 */
        padding: 0; /* 移除默认内边距 */
    }
}

.product-type-row {
    display: flex;
    align-items: center;
    margin-left: 113px;
    margin-top: 25px;
    gap: 20px;

    .product-type-label {
        font-size: 30px;
        min-width: 120px;
    }

    .checkbox-container {
        display: flex;
        align-items: center;
        gap: 30px;
        margin-left: 129px;

        :deep(.el-checkbox.square-checkbox) {
            .el-checkbox__inner {
                border-radius: 4px; /* 方形复选框 */
                width: 20px;
                height: 20px;

                &::after {
                    top: 2px;
                    left: 6px;
                }
            }

            .el-checkbox__label {
                font-size: 24px;
                color: #333;
            }
        }
    }

    .export-btn {
        margin-left: 430px;
        width: 120px;
        height: 42px;
        font-size: 20px;
        background-color: #FF8D1A;
        border: none;
        color: #000;

    }
}

.allButton {
    margin-top: 32px;
    margin-left: 303px;

    .button {
        width: 247px;
        height: 59px;
        margin-right: 41px;
        border-radius: 30px;
        border: 2px solid #3A58CF;
        font-size: 30px;

        color: #3A58CF;
    }
}

.table-container {
    margin-top: 20px;
    padding: 0 20px;

    :deep(.custom-header) {
        th {
            background-color: #83A4EB !important;
            color: #000;
            font-weight: bold;
            font-size: 16px;

            .el-checkbox {
                .el-checkbox__inner {
                    border-radius: 4px;
                    width: 16px;
                    height: 16px;
                }

                .el-checkbox__label {
                    font-size: 16px;
                    color: #000;
                }
            }
        }
    }

    :deep(.custom-row) {
        td {
            background-color: #D2E0FB;

            .el-checkbox {
                .el-checkbox__inner {
                    border-radius: 4px;
                    width: 16px;
                    height: 16px;
                }

                .el-checkbox__label {
                    font-size: 14px;
                }
            }
        }

        &:hover td {
            background-color: #b8cdf9 !important;
        }
    }

    :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;

        .el-table__cell {
            padding: 12px 0;
        }
    }
}

// 地址选择器容器样式
.address-selectors {
    display: flex;
    gap: 15px;
    width: 100%;
}

// 地址选择器样式
.address-select {
    flex: 1;
    min-width: 120px;
    max-width: 250px;
}

// 全局样式，修复下拉菜单宽度
:deep(.address-dropdown) {
    min-width: 200px !important;
}

// 确保选择器内容完全可见
:deep(.el-select-dropdown__item) {
    padding: 0 15px;
    white-space: normal;
    word-break: break-all;
    height: auto;
    min-height: 34px;
    line-height: 1.5;
    display: flex;
    align-items: center;
}

// 添加固定宽度
:deep(.el-input__wrapper) {
    width: 100%;
}
</style>
