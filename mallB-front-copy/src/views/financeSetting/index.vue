<script setup>
import {useRouter} from 'vue-router'
import {onMounted} from 'vue';
import {Session} from "@/utils/storage.js";
import {permissionSetting} from "@/stores/permissionSetting.js";
import {ElMessage} from 'element-plus'

const router = useRouter()

const state = reactive({
    form: {},
    buttonList: []
})
const getConfigurationData = async () => {
    let result = await permissionSetting().ConfigurationData(7);
    if (result.data != null) {
        state.form = result.data
    }
}
const saveSettings = async () => {
    let data = {
        "configValue": state.form
    }
    let result = await permissionSetting().GetThroughList(data, 7);
    if (result.code == 200) {
        ElMessage.success('配置成功')
        await getConfigurationData()
    }
}
const handleButtonClick = (item) => {
    router.push(item.component)
}
onMounted(() => {
    getConfigurationData()
    let menuList = Session.getMenu2()
    let menuId = Session.get('homeMenuId');
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index];
        if (element.menuId == menuId) {
            state.buttonList = element.children
        }
    }
})

</script>

<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>

        <div class="right-content">
            <div class="coupon-item">
                <span class="coupon-label">平台补贴金</span>
                <div class="radio-group">
                    <label class="radio-option">
                        <input
                            v-model="state.form.voucherOption"
                            class="square-radio"
                            name="voucher"
                            type="radio"
                            value="1"
                        />
                        <span>开启</span>
                    </label>

                    <label class="radio-option">
                        <input
                            v-model="state.form.voucherOption"
                            class="square-radio"
                            name="voucher"
                            type="radio"
                            value="0"
                        />
                        <span>关闭</span>
                    </label>
                </div>
            </div>

            <div class="coupon-item">
                <span class="coupon-label">平台促销金赠送</span>
                <div class="radio-group">
                    <label class="radio-option">
                        <input
                            v-model="state.form.promotionOption"
                            class="square-radio"
                            name="promotion"
                            type="radio"
                            value="1"
                        />
                        <span>开启</span>
                    </label>

                    <label class="radio-option">
                        <input
                            v-model="state.form.promotionOption"
                            class="square-radio"
                            name="promotion"
                            type="radio"
                            value="0"
                        />
                        <span>关闭</span>
                    </label>
                </div>
            </div>

            <div class="settlement-item">
                <div class="settlement-header">
                    <span class="settlement-label">手动结算到微信</span>
                    <div class="radio-group">
                        <label class="radio-option">
                            <input
                                v-model="state.form.wechatSettlementOption"
                                class="square-radio"
                                name="wechat"
                                type="radio"
                                value="1"
                            />
                            <span>开启</span>
                        </label>

                        <label class="radio-option">
                            <input
                                v-model="state.form.wechatSettlementOption"
                                class="square-radio"
                                name="wechat"
                                type="radio"
                                value="0"
                            />
                            <span>关闭</span>
                        </label>
                    </div>
                </div>

                <div class="amount-setting">
                    <div class="daily-limit">
                        <span>每日</span>
                        <input
                            v-model.number="state.form.wechatDailyLimit"
                            class="daily-limit-input"
                            min="1"
                            type="number"
                        />
                        <span>次</span>
                    </div>
                    <span class="max-amount-label">单笔最高金额</span>
                    <div class="amount-input">
                        <input
                            v-model="state.form.wechatMaxAmount"
                            class="amount-field"
                            placeholder="0.00"
                            type="text"
                        />
                        <span class="currency">￥</span>
                    </div>
                </div>
            </div>

            <div class="settlement-item">
                <div class="settlement-header">
                    <span class="settlement-label">手动结算到支付宝</span>
                    <div class="radio-group">
                        <label class="radio-option">
                            <input
                                v-model="state.form.alipaySettlementOption"
                                class="square-radio"
                                name="alipay"
                                type="radio"
                                value="1"
                            />
                            <span>开启</span>
                        </label>

                        <label class="radio-option">
                            <input
                                v-model="state.form.alipaySettlementOption"
                                class="square-radio"
                                name="alipay"
                                type="radio"
                                value="0"
                            />
                            <span>关闭</span>
                        </label>
                    </div>
                </div>

                <div class="amount-setting">
                    <div class="daily-limit">
                        <span>每日</span>
                        <input
                            v-model.number="state.form.alipayDailyLimit"
                            class="daily-limit-input"
                            min="1"
                            type="number"
                        />
                        <span>次</span>
                    </div>
                    <span class="max-amount-label">单笔最高金额</span>
                    <div class="amount-input">
                        <input
                            v-model="state.form.alipayMaxAmount"
                            class="amount-field"
                            placeholder="0.00"
                            type="text"
                        />
                        <span class="currency">￥</span>
                    </div>
                </div>
            </div>

            <div class="settlement-item">
                <div class="settlement-header">
                    <span class="settlement-label">手动结算到银行卡</span>
                    <div class="radio-group">
                        <label class="radio-option">
                            <input
                                v-model="state.form.bankSettlementOption"
                                class="square-radio"
                                name="bank"
                                type="radio"
                                value="1"
                            />
                            <span>开启</span>
                        </label>

                        <label class="radio-option">
                            <input
                                v-model="state.form.bankSettlementOption"
                                class="square-radio"
                                name="bank"
                                type="radio"
                                value="0"
                            />
                            <span>关闭</span>
                        </label>
                    </div>
                </div>

                <div class="amount-setting">
                    <div class="daily-limit">
                        <span>每日</span>
                        <input
                            v-model.number="state.form.bankDailyLimit"
                            class="daily-limit-input"
                            min="1"
                            type="number"
                        />
                        <span>次</span>
                    </div>
                    <span class="max-amount-label">单笔最高金额</span>
                    <div class="amount-input">
                        <input
                            v-model="state.form.bankMaxAmount"
                            class="amount-field"
                            placeholder="0.00"
                            type="text"
                        />
                        <span class="currency">￥</span>
                    </div>
                </div>
            </div>

            <!-- 结算手续费模块 -->
            <div class="fee-settings-item">
                <div class="fee-setting">
                    <span class="fee-label">结算手续费</span>
                    <div class="fee-input">
                        <input
                            v-model="state.form.settlementFeeRate"
                            class="fee-field"
                            placeholder=""
                            type="text"
                        />
                        <span class="percent">%</span>
                    </div>
                </div>

                <div class="amount-limit-setting">
                    <span class="amount-limit-label">每日最高结算金额</span>
                    <div class="amount-input">
                        <input
                            v-model="state.form.settlementAmountLimit"
                            class="amount-field"
                            placeholder="0.00"
                            type="text"
                        />
                        <span class="currency">￥</span>
                    </div>
                </div>
            </div>

            <!-- 居中的保存按钮 -->
            <div class="save-button-container">
                <el-button
                    class="save-btn"
                    type="primary"
                    @click="saveSettings"
                >
                    提交
                </el-button>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.right-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.coupon-item, .settlement-item, .fee-settings-item {
    display: flex;
    flex-direction: column;
    background: #fff;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.coupon-item {
    align-items: center;
    flex-direction: row;

    .coupon-label {
        font-size: 24px;
        font-weight: 500;
        margin-right: 20px;
        min-width: 120px;
    }
}

.settlement-item {
    gap: 15px;
}

.settlement-header {
    display: flex;
    align-items: center;

    .settlement-label {
        font-size: 24px;
        font-weight: 500;
        margin-right: 20px;
        min-width: 160px;
    }
}

.amount-setting {
    display: flex;
    align-items: center;
    padding: 10px 0;
    margin-left: 180px;

    .daily-limit {
        display: flex;
        align-items: center;
        margin-right: 30px;
        font-size: 16px;
        color: #666;

        .daily-limit-input {
            width: 50px;
            padding: 5px;
            margin: 0 5px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            text-align: center;

            &:focus {
                border-color: #3A58CF;
                outline: none;
            }
        }
    }

    .max-amount-label {
        margin-right: 10px;
        font-size: 16px;
        color: #666;
    }

    .amount-input {
        position: relative;
        display: flex;
        align-items: center;

        .amount-field {
            width: 100px;
            padding: 8px 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            text-align: right;
            padding-right: 25px;

            &:focus {
                border-color: #3A58CF;
                outline: none;
            }
        }

        .currency {
            position: absolute;
            right: 10px;
            color: #666;
        }
    }
}

.fee-settings-item {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.fee-setting, .amount-limit-setting {
    display: flex;
    align-items: center;
    padding: 10px 0;

    .fee-label, .amount-limit-label {
        font-size: 16px;
        color: #666;
        min-width: 120px;
        margin-right: 20px;
    }

    .fee-input, .amount-input {
        position: relative;
        display: flex;
        align-items: center;

        .fee-field, .amount-field {
            width: 100px;
            padding: 8px 10px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            text-align: right;
            padding-right: 25px;

            &:focus {
                border-color: #3A58CF;
                outline: none;
            }
        }

        .percent, .currency {
            position: absolute;
            right: 10px;
            color: #666;
        }
    }
}

.save-button-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;

    .save-btn {
        padding: 12px 40px;
        font-size: 16px;
        background-color: #3A58CF;
        border: none;

        &:hover {
            background-color: #2a48bf;
        }
    }
}

.dialog-footer {
    display: flex;
    justify-content: center;
}

.radio-group {
    display: flex;
    gap: 15px;
}

.radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
}

/* 隐藏原生单选框 */
.square-radio {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid #ccc;
    border-radius: 3px;
    margin-right: 8px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s;
}

/* 选中状态样式 */
.square-radio:checked {
    background-color: #3A58CF;
    border-color: #3A58CF;
}

/* 选中时的内部标记 */
.square-radio:checked::after {
    content: "";
    position: absolute;
    left: 4px;
    top: 1px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}
</style>
