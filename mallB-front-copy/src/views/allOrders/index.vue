<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="right-content">
            <div class="filter-section">
                <div class="title">
                    <span>订单筛选</span>
                </div>
                <div class="filter-form">
                    <el-form :inline="true" :model="state.filterForm">
                        <el-form-item label="订单编号">
                            <el-input
                                v-model="state.filterForm.orderNo"
                                class="filter-input"
                                clearable
                                placeholder="请输入订单编号"
                            />
                        </el-form-item>
                        <el-form-item label="商品名称">
                            <el-input
                                v-model="state.filterForm.orderName"
                                class="filter-input"
                                clearable
                                placeholder="请输入商品名称"
                            />
                        </el-form-item>
                        <el-form-item label="收货人电话">
                            <el-input
                                v-model="state.filterForm.receiverPhone"
                                class="filter-input"
                                clearable
                                placeholder="请输入收货人电话"
                            />
                        </el-form-item>
                        <el-form-item label="订单状态">
                            <el-select v-model="state.filterForm.status" class="filter-input" clearable
                                       placeholder="请选择订单状态">
                                <el-option
                                    v-for="item in orderStatusList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="核销方式">
                            <el-select v-model="state.filterForm.payType" class="filter-input" clearable
                                       placeholder="请选择核销方式">
                                <el-option
                                    v-for="item in payTypeList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="时间查询">
                            <div class="date-range-container">
                                <el-date-picker
                                    v-model="state.filterForm.startTime"
                                    class="date-picker"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    placeholder="开始时间"
                                    type="datetime"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                />
                                <span class="date-separator">至</span>
                                <el-date-picker
                                    v-model="state.filterForm.endTime"
                                    class="date-picker"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    placeholder="结束时间"
                                    type="datetime"
                                    value-format="YYYY-MM-DD HH:mm:ss"
                                />
                            </div>
                        </el-form-item>
                        <el-form-item>
                            <el-button
                                :loading="loading"
                                type="primary"
                                @click="getProductList()"
                            >
                                <el-icon>
                                    <Search/>
                                </el-icon>
                                查询
                            </el-button>
                            <el-button @click="resetFilter">
                                <el-icon>
                                    <Refresh/>
                                </el-icon>
                                重置
                            </el-button>
                            <el-button :loading="exportLoading" type="success" @click="handleExport">
                                <el-icon>
                                    <Download/>
                                </el-icon>
                                导出
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
            <div class="table-container">
                <el-table
                    v-loading="loading"
                    :data="state.tableData"
                    border
                    class="order-table"
                    highlight-current-row
                    stripe
                    style="width: 100%"
                >
                    <el-table-column
                        header-class-name="table-header"
                        label="账号"
                        prop="userPhone"
                        width="120"
                    />
                    <el-table-column
                        header-class-name="table-header"
                        label="订单号"
                        prop="orderNo"
                        width="200"
                    />
                    <el-table-column
                        header-class-name="table-header"
                        label="订单状态"
                        prop="status"
                        width="100"
                    >
                        <template #default="scope">
                            <el-tag :type="getStatusType(scope.row.status)" effect="light">
                                <span v-if="scope.row.status=='0'">待支付</span>
                                <span v-if="scope.row.status=='1'">支付中</span>
                                <span v-if="scope.row.status=='2'">待发货</span>
                                <span v-if="scope.row.status=='3'">待收货</span>
                                <span v-if="scope.row.status=='4'">已完成</span>
                                <span v-if="scope.row.status=='5'">已取消</span>
                                <span v-else-if="scope.row.status=='6'">已退款</span>
                            </el-tag>
                        </template>
                    </el-table-column>
                    <!--                    <el-table-column-->
                    <!--                        header-class-name="table-header"-->
                    <!--                        label="售后状态"-->
                    <!--                        width="100"-->
                    <!--                    >-->
                    <!--                        <template #default="scope">-->
                    <!--                            <el-tag v-if="scope.row.refundStatus=='0'" effect="light" type="warning">申请退款</el-tag>-->
                    <!--                            <el-tag v-if="scope.row.refundStatus=='1'" effect="light" type="success">退款成功</el-tag>-->
                    <!--                            <el-tag v-if="scope.row.refundStatus=='2'" effect="light" type="danger">退款失败</el-tag>-->
                    <!--                        </template>-->
                    <!--                    </el-table-column>-->
                    <el-table-column
                        header-class-name="table-header"
                        label="收货方式"
                        width="100"
                    >
                        <template #default="scope">
                            <el-tag effect="light" type="success">{{ scope.row.receiverType == 1 ? '自提' : '配送' }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        header-class-name="table-header"
                        label="商品名称"
                        prop="orderName"
                        show-overflow-tooltip
                        width="140"
                    />
                    <el-table-column
                        header-class-name="table-header"
                        label="订单金额"
                        prop="totalAmount"
                        width="120"
                    >
                        <template #default="scope">
                            <span class="amount">¥{{ scope.row.totalAmount }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        header-class-name="table-header"
                        label="购买数量"
                        prop="totalQuantity"
                        width="120"
                    />
                    <el-table-column
                        header-class-name="table-header"
                        label="补贴金"
                        prop="deductionAmount"
                        width="100"
                    >
                        <template #default="scope">
                            <span v-if="scope.row.deductionAmount"
                                  class="deduction-amount">¥{{ scope.row.deductionAmount }}</span>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        header-class-name="table-header"
                        label="支付类型"
                        width="130"
                    >
                        <template #default="scope">
                            <el-tag v-if="scope.row.payType == 1" effect="plain" type="success">微信支付</el-tag>
                            <el-tag v-if="scope.row.payType == 2 || scope.row.payType == 4" effect="plain"
                                    type="primary">支付宝支付
                            </el-tag>
                            <el-tag v-if="scope.row.payType == 3" effect="plain" type="warning">用户补贴金</el-tag>
                            <el-tag v-if="scope.row.payType == 5" effect="plain" type="info">商家平台兑换金</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column
                        header-class-name="table-header"
                        label="实付金额"
                        prop="payAmount"
                        width="120"
                    >
                        <template #default="scope">
                            <span class="amount">¥{{ Number(scope.row.payAmount).toFixed(2) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                        header-class-name="table-header"
                        label="收件人"
                        prop="receiverName"
                        width="80"
                    />
                    <el-table-column
                        header-class-name="table-header"
                        label="收件人电话"
                        prop="receiverPhone"
                        width="150"
                    />
                    <el-table-column
                        header-class-name="table-header"
                        label="收货地址"
                        min-width="150"
                        prop="receiverAddress"
                        show-overflow-tooltip
                    >
                        <template #default="scope">
                            {{ getFullAddress(scope.row) }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        header-class-name="table-header"
                        label="创建时间"
                        prop="createTime"
                        width="200"
                    />
                    <el-table-column fixed="right" label="操作" width="220">
                        <template #default="scope">
                            <div class="action-buttons">
                                <el-button size="small" type="success" @click="viewOrderDetail(scope.row)">
                                    查看
                                </el-button>
                                <el-button v-if="userType == 2 && scope.row.status=='0' && scope.row.receiverType == 1" size="small"
                                           type="success"
                                           @click="updateOrderPrice(scope.row)">
                                    修改价格
                                </el-button>
                                <el-button v-if="scope.row.status=='2' && userType == 2" size="small" type="primary"
                                           @click="shipment(scope.row)">
                                    <el-icon>
                                        <Van/>
                                    </el-icon>
                                    发货
                                </el-button>
                                <el-button v-if="scope.row.status=='2' && userType == 2" size="small" type="info"
                                           @click="updateAddress(scope.row)">
                                    <el-icon>
                                        <Edit/>
                                    </el-icon>
                                    修改地址
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="pagination">
                <el-pagination
                    v-model:current-page="currentPage"
                    v-model:page-size="pageSize"
                    :page-sizes="[10, 20, 50, 100]"
                    :total="total"
                    background
                    layout="total, sizes, prev, pager, next, jumper"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                />
            </div>
        </div>

        <!-- 订单详情对话框 -->
        <el-dialog v-model="state.orderDetailVisible" title="订单详情" width="800px" @close="closeOrderDetail">
            <div v-loading="orderDetailLoading" class="order-detail-content">
                <div v-if="state.orderDetail" class="order-info">
                    <!-- 基本信息 -->
                    <div class="info-section">
                        <h3>基本信息</h3>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">订单编号：</span>
                                    <span class="value">{{ state.orderDetail.orderNo }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">订单状态：</span>
                                    <el-tag :type="getStatusType(state.orderDetail.status)" effect="light">
                                        {{ getStatusText(state.orderDetail.status) }}
                                    </el-tag>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">创建时间：</span>
                                    <span class="value">{{ state.orderDetail.createTime }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">支付时间：</span>
                                    <span class="value">{{ state.orderDetail.payTime || '-' }}</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 店铺信息 -->
                    <div class="info-section">
                        <h3>店铺信息</h3>
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <div class="info-item">
                                    <span class="label">店铺名称：</span>
                                    <span class="value">{{ state.orderDetail.shopName || '-' }}</span>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="info-item">
                                    <span class="label">店铺电话：</span>
                                    <span class="value">{{ state.orderDetail.shopPhone || '-' }}</span>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="info-item shop-logo-item">
                                    <span class="label">店铺Logo：</span>
                                    <el-image
                                        v-if="getImageUrl(state.orderDetail.shopLogo)"
                                        :src="getImageUrl(state.orderDetail.shopLogo)"
                                        class="shop-logo"
                                        fit="cover"
                                    />
                                    <span v-else class="value">-</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 收货信息 -->
                    <div class="info-section">
                        <h3>收货信息</h3>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">收货人：</span>
                                    <span class="value">{{ state.orderDetail.receiverName }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">联系电话：</span>
                                    <span class="value">{{ state.orderDetail.receiverPhone }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">收货方式：</span>
                                    <el-tag :type="state.orderDetail.receiverType == 1 ? 'success' : 'primary'" effect="light">
                                        {{ state.orderDetail.receiverType == 1 ? '自提' : '配送' }}
                                    </el-tag>
                                </div>
                            </el-col>
                            <el-col :span="24">
                                <div class="info-item">
                                    <span class="label">收货地址：</span>
                                    <span class="value">{{ getFullAddress(state.orderDetail) }}</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 商品信息 -->
                    <div class="info-section">
                        <h3>商品信息</h3>
                        <el-table :data="state.orderDetail.orderItems" border stripe>
                            <el-table-column label="商品图片" width="80">
                                <template #default="scope">
                                    <el-image
                                        v-if="getImageUrl(scope.row.productImage)"
                                        :src="getImageUrl(scope.row.productImage)"
                                        class="product-image"
                                        fit="cover"
                                    />
                                    <div v-else class="no-image">暂无图片</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="商品名称" prop="productName" show-overflow-tooltip/>
                            <el-table-column label="规格" prop="skuSpec" width="120"/>
                            <el-table-column label="单价" width="100">
                                <template #default="scope">
                                    ¥{{ scope.row.productPrice }}
                                </template>
                            </el-table-column>
                            <el-table-column label="数量" prop="quantity" width="80"/>
                            <el-table-column label="小计" width="100">
                                <template #default="scope">
                                    ¥{{ scope.row.totalAmount }}
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>

                    <!-- 费用信息 -->
                    <div class="info-section">
                        <h3>费用信息</h3>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">商品总额：</span>
                                    <span class="value amount">¥{{ state.orderDetail.totalAmount }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">补贴金：</span>
                                    <span class="value deduction-amount">¥{{ state.orderDetail.deductionAmount || '0.00' }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">实付金额：</span>
                                    <span class="value amount final-amount">¥{{ state.orderDetail.payAmount }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">支付方式：</span>
                                    <el-tag v-if="state.orderDetail.payType == 1" effect="plain" type="success">微信支付</el-tag>
                                    <el-tag v-else-if="state.orderDetail.payType == 2 || state.orderDetail.payType == 4" effect="plain" type="primary">支付宝支付</el-tag>
                                    <el-tag v-else-if="state.orderDetail.payType == 3" effect="plain" type="warning">用户补贴金</el-tag>
                                    <el-tag v-else-if="state.orderDetail.payType == 5" effect="plain" type="info">商家平台兑换金</el-tag>
                                    <span v-else>-</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>

                    <!-- 物流信息 -->
                    <div v-if="state.orderDetail.deliveryName" class="info-section">
                        <h3>物流信息</h3>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">快递公司：</span>
                                    <span class="value">{{ state.orderDetail.deliveryName }}</span>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="info-item">
                                    <span class="label">快递单号：</span>
                                    <span class="value">{{ state.orderDetail.deliveryNumber }}</span>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="closeOrderDetail">关闭</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 修改收货地址 -->
        <el-dialog v-model="state.updateAddressVisible" title="修改收货地址" width="500" @close="closeUpdateAddress">
            <el-form :model="state.updateAddressForm" label-width="100px">
                <el-form-item label="收货人姓名">
                    <el-input v-model="state.updateAddressForm.receiverName" placeholder="请输入收货人姓名"/>
                </el-form-item>
                <el-form-item label="收货人电话">
                    <el-input v-model="state.updateAddressForm.receiverPhone" placeholder="请输入收货人电话"/>
                </el-form-item>
                <el-form-item label="收货人省份">
                    <el-input v-model="state.updateAddressForm.receiverProvince" placeholder="请输入省份"/>
                </el-form-item>
                <el-form-item label="收货人城市">
                    <el-input v-model="state.updateAddressForm.receiverCity" placeholder="请输入城市"/>
                </el-form-item>
                <el-form-item label="收货人区域">
                    <el-input v-model="state.updateAddressForm.receiverDistrict" placeholder="请输入区域"/>
                </el-form-item>
                <el-form-item label="详细地址">
                    <el-input v-model="state.updateAddressForm.receiverAddress" :rows="3"
                              placeholder="请输入详细地址" type="textarea"/>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="closeUpdateAddress">取消</el-button>
                    <el-button :loading="submitLoading" type="primary" @click="submitUpdateAddress">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup>
import {useRouter} from 'vue-router'
import {allOrders} from '../../stores/allOrders'
import {Session} from "@/utils/storage.js";
import {nextTick, onMounted, reactive, ref} from 'vue'
import {Download, Edit, Refresh, Search, Van} from '@element-plus/icons-vue'
import {ElMessage} from 'element-plus'
import {getImageUrl} from "@/utils/common.js";

const router = useRouter()
const state = reactive({
    buttonList: [],
    updateAddressVisible: false,
    orderDetailVisible: false,
    tableData: [],
    orderDetail: null,
    // 修改收货地址表单
    updateAddressForm: {
        receiverName: '',
        receiverPhone: '',
        receiverProvince: '',
        receiverCity: '',
        receiverDistrict: '',
        receiverAddress: '',
    },
    filterForm: {
        userPhone: '',
        orderNo: '',
        receiverPhone: '',
        orderName: '',
        status: null,
        payType: null,
        startTime: null,
        endTime: null
    }
})

// 加载状态
const loading = ref(false)
const submitLoading = ref(false)
const exportLoading = ref(false)
const orderDetailLoading = ref(false)

// 分页
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
// 用户类型 (1:平台管理员, 2:商家)
const userType = ref(Session.get('userType'))

const handleButtonClick = (item) => {
    router.push(item.component)
}

// 分页处理
const handleSizeChange = (size) => {
    pageSize.value = size
    getProductList()
}

const handleCurrentChange = (page) => {
    currentPage.value = page
    getProductList()
}

// 订单状态列表
const orderStatusList = [
    {
        value: null,
        label: '全部',
    },
    {
        value: 0,
        label: '待支付',
    },
    // {
    //     value: 1,
    //     label: '支付中',
    // },
    {
        value: 2,
        label: '待发货',
    },
    {
        value: 3,
        label: '待收货',
    },
    {
        value: 4,
        label: '已完成',
    },
    {
        value: 5,
        label: '已取消',
    },
    {
        value: 6,
        label: '已退款',
    }
]

// 核销方式列表
const payTypeList = [
    {
        value: null,
        label: '全部',
    },
    // {
    //     value: 1,
    //     label: '微信支付',
    // },
    {
        value: 2,
        label: '支付宝支付',
    },
    {
        value: 3,
        label: '用户补贴金',
    },
    // {
    //     value: 4,
    //     label: '支付宝PC端',
    // },
    // {
    //     value: 5,
    //     label: '商家平台兑换金',
    // }
]

// 获取订单状态对应的类型
const getStatusType = (status) => {
    switch (status) {
        case '0':
            return 'warning'  // 待支付
        case '1':
            return 'primary'  // 支付中
        case '2':
            return 'info'     // 待发货
        case '3':
            return 'info'     // 待收货
        case '4':
            return 'success'  // 已完成
        case '5':
            return 'danger'   // 已取消
        case '6':
            return 'danger'   // 已退款
        default:
            return 'info'
    }
}

// 获取状态文本
const getStatusText = (status) => {
    switch (status) {
        case '0':
            return '待支付'
        case '1':
            return '支付中'
        case '2':
            return '待发货'
        case '3':
            return '待收货'
        case '4':
            return '已完成'
        case '5':
            return '已取消'
        case '6':
            return '已退款'
        default:
            return '未知状态'
    }
}

// 获取完整地址
const getFullAddress = (row) => {
    const province = row.receiverProvince || ''
    const city = row.receiverCity || ''
    const district = row.receiverDistrict || ''
    const address = row.receiverAddress || ''
    return `${province}${city}${district}${address}`
}

// 重置筛选条件
const resetFilter = () => {
    state.filterForm = {
        userPhone: '',
        orderNo: '',
        receiverPhone: '',
        orderName: '',
        status: null,
        payType: null,
        startTime: null,
        endTime: null
    }
    getProductList()
}

// 获取订单列表
const getProductList = async () => {
    loading.value = true
    try {
        let result = await allOrders().GetOrderList({
            ...state.filterForm,
            pageNum: currentPage.value,
            pageSize: pageSize.value
        });
        state.tableData = result.rows;
        total.value = result.total || 0;
    } catch (error) {
        console.error('获取订单列表失败:', error)
    } finally {
        loading.value = false
    }
}

//发货
const shipment = async (row) => {
    router.push({
        path: '/shipping',
        query: {
            orderNo: row.orderNo
        }
    });
}
// 修改订单价格
const updateOrderPrice = async (row) => {

}


// 查看订单详情
const viewOrderDetail = async (row) => {
    orderDetailLoading.value = true
    state.orderDetailVisible = true
    try {
        const result = await allOrders().GetOrderDetail(row.orderNo)
        state.orderDetail = result.data
    } catch (error) {
        console.error('获取订单详情失败:', error)
        ElMessage.error('获取订单详情失败')
        state.orderDetailVisible = false
    } finally {
        orderDetailLoading.value = false
    }
}

// 关闭订单详情对话框
const closeOrderDetail = () => {
    state.orderDetailVisible = false
    state.orderDetail = null
}

// 修改收货地址
const updateAddress = async (row) => {
    state.updateAddressForm.orderNo = row.orderNo
    state.updateAddressForm.receiverName = row.receiverName
    state.updateAddressForm.receiverPhone = row.receiverPhone
    state.updateAddressForm.receiverProvince = row.receiverProvince
    state.updateAddressForm.receiverCity = row.receiverCity
    state.updateAddressForm.receiverDistrict = row.receiverDistrict
    state.updateAddressForm.receiverAddress = row.receiverAddress
    state.updateAddressVisible = true
}

// 关闭修改收货地址
const closeUpdateAddress = () => {
    state.updateAddressVisible = false
    state.updateAddressForm.orderNo = ''
    state.updateAddressForm.receiverName = ''
    state.updateAddressForm.receiverPhone = ''
    state.updateAddressForm.receiverProvince = ''
    state.updateAddressForm.receiverCity = ''
    state.updateAddressForm.receiverDistrict = ''
    state.updateAddressForm.receiverAddress = ''
}

// 提交修改收货地址
const submitUpdateAddress = async () => {
    submitLoading.value = true
    try {
        let result = await allOrders().UpdateOrderAddress(state.updateAddressForm)
        if (result.code === 200) {
            ElMessage.success('修改收货地址成功')
            getProductList()
            closeUpdateAddress()
        }
    } catch (error) {
        console.error('修改收货地址失败:', error)
        ElMessage.error('修改收货地址失败')
    } finally {
        submitLoading.value = false
    }
}

// 导出订单数据
const handleExport = async () => {
    exportLoading.value = true
    try {
        const exportParams = {
            ...state.filterForm,
            startTime: state.filterForm.startTime,
            endTime: state.filterForm.endTime
        }
        const blob = await allOrders().ExportOrderList(exportParams)

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([blob]))
        const link = document.createElement('a')
        link.href = url
        // 获取当前日期作为文件名的一部分
        const date = new Date()
        const dateString = `${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}${date.getDate().toString().padStart(2, '0')}`
        link.setAttribute('download', `订单数据_${dateString}.xlsx`)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        ElMessage.success('订单数据导出成功')
    } catch (error) {
        console.error('订单导出失败:', error)
        ElMessage.error('订单导出失败')
    } finally {
        exportLoading.value = false
    }
}

onMounted(() => {
    // 获取URL的参数
    const query = router.currentRoute.value.query
    if (query.status) {
        state.filterForm.status = Number(query.status)
    }
    console.log(query)

    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })
    getProductList()
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    min-height: 100%;
    box-sizing: border-box;
    overflow: hidden; /* 防止容器溢出 */
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-right: 20px;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-width: 0; /* 允许内容收缩 */
}

.filter-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.title {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
}

.filter-form {
    :deep(.el-form-item) {
        margin-bottom: 15px;
    }
}

.filter-input {
    width: 200px;
}

.date-range-container {
    display: flex;
    align-items: center;
    gap: 5px;
    flex-wrap: wrap; /* 允许在小屏幕上换行 */
}

.date-picker {
    width: 180px;
}

.date-separator {
    color: #606266;
    margin: 0 5px;
}

.table-container {
    flex: 1;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    //overflow: auto; /* 改为auto而不是仅overflow-x */
    width: 100%;
    //max-height: calc(100vh - 280px); /* 限制最大高度防止撑开页面 */
}

.order-table {
    width: 100%;

    :deep(.el-table__header) {
        .table-header {
            background-color: #3A58CF;
            color: white;
            font-weight: bold;
        }
    }

    :deep(.el-table__cell) {
        padding: 8px 0; /* 减小内边距以更紧凑显示 */
    }

    /* 确保右侧固定列正常显示 */
    :deep(.el-table__fixed-right) {
        height: auto !important;
    }
}

.pagination {
    display: flex;
    justify-content: flex-end;
    padding: 15px 0;
}

.amount {
    color: #f56c6c;
    font-weight: 600;
}

.deduction-amount {
    color: #67c23a;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

// 订单详情对话框样式
.order-detail-content {
    .info-section {
        margin-bottom: 24px;

        h3 {
            margin: 0 0 16px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #e6f7ff;
            color: #1890ff;
            font-size: 16px;
            font-weight: 600;
        }

        .info-item {
            margin-bottom: 12px;
            display: flex;
            align-items: center;

            .label {
                font-weight: 500;
                color: #666;
                min-width: 100px;
                margin-right: 8px;
            }

            .value {
                color: #333;

                &.amount {
                    color: #f56c6c;
                    font-weight: 600;
                }

                &.deduction-amount {
                    color: #67c23a;
                    font-weight: 600;
                }

                &.final-amount {
                    font-size: 16px;
                    font-weight: 700;
                }
            }
        }
    }

    .product-image {
        width: 50px;
        height: 50px;
        border-radius: 4px;
    }

    .no-image {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        color: #999;
        font-size: 12px;
        border-radius: 4px;
    }

    .shop-logo-item {
        .shop-logo {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
        }
    }
}
</style>
