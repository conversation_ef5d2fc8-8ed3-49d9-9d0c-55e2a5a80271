<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {onMounted, reactive, ref} from 'vue'
import {Refresh} from '@element-plus/icons-vue'
import {Session} from "@/utils/storage.js";
import {ElMessage} from "element-plus";
import {formatDate} from "../../utils/common";
// 导入结算统计
import {exportSettlementStats, listSettlementStats} from '@/api/settlementCount/SettlementStats'

const router = useRouter()


/*----------------------- 分割线 ------------------------------*/

// 查询参数
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    phone: '',
    beginTime: '',
    endTime: '',
    //  shopName: '' // 商家名称
})


// 重置按钮 处理
const resetQuery = () => {
    Object.assign(queryParams, {
        pageNum: 1,
        pageSize: 10,
        phone: '',
        beginTime: '',
        endTime: '',
        // shopName: '' // 商家名称
    })
    beginTime.value = null // 清空开始时间选择器
    endTime.value = null  // 清空结束时间选择器
    getList() // 可选：触发列表刷新
}

// 结算统计数据列表
const settlementStatsList = ref<any[]>([])
const total = ref(0)

// 加载状态
const loading = ref(false)

// 时间范围选择器
// const timeRange = ref<Date[] | null>(null)

// 获取结算统计列表
const getList = async () => {
    loading.value = true
    try {
        const res = await listSettlementStats(queryParams)
        settlementStatsList.value = res.rows || []
        // total.value = res.total
        pagination.total = res.total
    } catch (error) {
        console.error('获取商家结算统计列表失败:', error)
        ElMessage.error('获取商家结算统计列表失败')
    } finally {
        loading.value = false
    }
}

// 分页参数
const pagination = reactive({
    pageNum: 1,
    pageSize: 10,
    total: 0
})
// 分页处理
const handleSizeChange = (size) => {
    // pagination.pageSize = size
    queryParams.pageSize = size
    getList()
}
const handleCurrentChange = (page) => {
    // pagination.pageNum = page
    queryParams.pageNum = page
    getList()
}


// 导出Excel
const handleExport = () => {
    exportSettlementStats(queryParams)
        .then(response => {
            const blob = new Blob([response])
            const downloadUrl = URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = downloadUrl
            link.setAttribute('download', `结算统计_${new Date().getTime()}.xlsx`)
            document.body.appendChild(link)
            link.click()
            link.remove()
            ElMessage.success('导出成功')
        })
        .catch(() => {
            ElMessage.error('导出失败')
        })
}
// 导出Excel
/*const handleExport = async () => {
    loading.value = true
    try {
        ElMessage.info('正在导出数据...')

        // 使用当前查询参数导出数据
        const response = await exportSettlementStats(queryParams)

        const blob = new Blob([response])
        const downloadUrl = URL.createObjectURL(blob)

        const link = document.createElement('a')
        link.href = downloadUrl
        const filename = `结算统计_${new Date().toLocaleDateString()}.xlsx`
        link.setAttribute('download', filename)

        document.body.appendChild(link)
        link.click()
        link.remove()

        ElMessage.success('导出成功')
    } catch (error) {
        console.error('导出失败:', error)
        ElMessage.error('导出失败，请重试')
    }finally {
        loading.value = false
    }
}*/



// 时间选择器变化处理
const beginTime = ref<Date | null>(null)
const endTime = ref<Date | null>(null)
const handleBeginDateChange = (value: Date | null) => {
    if (value instanceof Date && !isNaN(value.getTime())) {
        queryParams.beginTime = formatDate(value)
    } else {
        queryParams.beginTime = ''
    }
}

const handleEndDateChange = (value: Date | null) => {
    if (value instanceof Date && !isNaN(value.getTime())) {
        queryParams.endTime = formatDate(value)
    } else {
        queryParams.endTime = ''
    }
}


/*--------------------------分割线-------------------------------*/


const state = reactive({
    form: {},
    buttonList: []
})

// const onSubmit = () => {
//     console.log('提交表单:', form)
// }


const handleShelf = (row: any) => {
    console.log('上架操作:', row)
}

const handleUnshelf = (row: any) => {
    console.log('下架操作:', row)
}


const handleButtonClick = (item) => {
    router.push(item.component)
}
onMounted(() => {
    getList()

    let menuList = Session.getMenu2()
    let menuId = Session.get('homeMenuId');
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index];
        if (element.menuId == menuId) {
            state.buttonList = element.children
        }
    }
})


</script>

<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>

        <div class="right-content">
            <div class="filter-container">
                <div class="filter-header">
                    <span class="filter-title">时间表</span>
                </div>
                <el-form :model="queryParams" class="filter-form">
                    <!-- 第一行：手机号 -->
                    <div class="form-row">
                        <el-form-item label="手机号">
                            <el-input v-model="queryParams.phone" clearable placeholder="请输入手机号"/>
                        </el-form-item>

                        <!-- 时间范围选择 -->
                        <el-form-item label="时间范围">
                            <div class="date-range-container">
                                <el-date-picker
                                    v-model="beginTime"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    placeholder="选择开始日期时间"
                                    type="datetime"
                                    @change="handleBeginDateChange"
                                />
                                <span class="time-separator">至</span>
                                <el-date-picker
                                    v-model="endTime"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    placeholder="选择结束日期时间"
                                    type="datetime"
                                    @change="handleEndDateChange"
                                />
                            </div>
                        </el-form-item>

                        <el-button class="search-btn" type="primary" @click="getList">
                            查询
                        </el-button>
                        <el-button class="export-btn" @click="handleExport">
                            导出Excel
                        </el-button>
                        <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
                    </div>
                </el-form>
                <!-- 分隔线 -->
                <el-divider/>
            </div>

            <!-- 表格区域 -->
            <div class="table-container">
                <el-table v-loading="loading" :data="settlementStatsList" border style="width: 100%">
                    <el-table-column label="结算日期" prop="settlementDate" width="200"/>
                    <el-table-column label="商家名称" prop="shopName" width="200"/>
                    <el-table-column label="手机号" prop="phone" width="200"/>
                    <!--                    <el-table-column label="订单信息" width="360">
                                            <template #default="{row}">
                                                <div>订单编号：{{ row.orderNo }}</div>
                                                <div>商品名称：{{ row.orderName }}</div>
                                            </template>
                                        </el-table-column>-->
                    <el-table-column label="银行卡" prop="bankCardActualAmount" width="200"/>
                    <el-table-column label="微信" width="160">
                        <template #default="{row}">
                            <div>{{ "0.00" }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="支付宝" width="200">
                        <template #default="{row}">
                            <div>{{ "0.00" }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="当天累计金额" min-width="200" prop="totalAmount"/>

                    <!-- 新增的操作列 -->


                    <!--                    <el-table-column label="操作" min-width="180">
                                            <template #default="{row}">
                                                <el-button
                                                        class="shelf-btn"
                                                        @click="handleShelf(row)"
                                                >
                                                    上架
                                                </el-button>
                                                <el-button
                                                        class="unshelf-btn"
                                                        @click="handleUnshelf(row)"
                                                >
                                                    下架
                                                </el-button>
                                            </template>
                                        </el-table-column>-->


                </el-table>

                <!-- 分页组件 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.pageNum"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        background
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>

            </div>

        </div>
    </div>
    <!--  </ManageBg>-->
</template>


<style lang="scss" scoped>

/*


.container {
  position: relative;
  display: flex;
  max-height: 100vh;
  box-sizing: border-box;
}

.left-buttons {
  width: 235px;
  height: 100%;
  overflow-y: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  :deep(.el-button.data-button) {
    width: 100%;
    height: 60px;
    background-color: #3A58CF;
    color: white;
    font-size: 20px;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0;
    display: block;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: background-color 0.3s;

    &:hover {
      background-color: #2a48bf;
    }

    &:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }

    &:last-child {
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-bottom: none;
    }

    &.el-button {
      --el-button-hover-text-color: white;
      --el-button-hover-bg-color: #2a48bf;
      --el-button-active-bg-color: #1a38af;
      --el-button-active-border-color: transparent;
    }
  }
}

.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.filter-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;

}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #3A58CF;
  padding: 12px 20px;
  border-radius: 8px;
  margin-bottom: 20px;

  .filter-title {
    font-size: 24px;
    font-weight: bold;
    color: white;
  }

  .search-item {
    margin-left: auto;

    :deep(.el-input-group__append) {
      background-color: #3A58CF;
      border: none;

      .el-button {
        color: white;
      }
    }
  }
}

.filter-form {
  .form-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;

    .el-form-item {
      margin-right: 20px;
      margin-bottom: 0;

      :deep(.el-form-item__label) {
        padding-bottom: 0;
        font-weight: normal;
      }
    }

    .time-separator {
      margin: 0 10px;
      color: #666;
    }

    .export-btn {
      margin-left: 20px;
      background-color: #FF8D1A;
    }
  }
}

.table-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  flex: 1;

  :deep(.el-table) {
    font-size: 14px;

    th {
      background-color: #f5f7fa;
      color: #333;
      font-weight: bold;
    }

    .cell {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }
}

:deep(.shelf-btn) {
  border-radius: 4px;
  padding: 8px 16px;
  transition: all 0.3s;
  background-color: pink;

  &:hover {
    transform: translateY(-1px);
  }
}

:deep(.unshelf-btn) {
  border-radius: 4px;
  padding: 8px 16px;
  transition: all 0.3s;
  margin-left: 8px;
  margin-left: 0px;
  background-color: cyan;

  &:hover {
    transform: translateY(-1px);
  }
}
*/


.truncate-with-title {
    display: inline-block;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}


.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 30px);
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.right-content {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    max-width: calc(100% - 250px);
}

.filter-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #3A58CF;
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 20px;

    .filter-title {
        font-size: 24px;
        font-weight: bold;
        color: white;
    }
}

.filter-form {
    .form-row {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;

        .el-form-item {
            margin-right: 20px;
            margin-bottom: 0;
            min-width: 180px;

            :deep(.el-form-item__label) {
                padding-bottom: 0;
                font-weight: normal;
            }

            :deep(.el-input) {
                width: 180px;
            }

            :deep(.el-select) {
                width: 180px;
            }

            :deep(.el-date-editor) {
                width: 180px;
            }
        }

        .time-separator {
            margin: 0 10px;
            color: #666;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-left: auto;
        }
    }
}

.table-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    flex: 1;
    overflow-x: auto;

    :deep(.el-table) {
        font-size: 13px;

        th {
            background-color: #f5f7fa;
            color: #333;
            font-weight: bold;
            padding: 8px 0;
        }

        td {
            padding: 6px 0;
        }
    }
}

.user-info {
    display: flex;
    flex-direction: column;
    gap: 5px;

    div {
        line-height: 1.4;
    }
}

.deduction-info {
    display: flex;
    flex-direction: column;
    gap: 5px;

    .deduction-item {
        display: flex;
        align-items: center;
        line-height: 1.4;

        .label {
            width: 80px;
            color: #606266;
        }

        .value {
            font-weight: 500;

            &.total {
                color: #409EFF;
            }

            &.used {
                color: #F56C6C;
            }

            &.remain {
                color: #67C23A;
            }
        }
    }
}


// 导出按钮样式

.export-btn {
    margin-left: 20px;
    background: linear-gradient(135deg, #4CAF50, #66BB6A);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
    }

    &:active {
        transform: scale(0.98);
    }

    .el-icon {
        font-size: 16px;
    }
}


// 分页组件样式
.pagination-container {
    display: flex;
    justify-content: flex-end; // 右对齐
    align-items: center;
    margin-top: 20px;
    gap: 15px; // 按钮间距更清晰
}

.pagination-container .el-pagination {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 8px 12px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.pagination-container .el-pagination:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 150, 255, 0.2);
}

// 时间选择器样式
.date-range-container {
    display: flex;
    align-items: center;
    gap: 10px; // 控制两个选择器之间的间距
}

.time-separator {
    margin: 0 5px;
    color: #666;
    font-weight: normal;
}


</style>
