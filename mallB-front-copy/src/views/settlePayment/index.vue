<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="main">
            <div class="main-content">货款结算</div>

            <!-- 钱包信息卡片 - 仅商户可见 -->
            <el-card v-if="isMerchant" v-loading="loading" class="wallet-card">
                <div class="wallet-info">
                    <div class="wallet-item">
                        <span class="label">货款：</span>
                        <span class="amount">{{ walletInfo.balance ? walletInfo.balance : '0.00' }} 元</span>
                    </div>
                    <div class="wallet-item">
                        <span class="label">冻结金额：</span>
                        <span class="frozen">{{ walletInfo.frozenAmount ? walletInfo.frozenAmount : '0.00' }} 元</span>
                    </div>
                    <div class="wallet-item">
                        <span class="label">累计收入：</span>
                        <span class="income">{{ walletInfo.totalIncome ? walletInfo.totalIncome : '0.00' }} 元</span>
                    </div>
                    <div class="wallet-item">
                        <span class="label">累计支出：</span>
                        <span class="expense">{{ walletInfo.totalExpense ? walletInfo.totalExpense : '0.00' }} 元</span>
                    </div>
                </div>
                <div class="wallet-action">
                    <el-button :disabled="!canSettlement" type="primary" @click="openSettleDialog">申请结算</el-button>
                </div>
            </el-card>

            <!-- 结算记录卡片 -->
            <el-card class="settlement-history-card">
                <template #header>
                    <div class="card-header">
                        <span>结算记录</span>
                    </div>
                </template>

                <!-- 搜索表单 -->
                <el-form :inline="true" :model="searchForm" class="search-form">
                    <el-form-item label="结算单号">
                        <el-input v-model="searchForm.settlementNo" clearable placeholder="请输入结算单号"/>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select v-model="searchForm.status" clearable placeholder="请选择状态" style="width: 120px">
                            <el-option label="待审核" value="0"/>
                            <el-option label="审核通过" value="1"/>
                            <el-option label="审核拒绝" value="2"/>
                            <el-option label="已打款" value="3"/>
                            <el-option label="已完成" value="4"/>
                            <el-option label="已取消" value="5"/>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="handleSearch">搜索</el-button>
                        <el-button @click="resetSearch">重置</el-button>
                    </el-form-item>
                </el-form>

                <el-table v-loading="historyLoading" :data="settlementHistory" stripe style="width: 100%">
                    <el-table-column label="结算单号" prop="settlementNo"/>
                    <el-table-column label="申请金额" prop="amount"/>
                    <el-table-column label="实际到账" prop="actualAmount"/>
                    <el-table-column label="申请时间" prop="createTime">
                        <template #default="scope">
                            {{ formatDate(scope.row.createTime) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" prop="status">
                        <template #default="scope">
                            <el-tag :type="getStatusType(scope.row.status)">
                                {{ getStatusText(scope.row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120">
                        <template #default="scope">
                            <el-button size="small" type="text" @click="viewDetail(scope.row)">查看详情</el-button>
                            <!-- 管理员可见的操作按钮 -->
                            <template v-if="isAdmin">
                                <el-button
                                    v-if="scope.row.status === '0'"
                                    size="small"
                                    type="text"
                                    @click="auditDetail(scope.row)"
                                >
                                    审核
                                </el-button>
                                <el-button
                                    v-if="scope.row.status === '1'"
                                    size="small"
                                    type="text"
                                    @click="payment(scope.row)"
                                >
                                    确认打款
                                </el-button>
                                <el-button
                                    v-if="scope.row.status === '3'"
                                    size="small"
                                    type="text"
                                    @click="complete(scope.row)"
                                >
                                    完成结算
                                </el-button>
                                <el-button
                                    v-if="scope.row.status === '0' || scope.row.status === '1'"
                                    size="small"
                                    type="text"
                                    @click="cancel(scope.row)"
                                >
                                    取消结算
                                </el-button>
                            </template>
                            <!-- 商户可见的操作按钮（仅取消自己的申请） -->
                            <template v-else-if="isMerchant">
                                <el-button
                                    v-if="scope.row.status === '0'"
                                    size="small"
                                    type="text"
                                    @click="cancel(scope.row)"
                                >
                                    取消申请
                                </el-button>
                            </template>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pagination-container">
                    <el-pagination
                        v-model:currentPage="currentPage"
                        v-model:page-size="pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </el-card>

            <!-- 结算申请对话框 - 仅商户可见 -->
            <el-dialog v-model="settleDialogVisible" title="申请结算" width="500px" @closed="resetSettleForm">
                <el-form ref="settleFormRef" :model="settleForm" :rules="settleRules" label-width="120px">
                    <!-- 结算金额 -->
                    <el-form-item label="结算金额" prop="amount">
                        <el-input-number
                            v-model="settleForm.amount"
                            :max="availableAmount"
                            :min="100"
                            :precision="2"
                            :step="100"
                            style="width: 220px"
                        />
                        <span class="form-tip">可结算金额: {{ availableAmount - walletInfo.frozenAmount | 0 }} 元</span>
                    </el-form-item>

                    <!-- 收款信息 -->
                    <el-form-item label="收款账户名" prop="accountName">
                        <el-input v-model="settleForm.accountName" disabled placeholder="请输入收款账户名"
                                  readonly style="width: 320px"/>
                        <el-tooltip content="收款账户信息来自您的店铺设置" placement="top">
                            <el-icon class="info-icon">
                                <InfoFilled/>
                            </el-icon>
                        </el-tooltip>
                    </el-form-item>

                    <el-form-item label="收款账号" prop="accountNo">
                        <el-input v-model="settleForm.accountNo" disabled placeholder="请输入收款账号"
                                  readonly style="width: 320px"/>
                    </el-form-item>

                    <el-form-item label="银行名称" prop="bankName">
                        <el-input v-model="settleForm.bankName" disabled placeholder="请输入银行名称"
                                  readonly style="width: 320px"/>
                    </el-form-item>

                    <el-form-item label="开户行" prop="bankBranchName">
                        <el-input v-model="settleForm.bankBranchName" disabled placeholder="请输入开户行"
                                  readonly style="width: 320px"/>
                    </el-form-item>
                    <el-form-item label="图形验证码" prop="randomCode">
                        <div style="display: flex">
                            <el-input v-model="settleForm.randomCode" placeholder="请输入验证码"
                                      style="width: 160px"/>
                            <img @click="getVerifyCode" style="height: 60px;" :src="imgSrc">
                        </div>
                    </el-form-item>
                    <el-form-item label="操作密码" prop="operationPassword">
                        <el-input v-model="settleForm.operationPassword" type="password" show-password
                                  placeholder="请输入操作密码" style="width: 320px">
                        </el-input>
                    </el-form-item>

                    <!-- 手机验证码 -->
<!--                    <el-form-item label="手机号码" prop="contactPhone">-->
<!--                        <el-input v-model="settleForm.contactPhone" disabled placeholder="请输入手机号码"-->
<!--                                  readonly style="width: 180px"/>-->
<!--                        <el-button-->
<!--                            :disabled="smsDisabled"-->
<!--                            style="margin-left: 10px"-->
<!--                            type="primary"-->
<!--                            @click="sendSmsCode"-->
<!--                        >-->
<!--                            {{ smsButtonText }}-->
<!--                        </el-button>-->
<!--                        <el-tooltip content="手机号码来自您的店铺联系方式" placement="top">-->
<!--                            <el-icon class="info-icon">-->
<!--                                <InfoFilled/>-->
<!--                            </el-icon>-->
<!--                        </el-tooltip>-->
<!--                    </el-form-item>-->
<!--                    <el-form-item label="验证码" prop="code">-->
<!--                        <el-input v-model="settleForm.code" placeholder="请输入验证码" style="width: 220px"/>-->
<!--                    </el-form-item>-->


                    <!-- 提示信息 -->
                    <el-alert
                        :closable="false"
                        description="1. 单笔结算金额最低100元；2. 结算申请提交后将进入审核流程，审核通过后1-3个工作日内完成打款；3. 请确保收款信息准确无误。"
                        show-icon
                        style="margin-bottom: 20px"
                        title="申请结算说明"
                        type="info"
                    />

                    <!-- 底部按钮 -->
                    <el-form-item>
                        <el-button :loading="submitting" type="primary" @click="submitSettlement">提交申请</el-button>
                        <el-button @click="settleDialogVisible = false">取消</el-button>
                    </el-form-item>
                </el-form>
            </el-dialog>

            <!-- 结算详情对话框 -->
            <el-dialog v-model="detailDialogVisible" title="结算详情" width="600px">
                <div v-if="currentDetail" class="settlement-detail">
                    <el-descriptions :column="1" border>
                        <el-descriptions-item label="结算单号">{{ currentDetail.settlementNo }}</el-descriptions-item>
                        <el-descriptions-item label="申请金额">{{ currentDetail.amount }} 元</el-descriptions-item>
                        <el-descriptions-item label="实际到账">{{ currentDetail.actualAmount || '待审核' }} 元
                        </el-descriptions-item>
                        <el-descriptions-item label="手续费">{{ currentDetail.fee || '待审核' }} 元
                        </el-descriptions-item>
                        <el-descriptions-item label="收款账户名">{{ currentDetail.accountName }}</el-descriptions-item>
                        <el-descriptions-item label="收款账号">{{ currentDetail.accountNo }}</el-descriptions-item>
                        <el-descriptions-item label="银行名称">{{ currentDetail.bankName }}</el-descriptions-item>
                        <el-descriptions-item label="开户行">{{ currentDetail.bankBranchName }}</el-descriptions-item>
                        <el-descriptions-item label="联系电话">{{ currentDetail.contactPhone }}</el-descriptions-item>
                        <el-descriptions-item label="申请时间">{{
                                formatDate(currentDetail.createTime)
                            }}
                        </el-descriptions-item>
                        <el-descriptions-item label="当前状态">
                            <el-tag :type="getStatusType(currentDetail.status)">
                                {{ getStatusText(currentDetail.status) }}
                            </el-tag>
                        </el-descriptions-item>

                        <el-descriptions-item v-if="currentDetail.status === '1' || currentDetail.status === '2'"
                                              label="审核意见">
                            {{ currentDetail.auditRemark || '无' }}
                        </el-descriptions-item>

                        <el-descriptions-item v-if="currentDetail.status === '3' || currentDetail.status === '4'"
                                              label="打款凭证">
                            <el-image
                                v-if="currentDetail.payProof"
                                :preview-src-list="[COS_URL + '/' + currentDetail.payProof]"
                                :src="COS_URL + '/' + currentDetail.payProof"
                                fit="cover"
                                style="width: 100px; height: 100px"
                            />
                            <span v-else>无</span>
                        </el-descriptions-item>

                        <el-descriptions-item v-if="currentDetail.status === '3' || currentDetail.status === '4'"
                                              label="打款备注">
                            {{ currentDetail.payRemark || '无' }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </el-dialog>

            <!-- 审核对话框 - 仅管理员可见 -->
            <el-dialog v-if="isAdmin" v-model="auditDialogVisible" title="审核结算单" width="500px"
                       @closed="resetAuditForm">
                <el-form ref="auditFormRef" :model="auditForm" :rules="auditRules" label-width="120px">
                    <el-form-item label="审核结果" prop="status">
                        <el-select v-model="auditForm.status" placeholder="请选择审核结果">
                            <el-option label="审核通过" value="1"/>
                            <el-option label="审核拒绝" value="2"/>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="审核意见" prop="auditRemark">
                        <el-input v-model="auditForm.auditRemark" placeholder="请输入审核意见"/>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="submitAudit">提交审核</el-button>
                        <el-button @click="auditDialogVisible = false">取消</el-button>
                    </el-form-item>
                </el-form>
            </el-dialog>

            <!-- 打款对话框 - 仅管理员可见 -->
            <el-dialog v-if="isAdmin" v-model="paymentDialogVisible" title="确认打款" width="500px"
                       @closed="resetPaymentForm">
                <el-form ref="paymentFormRef" :model="paymentForm" :rules="paymentRules" label-width="120px">
                    <el-form-item label="打款凭证" prop="payProof">
                        <el-upload
                            :action="UPLOAD_URL"
                            :before-upload="beforeAvatarUpload"
                            :headers="headers"
                            :on-remove="handleRemove"
                            :on-success="handleSuccessPic"
                            class="avatar-uploader"
                            list-type="picture-card"
                        >
                            <el-icon>
                                <Plus/>
                            </el-icon>
                        </el-upload>
                        <div class="el-upload__tip">建议尺寸：200x200px，仅支持JPG/PNG格式</div>
                    </el-form-item>
                    <el-form-item label="打款备注" prop="payRemark">
                        <el-input v-model="paymentForm.payRemark" placeholder="请输入打款备注"/>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="submitPayment">提交打款确认</el-button>
                        <el-button @click="paymentDialogVisible = false">取消</el-button>
                    </el-form-item>
                </el-form>
            </el-dialog>

            <!-- 取消对话框 -->
            <el-dialog v-model="cancelDialogVisible" title="取消结算" width="500px" @closed="resetCancelForm">
                <el-form ref="cancelFormRef" :model="cancelForm" :rules="cancelRules" label-width="120px">
                    <el-form-item label="取消原因" prop="remark">
                        <el-input v-model="cancelForm.remark" placeholder="请输入取消原因"/>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="submitCancel">提交取消结算</el-button>
                        <el-button @click="cancelDialogVisible = false">取消</el-button>
                    </el-form-item>
                </el-form>
            </el-dialog>
        </div>
    </div>
</template>

<script setup>
import {useRouter} from 'vue-router'
import {Session} from '../../utils/storage'
import {computed, onMounted, reactive, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {getWalletInfo} from '@/api/wallet'
import {getShopInfo} from '@/api/shop'
import {generateUUID} from "../../utils/other";
import {
    auditSettlement,
    cancelSettlement,
    completeSettlement,
    confirmPayment,
    getSettlementDetail,
    getSettlementList,
    sendSettlementSmsCode,
    verifyAndApplySettlement
} from '@/api/settlement'
import {InfoFilled, Plus} from '@element-plus/icons-vue'
import {COS_URL} from '../../config/baseUrl'
import {useUserInfoLogin} from "@/stores/login.js";

const baseUrl = import.meta.env.VITE_IP;
// 上传相关
const UPLOAD_URL = baseUrl  + '/system/oss/upload'

// 页面状态
const state = reactive({
    buttonList: [],
    userType: 2 // 默认为商户类型：1-管理员，2-商户
})

// 路由
const router = useRouter()
const handleButtonClick = (item) => {
    router.push(item.component)
}

// 分页参数
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索参数
const searchForm = reactive({
    settlementNo: '',  // 结算单号
    status: ''         // 状态
})

// 处理分页变化
const handleSizeChange = (size) => {
    pageSize.value = size
    getSettlementHistory()
}

const handleCurrentChange = (page) => {
    currentPage.value = page
    getSettlementHistory()
}

// 钱包信息
const walletInfo = ref({})
const loading = ref(false)

// 结算记录
const settlementHistory = ref([])
const historyLoading = ref(false)
const currentDetail = ref(null)
const detailDialogVisible = ref(false)

// 结算对话框
const settleDialogVisible = ref(false)
const settleFormRef = ref(null)
const imgSrc = ref('')
const submitting = ref(false)

// 验证码倒计时
const smsDisabled = ref(false)
const smsCountdown = ref(60)
const smsTimer = ref(null)
const smsSessionId = ref('') // 存储发送验证码后返回的会话ID

// 商家信息
const shopInfo = ref({})

//重新获取验证码
const getVerifyCode = () => {
    settleForm.randomStr = generateUUID()
    useUserInfoLogin().getCode(settleForm.randomStr).then(res => {
        if (res.code === 200) {
            imgSrc.value = "data:image/png;base64," + res.data
        }
    })
}
// 结算表单
const settleForm = reactive({
    amount: 100,
    accountName: '',
    accountNo: '',
    bankName: '',
    contactPhone: '',
    operationPassword:'',
    code: '',
    randomCode: ''
})

// 验证码按钮文字
const smsButtonText = computed(() => {
    return smsDisabled.value ? `重新发送(${smsCountdown.value}s)` : '发送验证码'
})

// 是否是管理员
const isAdmin = computed(() => {
    return state.userType === 1
})

// 是否是商户
const isMerchant = computed(() => {
    return state.userType === 2
})

// 可结算金额
const availableAmount = computed(() => {
    return walletInfo.value.balance ? parseFloat(walletInfo.value.balance) : 0
})

// 是否可以结算
const canSettlement = computed(() => {
    return availableAmount.value >= 100
})

// 表单验证规则
const settleRules = reactive({
    amount: [
        {required: true, message: '请输入结算金额', trigger: 'blur'},
        {
            validator: (rule, value, callback) => {
                if (value < 100) {
                    callback(new Error('结算金额不能低于100元'))
                } else if (value > availableAmount.value) {
                    callback(new Error('结算金额不能超过可结算余额'))
                } else {
                    callback()
                }
            },
            trigger: 'blur'
        }
    ],
    accountName: [
        {required: true, message: '收款账户名不能为空', trigger: 'blur'}
    ],
    accountNo: [
        {required: true, message: '收款账号不能为空', trigger: 'blur'}
    ],
    bankName: [
        {required: true, message: '开户行不能为空', trigger: 'blur'}
    ],
    operationPassword: [
        {required: true, message: '操作密码不能为空', trigger: 'blur'}
    ],
    contactPhone: [
        {required: true, message: '手机号码不能为空', trigger: 'blur'}
    ],
    randomCode: [
        {required: true, message: '验证码不能为空', trigger: 'blur'}
    ],
    code: [
        {required: true, message: '请输入验证码', trigger: 'blur'},
        {pattern: /^\d{6}$/, message: '验证码为6位数字', trigger: 'blur'}
    ]
})

// 审核对话框
const auditDialogVisible = ref(false)
const auditForm = reactive({
    id: null,
    status: '1', // 默认通过
    auditRemark: ''
})
const auditFormRef = ref(null)
const auditRules = reactive({
    status: [
        {required: true, message: '请选择审核结果', trigger: 'change'}
    ],
    auditRemark: [
        {required: false, message: '请输入审核意见'}
    ]
})

// 打款对话框
const paymentDialogVisible = ref(false)
const paymentForm = reactive({
    id: null,
    payProof: '',
    payRemark: ''
})
const paymentFormRef = ref(null)
const paymentRules = reactive({
    payProof: [
        {required: true, message: '请上传打款凭证', trigger: 'blur'}
    ]
})
const fileList = ref([])

// 取消对话框
const cancelDialogVisible = ref(false)
const cancelForm = reactive({
    id: null,
    remark: ''
})
const cancelFormRef = ref(null)
const cancelRules = reactive({
    remark: [
        {required: true, message: '请输入取消原因', trigger: 'blur'}
    ]
})

// 上传请求头
const headers = {
    Authorization: `Bearer ${Session.getToken()}`
}

// 获取钱包信息
const getWalletData = async () => {
    loading.value = true
    try {
        const response = await getWalletInfo()
        if (response.code === 200) {
            walletInfo.value = response.data
        }
    } catch (error) {
        console.error('获取钱包信息错误', error)
        ElMessage.error('获取钱包信息失败，请稍后重试')
    } finally {
        loading.value = false
    }
}

// 获取结算记录
const getSettlementHistory = async () => {
    historyLoading.value = true
    try {
        const params = {
            pageNum: currentPage.value,
            pageSize: pageSize.value,
            settlementNo: searchForm.settlementNo,
            status: searchForm.status
        }
        const response = await getSettlementList(params)
        if (response.code === 200) {
            settlementHistory.value = response.rows || []
            total.value = response.total || 0
        }
    } catch (error) {
        console.error('获取结算记录错误', error)
        ElMessage.error('获取结算记录失败，请稍后重试')
    } finally {
        historyLoading.value = false
    }
}

// 获取商家信息
const getShopData = async () => {
    const response = await getShopInfo()
    if (response.code === 200) {
        shopInfo.value = response.data
        // 设置联系电话
        settleForm.contactPhone = shopInfo.value.phone || ''
        settleForm.accountName = shopInfo.value.shopBankAccount?.bankAccountName || ''
        settleForm.accountNo = shopInfo.value.shopBankAccount?.bankAccountNumber || ''
        settleForm.bankName = shopInfo.value.shopBankAccount?.bankName || ''
        settleForm.bankBranchName = shopInfo.value.shopBankAccount?.bankBranchName || ''
    }
}

// 打开结算对话框
const openSettleDialog = () => {
    if (!canSettlement.value) {
        ElMessage.warning('可结算金额不足，最低结算金额为100元')
        return
    }
    getVerifyCode()
    getShopData()
    settleDialogVisible.value = true
}

// 发送验证码
const sendSmsCode = async () => {
    // 验证手机号
    if (!settleForm.contactPhone) {
        ElMessage.warning('未获取到商家联系电话')
        return
    }
    smsDisabled.value = true
    try {
        const response = await sendSettlementSmsCode({
            contactPhone: settleForm.contactPhone
        })
        // 开始倒计时
        smsCountdown.value = 60
        smsTimer.value = setInterval(() => {
            smsCountdown.value--
            if (smsCountdown.value <= 0) {
                clearInterval(smsTimer.value)
                smsDisabled.value = false
            }
        }, 1000)
        if (response.code === 200) {
            ElMessage.success('验证码已发送，请注意查收')
            smsSessionId.value = response.data // 保存会话ID
        }
    } catch (error) {
        console.error('发送验证码错误', error)
        ElMessage.error('发送验证码失败，请稍后重试')
        smsDisabled.value = false
    }
}

// 提交结算申请
const submitSettlement = async () => {
    await settleFormRef.value.validate(async (valid) => {
        if (!valid) {
            return false
        }
        // 二次确认
        try {
            await ElMessageBox.confirm(
                `您确定要申请结算${settleForm.amount}元吗？`,
                '确认结算',
                {
                    confirmButtonText: '确认',
                    cancelButtonText: '取消',
                    type: 'warning'
                }
            )
        } catch (e) {
            return
        }
        submitting.value = true
        try {
            const response = await verifyAndApplySettlement({
                contactPhone: settleForm.contactPhone,
                code: settleForm.code,
                amount: settleForm.amount,
                accountName: settleForm.accountName,
                accountNo: settleForm.accountNo,
                bankName: settleForm.bankName,
                bankBranchName: settleForm.bankBranchName,
                operationPassword: settleForm.operationPassword,
                randomStr: settleForm.randomStr,
                randomCode: settleForm.randomCode
            })
            if (response.code === 200) {
                ElMessage.success('结算申请提交成功')
                settleDialogVisible.value = false
                if (isMerchant.value) {
                    // 刷新数据
                    await getWalletData()
                    await getSettlementHistory()
                }
            }
        } catch (error) {
            console.error('提交结算申请错误', error)
        } finally {
            getVerifyCode()
            submitting.value = false
        }
    })
}

// 查看结算详情
const viewDetail = async (row) => {
    try {
        const response = await getSettlementDetail(row.id)
        if (response.code === 200) {
            currentDetail.value = response.data
            detailDialogVisible.value = true
        }
    } catch (error) {
        console.error('获取结算详情错误', error)
        ElMessage.error('获取结算详情失败，请稍后重试')
    }
}

// 打开审核对话框
const auditDetail = (row) => {
    if (row.status !== '0') {
        ElMessage.warning('只能审核待审核状态的结算单')
        return
    }
    auditForm.id = row.id
    auditForm.status = '1'
    auditForm.auditRemark = ''
    auditDialogVisible.value = true
}

// 提交审核
const submitAudit = async () => {
    await auditFormRef.value.validate(async (valid) => {
        if (!valid) return false
        try {
            const response = await auditSettlement(auditForm)
            if (response.code === 200) {
                ElMessage.success('审核操作成功')
                auditDialogVisible.value = false
                // 刷新数据
                getSettlementHistory()
            }
        } catch (error) {
            console.error('审核操作错误', error)
            ElMessage.error('审核操作失败，请稍后重试')
        }
    })
}

// 打开打款对话框
const payment = (row) => {
    if (row.status !== '1') {
        ElMessage.warning('只能对审核通过的结算单进行打款操作')
        return
    }
    paymentForm.id = row.id
    paymentForm.payProof = ''
    paymentForm.payRemark = ''
    paymentDialogVisible.value = true
}

// 提交打款确认
const submitPayment = async () => {
    await paymentFormRef.value.validate(async (valid) => {
        if (!valid) return false

        try {
            const response = await confirmPayment(paymentForm)
            if (response.code === 200) {
                ElMessage.success('确认打款操作成功')
                paymentDialogVisible.value = false
                // 刷新数据
                getSettlementHistory()
            }
        } catch (error) {
            console.error('确认打款操作错误', error)
            ElMessage.error('确认打款操作失败，请稍后重试')
        }
    })
}

// 完成结算
const complete = async (row) => {
    if (row.status !== '3') {
        ElMessage.warning('只能对已打款的结算单进行完成操作')
        return
    }

    try {
        await ElMessageBox.confirm(
            '确定要完成该结算单吗？',
            '提示',
            {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        const response = await completeSettlement(row.id)
        if (response.code === 200) {
            ElMessage.success('结算单已完成')
            if (isMerchant.value) {
                // 刷新数据
                getWalletData()
                getSettlementHistory()
            }
        }
    } catch (error) {
        if (error === 'cancel') return
        console.error('完成结算操作错误', error)
        ElMessage.error('完成结算操作失败，请稍后重试')
    }
}

// 打开取消结算对话框
const cancel = (row) => {
    // 只有待审核和审核通过的结算单可以取消
    if (row.status !== '0' && row.status !== '1') {
        ElMessage.warning('只能取消待审核或审核通过的结算单')
        return
    }

    cancelForm.id = row.id
    cancelForm.remark = ''
    cancelDialogVisible.value = true
}

// 提交取消结算
const submitCancel = async () => {
    await cancelFormRef.value.validate(async (valid) => {
        if (!valid) return false

        try {
            const response = await cancelSettlement(cancelForm.id, cancelForm.remark)
            if (response.code === 200) {
                ElMessage.success('结算单已取消')
                cancelDialogVisible.value = false
                if (isMerchant.value) {
                    // 刷新数据
                    getWalletData()
                    getSettlementHistory()
                }
            }
        } catch (error) {
            console.error('取消结算操作错误', error)
            ElMessage.error('取消结算操作失败，请稍后重试')
        }
    })
}

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '暂无'

    const date = new Date(dateString)
    return date.toLocaleString()
}

// 获取状态显示文字
const getStatusText = (status) => {
    const statusMap = {
        '0': '待审核',
        '1': '审核通过',
        '2': '审核拒绝',
        '3': '已打款',
        '4': '已完成',
        '5': '已取消'
    }
    return statusMap[status] || '未知状态'
}

// 获取状态标签类型
const getStatusType = (status) => {
    const typeMap = {
        '0': 'info',
        '1': 'success',
        '2': 'danger',
        '3': 'warning',
        '4': 'success',
        '5': 'info'
    }
    return typeMap[status] || 'info'
}

// 重置表单
const resetSettleForm = () => {
    settleForm.amount = 100
    settleForm.accountName = ''
    settleForm.accountNo = ''
    settleForm.bankName = ''
    settleForm.contactPhone = ''
    settleForm.code = ''

    // 清除定时器
    if (smsTimer.value) {
        clearInterval(smsTimer.value)
        smsTimer.value = null
        smsDisabled.value = false
    }
}

// 重置审核表单
const resetAuditForm = () => {
    auditForm.id = null
    auditForm.status = '1'
    auditForm.auditRemark = ''
}

// 重置打款表单
const resetPaymentForm = () => {
    paymentForm.id = null
    paymentForm.payProof = ''
    paymentForm.payRemark = ''
    fileList.value = []
}

// 重置取消表单
const resetCancelForm = () => {
    cancelForm.id = null
    cancelForm.remark = ''
}

// 删除打款凭证
const handleRemove = (file, fileList) => {
    paymentForm.payProof = ''
}

// 处理上传成功
const handleSuccessPic = (response) => {
    if (response && response.code === 200 && response.data) {
        paymentForm.payProof = response.data.filePath;
        // 如果已有URL部分，移除它
        if (paymentForm.payProof && paymentForm.payProof.includes(COS_URL + '/')) {
            paymentForm.payProof = paymentForm.payProof.replace(COS_URL + '/', '');
        }
    }
}

// 上传限制
const beforeAvatarUpload = (rawFile) => {
    console.log(rawFile.type)
    if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png') {
        ElMessage.error('只能上传图片')
        return false
    } else if (rawFile.size / 1024 / 1024 > 2) {
        ElMessage.error('图片最大2MB!')
        return false
    }
    return true
}

// 搜索
const handleSearch = () => {
    currentPage.value = 1
    getSettlementHistory()
}

// 重置搜索
const resetSearch = () => {
    searchForm.settlementNo = ''
    searchForm.status = ''
    currentPage.value = 1
    getSettlementHistory()
}

// 页面初始化
onMounted(async () => {
    // 加载导航菜单
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }

        // 获取用户类型 - 实际项目中可能需要从后端或Session中获取
        const userType = Session.get('userType');
        if (userType) {
            state.userType = parseInt(userType);
        } else {
            // 默认为商户类型
            state.userType = 2;
        }
    })

    // 结算记录和商家信息
    await getSettlementHistory()

    // 商户才需要加载钱包信息和商家信息
    if (isMerchant.value) {
        await getWalletData()
        await getShopData()
    }
})
</script>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.main {
    position: absolute;
    left: 250px;
    right: 0;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.main-content {
    width: 100%;
    height: 50px;
    font-weight: bold;
    font-size: 20px;
    display: flex;
    line-height: 50px;
    padding-left: 100px;
    border-bottom: 3px solid #000;
    margin-bottom: 20px;
}

.wallet-card {
    margin-bottom: 20px;

    .wallet-info {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-bottom: 20px;

        .wallet-item {
            flex: 1;
            min-width: 200px;
            margin: 10px;

            .label {
                font-size: 16px;
                font-weight: bold;
                color: #333;
            }

            .amount {
                font-size: 24px;
                font-weight: bold;
                color: #409EFF;
            }

            .frozen {
                font-size: 18px;
                color: #E6A23C;
            }

            .income {
                font-size: 18px;
                color: #67C23A;
            }

            .expense {
                font-size: 18px;
                color: #F56C6C;
            }
        }
    }

    .wallet-action {
        text-align: center;
    }
}

.settlement-history-card {
    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .search-form {
        margin-bottom: 20px;
        padding: 10px;
        background-color: #f5f7fa;
        border-radius: 4px;

        .el-form-item {
            margin-bottom: 10px;
        }
    }
}

.form-tip {
    margin-left: 10px;
    color: #909399;
    font-size: 14px;
}

.settlement-detail {
    max-height: 60vh;
    overflow-y: auto;
}

.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
}

.info-icon {
    margin-left: 5px;
    color: #909399;
    cursor: help;
}
</style>
