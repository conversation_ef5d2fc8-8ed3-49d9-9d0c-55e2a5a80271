<script setup>
import {useRouter} from 'vue-router'
import {ref} from 'vue'

const router = useRouter()
const billingMethod = ref('weight') // 默认选中按重量计费

// 表单数据
const formData = ref({
    courierName: '',
    weightFirst: '',
    weightFirstFee: '',
    weightAdditional: '',
    weightAdditionalFee: '',
    pieceFirst: '',
    pieceFirstFee: '',
    pieceAdditional: '',
    pieceAdditionalFee: '',
    price: ''
})

// 按钮列表数据
const buttonList = [
    '发布商品', '商品列表', '商品分类', '品牌管理', '配送管理',
    '评论管理', '退货地址', '平台促销券', '商品链接', '商品链接生成',
    '商品链接导入', '商品代销申请'
]

const handleButtonClick = (item) => {
    if (item === '商品列表') router.push('/productList')
    if (item === '发布商品') router.push('/commodity')
    if (item === '商品分类') router.push('/productCategory')
    if (item === '品牌管理') router.push('/brandManage')
    if (item === '配送管理') router.push('./deliveryManage')
    if (item === '评论管理') router.push('./commentManage')
    if (item === '退货地址') router.push('./returnAddress')
    if (item === '商品链接') router.push('./productLink')
    if (item === '商品链接生成') router.push('./buildProductLink')
    if (item === '商品链接导入') router.push('./productLinkImport')
}


const saveSettings = () => {
    router.push('/deliveryManage')
}
</script>

<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item }}
            </el-button>
        </div>

        <div class="main">
            <div class="head">
                <span class="name">名称</span>
                <input
                    v-model="formData.courierName"
                    class="courier"
                    placeholder="快递公司"
                    type="text"
                >
            </div>

            <div class="middle">
                <div class="fee-box">
                    <div
                        :class="{ active: billingMethod === 'weight' }"
                        class="calMethod"
                        @click="billingMethod = 'weight'"
                    >
                        按重量计费
                    </div>
                    <input
                        v-model="billingMethod"
                        class="radio-input"
                        type="radio"
                        value="weight"
                    >
                </div>

                <div class="fee-box">
                    <div
                        :class="{ active: billingMethod === 'piece' }"
                        class="calMethod"
                        @click="billingMethod = 'piece'"
                    >
                        按件计费
                    </div>
                    <input
                        v-model="billingMethod"
                        class="radio-input"
                        type="radio"
                        value="piece"
                    >
                </div>
            </div>

            <div class="fee-details">
                <!-- 按重量计费部分 -->
                <div v-show="billingMethod === 'weight'" class="weight-section">
                    <div class="fee-row">
                        <div class="box">
                            <span class="text">首重</span>
                            <div class="input-container">
                                <input
                                    v-model="formData.weightFirst"
                                    class="input"
                                    type="number"
                                >
                                <span class="unit">克</span>
                            </div>
                        </div>
                        <button class="fee">运费</button>
                    </div>
                    <div class="fee-row">
                        <div class="box">
                            <span class="text">续重</span>
                            <div class="input-container">
                                <input
                                    v-model="formData.weightAdditional"
                                    class="input"
                                    type="number"
                                >
                                <span class="unit">克</span>
                            </div>
                        </div>
                        <button class="fee">操作</button>
                    </div>
                    <div class="fee-row">
                        <div class="box">
                            <span class="text">价格</span>
                            <div class="input-container">
                                <input
                                    v-model="formData.price"
                                    class="input"
                                    type="number"
                                >
                                <span class="unit">元</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 按件计费部分 -->
                <div v-show="billingMethod === 'piece'" class="piece-section">
                    <div class="fee-row">
                        <div class="box">
                            <span class="text">首件</span>
                            <div class="input-container">
                                <input
                                    v-model="formData.pieceFirst"
                                    class="input"
                                    type="number"
                                >
                                <span class="unit">个</span>
                            </div>
                        </div>
                        <button class="fee">首费</button>
                    </div>
                    <div class="fee-row">
                        <div class="box">
                            <span class="text">续件</span>
                            <div class="input-container">
                                <input
                                    v-model="formData.pieceAdditional"
                                    class="input"
                                    type="number"
                                >
                                <span class="unit">个</span>
                            </div>
                        </div>
                        <button class="fee">操作</button>
                    </div>
                    <div class="fee-row">
                        <div class="box">
                            <span class="text">价格</span>
                            <div class="input-container">
                                <input
                                    v-model="formData.price"
                                    class="input"
                                    type="number"
                                >
                                <span class="unit">元</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="save-button">
                <button @click="saveSettings">保存设置</button>
            </div>
        </div>
    </div>
    <!--  </ManageBg>-->
</template>

<style lang="scss" scoped>
.container {
    display: flex;
    max-height: 100vh;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.main {
    width: 1291px;
    height: 761px;
    margin-left: 70px;
    margin-top: 68px;
    border: 1px solid #3A58CF;
    padding-bottom: 20px;

    .head {
        display: flex;
        align-items: center;
        margin-top: 76px;

        .name {
            font-size: 40px;
            margin-left: 96px;
            min-width: 120px;
        }

        .courier {
            width: 793px;
            height: 71px;
            background-color: #D2E0FB;
            font-size: 36px;
            margin-left: 86px;
            padding: 0 20px;
            border: none;
            outline: none;
        }
    }

    .middle {
        display: flex;
        gap: 50px;
        margin-top: 40px;
        margin-left: 96px;

        .fee-box {
            display: flex;
            align-items: center;
            margin-right: 291px;
            gap: 20px;

            .calMethod {
                width: 235px;
                height: 71px;
                background-color: #D2E0FB;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                cursor: pointer;
                transition: all 0.3s;

                &.active {
                    background-color: #3A58CF;
                    color: white;
                }
            }

            .radio-input {
                width: 54px;
                height: 54px;
                cursor: pointer;
                appearance: none;
                -webkit-appearance: none;
                border: 2px solid #3A58CF;
                border-radius: 4px;
                outline: none;
                position: relative;

                &:checked {
                    background-color: #3A58CF;

                    &::after {
                        content: '✓';
                        position: absolute;
                        color: white;
                        font-size: 30px;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                    }
                }
            }
        }
    }

    .fee-details {
        margin-left: 96px;
        margin-top: 30px;
        display: flex;
        justify-content: center;
        align-items: center;

        .fee-row {
            display: flex;
            gap: 30px;
            margin-bottom: 20px;
        }

        .box {
            width: 344px;
            height: 71px;
            background-color: #D2E0FB;
            display: flex;
            align-items: center;
            border-radius: 4px;

            .text {
                font-size: 30px;
                margin-left: 16px;
                min-width: 60px;
            }

            .input-container {
                flex: 1;
                height: 100%;
                display: flex;
                align-items: center;
                position: relative;

                .input {
                    width: 100%;
                    height: 100%;
                    background: transparent;
                    border: none;
                    outline: none;
                    font-size: 30px;
                    padding: 0 50px 0 10px;
                    text-align: left;

                    &::-webkit-outer-spin-button,
                    &::-webkit-inner-spin-button {
                        -webkit-appearance: none;
                        margin: 0;
                    }
                }

                .unit {
                    position: absolute;
                    right: 10px;
                    font-size: 30px;
                    color: #666;
                }
            }
        }

        .fee {
            width: 121px;
            height: 71px;
            background-color: #3A58CF;
            color: #fff;
            font-size: 30px;
            border: none;
            cursor: pointer;
        }
    }

    .save-button {
        display: flex;
        justify-content: center;
        margin-top: 40px;

        button {
            width: 200px;
            height: 60px;
            background-color: #3A58CF;
            color: white;
            font-size: 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;

            &:hover {
                background-color: #2a48bf;
            }
        }
    }
}
</style>
