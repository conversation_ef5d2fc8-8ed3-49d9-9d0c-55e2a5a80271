<template>
    <div class="platform-server-container">
        <!-- 左侧用户列表 -->
        <div class="user-list-container">
            <div class="platform-header">
                <h5 class="mb-0">平台客服系统 <span class="platform-badge">客服端</span></h5>
            </div>
            <div id="userList" class="user-list">
                <!-- 加载错误提示 -->
                <div v-if="loadError" class="error-conversations">
                    <div class="error-icon">⚠️</div>
                    <p>加载会话列表失败</p>
                    <button class="retry-btn" @click="loadConversations">重新加载</button>
                </div>

                <!-- 用户列表为空时显示提示 -->
                <div v-else-if="conversations.length === 0" class="empty-conversations">
                    <div class="empty-icon">💬</div>
                    <p>暂无聊天记录</p>
                    <p class="empty-tip">当用户与您联系时，会显示在这里</p>
                </div>

                <!-- 用户列表 -->
                <div
                    v-for="conversation in conversations"
                    v-else
                    :key="conversation.id"
                    :class="{ active: currentConversationId === conversation.id }"
                    class="user-item"
                    @click="selectConversation(conversation)"
                >
                    <div class="user-avatar">
                        <img :src="getConversationAvatar(conversation)" alt="头像">
                    </div>
                    <div class="user-info">
                        <div class="user-name">
                            {{ getConversationName(conversation) }}
                            <!--                            <span v-if="conversation.unreadCount > 0" class="unread-badge">{{ conversation.unreadCount }}</span>-->
                        </div>
                        <div v-if="conversation.content" class="last-message">
                            {{ conversation.content }}
                        </div>
                        <div class="message-time">
                            {{ formatTime(conversation.lastMessageTime) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧聊天窗口 -->
        <div class="chat-container">
            <div id="chatHeader" class="chat-header">
                <h5>
                    <template v-if="currentConversation">
                        <template v-if="currentConversation.type === '5'">
                            {{
                                currentConversation.merchantName || (currentConversation.merchantId ? '商家' + currentConversation.merchantId : '未知商家')
                            }}
                        </template>
                        <template v-else>
                            {{
                                currentConversation.userName || (currentConversation.userId ? '用户' + currentConversation.userId : '未知用户')
                            }}
                        </template>
                    </template>
                    <template v-else>请选择一个会话开始聊天</template>
                </h5>
            </div>
            <div id="chatMessages" ref="chatMessagesRef" class="chat-messages">
                <!-- 到顶提示 -->
                <div v-if="showScrollTopTip" class="scroll-top-tip">
                    <span>已经到顶了，没有更多消息了</span>
                </div>

                <!-- 没有更多消息提示 -->
                <div v-if="noMoreMessages" class="no-more-messages">
                    <span>没有更多历史消息了</span>
                </div>

                <!-- 加载更多指示器 -->
                <div v-if="isLoadingMessages && currentPage > 1" class="loading-indicator">
                    <div class="spinner"></div>
                    <span>加载更多消息...</span>
                </div>

                <template v-if="messages.length > 0">
                    <div
                        v-for="message in messages"
                        :key="message.id"
                        :class="{ 'message-self': message.senderType === '3', 'message-other': message.senderType !== '3' }"
                        class="message-item"
                    >
                        <div class="message-avatar">
                            <img
                                :src="getMessageAvatar(message)"
                                alt="头像"
                            >
                        </div>
                        <div class="message-content">
                            <div class="message-sender">
                                {{ getMessageSenderName(message) }}
                            </div>
                            <div class="message-bubble">
                                <!-- 根据消息类型显示不同内容 -->
                                <div v-if="message.contentType === '1'" class="message-text">{{ message.content }}</div>
                                <div v-else-if="message.contentType === '2'" class="message-image">
                                    <img :src="getImageUrl(message.content)" alt="图片" class="preview-image"
                                         @click="previewImage(getImageUrl(message.content))">
                                </div>
                                <div v-else-if="message.contentType === '3'" class="message-audio">
                                    <audio :src="message.content" controls></audio>
                                </div>
                                <div v-else-if="message.contentType === '4'" class="message-video">
                                    <video :src="message.content" controls></video>
                                </div>
                                <div v-else-if="message.contentType === '5'" class="message-file">
                                    <a :href="message.content" target="_blank">查看文件</a>
                                </div>
                                <div v-else-if="message.contentType === '6'" class="message-product">
                                    <div class="product-card">商品信息</div>
                                </div>
                            </div>
                            <div class="message-time">{{ formatTime(message.sendTime) }}</div>
                        </div>
                    </div>
                </template>
                <div v-else class="no-message">
                    <p>请选择一个用户开始聊天</p>
                </div>
            </div>
            <div v-if="currentConversationId" class="chat-input">
                <div class="toolbar">
                    <div class="tool-item" @click="openImageUpload">
                        <i class="el-icon-picture">🖼️</i>
                        <input ref="imageInput" accept="image/*" style="display: none" type="file"
                               @change="handleImageUpload">
                    </div>
                </div>
                <div class="input-area">
                    <textarea
                        v-model="messageInput"
                        :disabled="!currentConversationId"
                        class="message-textarea"
                        placeholder="请输入消息..."
                        @keydown.enter.prevent="sendMessage"
                    ></textarea>
                </div>
                <div class="send-button">
                    <button
                        :disabled="!currentConversationId || !messageInput.trim()"
                        class="btn-send"
                        @click="sendMessage"
                    >
                        发送
                    </button>
                </div>
            </div>
        </div>

        <!-- 图片预览组件 -->
        <el-image-viewer
            v-if="showImageViewer"
            :hide-on-click-modal="true"
            :initial-index="0"
            :url-list="previewImageList"
            @close="closeImageViewer"
        />
    </div>
</template>
<script setup>
import {computed, nextTick, onMounted, onUnmounted, reactive, ref, watch} from 'vue'
import {
    getConversationMessages,
    getPlatformConversations,
    markAllMessagesAsRead,
    markMessageAsRead,
    uploadChatFile
} from '../../api/chat'
import {Session} from "@/utils/storage.js";
import {getImageUrl} from "@/utils/common.js";
import {ElImageViewer} from 'element-plus'

const conversations = ref([])
const messages = ref([])
const currentConversationId = ref(null)
const currentConversation = ref(null)
const messageInput = ref('')
const chatMessagesRef = ref(null)
const imageInput = ref(null)
const platformAvatar = ref('/platform-avatar.png') // 默认平台头像
const webSocket = ref(null)
const currentPage = ref(1)
const hasMoreMessages = ref(true)
const isLoadingMessages = ref(false)
const showImageViewer = ref(false)
const previewImageUrl = ref('')
const previewImageList = ref([])
const noMoreMessages = ref(false)
const showScrollTopTip = ref(false)
const loadError = ref(false)

const state = reactive({
    webSocketRetryCount: 0,
    MAX_RETRY: 5
})

// 收集所有图片的计算属性
const allMessageImages = computed(() => {
    // 从消息中提取所有图片URL
    const imageUrls = messages.value
        .filter(msg => msg.contentType === '2' && msg.content)
        .map(msg => getImageUrl(msg.content))
        .filter(url => url) // 过滤掉空值

    return imageUrls
})

// 格式化时间
const formatTime = (time) => {
    if (!time) return '未知时间'
    const date = new Date(time)
    return date.toLocaleString()
}

// 选择会话
const selectConversation = (conversation) => {
    currentConversationId.value = conversation.id
    currentConversation.value = conversation
    // 重置分页状态
    currentPage.value = 1
    hasMoreMessages.value = true
    // 加载第一页消息
    loadMessages(conversation.id)
    markAllAsRead(conversation.id)
}

// 加载会话列表
const loadConversations = async () => {
    try {
        loadError.value = false
        const response = await getPlatformConversations()
        if (response.code === 200) {
            conversations.value = response.data
        } else {
            console.error('加载会话列表失败:', response.msg)
            loadError.value = true
        }
    } catch (error) {
        console.error('加载会话列表失败:', error)
        loadError.value = true
    }
}

// 修改后的加载消息列表函数，支持分页加载
const loadMessages = async (conversationId, page = 1, append = false) => {
    if (isLoadingMessages.value) return

    try {
        isLoadingMessages.value = true
        // 设置每页加载的消息数量为20条
        const pageSize = 20
        console.log(`加载消息: 会话ID=${conversationId}, 页码=${page}, 每页${pageSize}条`)

        const response = await getConversationMessages(conversationId, page, pageSize)

        if (response.code === 200) {
            const newMessages = response.rows || []
            console.log(`获取到${newMessages.length}条消息`)

            // 判断是否还有更多消息 - 如果返回的消息数量等于pageSize，说明可能还有更多
            hasMoreMessages.value = newMessages.length === pageSize

            // 如果没有更多消息且是加载更多操作，显示提示
            if (!hasMoreMessages.value && page > 1) {
                noMoreMessages.value = true
                // 5秒后自动隐藏提示
                setTimeout(() => {
                    noMoreMessages.value = false
                }, 5000)
            }

            console.log(`是否还有更多消息: ${hasMoreMessages.value}`)

            if (append) {
                // 加载更多历史消息时，将新消息添加到列表前面
                messages.value = [...newMessages.reverse(), ...messages.value]
                console.log(`追加模式: 总消息数=${messages.value.length}`)
            } else {
                // 首次加载，直接替换
                messages.value = newMessages.reverse()
                console.log(`替换模式: 总消息数=${messages.value.length}`)
                scrollToBottom()
            }
        } else {
            console.error('加载消息失败:', response.msg)
        }
    } catch (error) {
        console.error('加载消息失败:', error)
    } finally {
        isLoadingMessages.value = false
    }
}

// 加载更多历史消息
const loadMoreMessages = () => {
    if (!hasMoreMessages.value || isLoadingMessages.value || !currentConversationId.value) return

    currentPage.value++
    loadMessages(currentConversationId.value, currentPage.value, true)
}

// 增强handleScroll函数，添加顶部提示逻辑
const handleScroll = () => {
    if (!chatMessagesRef.value) return

    const {scrollTop, scrollHeight, clientHeight} = chatMessagesRef.value

    // 当滚动到顶部附近时加载更多消息
    if (scrollTop < 50) {
        if (hasMoreMessages.value && !isLoadingMessages.value) {
            console.log(`触发加载更多: scrollTop=${scrollTop}, 当前页=${currentPage.value}`)

            // 记录当前滚动位置和内容高度
            const oldScrollHeight = scrollHeight

            loadMoreMessages()

            // 加载完成后调整滚动位置，保持用户的相对位置不变
            nextTick(() => {
                if (chatMessagesRef.value) {
                    const newScrollHeight = chatMessagesRef.value.scrollHeight
                    const newScrollTop = newScrollHeight - oldScrollHeight
                    console.log(`调整滚动位置: 旧高度=${oldScrollHeight}, 新高度=${newScrollHeight}, 新位置=${newScrollTop}`)
                    chatMessagesRef.value.scrollTop = newScrollTop > 0 ? newScrollTop : 0
                }
            })
        } else if (!hasMoreMessages.value && !noMoreMessages.value && messages.value.length > 0) {
            // 如果没有更多消息且未显示提示，则显示提示
            showScrollTopTip.value = true
            // 5秒后自动隐藏提示
            setTimeout(() => {
                showScrollTopTip.value = false
            }, 5000)
        }
    }

    // 检测是否滚动到底部，可以用于未来的"新消息"提示
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 50
    console.log(`滚动状态: 是否在底部=${isAtBottom}`)
}

// 标记所有消息为已读
const markAllAsRead = async (conversationId) => {
    try {
        await markAllMessagesAsRead(conversationId)
        // 更新会话列表中的未读数
        const index = conversations.value.findIndex(c => c.id === conversationId)
        if (index !== -1) {
            conversations.value[index].unreadCount = 0
        }
    } catch (error) {
        console.error('标记已读失败:', error)
    }
}

// 发送消息
const sendMessage = async () => {
    if (!currentConversationId.value || !messageInput.value.trim()) return

    try {
        // 确定接收者ID
        let receiverId;

        // 根据会话类型确定接收者ID
        if (currentConversation.value.type === "5") { // 商家对平台
            receiverId = currentConversation.value.merchantId; // 使用商家ID作为接收者
        } else if (currentConversation.value.type === "4") { // 用户对平台
            receiverId = currentConversation.value.userId; // 使用用户ID作为接收者
        } else {
            // 其他类型会话，默认使用用户ID
            receiverId = currentConversation.value.userId;
        }

        // 如果接收者ID为空，则使用默认值
        if (!receiverId) {
            console.warn('接收者ID为空，尝试使用商家ID');
            receiverId = currentConversation.value.merchantId;

            if (!receiverId) {
                console.error('无法确定接收者ID');
                alert('发送失败：无法确定接收者');
                return;
            }
        }

        // 构建消息对象
        const message = {
            action: 'sendMessage',
            conversationId: currentConversationId.value,
            content: messageInput.value.trim(),
            contentType: '1', // 文本消息
            receiverId: receiverId,
            senderType: '3' // 平台身份
        }

        console.log('平台发送消息:', message);

        // 通过WebSocket发送消息
        if (webSocket.value && webSocket.value.readyState === WebSocket.OPEN) {
            webSocket.value.send(JSON.stringify(message))

            // 清空输入框
            messageInput.value = ''

            scrollToBottom()
        } else {
            console.error('WebSocket未连接')
            alert('消息发送失败，请刷新页面重试')
        }
    } catch (error) {
        console.error('发送消息失败:', error)
    }
}

// 滚动到底部
const scrollToBottom = () => {
    nextTick(() => {
        if (chatMessagesRef.value) {
            chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight
        }
    })
}

// 打开图片上传
const openImageUpload = () => {
    if (imageInput.value) {
        imageInput.value.click()
    }
}

// 处理图片上传
const handleImageUpload = async (event) => {
    const file = event.target.files[0]
    if (!file) return

    try {
        const response = await uploadChatFile(file)

        if (response.code === 200) {
            // 确定接收者ID
            let receiverId;

            // 根据会话类型确定接收者ID
            if (currentConversation.value.type === "5") { // 商家对平台
                receiverId = currentConversation.value.merchantId; // 使用商家ID作为接收者
            } else if (currentConversation.value.type === "4") { // 用户对平台
                receiverId = currentConversation.value.userId; // 使用用户ID作为接收者
            } else {
                // 其他类型会话，默认使用用户ID
                receiverId = currentConversation.value.userId;
            }

            // 如果接收者ID为空，则使用默认值
            if (!receiverId) {
                console.warn('接收者ID为空，尝试使用商家ID');
                receiverId = currentConversation.value.merchantId;

                if (!receiverId) {
                    console.error('无法确定接收者ID');
                    alert('发送失败：无法确定接收者');
                    return;
                }
            }

            // 发送图片消息
            const imageUrl = response.data.filePath
            const message = {
                action: 'sendMessage',
                conversationId: currentConversationId.value,
                content: imageUrl,
                contentType: '2', // 图片消息
                receiverId: receiverId,
                senderType: '3' // 平台身份
            }

            console.log('平台发送图片消息:', message);

            if (webSocket.value && webSocket.value.readyState === WebSocket.OPEN) {
                webSocket.value.send(JSON.stringify(message))
                scrollToBottom()
            }
        } else {
            console.error('上传图片失败:', response.msg)
        }
    } catch (error) {
        console.error('上传图片失败:', error)
    }

    // 清空input，允许重复上传相同文件
    event.target.value = ''
}

// 优化预览图片方法，支持浏览当前会话的所有图片
const previewImage = (url) => {
    if (!url) return

    // 使用所有图片列表
    const imageList = allMessageImages.value

    // 如果没有图片，直接返回
    if (imageList.length === 0) return

    previewImageUrl.value = url
    previewImageList.value = imageList
    showImageViewer.value = true
}

// 关闭图片预览
const closeImageViewer = () => {
    showImageViewer.value = false
    previewImageUrl.value = ''
}

// 连接WebSocket
const connectWebSocket = () => {
    try {
        const token = Session.get('token')
        const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
        const wsUrl = protocol + window.location.host + `/api/websocket/chat/${token}/platform`;
        console.log("wsUrl", wsUrl)
        webSocket.value = new WebSocket(wsUrl)

        webSocket.value.onopen = () => {
            console.log('WebSocket连接成功')
            // 发送心跳消息
            startHeartbeat()
        }

        webSocket.value.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data)
                if (data && data.type === 'message') {
                    // 处理新消息
                    handleNewMessage(data.data)
                }
            } catch (error) {
                console.error('处理消息失败:', error)
            }
        }

        webSocket.value.onclose = () => {
            window.chatSocket = null;
            // 尝试重连，但有最大重试次数限制
            if (state.webSocketRetryCount < state.MAX_RETRY) {
                state.webSocketRetryCount++;
                const delay = Math.min(1000 * Math.pow(2, state.webSocketRetryCount), 30000);
                console.log(`尝试第 ${state.webSocketRetryCount} 次重连，延迟 ${delay}ms`);
                setTimeout(connectWebSocket, delay);
            } else {
                console.error('WebSocket连接失败，已达到最大重试次数');
            }
        };

        webSocket.value.onerror = (error) => {
            console.error('WebSocket错误:', error)
        }
    } catch (error) {
        console.error('连接WebSocket失败:', error)
    }
}

// 处理新消息
const handleNewMessage = (message) => {
    // 如果是当前会话的消息，检查是否已存在，避免重复显示
    if (message.conversationId === currentConversationId.value) {
        // 检查消息是否已存在
        const messageExists = messages.value.some(m => m.id === message.id);

        if (!messageExists) {
            // 消息不存在，添加到列表末尾
            messages.value.push(message);
            scrollToBottom();
            // 标记为已读
            markMessageAsRead(message.id);
        } else {
            console.log('消息已存在，忽略重复消息:', message.id);
        }
    }

    // 更新会话列表
    updateConversationList(message);
}

// 更新会话列表
const updateConversationList = (message) => {
    const index = conversations.value.findIndex(c => c.id === message.conversationId)

    if (index !== -1) {
        // 更新现有会话
        const conversation = conversations.value[index]
        conversation.lastMessageId = message.id
        conversation.lastMessageTime = message.sendTime
        conversation.content = message.content

        // 如果不是当前会话，增加未读数
        if (message.conversationId !== currentConversationId.value) {
            conversation.unreadCount = (conversation.unreadCount || 0) + 1
        }

        // 将该会话移到顶部
        const updatedConversation = {...conversation}
        conversations.value.splice(index, 1)
        conversations.value.unshift(updatedConversation)
    } else {
        // 如果是新会话，重新加载会话列表
        loadConversations()
    }
}

// 心跳机制
const startHeartbeat = () => {
    setInterval(() => {
        if (webSocket.value && webSocket.value.readyState === WebSocket.OPEN) {
            webSocket.value.send(JSON.stringify({action: 'heartbeat'}))
        }
    }, 30000) // 每30秒发送一次心跳
}

// 获取会话头像
const getConversationAvatar = (conversation) => {
    // 默认头像
    const defaultUserAvatar = '/default-avatar.png';
    const defaultMerchantAvatar = '/merchant-avatar.png';
    const platformAvatar = '/platform-avatar.png';

    if (conversation.type === '5') { // 商家对平台
        // 检查商家头像是否存在
        const merchantAvatar = conversation.merchantAvatar;
        return merchantAvatar ? getImageUrl(merchantAvatar) : defaultMerchantAvatar;
    } else if (conversation.type === '4') { // 用户对平台
        // 检查用户头像是否存在
        const userAvatar = conversation.userAvatar;
        return userAvatar ? getImageUrl(userAvatar) : defaultUserAvatar;
    } else {
        // 默认用户头像
        const userAvatar = conversation.userAvatar;
        return userAvatar ? getImageUrl(userAvatar) : defaultUserAvatar;
    }
}

// 获取消息发送者头像
const getMessageAvatar = (message) => {
    // 默认头像
    const defaultUserAvatar = '/default-avatar.png';
    const defaultMerchantAvatar = '/merchant-avatar.png';
    const platformAvatar = getImageUrl('mall/2025/07/15/b094b88a83874580b8636ce57753d13b.png');

    if (message.senderType === '3') { // 平台
        return platformAvatar;
    } else if (message.senderType === '2') { // 商家
        const merchantAvatar = currentConversation.value?.merchantAvatar;
        return merchantAvatar ? getImageUrl(merchantAvatar) : defaultMerchantAvatar;
    } else { // 用户
        const userAvatar = currentConversation.value?.userAvatar;
        return userAvatar ? getImageUrl(userAvatar) : defaultUserAvatar;
    }
}

// 获取会话名称
const getConversationName = (conversation) => {
    if (conversation.type === '5') { // 商家对平台
        return conversation.merchantName || (conversation.merchantId ? '商家' + conversation.merchantId : '未知商家');
    } else if (conversation.type === '4') { // 用户对平台
        return conversation.userName || (conversation.userId ? '用户' + conversation.userId : '未知用户');
    } else if (conversation.type === '3') { // 用户对平台
        return "（群聊）" + conversation.userName || (conversation.userId ? '用户' + conversation.userId : '未知用户');
    } else {
        return conversation.userName || (conversation.userId ? '用户' + conversation.userId : '未知用户');
    }
}

// 获取消息发送者名称
const getMessageSenderName = (message) => {
    if (message.senderType === '3') { // 平台
        return currentConversation.value?.platformName || '平台';
    } else if (message.senderType === '2') { // 商家
        return currentConversation.value?.merchantName || (currentConversation.value?.merchantId ? '商家' + currentConversation.value.merchantId : '未知商家');
    } else { // 用户
        return currentConversation.value?.userName || (currentConversation.value?.userId ? '用户' + currentConversation.value.userId : '未知用户');
    }
}

// 监听会话变化
watch(currentConversationId, (newVal) => {
    if (newVal) {
        // 重置分页状态
        currentPage.value = 1
        hasMoreMessages.value = true
        loadMessages(newVal)
    } else {
        messages.value = []
    }
})

// 组件挂载
onMounted(async () => {
    await loadConversations()
    connectWebSocket()

    // 给聊天消息容器添加滚动事件监听
    if (chatMessagesRef.value) {
        chatMessagesRef.value.addEventListener('scroll', handleScroll)
    }

    // 检查URL参数是否有指定会话ID
    const urlParams = new URLSearchParams(window.location.search)
    const conversationId = urlParams.get('conversationId')
    if (conversationId) {
        // 找到对应的会话
        const conversation = conversations.value.find(c => c.id === parseInt(conversationId))
        if (conversation) {
            selectConversation(conversation)
        }
    }
})

// 组件卸载时移除滚动事件监听器
onUnmounted(() => {
    if (chatMessagesRef.value) {
        chatMessagesRef.value.removeEventListener('scroll', handleScroll)
    }
})
</script>
<style lang="scss" scoped>
.platform-server-container {
    display: flex;
    height: 85vh;
    background-color: #f5f5f5;
}

/* 左侧用户列表样式 */
.user-list-container {
    width: 300px;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #dee2e6;
    background-color: #fff;
}

.platform-header {
    padding: 15px;
    background-color: #2c3e50; /* 平台颜色与商家不同 */
    color: white;
    border-bottom: 1px solid #dee2e6;
}

.platform-badge {
    background-color: #17a2b8; /* 平台徽章颜色 */
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    margin-left: 5px;
}

.user-list {
    flex: 1;
    overflow-y: auto;
}

.user-item {
    display: flex;
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
}

.user-item:hover {
    background-color: #f8f9fa;
}

.user-item.active {
    background-color: #e9ecef;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-weight: bold;
    margin-bottom: 3px;
    display: flex;
    justify-content: space-between;
}

.last-message {
    color: #6c757d;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.message-time {
    font-size: 0.8rem;
    color: #adb5bd;
    margin-top: 3px;
}

.unread-badge {
    background-color: #ff5722;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.7rem;
}

/* 右侧聊天窗口样式 */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.chat-header {
    padding: 15px;
    background-color: #2c3e50; /* 平台颜色与商家不同 */
    color: white;
    border-bottom: 1px solid #dee2e6;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background-color: #f5f7fa;
}

.no-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #adb5bd;
}

.message-item {
    display: flex;
    margin-bottom: 15px;
}

.message-self {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 10px;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.message-content {
    max-width: 70%;
}

.message-self .message-content {
    align-items: flex-end;
}

.message-sender {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 4px;
}

.message-self .message-sender {
    text-align: right;
}

.message-bubble {
    padding: 10px 15px;
    border-radius: 18px;
    position: relative;
    word-break: break-word;
}

.message-self .message-bubble {
    background-color: #17a2b8; /* 平台消息气泡颜色 */
    color: white;
    border-top-right-radius: 4px;
}

.message-other .message-bubble {
    background-color: #e9ecef;
    color: #212529;
    border-top-left-radius: 4px;
}

.message-time {
    font-size: 0.7rem;
    color: #adb5bd;
    margin-top: 4px;
}

.message-self .message-time {
    text-align: right;
}

/* 图片消息样式 */
.message-image {
    padding: 0;
    background-color: #fff;
}

.message-image img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
}

.message-image img:hover {
    transform: scale(1.03);
}

.preview-image {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 音频消息样式 */
.message-audio audio {
    max-width: 250px;
}

/* 视频消息样式 */
.message-video video {
    max-width: 250px;
    max-height: 200px;
    border-radius: 4px;
}

/* 文件消息样式 */
.message-file a {
    display: inline-block;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    color: #007bff;
    text-decoration: none;
}

/* 商品消息样式 */
.product-card {
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background-color: white;
}

/* 聊天输入区域样式 */
.chat-input {
    padding: 15px;
    border-top: 1px solid #dee2e6;
    background-color: white;
}

.toolbar {
    display: flex;
    margin-bottom: 10px;
}

.tool-item {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-right: 10px;
    border-radius: 4px;
}

.tool-item:hover {
    background-color: #f0f0f0;
}

.input-area {
    margin-bottom: 10px;
}

.message-textarea {
    width: 100%;
    height: 80px;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    resize: none;
    outline: none;
}

.send-button {
    display: flex;
    justify-content: flex-end;
}

.btn-send {
    padding: 8px 20px;
    background-color: #17a2b8; /* 平台发送按钮颜色 */
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-send:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* 加载指示器样式 */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    color: #6c757d;
    font-size: 0.9rem;
}

.spinner {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #17a2b8; /* 平台加载指示器颜色 */
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 到顶提示样式 */
.scroll-top-tip {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 10px;
    border-radius: 4px;
    animation: fadeIn 0.3s ease-in-out;
}

/* 没有更多消息提示样式 */
.no-more-messages {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 10px;
    border-radius: 4px;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 添加空会话列表样式 */
.empty-conversations {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 20px;
    text-align: center;
    color: #6c757d;
}

.empty-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    opacity: 0.7;
}

.empty-tip {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 5px;
}

/* 错误提示样式 */
.error-conversations {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 20px;
    text-align: center;
    color: #dc3545;
}

.error-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.retry-btn {
    margin-top: 15px;
    padding: 6px 15px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.retry-btn:hover {
    background-color: #bd2130;
}

/* 图片预览相关样式 */
:deep(.el-image-viewer__close) {
    color: #fff;
    font-size: 24px;
}

:deep(.el-image-viewer__canvas) {
    max-width: 100%;
    max-height: 100%;
}

:deep(.el-image-viewer__btn) {
    opacity: 0.8;
}

:deep(.el-image-viewer__prev, .el-image-viewer__next) {
    width: 44px;
    height: 44px;
    font-size: 24px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
}
</style>
