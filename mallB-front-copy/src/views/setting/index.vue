<script setup>
import {useRouter} from 'vue-router'
import {CloseBold, Lock, Refresh} from '@element-plus/icons-vue'
import {ref} from 'vue'
import OperationPasswordReset from '@/components/OperationPasswordReset.vue'
import LoginPasswordReset from '@/components/LoginPasswordReset.vue'
import {Session} from "@/utils/storage.js";

const router = useRouter()
// 操作密码弹窗控制
const operationPasswordResetVisible = ref(false)

// 打开重置操作密码弹窗
const openOperationPasswordReset = () => {
    operationPasswordResetVisible.value = true
}

// 操作密码设置成功回调
const handleOperationPasswordSuccess = () => {
    // 可以添加成功后的处理逻辑
}

// 登录密码修改弹窗控制
const loginPasswordResetVisible = ref(false)

// 打开修改登录密码弹窗
const handleToChangePassword = () => {
    loginPasswordResetVisible.value = true
}
const toLogout = () => {
    router.push("/logout")
}

// 登录密码修改成功回调
const handleLoginPasswordSuccess = () => {
    // 可以添加成功后的处理逻辑
    Session.remove('adminMenuId');
    Session.remove('currentMenu');
    Session.remove('homeMenuId');
    Session.remove('menus1');
    Session.remove('menus2');
    Session.remove('token');
    Session.remove('userType');
    router.push("/")
}
</script>

<template>
    <div class="container">
        <div class="left">
            <div class="leftBox">
                <span class="title">基础设置</span>
            </div>
        </div>
        <div class="rightBox">
            <ul>
                <li @click="handleToChangePassword">
                    <span class="icon">
                      <component :is="Lock"/>
                    </span>
                    <span class="text">修改登录密码</span>
                </li>
                <li @click="openOperationPasswordReset">
                    <span class="icon">
                      <component :is="Refresh"/>
                    </span>
                    <span class="text">重置操作密码</span>
                </li>

                <li @click="toLogout">
                    <span class="icon">
                      <component :is="CloseBold"/>
                    </span>
                    <span class="text">注销账号</span>
                </li>
            </ul>
        </div>

        <!-- 重置操作密码弹窗 -->
        <OperationPasswordReset
            v-model="operationPasswordResetVisible"
            @success="handleOperationPasswordSuccess"
        />

        <!-- 修改登录密码弹窗 -->
        <LoginPasswordReset
            v-model="loginPasswordResetVisible"
            @success="handleLoginPasswordSuccess"
        />
    </div>
</template>

<style lang="scss" scoped>
.container {
    width: 100%;
    min-height: 100vh;
    box-sizing: border-box;
    overflow-x: hidden;
    background-color: #F3F6F9;
    flex-direction: column;
    padding: 30px;
}

.left {
    width: 100%;

    .leftBox {
        display: flex;
        align-items: center;
        font-size: 17px;
        font-weight: 600;
        margin-bottom: 10px;

        .title {
            flex: 1;
        }
    }
}

.rightBox {
    width: 100%;
    height: 200px;
    background-color: #fff;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 10px;
    box-sizing: border-box;

    ul {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-wrap: wrap;

        li {
            width: calc(33.33% - 20px);
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px;
            border-radius: 4px;
            cursor: pointer;
            padding: 0 10px;
            transition: all 0.3s;

            &:hover {
                background-color: #f5f5f5;
            }

            .icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 10px;
                color: #fff;
                font-size: 20px;
            }

            &:nth-child(1) {
                .icon {
                    background-color: #409EFF;
                }
            }

            &:nth-child(2) {
                .icon {
                    background-color: #67C23A;
                }
            }

            &:nth-child(3) {
                .icon {
                    background-color: #E6A23C;
                }
            }

            &:nth-child(4) {
                .icon {
                    background-color: #F56C6C;
                }
            }

            &:nth-child(5) {
                .icon {
                    background-color: #909399;
                }
            }

            &:nth-child(6) {
                .icon {
                    background-color: #409EFF;
                }
            }

            &:nth-child(7) {
                .icon {
                    background-color: #67C23A;
                }
            }

            &:nth-child(8) {
                .icon {
                    background-color: #E6A23C;
                }
            }

            &:nth-child(9) {
                .icon {
                    background-color: #F56C6C;
                }
            }

            .text {
                flex: 1;
                font-size: 16px;
                font-weight: 500;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }
}
</style>

