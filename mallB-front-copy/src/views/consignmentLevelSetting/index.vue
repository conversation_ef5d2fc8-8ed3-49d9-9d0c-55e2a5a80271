<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="right-content">
            <span class="fixed-title">代销权限一数量</span>
            <div class="main-content">
                <!-- 代销权限一配置 -->
                <div class="permission-section">
                    <h3 class="section-title">代销权限一数量</h3>
                    <div class="permission-config">
                        <div class="config-row">
                            <label class="config-label">原价：</label>
                            <el-input
                                v-model="state.level1Form.originalPrice"
                                class="config-input"
                                placeholder="请输入原价"
                                step="0.01"
                                type="number"
                            />
                            <span class="unit">元/月</span>
                        </div>
                        <div class="config-row">
                            <label class="config-label">优惠价：</label>
                            <el-input
                                v-model="state.level1Form.discountPrice"
                                class="config-input"
                                placeholder="请输入优惠价"
                                step="0.01"
                                type="number"
                            />
                            <span class="unit">元/月</span>
                        </div>
                        <div class="config-row">
                            <label class="config-label">可代销商品数量：</label>
                            <el-input
                                v-model="state.level1Form.maxProducts"
                                class="config-input"
                                placeholder="请输入可代销商品数量"
                                type="number"
                            />
                            <span class="unit">个</span>
                        </div>
                    </div>
                </div>

                <!-- 保存按钮 -->
                <div class="save-section">
                    <el-button :loading="state.saving1" type="primary" @click="saveLevel1Config">
                        保存权限一数量
                    </el-button>
                </div>
            </div>
        </div>
        <div class="right-content">
            <span class="fixed-title">代销权限二数量</span>
            <div class="main-content">

                <!-- 代销权限二配置 -->
                <div class="permission-section">
                    <h3 class="section-title">代销权限二数量</h3>

                    <!-- 开关配置 -->
                    <div class="config-row switch-row">
                        <label class="config-label">启用代销权限二：</label>
                        <el-switch
                            v-model="state.level2Form.enabled"
                            active-color="#13ce66"
                            active-text="启用"
                            inactive-color="#ff4949"
                            inactive-text="禁用"
                        />
                    </div>

                    <div :class="{ 'disabled': !state.level2Form.enabled }" class="permission-config">
                        <div class="config-row">
                            <label class="config-label">原价：</label>
                            <el-input
                                v-model="state.level2Form.originalPrice"
                                :disabled="!state.level2Form.enabled"
                                class="config-input"
                                placeholder="请输入原价"
                                step="0.01"
                                type="number"
                            />
                            <span class="unit">元/月</span>
                        </div>
                        <div class="config-row">
                            <label class="config-label">优惠价：</label>
                            <el-input
                                v-model="state.level2Form.discountPrice"
                                :disabled="!state.level2Form.enabled"
                                class="config-input"
                                placeholder="请输入优惠价"
                                step="0.01"
                                type="number"
                            />
                            <span class="unit">元/月</span>
                        </div>
                        <div class="config-row">
                            <label class="config-label">可代销商品数量：</label>
                            <el-input
                                v-model="state.level2Form.maxProducts"
                                :disabled="!state.level2Form.enabled"
                                class="config-input"
                                placeholder="请输入可代销商品数量"
                                type="number"
                            />
                            <span class="unit">个</span>
                        </div>
                    </div>
                </div>

                <!-- 保存按钮 -->
                <div class="save-section">
                    <el-button :loading="state.saving2" type="primary" @click="saveLevel2Config">
                        保存权限二数量
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import {onMounted, reactive} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage} from 'element-plus'
import {Session} from '../../utils/storage'
import {permissionSetting} from '../../stores/permissionSetting'

const router = useRouter()

const state = reactive({
    level1Form: {
        originalPrice: '0',
        discountPrice: '0',
        maxProducts: 0,
    },
    level2Form: {
        originalPrice: '0',
        discountPrice: '0',
        maxProducts: 0,
        enabled: true, // 添加开关状态
    },
    buttonList: [],
    saving1: false,
    saving2: false
})

const handleButtonClick = (item) => {
    router.push(item.component)
}

// 保存代销权限一配置
const saveLevel1Config = async () => {
    state.saving1 = true
    try {
        let data = {"configValue": state.level1Form}
        let result = await permissionSetting().GetThroughList(data, 11) // 使用索引11
        if (result.code == 200) {
            ElMessage.success('代销权限一配置成功')
        } else {
            ElMessage.error('代销权限一配置失败，请重新输入')
        }
    } catch (error) {
        ElMessage.error('代销权限一配置失败，请稍后重试')
    } finally {
        state.saving1 = false
    }
}

// 保存代销权限二配置
const saveLevel2Config = async () => {
    state.saving2 = true
    try {
        let data = {"configValue": state.level2Form}
        let result = await permissionSetting().GetThroughList(data, 12) // 使用索引12
        if (result.code == 200) {
            ElMessage.success('代销权限二配置成功')
        } else {
            ElMessage.error('代销权限二配置失败，请重新输入')
        }
    } catch (error) {
        ElMessage.error('代销权限二配置失败，请稍后重试')
    } finally {
        state.saving2 = false
    }
}

// 获取代销权限一配置数据
const getLevel1ConfigurationData = async () => {
    try {
        let result = await permissionSetting().ConfigurationData(11) // 使用索引11
        if (result.data != null) {
            state.level1Form = {...state.level1Form, ...result.data}
        }
    } catch (error) {
        console.error('获取代销权限一配置数据失败:', error)
    }
}

// 获取代销权限二配置数据
const getLevel2ConfigurationData = async () => {
    try {
        let result = await permissionSetting().ConfigurationData(12) // 使用索引12
        if (result.data != null) {
            // 确保开关状态被正确设置，默认为true
            const configData = {
                ...result.data,
                enabled: result.data.enabled !== undefined ? result.data.enabled : true
            }
            state.level2Form = {...state.level2Form, ...configData}
        }
    } catch (error) {
        console.error('获取代销权限二配置数据失败:', error)
    }
}

onMounted(() => {
    // 同时获取两个配置的数据
    getLevel1ConfigurationData()
    getLevel2ConfigurationData()

    let menuList = Session.getMenu()
    let menuId = Session.get('adminMenuId')
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index]
        if (element.menuId == menuId) {
            state.buttonList = element.children
        }
    }
})
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    width: 100%;
    max-height: 100vh;
    box-sizing: border-box;
    position: relative;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    position: relative;
    z-index: 1;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.right-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.fixed-title {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 30px;
    display: block;
}

.main-content {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toggle-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.toggle-label {
    font-size: 16px;
    font-weight: 500;
}

.permission-section {
    margin-bottom: 30px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    overflow: hidden;
}

.section-title {
    background-color: #67c23a;
    color: white;
    margin: 0;
    padding: 15px 20px;
    font-size: 18px;
    font-weight: 500;
}

/* 代销权限二的标题样式 */
.right-content:last-child .section-title {
    background-color: #e6a23c;
}

.permission-config {
    padding: 20px;
}

.config-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    &:last-child {
        margin-bottom: 0;
    }
}

.switch-row {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 25px;
}

.switch-desc {
    margin-left: 15px;
    color: #606266;
    font-size: 14px;
}

.permission-config.disabled {
    opacity: 0.6;
    pointer-events: none;
}

.config-label {
    width: 150px;
    font-weight: 500;
    color: #333;
    text-align: right;
    margin-right: 15px;
}

.config-input {
    width: 200px;
    margin-right: 10px;
}

.config-input-wide {
    width: 400px;
}

.unit {
    color: #666;
    font-size: 14px;
}

.save-section {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

:deep(.el-radio) {
    margin-right: 30px;
}

:deep(.el-input__inner) {
    border-radius: 4px;
}

:deep(.el-button--primary) {
    background-color: #67c23a;
    border-color: #67c23a;
    padding: 12px 30px;
    font-size: 16px;
}
</style>
