<script setup>
import {useRouter} from 'vue-router'
import {onMounted, ref} from 'vue'
import axios from 'axios'
import {ElMessage} from 'element-plus'

const router = useRouter()
const handleButtonClick = (item) => {
    if (item === '全部客户') router.push('/allCustomer')
    if (item === '资格申请') router.push('/eligibilityApply')
    if (item === '广告') router.push('/ad')
    if (item === '技术引流') router.push('/allCustomertechnicalDrainage')
}

const buttonList = [
    '全部客户', '资格申请', '广告', '技术引流'
]

// 查询表单数据
const searchForm = ref({
    account: '',
    legalPerson: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    street: '',
    startTime: '',
    endTime: ''
})

// 表格数据
const tableData = ref([
    {
        account: 'user001',
        name: '张三',
        province: '广东省',
        city: '广州市',
        district: '黄埔区',
        street: '车陂街道',
        status: '有效',
        phone: '138****1234'
    }
])

// 地区数据
const regionData = ref({
    provinces: [],
    cities: [],
    districts: [],
    streets: []
})

// 加载地区数据
const loadRegions = async (type, parentCode = '') => {
    try {
        const response = await axios.get('/api/regions', {
            params: {type, parent_code: parentCode}
        })
        return response.data.data || []
    } catch (error) {
        ElMessage.error('加载地区数据失败')
        return []
    }
}

// 初始化加载省份数据
onMounted(async () => {
    regionData.value.provinces = await loadRegions('province')
})

// 当省份改变时加载城市
const handleProvinceChange = async (value) => {
    searchForm.value.city = ''
    searchForm.value.district = ''
    searchForm.value.street = ''
    regionData.value.cities = await loadRegions('city', value)
    regionData.value.districts = []
    regionData.value.streets = []
}

// 当城市改变时加载区县
const handleCityChange = async (value) => {
    searchForm.value.district = ''
    searchForm.value.street = ''
    regionData.value.districts = await loadRegions('district', value)
    regionData.value.streets = []
}

// 当区县改变时加载街道
const handleDistrictChange = async (value) => {
    searchForm.value.street = ''
    regionData.value.streets = await loadRegions('street', value)
}

// 查询方法
const handleSearch = async () => {
    try {
        const response = await axios.get('/api/customers', {
            params: {
                ...searchForm.value,
                start_time: searchForm.value.startTime,
                end_time: searchForm.value.endTime
            }
        })
        tableData.value = response.data.data
        ElMessage.success('查询成功')
    } catch (error) {
        ElMessage.error('查询失败')
        console.error('查询失败:', error)
    }
}
</script>

<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item }}
            </el-button>
        </div>

        <div class="right-content">
            <div class="header">
                <span class="title">申请查询</span>
            </div>

            <div class="search-form">
                <div class="form-row">
                    <el-input
                        v-model="searchForm.account"
                        class="form-input"
                        placeholder="账号"
                    />
                    <el-input
                        v-model="searchForm.legalPerson"
                        class="form-input"
                        placeholder="法人"
                    />
                    <el-input
                        v-model="searchForm.phone"
                        class="form-input"
                        placeholder="手机号"
                    />
                </div>

                <div class="form-row">
                    <!-- 省份选择 -->
                    <el-select
                        v-model="searchForm.province"
                        class="form-input"
                        placeholder="省份"
                        @change="handleProvinceChange"
                    >
                        <el-option
                            v-for="item in regionData.provinces"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>

                    <!-- 城市选择 -->
                    <el-select
                        v-model="searchForm.city"
                        :disabled="!searchForm.province"
                        class="form-input"
                        placeholder="城市"
                        @change="handleCityChange"
                    >
                        <el-option
                            v-for="item in regionData.cities"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>

                    <!-- 区县选择 -->
                    <el-select
                        v-model="searchForm.district"
                        :disabled="!searchForm.city"
                        class="form-input"
                        placeholder="区县"
                        @change="handleDistrictChange"
                    >
                        <el-option
                            v-for="item in regionData.districts"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>

                    <!-- 街道选择 -->
                    <el-select
                        v-model="searchForm.street"
                        :disabled="!searchForm.district"
                        class="form-input"
                        placeholder="街道"
                    >
                        <el-option
                            v-for="item in regionData.streets"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                        />
                    </el-select>
                </div>

                <div class="form-row">
                    <el-date-picker
                        v-model="searchForm.startTime"
                        class="date-picker"
                        placeholder="注册时间"
                        type="date"
                    />
                    <span class="to-text">至</span>
                    <el-date-picker
                        v-model="searchForm.endTime"
                        class="date-picker"
                        placeholder="结束时间"
                        type="date"
                    />

                    <el-button
                        class="search-btn"
                        type="primary"
                        @click="handleSearch"
                    >
                        查询
                    </el-button>
                </div>
            </div>

            <el-table :data="tableData" class="data-table">
                <el-table-column label="账号" prop="account" width="180"/>
                <el-table-column label="姓名" prop="name" width="120"/>
                <el-table-column label="地区" width="300">
                    <template #default="{ row }">
                        {{ row.province }} {{ row.city }} {{ row.district }} {{ row.street }}
                    </template>
                </el-table-column>
                <el-table-column label="状态" width="120">
                    <template #default="{ row }">
                        <el-tag :type="row.status === '有效' ? 'success' : 'danger'">
                            {{ row.status }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="手机号" prop="phone" width="150"/>
                <el-table-column label="操作" width="120">
                    <el-button size="small" type="text">详情</el-button>
                </el-table-column>
            </el-table>
        </div>
    </div>
    <!--  </ManageBg>-->
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.right-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.header {
    height: 76px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #3A58CF;
    margin-bottom: 20px;
    border-radius: 8px;

    .title {
        font-size: 30px;
        color: white;
        font-weight: bold;
    }
}

.search-form {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;

    &:last-child {
        margin-bottom: 0;
    }
}

.form-input {
    width: 200px;
    margin-right: 15px;
    margin-bottom: 10px;
}

.date-picker {
    width: 180px;
    margin-right: 15px;
}

.to-text {
    margin: 0 10px;
    color: #606266;
}

.search-btn {
    margin-left: 15px;
}

.data-table {
    flex: 1;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-table__cell) {
        padding: 12px 0;
    }
}
</style>
