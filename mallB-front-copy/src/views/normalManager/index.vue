<script setup>
import {ref} from 'vue'
import {useRouter} from 'vue-router'

const router = useRouter();
const handleToconstructUser = () => {
    router.push({
        path: '/constructUser'
    })
}
const handleToconstructCustomer = () => {
    router.push({
        path: '/constructCustomer'
    })
}
// 表格数据
const tableData = ref([
    {
        ID: '1001',
        phoneNumber: '19989887451',
        StoreName: '喜刷刷',
        LegalPerson: '车厘子',
        regTime: '2010-2-20',
        State: '在线',
        Operation: '',
    },
    {
        ID: '1002',
        phoneNumber: '11239887451',
        StoreName: '笑嘻嘻',
        LegalPerson: '小李子',
        regTime: '2015-1-50',
        State: '下线',
        Operation: '',
    },
])

// 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

// 搜索
const searchQuery = ref('')

// 修改密码对话框
const dialogVisible = ref(false)
const passwordForm = ref({
    original: '',
    new: '',
    confirm: ''
})

// 打开修改密码对话框
const openPasswordDialog = () => {
    dialogVisible.value = true
}

// 提交修改密码
const submitPassword = () => {
    console.log('修改密码:', passwordForm.value)
    dialogVisible.value = false
    // 这里可以添加实际的修改密码逻辑
}

// 搜索功能
const handleSearch = () => {
    console.log('搜索内容:', searchQuery.value)
    // 这里可以添加实际的搜索逻辑
}
</script>

<template>
    <!--  <HomeBg>-->
    <div class="page-container">
        <!-- 顶部按钮 -->
        <div class="header-buttons">
            <el-button class="create-btn" @click="handleToconstructCustomer">创建客户+</el-button>
            <el-button class="create-btn" @click="handleToconstructUser">创建用户+</el-button>
        </div>

        <!-- 搜索区域 -->
        <div class="search-area">
            <el-input
                v-model="searchQuery"
                class="search-input"
                clearable
                placeholder="店铺名字/法人/手机号/ID号/"
                @keyup.enter="handleSearch"
            />
            <el-button class="search-btn" type="primary" @click="handleSearch">搜索</el-button>
        </div>

        <!-- 表格区域 -->
        <div class="table-wrapper">
            <el-table :data="tableData" style="width: 100%">
                <el-table-column label="账号" prop="ID" width="150"/>
                <el-table-column label="手机号" prop="phoneNumber" width="150"/>
                <el-table-column label="店铺名称" prop="StoreName" width="180"/>
                <el-table-column label="法人" prop="LegalPerson" width="150"/>
                <el-table-column label="注册时间" prop="regTime" width="180"/>
                <el-table-column label="状态" prop="State" width="120"/>
                <el-table-column label="操作" width="150">
                    <template #default>
                        <el-button link type="primary" @click="openPasswordDialog">修改密码</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 固定在右下角的分页 -->
        <div class="fixed-pagination">
            <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
            />
        </div>

        <!-- 修改密码对话框 -->
        <el-dialog v-model="dialogVisible" title="修改密码" width="30%">
            <el-form :model="passwordForm" label-width="100px">
                <el-form-item label="原始密码">
                    <el-input v-model="passwordForm.original" show-password type="password"/>
                </el-form-item>
                <el-form-item label="新密码">
                    <el-input v-model="passwordForm.new" show-password type="password"/>
                </el-form-item>
                <el-form-item label="确认新密码">
                    <el-input v-model="passwordForm.confirm" show-password type="password"/>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitPassword">确认</el-button>
            </template>
        </el-dialog>
    </div>
    <!--  </HomeBg>-->
</template>

<style lang="scss" scoped>
.page-container {
    width: 100%;
    max-height: 100vh;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: relative;

    .header-buttons {
        display: flex;
        justify-content: flex-start;
        gap: 20px;
        margin-bottom: 20px;

        .create-btn {
            width: 200px;
            height: 50px;
            background-color: #3A58CF;
            color: white;
            font-size: 18px;
            font-weight: bold;
        }
    }

    .search-area {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;

        .search-input {
            flex: 1;

            :deep(.el-input__wrapper) {
                height: 50px;
                font-size: 16px;
            }
        }

        .search-btn {
            width: 100px;
            height: 50px;
            font-size: 16px;
        }
    }

    .table-wrapper {
        flex: 1;
        overflow: auto;
        margin-bottom: 60px; /* 为分页留出空间 */

        :deep(.el-table) {
            font-size: 14px;
        }

        :deep(.el-table__cell) {
            padding: 12px 0;
        }
    }

    .fixed-pagination {
        position: fixed;
        right: 20px;
        bottom: 20px;
        background: white;
        padding: 10px;
        border-radius: 4px;;
        z-index: 10;
    }
}
</style>
