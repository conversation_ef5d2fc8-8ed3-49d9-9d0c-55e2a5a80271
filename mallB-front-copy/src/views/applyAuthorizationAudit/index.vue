<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="main">
            申请授权审核
        </div>
    </div>
</template>
<script setup>
import {useRouter} from 'vue-router'
import {Session} from '../../utils/storage'
import {ref} from 'vue'

const value = ref('')
const state = reactive({
    buttonList: []
})
const router = useRouter()
const handleButtonClick = (item) => {
    router.push(item.component)
}
onMounted(() => {
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
                console.log(state.menuList)
            }
        }
    })
})

</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.middle-container {
    border: 1px solid red;
    flex: 1;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    height: 2000px;
}

.header {
    display: flex;
    align-items: center;
    flex: 1;
    height: 61px;
    padding: 0;
    background-color: #83A4EB;
    margin-left: 235px;

    .putaway {
        width: 172px;
        height: 31px;
        margin-left: 167px;
        font-size: 24px;
        color: #000;
    }

    .soldOut {
        width: 182px;
        height: 33px;
        margin-left: 277px;
        margin-right: 277px;
        font-size: 24px;
        color: #000;
    }

    .allProduct {
        width: 224px;
        height: 42px;
        margin-right: 122px;
        font-size: 24px;
        color: #000;
    }
}

.main {
    position: absolute;
    left: 235px;
    right: 0;
}

.filter-row {
    display: flex;
    align-items: center;
    margin-left: 93px;
    gap: 20px;
}

.product-selector {
    width: 229px;
    height: 52px;
}

.commBox {
    background-color: #CCCCCC;
    width: 281px;
    height: 42px;
    display: flex;
    align-items: center;
    font-size: 24px;
    margin-top: -10px;

    .Text {
        margin-left: 13px;

        &:first-child {
            min-width: 120px;
        }
    }

    input {
        flex: 1;
        border: none;
        background: transparent;
        outline: none;
        font-size: 24px;
    }
}

.filter-row1 {
    display: flex;
    align-items: center;
    margin-left: 40px;
    gap: 20px;
}

.product-selector1 {
    width: 196px;
    height: 52px;
}

.brandBox {
    background-color: #3A58CF;
    width: 145px;
    height: 42px;
    margin-top: -10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;

    .brand {
        font-size: 24px;
        color: #fff;
    }
}

.priceBox {
    margin-left: 113px;
    margin-top: 25px;
    display: flex;
    align-items: center; /* 添加这行确保垂直居中 */

    .pirceRange {
        font-size: 30px;
        line-height: 1; /* 确保文本行高一致 */
    }

    .leftPrice {
        width: 277px;
        height: 32px;
        margin-left: 10px;
        background-color: #ccc;
        vertical-align: middle; /* 添加垂直对齐 */
    }

    .to {
        margin-left: 10px;
        line-height: 1; /* 确保文本行高一致 */
    }

    .rightPrice {
        width: 277px;
        height: 32px;
        margin-left: 10px;
        background-color: #ccc;
        vertical-align: middle; /* 添加垂直对齐 */
    }

    .nullBtn {
        width: 277px;
        height: 32px;
        margin-left: 105px;
        background-color: #fff;
        border: 3px solid #3A58CF;
        vertical-align: middle; /* 添加垂直对齐 */
        line-height: 1; /* 重置按钮行高 */
        padding: 0; /* 移除默认内边距 */
    }
}

.product-type-row {
    display: flex;
    align-items: center;
    margin-left: 113px;
    margin-top: 25px;
    gap: 20px;

    .product-type-label {
        font-size: 30px;
        min-width: 120px;
    }

    .checkbox-container {
        display: flex;
        align-items: center;
        gap: 30px;
        margin-left: 129px;

        :deep(.el-checkbox.square-checkbox) {
            .el-checkbox__inner {
                border-radius: 4px; /* 方形复选框 */
                width: 20px;
                height: 20px;

                &::after {
                    top: 2px;
                    left: 6px;
                }
            }

            .el-checkbox__label {
                font-size: 24px;
                color: #333;
            }
        }
    }

    .export-btn {
        margin-left: 430px;
        width: 120px;
        height: 42px;
        font-size: 20px;
        background-color: #FF8D1A;
        border: none;
        color: #000;

    }
}

.allButton {
    margin-top: 32px;
    margin-left: 303px;

    .button {
        width: 247px;
        height: 59px;
        margin-right: 41px;
        border-radius: 30px;
        border: 2px solid #3A58CF;
        font-size: 30px;

        color: #3A58CF;
    }
}

.table-container {
    margin-top: 20px;
    padding: 0 20px;

    :deep(.custom-header) {
        th {
            background-color: #83A4EB !important;
            color: #000;
            font-weight: bold;
            font-size: 16px;

            .el-checkbox {
                .el-checkbox__inner {
                    border-radius: 4px;
                    width: 16px;
                    height: 16px;
                }

                .el-checkbox__label {
                    font-size: 16px;
                    color: #000;
                }
            }
        }
    }

    :deep(.custom-row) {
        td {
            background-color: #D2E0FB;

            .el-checkbox {
                .el-checkbox__inner {
                    border-radius: 4px;
                    width: 16px;
                    height: 16px;
                }

                .el-checkbox__label {
                    font-size: 14px;
                }
            }
        }

        &:hover td {
            background-color: #b8cdf9 !important;
        }
    }

    :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;

        .el-table__cell {
            padding: 12px 0;
        }
    }
}
</style>
