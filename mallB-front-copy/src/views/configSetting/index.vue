<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="right-content">
            <span class="fixed-title">设置管理</span>
            <div class="main-content">

                <div class="toggle-section">
                    <el-radio-group v-model="state.form2.permissionOpen">
                        <el-radio label="open" size="large">
                            <span class="toggle-label">开启</span>
                        </el-radio>
                        <el-radio label="close" size="large">
                            <span class="toggle-label">关闭</span>
                        </el-radio>
                    </el-radio-group>
                </div>

                <div class="price-row">
                    <span class="price-label">开启推荐权限：</span>
                    <el-input v-model="state.form2.recommendRenewal" class="price-input"/>
                    <span class="price-unit">￥/周</span>
                    <span class="price-label">优惠：</span>
                    <el-input v-model="state.form2.recommendOffer" class="price-input"/>
                    <span class="price-unit">折扣</span>

                    <div style="display: flex">
                        <el-radio-group v-model="state.form2.recommendPermissionOpen">
                            <el-radio label="open" size="large">
                                <span class="toggle-label">开启</span>
                            </el-radio>
                            <el-radio label="close" size="large">
                                <span class="toggle-label">关闭</span>
                            </el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="price-row">
                    <span class="price-label">开启新品权限：</span>
                    <el-input v-model="state.form2.newRenewal" class="price-input"/>
                    <span class="price-unit">￥/周</span>
                    <span class="price-label">优惠：</span>
                    <el-input v-model="state.form2.newOffer" class="price-input"/>
                    <span class="price-unit">折扣</span>
                    <div style="display: flex">
                        <el-radio-group v-model="state.form2.newPermissionOpen">
                            <el-radio label="open" size="large">
                                <span class="toggle-label">开启</span>
                            </el-radio>
                            <el-radio label="close" size="large">
                                <span class="toggle-label">关闭</span>
                            </el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div class="price-row">
                    <span class="price-label">开启热销权限：</span>
                    <el-input v-model="state.form2.hotRenewal" class="price-input"/>
                    <span class="price-unit">￥/周</span>
                    <span class="price-label">优惠：</span>
                    <el-input v-model="state.form2.hotOffer" class="price-input"/>
                    <span class="price-unit">折扣</span>
                    <div style="display: flex">
                        <el-radio-group v-model="state.form2.hotPermissionOpen">
                            <el-radio label="open" size="large">
                                <span class="toggle-label">开启</span>
                            </el-radio>
                            <el-radio label="close" size="large">
                                <span class="toggle-label">关闭</span>
                            </el-radio>
                        </el-radio-group>
                    </div>
                </div>
                <div style="text-align: center">
                    <el-button style="margin-top: 10px;" type="success" @click="saveTwo">保存</el-button>
                </div>

                <el-divider/>

                <div class="toggle-section">
                    <el-radio-group v-model="state.form3.permissionOpen">
                        <el-radio label="open" size="large">
                            <span class="toggle-label">开启</span>
                        </el-radio>
                        <el-radio label="close" size="large">
                            <span class="toggle-label">关闭</span>
                        </el-radio>
                    </el-radio-group>
                </div>
                <div class="price-row">
                    <span class="price-label">开启量化权限：</span>
                    <el-input v-model="state.form3.renewal" class="price-input"/>
                    <span class="price-unit">￥/月</span>
                    <span class="price-label">优惠：</span>
                    <el-input v-model="state.form3.offer" class="price-input"/>
                    <span class="price-unit">折扣</span>
                </div>
                <div style="text-align: center">
                    <el-button style="margin-top: 10px;" type="success" @click="saveThree">保存</el-button>
                </div>

                <el-divider/>


                <div class="toggle-section">
                    <el-radio-group v-model="state.form4.permissionOpen">
                        <el-radio label="open" size="large">
                            <span class="toggle-label">开启</span>
                        </el-radio>
                        <el-radio label="close" size="large">
                            <span class="toggle-label">关闭</span>
                        </el-radio>
                    </el-radio-group>
                </div>
                <div class="price-row">
                    <span class="price-label">开启区域权限：</span>
                    <el-input v-model="state.form4.renewal" class="price-input"/>
                    <span class="price-unit">￥/年</span>
                    <span class="price-label">优惠：</span>
                    <el-input v-model="state.form4.offer" class="price-input"/>
                    <span class="price-unit">折扣</span>
                </div>
                <div style="text-align: center">
                    <el-button style="margin-top: 10px;" type="success" @click="saveFour">保存</el-button>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import {onMounted, reactive} from 'vue'
import {useRouter} from 'vue-router'
import {permissionSetting} from '../../stores/permissionSetting'
import {ElMessage} from 'element-plus'
import {Session} from '../../utils/storage'

const router = useRouter()
const state = reactive({
    form2: {},
    form3: {},
    form4: {},
    buttonList: []
})
const handleButtonClick = (item) => {
    router.push(item.component)
}


const saveTwo = async () => {
    let data = {"configValue": state.form2}
    let result = await permissionSetting().GetThroughList(data, 13);
    if (result.code == 200) {
        ElMessage.success('配置成功')
    }
}
const getConfigurationData2 = async () => {
    let result = await permissionSetting().ConfigurationData(13);
    if (result.data != null) {
        state.form2 = result.data
    }
}

const saveThree = async () => {
    let data = {"configValue": state.form3}
    let result = await permissionSetting().GetThroughList(data, 14);
    if (result.code == 200) {
        ElMessage.success('配置成功')
    }
}
const getConfigurationData3 = async () => {
    let result = await permissionSetting().ConfigurationData(14);
    if (result.data != null) {
        state.form3 = result.data
    }
}


const saveFour = async () => {
    let data = {"configValue": state.form4}
    let result = await permissionSetting().GetThroughList(data, 15);
    if (result.code == 200) {
        ElMessage.success('配置成功')
    }
}
const getConfigurationData4 = async () => {
    let result = await permissionSetting().ConfigurationData(15);
    if (result.data != null) {
        state.form4 = result.data
    }
}

onMounted(() => {
    getConfigurationData2()
    getConfigurationData3()
    getConfigurationData4()
    let menuList = Session.getMenu()
    let menuId = Session.get('adminMenuId');
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index];
        if (element.menuId == menuId) {
            state.buttonList = element.children
        }
    }
})
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    width: 100%;
    max-height: 100vh;
    box-sizing: border-box;
    position: relative;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    position: relative;
    z-index: 1;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}

.right-content {
    flex: 1;

    position: relative;
}

.fixed-title {
    position: absolute;
    left: 20px;
    top: 20px;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    z-index: 2;
}

.main-content {
    margin-left: 120px;
    width: calc(100% - 40px);
    height: 600px;
    margin-top: 60px;
    max-width: 1000px;
    background: #fff;
    border-radius: 8px;
    padding: 30px;

}

.toggle-section {
    :deep(.el-radio) {
        margin-right: 30px;

        .el-radio__label {
            font-size: 16px;
        }
    }
}

.permission-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40px;
}

.permission-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.permission-btn {
    width: 120px;
    height: 50px;
    margin-bottom: 10px;
    background-color: #3A58CF;
    border: none;

    &:hover {
        background-color: #2a48bf;
    }
}

.product-desc {
    font-size: 14px;
    color: #666;
}

.arrow {
    font-size: 24px;
    color: #999;
    margin: 0 30px;
}

.price-form {
    width: 100%;
}

.price-row {
    display: flex;
    align-items: center;
    margin-top: 20px;
    margin-bottom: 20px;
}

.price-label {
    width: 120px;
    font-size: 16px;
    color: #333;
}

.price-input {
    width: 120px;
    margin: 0 10px;
}

.price-unit {
    width: 60px;
    font-size: 14px;
    color: #666;
}

.down-arrow {
    margin-left: 36px;
    margin-bottom: 12px;
}
</style>
