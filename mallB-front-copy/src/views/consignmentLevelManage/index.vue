<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>
        <div class="content-container">
            <div class="page-header">
                <h2 class="page-title">代销权限管理</h2>
                <div class="header-actions">
                    <el-button :loading="state.loading" type="primary" @click="refreshPermissionInfo">
                        <el-icon>
                            <Refresh/>
                        </el-icon>
                        刷新状态
                    </el-button>
                </div>
            </div>

            <div class="main-content">

                <!-- 当前权限状态 -->
                <div class="current-status-card">
                    <div class="status-header">
                        <h3>当前权限状态</h3>
                        <el-tag :type="getStatusTagType()" size="large">
                            {{ getCurrentLevelName() }}
                        </el-tag>
                    </div>
                    <div class="status-content">
                        <div class="status-item">
                            <span class="label">可代销商品数：</span>
                            <span class="value">{{ state.permissionInfo.maxProducts }}个</span>
                        </div>
                        <div class="status-item">
                            <span class="label">已使用：</span>
                            <span class="value">{{ state.permissionInfo.usedProducts }}个</span>
                        </div>
                        <div class="status-item">
                            <span class="label">剩余：</span>
                            <span class="value remaining">{{
                                    state.permissionInfo.maxProducts - state.permissionInfo.usedProducts
                                }}个</span>
                        </div>
                        <div v-if="state.permissionInfo.levelExpireTime" class="status-item">
                            <span class="label">权限到期时间：</span>
                            <span class="value">{{ state.permissionInfo.basicExpireTime }}</span>
                        </div>
                    </div>
                </div>

                <!-- 权限升级选项 -->
                <div class="upgrade-section">
                    <h3>权限升级</h3>
                    <div class="upgrade-tips">
                        <el-alert
                            :closable="false"
                            show-icon
                            title="升级路径说明"
                            type="info">
                            <template #default>
                                <p>代销权限升级路径：<strong>基础代销 → 代销权限一 → 代销权限二</strong></p>
                                <p>• 代销权限一：只有基础代销用户可以升级</p>
                                <p>• 代销权限二：基础代销或代销权限一用户都可以升级</p>
                            </template>
                        </el-alert>
                    </div>
                    <div class="permission-cards">
                        <!-- 代销权限一 -->
                        <div v-if="canUpgradeToLevel0()" class="permission-card">
                            <div class="card-header">
                                <h4>基础代销</h4>
                                <div class="price-info">
                                    <span class="original-price">￥{{
                                            state.permissionInfo.level0Config?.originalPrice || '199.00'
                                        }}</span>
                                    <span class="discount-price">￥{{
                                            state.permissionInfo.level0Config?.discountPrice || '99.00'
                                        }}</span>
                                    <span class="period">/月</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <ul class="features">
                                    <li>可代销 {{ state.permissionInfo.level0Config?.maxProducts || 10 }} 个商品</li>
                                </ul>
                            </div>
                            <div class="card-footer">
                                <el-button :loading="upgrading" type="primary" @click="upgradeToLevel(0)">
                                    立即升级
                                </el-button>
                            </div>
                        </div>

                        <!-- 代销权限一 -->
                        <div v-if="canUpgradeToLevel1()" class="permission-card">
                            <div class="card-header">
                                <h4>代销权限一</h4>
                                <div class="price-info">
                                    <span class="original-price">￥{{
                                            state.permissionInfo.level1Config.originalPrice || '199.00'
                                        }}</span>
                                    <span class="discount-price">￥{{
                                            state.permissionInfo.level1Config.discountPrice || '99.00'
                                        }}</span>
                                    <span class="period">/月</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <ul class="features">
                                    <li>可代销 {{ state.permissionInfo.level1Config.maxProducts || 10 }} 个商品</li>
                                </ul>
                            </div>
                            <div class="card-footer">
                                <el-button :loading="upgrading" type="primary" @click="upgradeToLevel(1)">
                                    立即升级
                                </el-button>
                            </div>
                        </div>

                        <!-- 代销权限二 -->
                        <div v-if="canUpgradeToLevel2()" class="permission-card premium">
                            <div class="card-header">
                                <h4>代销权限二</h4>
                                <div class="price-info">
                                    <span class="original-price">￥{{
                                            state.permissionInfo.level2Config.originalPrice || '599.00'
                                        }}</span>
                                    <span class="discount-price">￥{{
                                            state.permissionInfo.level2Config.discountPrice || '299.00'
                                        }}</span>
                                    <span class="period">/月</span>
                                </div>
                            </div>
                            <div class="card-content">
                                <ul class="features">
                                    <li>可代销 {{ state.permissionInfo.level2Config.maxProducts || 50 }} 个商品</li>
                                </ul>
                            </div>
                            <div class="card-footer">
                                <!-- 检查代销权限二是否启用 -->
                                <el-button
                                    v-if="isLevel2Enabled()"
                                    :loading="upgrading"
                                    type="primary"
                                    @click="upgradeToLevel(2)"
                                >
                                    立即升级
                                </el-button>
                                <div v-else class="disabled-notice">
                                    <el-alert
                                        :closable="false"
                                        description="管理员已暂时关闭代销权限二功能，请稍后再试"
                                        show-icon
                                        title="代销权限二暂时不可用"
                                        type="warning"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- 如果已是最高等级 -->
                    <div v-if="isMaxLevel()" class="max-level-notice">
                        <el-result icon="success" sub-title="您已拥有最高等级的代销权限" title="恭喜！">
                            <template #extra>
                                <el-button type="primary" @click="goToConsignmentManage">
                                    管理我的代销商品
                                </el-button>
                            </template>
                        </el-result>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用支付对话框组件 -->
        <PaymentDialog
            v-model:visible="paymentDialogVisible"
            :orderData="currentOrder"
            @cancel-success="handleCancelSuccess"
        />
    </div>
</template>

<script lang="ts" setup>
import {onMounted, reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage, ElMessageBox} from 'element-plus'
import {Refresh} from '@element-plus/icons-vue'
import {orderGeneration} from '../../stores/orderGeneration'
import {Session} from '../../utils/storage'
import {useAdvertisementStore} from "@/stores/advertisement";
import PaymentDialog from "@/components/PaymentDialog.vue";

const router = useRouter()
// 虚拟订单
const adStore = useAdvertisementStore()
const paymentDialogVisible = ref(false)
const currentOrderNo = ref('')
const currentOrder = ref({})

const state = reactive({
    permissionInfo: {
        consignmentLevel: '0',
        maxProducts: 3,
        usedProducts: 0,
        levelExpireTime: null,
        level1Config: {},
        level2Config: {}
    },
    upgrading: false,
    loading: false,
    buttonList: []
})

const handleButtonClick = (item) => {
    router.push(item.component)
}

// 刷新权限信息
const refreshPermissionInfo = async () => {
    await getPermissionInfo()
    ElMessage.success('权限状态已刷新')
}
// 获取当前等级名称
const getCurrentLevelName = () => {
    switch (state.permissionInfo.consignmentLevel) {
        case '0':
            return '基础代销'
        case '1':
            return '代销权限一'
        case '2':
            return '代销权限二'
        default:
            return '无代销权限'
    }
}

// 获取状态标签类型
const getStatusTagType = () => {
    switch (state.permissionInfo.consignmentLevel) {
        case '1':
            return 'warning'
        case '2':
            return 'success'
        default:
            return 'info'
    }
}

// 是否可以升级到权限一
const canUpgradeToLevel0 = () => {
    // 当前等级为基础代销(0)才能升级到权限一
    return state.permissionInfo.consignmentLevel === null
}
// 是否可以升级到权限一
const canUpgradeToLevel1 = () => {
    // 当前等级为基础代销(0)才能升级到权限一
    return state.permissionInfo.consignmentLevel === '0'
}

// 是否可以升级到权限二
const canUpgradeToLevel2 = () => {
    // 当前等级为基础代销(0)或权限一(1)才能升级到权限二
    return state.permissionInfo.consignmentLevel === '1'
}

// 是否已是最高等级
const isMaxLevel = () => {
    return state.permissionInfo.consignmentLevel === '2'
}

// 检查代销权限二是否启用
const isLevel2Enabled = () => {
    return state.permissionInfo.level2Config.enabled !== false
}

// 格式化日期
const formatDate = (dateStr) => {
    if (!dateStr) return ''
    return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 解析特色功能列表
const getFeaturesList = (features) => {
    if (!features) return []
    return features.split(',').map(f => f.trim()).filter(f => f)
}

// 升级权限
const upgradeToLevel = async (level) => {
    // 检查升级路径
    const currentLevel = state.permissionInfo.consignmentLevel
    if (level === 1 && currentLevel !== '0') {
        ElMessage.error('只有基础代销用户才能升级到代销权限一')
        return
    }
    if (level === 2 && currentLevel !== '0' && currentLevel !== '1') {
        ElMessage.error('只有基础代销或代销权限一用户才能升级到代销权限二')
        return
    }

    // 如果是升级到代销权限二，检查是否启用
    if (level === 2 && !isLevel2Enabled()) {
        ElMessage.error('代销权限二功能已禁用，暂时无法升级')
        return
    }

    try {
        const meg = level === 0 ? '基础权限' : level === 1 ? '权限一' : '权限二'
        let confirmMessage = `确定要升级${meg}吗？`
        if (level === 2 && currentLevel === '0') {
            confirmMessage += '\n\n注意：您可以直接从基础代销升级到权限二，也可以先升级到权限一。'
        }

        await ElMessageBox.confirm(
            confirmMessage,
            '确认升级',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }
        )

        state.upgrading = true
        const orderData = {
            payType: level == 0 ? 4 : level + 6, // 7或8
            count: 1
        }

        const result = await orderGeneration().CreateVirtualOrder(orderData)
        if (result.code === 200) {
            const orderNo = result.data
            if (orderNo) {
                // 获取订单详情
                const orderDetail = await adStore.getVirtualOrderDetail(orderNo);
                // 显示支付对话框
                paymentDialogVisible.value = true;
                currentOrderNo.value = orderNo;
                currentOrder.value = orderDetail;
            }
        }
    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('升级失败，请稍后重试')
        }
    } finally {
        state.upgrading = false
    }
}

const handleCancelSuccess = () => {
    getPermissionInfo()
}

// 跳转到代销管理页面
const goToConsignmentManage = () => {
    router.push('/commoditySales')
}

// 获取权限信息
const getPermissionInfo = async () => {
    try {
        const result = await orderGeneration().GetConsignmentPermissionInfo()
        if (result.code === 200) {
            state.permissionInfo = result.data
        }
    } catch (error) {
        console.error('获取权限信息失败:', error)
    }
}

onMounted(() => {
    let menuList = Session.getMenu2()
    let menuId = Session.get('homeMenuId');
    if (menuId == null) {
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.openType == 2) {
                if (menuId == null) {
                    menuId = element.menuId
                }
            }
        }
    }
    for (let index = 0; index < menuList.length; index++) {
        const element = menuList[index];
        if (element.menuId == menuId) {
            state.buttonList = element.children
        }
    }
    getPermissionInfo()
})
</script>

<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    width: 100%;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 30px);
    overflow-y: scroll;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }
    }
}


.content-container {
    flex: 1;
    margin-left: 235px;
    padding: 20px;
}

.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;

    .page-title {
        font-size: 22px;
        font-weight: 600;
        color: #303133;
        margin: 0;
    }

    .header-actions {
        display: flex;
        gap: 10px;
    }
}

.main-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.current-status-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
        margin: 0;
        font-size: 20px;
    }
}

.status-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.status-item {
    .label {
        font-size: 14px;
        opacity: 0.9;
    }

    .value {
        font-size: 18px;
        font-weight: bold;
        margin-left: 10px;

        &.remaining {
            color: #ffd700;
        }
    }
}

.upgrade-section {
    h3 {
        color: #333;
        margin-bottom: 20px;
        font-size: 20px;
    }
}

.upgrade-tips {
    margin-bottom: 20px;

    :deep(.el-alert) {
        .el-alert__content {
            p {
                margin: 5px 0;
                line-height: 1.6;

                &:first-child {
                    margin-top: 0;
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}


.permission-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.permission-card {
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    padding: 25px;
    background: white;
    transition: all 0.3s ease;

    &:hover {
        border-color: #409EFF;
        box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
        transform: translateY(-2px);
    }

    &.premium {
        border-color: #f56c6c;
        position: relative;

        &::before {
            content: '推荐';
            position: absolute;
            top: -1px;
            right: 20px;
            background: #f56c6c;
            color: white;
            padding: 5px 15px;
            border-radius: 0 0 8px 8px;
            font-size: 12px;
        }
    }
}

.card-header {
    text-align: center;
    margin-bottom: 20px;

    h4 {
        color: #333;
        margin-bottom: 10px;
        font-size: 18px;
    }
}

.price-info {
    .original-price {
        text-decoration: line-through;
        color: #999;
        font-size: 14px;
        margin-right: 10px;
    }

    .discount-price {
        color: #f56c6c;
        font-size: 24px;
        font-weight: bold;
    }

    .period {
        color: #666;
        font-size: 14px;
    }
}

.card-content {
    margin-bottom: 25px;
}

.features {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
        padding: 8px 0;
        color: #555;
        position: relative;
        padding-left: 20px;

        &::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #67c23a;
            font-weight: bold;
        }
    }
}

.card-footer {
    text-align: center;
}

.disabled-notice {
    padding: 10px 0;

    :deep(.el-alert) {
        border-radius: 6px;
        text-align: left;
    }
}

.max-level-notice {
    text-align: center;
    padding: 40px;
    background: #f8f9fa;
    border-radius: 12px;
}

:deep(.el-button--primary) {
    background-color: #409EFF;
    border-color: #409EFF;
    padding: 12px 30px;
    font-size: 16px;
    border-radius: 8px;
}

:deep(.el-tag--large) {
    padding: 8px 16px;
    font-size: 14px;
}
</style>
