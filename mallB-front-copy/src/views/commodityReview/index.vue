<template>
    <div class="commodity-review-container">
        <!-- 左侧导航菜单 -->
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 搜索筛选区域 -->
            <el-card class="search-card">
                <el-form
                    :model="searchForm"
                    class="search-form"
                    :inline="true"
                    size="large"
                >
                    <el-form-item label="商品名称" prop="name">
                        <el-input v-model="searchForm.name"></el-input>
                    </el-form-item>
                    <el-form-item label="店铺名称" prop="merchantName">
                        <el-input v-model="searchForm.merchantName"></el-input>
                    </el-form-item>
                    <el-form-item label="最低价格" prop="minPrice">
                        <el-input-number v-model="searchForm.minPrice"></el-input-number>
                    </el-form-item>
                    <el-form-item label="最高价格" prop="maxPrice">
                        <el-input-number v-model="searchForm.maxPrice"></el-input-number>
                    </el-form-item>
                    <el-form-item class="search-actions">
                        <el-button
                            type="primary"
                            @click="handleSearch"
                            :loading="loading"
                            size="large"
                        >
                            搜索
                        </el-button>
                        <el-button
                            @click="handleReset"
                            size="large"
                        >
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-card>

            <!-- 数据表格 -->
            <el-card class="table-card">
                <el-table
                    v-loading="loading"
                    :data="state.tableData"
                    style="width: 100%"
                    @selection-change="handleSelectionChange"
                    :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
                    stripe
                    border
                >
                    <el-table-column type="selection" width="55" align="center" />

                    <el-table-column label="商品信息" min-width="200">
                        <template #default="scope">
                            <div class="product-info">
                                <el-image
                                    :src="getImageUrl(scope.row.cover) || '/default-product.png'"
                                    :preview-src-list="[getImageUrl(scope.row.cover)]"
                                    class="product-image"
                                    fit="cover"
                                />
                                <div class="product-details">
                                    <div class="product-name">{{ scope.row.name }}</div>
                                    <div class="product-code">商品编号: {{ scope.row.id }}</div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column label="商家信息" width="150">
                        <template #default="scope">
                            <div class="merchant-info">
                                <div class="merchant-name">{{ scope.row.merchantName || 'N/A' }}</div>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column label="价格" prop="price" width="120" align="center">
                        <template #default="scope">
                            <span class="price-text">¥{{ scope.row.price || 0 }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column label="库存" prop="stock" width="100" align="center">
                        <template #default="scope">
                            <el-tag :type="scope.row.stock > 10 ? 'success' : scope.row.stock > 0 ? 'warning' : 'danger'">
                                {{ scope.row.stock || 0 }}
                            </el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column label="分类" prop="category" width="120" align="center">
                        <template #default="scope">
                            <span>{{ scope.row.categoryName || 'N/A' }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column label="审核状态" width="120" align="center">
                        <template #default="scope">
                            <el-tag :type="getStatusType(scope.row.auditStatus)">
                                {{ getStatusText(scope.row.auditStatus) }}
                            </el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column label="提交时间" width="160" align="center">
                        <template #default="scope">
                            <span>{{ formatDate(scope.row.createTime) }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column label="操作" width="200" align="center" fixed="right">
                        <template #default="scope">
                            <div class="action-buttons">
                                <el-button
                                    v-if="scope.row.auditStatus === '0'"
                                    type="success"
                                    size="small"
                                    :icon="Check"
                                    @click="through(scope.row)"
                                >
                                    通过
                                </el-button>
                                <el-button
                                    v-if="scope.row.auditStatus === '0'"
                                    type="danger"
                                    size="small"
                                    :icon="Close"
                                    @click="rejection(scope.row)"
                                >
                                    驳回
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页组件 -->
                <div class="pagination-container">
                    <el-pagination
                        v-model:current-page="pagination.pageNum"
                        v-model:page-size="pagination.pageSize"
                        :page-sizes="[10, 20, 50, 100]"
                        :total="pagination.total"
                        layout="total, sizes, prev, pager, next, jumper"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                    />
                </div>
            </el-card>
        </div>

        <!-- 驳回理由对话框 -->
        <el-dialog
            v-model="dialogFormVisible"
            title="驳回理由"
            width="500px"
            :close-on-click-modal="false"
        >
            <el-form
                :model="state.form"
                :rules="rejectRules"
                ref="rejectFormRef"
                label-width="100px"
            >
                <el-form-item label="驳回理由" prop="auditRemake">
                    <el-input
                        v-model="state.form.auditRemake"
                        type="textarea"
                        :rows="4"
                        placeholder="请输入驳回理由，不少于10个字符"
                        maxlength="200"
                        show-word-limit
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="returnFun">取消</el-button>
                    <el-button type="primary" @click="confirm" :loading="submitLoading">确定</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 商品详情对话框 -->
        <el-dialog
            v-model="detailDialogVisible"
            title="商品详情"
            width="800px"
            :close-on-click-modal="false"
        >
            <div v-if="currentProduct" class="product-detail">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="商品名称">{{ currentProduct.name }}</el-descriptions-item>
                    <el-descriptions-item label="商品编号">{{ currentProduct.code }}</el-descriptions-item>
                    <el-descriptions-item label="商品价格">¥{{ currentProduct.price }}</el-descriptions-item>
                    <el-descriptions-item label="库存数量">{{ currentProduct.stock }}</el-descriptions-item>
                    <el-descriptions-item label="商品分类">{{ currentProduct.categoryName }}</el-descriptions-item>
                    <el-descriptions-item label="商品品牌">{{ currentProduct.brandName }}</el-descriptions-item>
                    <el-descriptions-item label="商家名称">{{ currentProduct.merchantName }}</el-descriptions-item>
                    <el-descriptions-item label="商家账号">{{ currentProduct.merchantAccount }}</el-descriptions-item>
                    <el-descriptions-item label="提交时间">{{ formatDate(currentProduct.createTime) }}</el-descriptions-item>
                    <el-descriptions-item label="审核状态">
                        <el-tag :type="getStatusType(currentProduct.auditStatus)">
                            {{ getStatusText(currentProduct.auditStatus) }}
                        </el-tag>
                    </el-descriptions-item>
                </el-descriptions>

                <div class="product-images" v-if="currentProduct.images && currentProduct.images.length">
                    <h4>商品图片</h4>
                    <el-image
                        v-for="(image, index) in currentProduct.images"
                        :key="index"
                        :src="image"
                        :preview-src-list="currentProduct.images"
                        class="detail-image"
                        fit="cover"
                    />
                </div>

                <div class="product-description" v-if="currentProduct.description">
                    <h4>商品描述</h4>
                    <div v-html="currentProduct.description"></div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script setup>
import {useRouter} from 'vue-router'
import {productList} from '../../stores/productList'
import {commodityReview} from '../../stores/commodityReview'
import {nextTick, onMounted, reactive, ref} from 'vue'
import {Session} from "@/utils/storage.js"
import {ElMessage, ElMessageBox} from 'element-plus'
import {Check, Close} from '@element-plus/icons-vue'
import {getImageUrl} from "@/utils/common.js";

const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const dialogFormVisible = ref(false)
const detailDialogVisible = ref(false)
const activeMenu = ref('')
const selectedRows = ref([])
const currentProduct = ref(null)
const rejectFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
    name:'',
    merchantName:'',
    minPrice: null,
    maxPrice: null,
})

// 分页数据
const pagination = reactive({
    pageNum: 1,
    pageSize: 10,
    total: 0
})

// 统计数据
const statistics = reactive({
    pending: 0,
    approved: 0,
    rejected: 0
})

const state = reactive({
    buttonList: [],
    tableData: [],
    form: {
        id: "",
        auditStatus: "",
        auditRemake: "",
    }
})


// 表单验证规则
const rejectRules = {
    auditRemake: [
        { required: true, message: '请输入驳回理由', trigger: 'blur' },
        { min: 10, message: '驳回理由不能少于10个字符', trigger: 'blur' }
    ]
}

// 工具方法
const getStatusType = (status) => {
    const statusMap = {
        '0': 'warning',  // 待审核
        '1': 'success',  // 已通过
        '2': 'danger'    // 已驳回
    }
    return statusMap[status] || 'info'
}

const getStatusText = (status) => {
    const statusMap = {
        '0': '待审核',
        '1': '已通过',
        '2': '已驳回'
    }
    return statusMap[status] || '未知'
}

const formatDate = (date) => {
    if (!date) return 'N/A'
    return new Date(date).toLocaleString('zh-CN')
}

// 事件处理方法
const handleButtonClick = (item) => {
    activeMenu.value = item.component
    router.push(item.component)
}

const handleSearch = () => {
    pagination.pageNum = 1
    getProductList()
}

const handleReset = () => {
    Object.assign(searchForm, {
        name:'',
        merchantName:'',
        minPrice: null,
        maxPrice: null,
    })
    handleSearch()
}

const handleSizeChange = (size) => {
    pagination.pageSize = size
    pagination.pageNum = 1
    getProductList()
}

const handleCurrentChange = (page) => {
    pagination.pageNum = page
    getProductList()
}

const handleSelectionChange = (selection) => {
    selectedRows.value = selection
}

// 数据获取方法
const getProductList = async () => {
    try {
        loading.value = true
        const params = {
            ...searchForm,
            pageNum: pagination.pageNum,
            pageSize: pagination.pageSize,
            auditStatus: 0
        }

        const result = await productList().GetProductList(params)
        state.tableData = result.rows || []
        pagination.total = result.total || 0

        // 更新统计数据
        await getStatistics()

    } catch (error) {
        ElMessage.error('获取商品列表失败')
        console.error('获取商品列表失败:', error)
    } finally {
        loading.value = false
    }
}

const getStatistics = async () => {
    try {
        // 这里应该调用统计接口
        // const result = await commodityReview().GetStatistics()
        // 临时模拟数据
        statistics.pending = state.tableData.filter(item => item.auditStatus === '0').length
        statistics.approved = state.tableData.filter(item => item.auditStatus === '1').length
        statistics.rejected = state.tableData.filter(item => item.auditStatus === '2').length
    } catch (error) {
        console.error('获取统计数据失败:', error)
    }
}

// 审核相关方法
const through = async (row) => {
    try {
        await ElMessageBox.confirm(
            '确定要通过该商品的审核吗？',
            '确认通过',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )

        loading.value = true
        state.form.id = row.id
        state.form.auditStatus = "1"

        const result = await commodityReview().GetThroughList(state.form)
        ElMessage.success('审核通过成功')
        getProductList()

    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('审核通过失败')
            console.error('审核通过失败:', error)
        }
    } finally {
        loading.value = false
    }
}

const rejection = (row) => {
    state.form.id = row.id
    state.form.auditStatus = "2"
    state.form.auditRemake = ""
    dialogFormVisible.value = true
}

const confirm = async () => {
    try {
        await rejectFormRef.value.validate()

        submitLoading.value = true
        const result = await commodityReview().GetThroughList(state.form)

        ElMessage.success('驳回成功')
        dialogFormVisible.value = false
        getProductList()

    } catch (error) {
        if (error !== false) {
            ElMessage.error('驳回失败')
            console.error('驳回失败:', error)
        }
    } finally {
        submitLoading.value = false
    }
}

const returnFun = () => {
    dialogFormVisible.value = false
    state.form.auditRemake = ""
}

const viewDetail = (row) => {
    currentProduct.value = row
    detailDialogVisible.value = true
}

// 批量操作方法
const batchPass = async () => {
    if (!selectedRows.value.length) {
        ElMessage.warning('请选择要操作的商品')
        return
    }

    try {
        await ElMessageBox.confirm(
            `确定要批量通过选中的 ${selectedRows.value.length} 个商品吗？`,
            '批量通过确认',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )

        loading.value = true
        const ids = selectedRows.value.map(item => item.id)
        const result = await commodityReview().BatchAudit({ ids })

        ElMessage.success(`成功通过 ${ids.length} 个商品`)
        selectedRows.value = []
        getProductList()

    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('批量通过失败')
            console.error('批量通过失败:', error)
        }
    } finally {
        loading.value = false
    }
}

const batchReject = async () => {
    if (!selectedRows.value.length) {
        ElMessage.warning('请选择要操作的商品')
        return
    }

    try {
        const { value: reason } = await ElMessageBox.prompt(
            '请输入批量驳回的理由',
            '批量驳回',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPattern: /.{10,}/,
                inputErrorMessage: '驳回理由不能少于10个字符'
            }
        )

        loading.value = true
        const ids = selectedRows.value.map(item => item.id)
        const result = await commodityReview().BatchReject({
            ids,
            auditRemake: reason
        })

        ElMessage.success(`成功驳回 ${ids.length} 个商品`)
        selectedRows.value = []
        getProductList()

    } catch (error) {
        if (error !== 'cancel') {
            ElMessage.error('批量驳回失败')
            console.error('批量驳回失败:', error)
        }
    } finally {
        loading.value = false
    }
}

// 初始化方法
const getMenuList = async () => {
    try {
        nextTick(() => {
            const menuList = Session.getMenu2()
            let menuId = Session.get('homeMenuId')

            if (menuId == null) {
                for (let index = 0; index < menuList.length; index++) {
                    const element = menuList[index]
                    if (element.openType == 2) {
                        if (menuId == null) {
                            menuId = element.menuId
                        }
                    }
                }
            }

            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index]
                if (element.menuId == menuId) {
                    state.buttonList = element.children
                }
            }
        })
    } catch (error) {
        console.error('获取菜单列表失败:', error)
    }
}

onMounted(() => {
    getMenuList()
    getProductList()
})
</script>
<style lang="scss" scoped>
.commodity-review-container {
    display: flex;
    height: 100vh;
    background-color: #f5f7fa;
}


.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}
// 左侧导航样式
.left-sidebar {
    width: 240px;
    background: #3A58CF;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

    .sidebar-menu {
        border: none;
        height: 100%;

        :deep(.el-menu-item) {
            height: 60px;
            line-height: 60px;
            font-size: 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);

            &:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }

            &.is-active {
                background-color: rgba(255, 255, 255, 0.2);
                border-right: 3px solid #ffffff;
            }
        }
    }
}

// 主内容区域样式
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

// 统计卡片样式
.statistics-header {
    margin-bottom: 20px;

    .stat-card {
        position: relative;
        overflow: hidden;
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        :deep(.el-card__body) {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stat-content {
            .stat-number {
                font-size: 32px;
                font-weight: bold;
                margin-bottom: 5px;
            }

            .stat-label {
                font-size: 14px;
                color: #666;
            }
        }

        .stat-icon {
            font-size: 40px;
            opacity: 0.8;
        }

        &.pending {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            .stat-number { color: #e17055; }
            .stat-icon { color: #e17055; }
        }

        &.approved {
            background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
            .stat-number { color: #00b894; }
            .stat-icon { color: #00b894; }
        }

        &.rejected {
            background: linear-gradient(135deg, #fab1a0, #e17055);
            .stat-number { color: #d63031; }
            .stat-icon { color: #d63031; }
        }
    }
}

// 搜索卡片样式
.search-card {
    margin-bottom: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .search-form {
        .price-range {
            display: flex;
            align-items: center;
            width: 100%;

            .price-separator {
                margin: 0 10px;
                color: #666;
            }
        }

        .search-actions {
            text-align: center;
            margin-top: 10px;
        }
    }
}

// 批量操作样式
.batch-actions {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 表格卡片样式
.table-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .product-info {
        display: flex;
        align-items: center;

        .product-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            margin-right: 12px;
        }

        .product-details {
            .product-name {
                font-weight: 500;
                color: #303133;
                margin-bottom: 4px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 150px;
            }

            .product-code {
                font-size: 12px;
                color: #909399;
            }
        }
    }

    .merchant-info {
        .merchant-name {
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
        }

        .merchant-account {
            font-size: 12px;
            color: #909399;
        }
    }

    .price-text {
        font-weight: 600;
        color: #e6a23c;
        font-size: 16px;
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
        flex-wrap: wrap;
    }
}

// 分页样式
.pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 20px 0;
}

// 商品详情样式
.product-detail {
    .product-images {
        margin-top: 20px;

        h4 {
            margin-bottom: 15px;
            color: #303133;
        }

        .detail-image {
            width: 100px;
            height: 100px;
            margin-right: 10px;
            margin-bottom: 10px;
            border-radius: 8px;
        }
    }

    .product-description {
        margin-top: 20px;

        h4 {
            margin-bottom: 15px;
            color: #303133;
        }

        div {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .left-sidebar {
        width: 200px;
    }

    .statistics-header {
        .stat-card {
            .stat-content .stat-number {
                font-size: 24px;
            }
            .stat-icon {
                font-size: 32px;
            }
        }
    }
}

@media (max-width: 768px) {
    .commodity-review-container {
        flex-direction: column;
    }

    .left-sidebar {
        width: 100%;
        height: auto;

        .sidebar-menu {
            :deep(.el-menu-item) {
                height: 50px;
                line-height: 50px;
                font-size: 14px;
            }
        }
    }

    .main-content {
        padding: 10px;
    }

    .search-form {
        :deep(.el-col) {
            margin-bottom: 15px;
        }
    }

    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }
}

// 动画效果
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s;
}

.fade-enter-from, .fade-leave-to {
    opacity: 0;
}

// 自定义滚动条
:deep(.el-table__body-wrapper) {
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
            background: #a8a8a8;
        }
    }
}

</style>
