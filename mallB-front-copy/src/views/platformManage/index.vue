<script setup>
import {Tickets, User} from '@element-plus/icons-vue'
import {onMounted, reactive} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {getCompanyHome} from '@/api/system/user'
import {add, list} from "@/api/sysUserDonationRecord";
import {Session} from "@/utils/storage.js";

const donationFormRef = ref(null)
const user = Session.get('user')
const userType = Session.get('userType');
const state = reactive({
    currentPage: 1,
    pageSize: 10,
    beginTime: null,
    endTime: null,
    customerCount: 0,
    verification: 0,
    orderCount: 0,
    // 核销表格Loading
    recordsLoading: false,
    // 核销对话框
    donationDialogVisible: false,
    // 核销表单
    donationForm: {
        amount: 1,
        operationPassword: '',
        remark: ''
    },
})

// 表单验证规则
const donationRules = {
    phone: [
        {required: true, message: '请输入用户手机号', trigger: 'blur'},
        {pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码', trigger: 'blur'}
    ],
    amount: [
        {required: true, message: '请输入核销值', trigger: 'blur'},
        {type: 'number', min: 1, message: '金额必须大于0', trigger: 'blur'}
    ],
    operationPassword: [
        {required: true, message: '请输入操作密码', trigger: 'blur'}
    ]
}

const GetCompanyHomeInfo = async () => {
    const res = await getCompanyHome()
    state.customerCount = res.data.customerCount
    state.verification = res.data.verification
    state.orderCount = res.data.orderCount
}

// 提交核销表单
const submitDonation = async () => {
    if (!donationFormRef.value) return
    if (state.donationForm.amount <= 0){
        ElMessage.error('核销金额不能小于0')
        return
    }
    if (state.donationForm.amount > state.verification){
        ElMessage.error('核销金额不能大于当前核销值')
        return
    }

    await donationFormRef.value.validate(async (valid) => {
        if (!valid) return

        // 二次确认
        try {
            await ElMessageBox.confirm(
                `确认核销 ¥${state.donationForm.amount} 吗？`,
                '核销确认',
                {
                    confirmButtonText: '确认核销',
                    cancelButtonText: '取消',
                    type: 'warning'
                }
            )

            // 发起核销请求
            state.donating = true
            try {
                const res = await add(state.donationForm)
                if (res.code === 200) {
                    ElMessage.success('核销成功')
                    // 重新加载商家信息
                    GetCompanyHomeInfo()
                    // 重新加载核销记录
                    loadDonationRecords()
                    // 重置表单
                    resetDonationForm()
                }
            } catch (error) {
                console.error('核销失败:', error)
                ElMessage.error('核销失败，请稍后重试')
            } finally {
                state.donating = false
            }
        } catch {
            // 用户取消操作
        }
    })
}

// 重置表单
const resetDonationForm = () => {
    if (donationFormRef.value) {
        donationFormRef.value.resetFields()
    }
    state.currentPage = 1
    state.pageSize = 10
    state.donationForm = {
        amount: 1,
        operationPassword: '',
        remark: ''
    }
}

// 显示核销对话框
const showDonationDialog = () => {
    state.donationDialogVisible = true
    // 重置表单
    state.donationForm = {
        amount: 1,
        operationPassword: '',
        remark: ''
    }
    // 加载核销记录
    loadDonationRecords()
}

// 加载核销记录
const loadDonationRecords = async () => {
    state.recordsLoading = true
    state.donationRecords = []
    state.total = 0
    try {
        const res = await list({
            pageNum: state.currentPage,
            pageSize: state.pageSize,
            beginTime: state.beginTime,
            endTime: state.endTime,

        })
        if (res.code === 200) {
            state.donationRecords = res.rows || []
            state.total = res.total || 0
        } else {
            ElMessage.error(res.msg || '获取核销记录失败')
        }
    } catch (error) {
        console.error('获取核销记录失败:', error)
        ElMessage.error('获取核销记录失败，请稍后重试')
    } finally {
        state.recordsLoading = false
    }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
    state.pageSize = size
    loadDonationRecords()
}

// 处理页码变化
const handleCurrentChange = (page) => {
    state.currentPage = page
    loadDonationRecords()
}
onMounted(() => {
    GetCompanyHomeInfo()
})

</script>
<template>
    <div class="container">
        <img class="bg" src="../../images/bigBackground.png">
        <div class="content">
            <div class="picBox">
                <img class="managePic" src="../../images/managePic.png">
            </div>
            <div class="buttons">
                <button class="button left-button">
                    <div class="button-content">
                        <el-icon class="user-icon">
                            <User/>
                        </el-icon>
                        <span class="text">{{userType == 1 ? "今日新增客户量" :"今日新增粉丝量"}}&nbsp;&nbsp;{{ state.customerCount }}</span>
                    </div>
                </button>
                <button class="button right-button" v-if="user.userId == 1">
                    <div class="button-content" @click="showDonationDialog">
                        <el-icon class="user-icon">
                            <Tickets/>
                        </el-icon>
                        <span class="text">核销值&nbsp;&nbsp;{{ state.verification }}</span>
                    </div>
                </button>
<!--                <button style="width: 340px" v-else/>-->
                <button class="button right-button">
                    <div class="button-content">
                        <el-icon class="user-icon">
                            <Tickets/>
                        </el-icon>
                        <span class="text">今日订单量&nbsp;&nbsp;{{ state.orderCount }}</span>
                    </div>
                </button>
            </div>
        </div>


        <!-- 平台促销金核销对话框 -->
        <el-dialog
            v-model="state.donationDialogVisible"
            destroy-on-close
            title="核销"
            width="1200px"
        >
            <div class="donation-container">
                <!-- 核销表单 -->
                <div class="donation-form">
                    <div class="form-header">
                        <h3>核销值</h3>
                        <div class="balance-info">
                            <span>当前核销值：</span>
                            <span class="balance-amount">¥{{ state.verification || "0" }}</span>
                        </div>
                    </div>
                    <el-form ref="donationFormRef" :model="state.donationForm" :rules="donationRules"
                             label-width="100px">
                        <el-form-item label="核销值" prop="amount">
                            <el-input-number
                                v-model="state.donationForm.amount"
                                :precision="2"
                                :step="1"
                                placeholder="请输入核销值"
                                style="width: 100%"
                            />
                        </el-form-item>
                        <el-form-item label="操作密码" prop="operationPassword">
                            <el-input
                                v-model="state.donationForm.operationPassword"
                                placeholder="请输入操作密码"
                                show-password
                                type="password"
                            >
                                <template #prefix>
                                    <el-icon>
                                        <Lock/>
                                    </el-icon>
                                </template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="备注" prop="remark">
                            <el-input
                                v-model="state.donationForm.remark"
                                :autosize="{ minRows: 3, maxRows: 5 }"
                                maxlength="200"
                                placeholder="请输入备注信息（选填）"
                                show-word-limit
                                type="textarea"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button :loading="state.donating" type="primary" @click="submitDonation">确认核销
                            </el-button>
                            <el-button @click="resetDonationForm">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 核销记录表格 -->
                <div class="donation-records">
                    <div class="records-header">
                        <h3>核销记录</h3>
                        <div class="search-box">
                            <el-date-picker
                                v-model="state.beginTime"
                                clearable
                                format="YYYY-MM-DD HH:mm:ss"
                                placeholder="开始时间"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                            >
                            </el-date-picker>
                            <el-date-picker
                                v-model="state.endTime"
                                clearable
                                format="YYYY-MM-DD HH:mm:ss"
                                placeholder="结束时间"
                                type="datetime"
                                value-format="YYYY-MM-DD HH:mm:ss"
                            >
                            </el-date-picker>
                            <el-button type="primary" @click="loadDonationRecords">
                                搜索
                            </el-button>
                        </div>
                    </div>

                    <el-table
                        v-loading="state.recordsLoading"
                        :data="state.donationRecords"
                        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
                        border
                        empty-text="暂无核销记录"
                        style="width: 100%"
                    >
                        <el-table-column label="核销值" prop="amount">
                            <template #default="scope">
                                <span class="amount">¥{{ scope.row.amount }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="核销时间" prop="donationTime">
                            <template #default="scope">
                                <el-tooltip :content="scope.row.donationTime" effect="light" placement="top">
                                    <span>{{ scope.row.donationTime }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态" prop="status">
                            <template #default="scope">
                                <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'" size="small">
                                    {{ scope.row.status === '1' ? '成功' : '失败' }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="备注" min-width="150" prop="remark" show-overflow-tooltip/>
                    </el-table>

                    <div class="pagination">
                        <el-pagination
                            v-model:current-page="state.currentPage"
                            v-model:page-size="state.pageSize"
                            :page-sizes="[10, 20, 50, 100]"
                            :total="state.total"
                            background
                            layout="total, sizes, prev, pager, next, jumper"
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                        />
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>


<style lang="scss" scoped>
.container {
    position: relative;
    width: 100%;
    max-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;

    .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }

    .content {
        z-index: 2;
        display: flex;
        flex-direction: column;
    }

    .picBox {
        .managePic {
            width: 1233px;
            height: 346px;
            margin-left: 250px;
            margin-top: 64px;
        }
    }

    .buttons {
        display: flex;
        margin-left: 250px;
        margin-top: 20px;
        gap: 39px;

        .button {
            width: 33%;
            height: 346px;
            border: none;
            cursor: pointer;
            background: #ffffff;
            border: 2px solid #3A58CF;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;

            &-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 30px;
                height: 100%;
            }

            .user-icon {
                font-size: 83px;
                color: #3A58CF;
            }

            .text {
                font-size: 28px;
                color: #333;
                text-align: center;
                width: 100%;
            }
        }
    }
}

// 新增的样式
.donation-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.donation-form {
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eaeaea;
}

.form-header h3 {
    margin: 0;
    color: #3A58CF;
    font-size: 18px;
}

.balance-info {
    font-size: 14px;
    color: #606266;
}

.balance-amount {
    font-weight: bold;
    color: #f56c6c;
    font-size: 16px;
}

.amount-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
    line-height: 1.4;
}

.donation-records {
    .records-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        h3 {
            margin: 0;
            color: #3A58CF;
            font-size: 18px;
        }

        .search-box {
            display: flex;
            gap: 10px;
            align-items: center;
            width: auto;
        }
    }

    .pagination {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
    }
}


</style>
