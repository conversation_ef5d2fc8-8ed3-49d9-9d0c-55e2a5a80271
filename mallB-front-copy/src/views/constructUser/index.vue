<script setup>
import {ref} from 'vue'
import {useRouter} from 'vue-router'

const router = useRouter();
const handleToNormalManager = () => {
    router.push({
        path: '/normalManager'
    })
}
const form = ref({
    UserName: '',
    password: '',
    password2: '',
    phoneNumber: '',
    identityCard: ''

})
</script>
<template>
    <div class="container">
        <img alt="背景图" class="bg" src="../../images/bigBackground.png">
        <div class="form-container">

            <div class="input-group">
                <el-input
                    v-model="form.UserName"
                    class="form-input"
                    placeholder="用户名"
                />
            </div>
            <div class="input-group">
                <el-input
                    v-model="form.password"
                    class="form-input"
                    placeholder="密码"
                />

            </div>

            <div class="input-group">
                <el-input
                    v-model="form.password2"
                    class="form-input"
                    placeholder="确认密码"
                    show-password
                    type="password"
                />

            </div>

            <div class="input-group">
                <el-input
                    v-model="form.phoneNumber"
                    class="form-input"
                    placeholder="手机号"
                />
            </div>

            <div class="input-group">
                <el-input
                    v-model="form.identityCard"
                    class="form-input"
                    placeholder="身份证"
                />
            </div>


            <div class="save-button-container">
                <el-button class="save-button" @click="handleToNormalManager">保存</el-button>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.container {
    position: relative;
    width: 100%;
    max-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;

    .bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 1;
    }

    .form-container {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        align-items: center;

        width: 100%;
        max-width: 1200px;

        .input-group {
            display: flex;
            align-items: center;
            margin-bottom: 61px;
            width: 100%;
            margin-left: 399px;

            .form-input {
                width: 805px;

                :deep(.el-input__wrapper) {
                    height: 53px;
                    font-size: 16px;
                }
            }

            .reserved-text {
                margin-left: 55px;
                color: #FF8D1A;
                font-size: 16px;
                white-space: nowrap;
            }
        }

        .save-button-container {
            width: 805px;
            margin: 0 auto;
            text-align: center;

            .save-button {
                width: 120px;
                height: 40px;
                background-color: #14097A;
                color: white;
                border: none;
                font-size: 16px;
                border-radius: 4px;

                &:hover {
                    background-color: #1a0da0;
                }

                &:active {
                    background-color: #0f0657;
                }
            }
        }
    }
}
</style>
