<template>
    <!--  <ManageBg>-->
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item }}
            </el-button>
        </div>

        <div class="right-content">
            <div class="right-content-header">
                <span class="productName">商品名称</span>
                <input class="enterName" placeholder="输入商品名称">
                <button class="search">
                    <el-icon class="search-icon">
                        <Search/>
                    </el-icon>
                    搜索
                </button>
            </div>

            <div class="main">
                <div class="header">
                    <el-button class="newDelivery" @click="dialogAddVisibleHandle">新增分类+</el-button>
                </div>
                <el-table
                    :data="tableData"
                    :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                    border
                    row-key="id"
                    style="width: 100%">
                    <el-table-column label="分类名称" prop="name" width="180"></el-table-column>
                    <el-table-column label="描述" prop="description"></el-table-column>
                </el-table>
                <!-- <el-table
                  :data="tableData"
                  row-key="id"
                  style="width: 100%; height: 685px"
                  header-row-class-name="table-header"
                  :default-expand-all="isExpandAll"
                 :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                >
                  <el-table-column
                    prop="name"
                    label="分类名称"
                    width="440"
                  />
                 <el-table-column
                    prop="code"
                    label="分类编码"
                    width="440"
                  />
                  <el-table-column
                    prop="status"
                    label="操作"
                    width="440"
                  >
                    <template #default="{ row }">
                       <el-button
                         link
                         type="warning"
                         size="small"
                         @click="handleEdit(row)"
                       >
                        修改
                     </el-button>
                      <el-button

                        :type="row.status === '上架中' ? 'danger' : 'success'"
                        @click="toggleStatus(row)"
                      >
                        {{ row.status === '上架中' ? '下架' : '上架' }}
                      </el-button>
                       <el-button
                         link
                         type="warning"
                         size="small"
                         @click="handleDetail(row)"
                       >
                        详情
                     </el-button>
                    </template>
                  </el-table-column>
                </el-table> -->
            </div>
            <el-divider/>
            <div class="bottom ">

            </div>
        </div>
    </div>
    <!-- 新增分类 -->
    <el-dialog v-model="dialogAddVisible" title="新增" width="500">
        <el-form :model="state.ruleForm">
            <el-form-item :label-width=" formLabelWidth" label="分类名称">
                <el-input v-model="state.ruleForm.name" :precision="0"/>
            </el-form-item>
            <el-form-item :label-width="formLabelWidth" label="分类编码">
                <el-input v-model="state.ruleForm.code" :precision="0"/>
            </el-form-item>
            <el-form-item :label-width="formLabelWidth" label="上级分类">
                <!-- 先判断是否有数据，若没有数据的话，那么默认上一级菜单。若有数据的话，需要重新获取数据 -->
                <el-tree-select
                    v-model="state.ruleForm.parent"
                    :data="parentData"
                    :render-after-expand="false"
                    check-strictly
                    style="width: 240px"
                />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogAddVisibleReturn">返回</el-button>
                <el-button type="primary" @click="dialogAddVisibleConfirm">确定</el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 修改分类以及详情分类 -->
    <!-- <el-dialog v-model="dialogEditVisible" title="详情" width="500">
            <el-form :model="form">
              <el-form-item label="分类名称" :label-width=" formLabelWidth">
                  <el-input v-model="form" :precision="0"/>
              </el-form-item>
              <el-form-item label="分类编码" :label-width="formLabelWidth">
                  <el-input v-model="form" :precision="0"/>
              </el-form-item>
              <el-form-item label="上级分类" :label-width="formLabelWidth">
                  <el-tree-select
                        v-model="value"
                        :data="data"
                        check-strictly
                        :render-after-expand="false"
                        style="width: 240px"
                  />
              </el-form-item>
            </el-form>
            <template #footer>
              <div class="dialog-footer">
                <el-button @click="dialogEditDetailVisibleReturn">返回</el-button>
                <el-button type="primary" @click="dialogEditDetailVisibleConfirm">确定</el-button>
              </div>
            </template>
    </el-dialog> -->
    <!--  </ManageBg>-->
</template>
<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {productCategory} from '../../stores/productCategory'
import {ref} from 'vue'
import {Search} from '@element-plus/icons-vue'

const state = reactive({
    ruleForm: {
        name: "",
        parentId: 0,
        parent: "一级分类",
        code: ""
    },
    parentData: []
});
const router = useRouter()
const buttonList = [
    '发布商品', '商品列表', '商品分类', '品牌管理', '配送管理',
    '评论管理', '退货地址', '商品链接', '商品链接生成',
    '商品链接导入', '商品代销申请'
]

const tableData = ref([
    {
        id: 1,
        name: '一级分类1',
        description: '描述1',
        children: [
            {
                id: 11,
                name: '二级分类1-1',
                description: '描述1-1'
            }
        ]
    },
    {
        id: 2,
        name: '一级分类2',
        description: '描述2',
        children: [
            {
                id: 21,
                name: '二级分类2-1',
                description: '描述2-1'
            }
        ]
    }
])

const handleButtonClick = (item) => {
    if (item === '商品列表') {
        router.push('/productList')
    }
    if (item === '发布商品') {
        router.push('/commodity')
    }
    if (item === '商品分类') {
        router.push('/productCategory')
    }
    if (item === '品牌管理') {
        router.push('/brandManage')
    }
    if (item === '配送管理') router.push('./deliveryManage')
    if (item === '评论管理') router.push('./commentManage')
    if (item === '退货地址') router.push('./returnAddress')
    if (item === '商品链接') router.push('./productLink')
    if (item === '商品链接生成') router.push('./buildProductLink')
    if (item === '商品链接导入') router.push('./productLinkImport')
    if (item === '商品代销申请') router.push('./productSellApply')
}

const toggleStatus = (row) => {
    row.status = row.status === '上架中' ? '已下架' : '上架中'
}
//新增数据弹窗
const dialogAddVisible = ref(false)
const dialogEditVisible = ref(false)
const dialogAddVisibleHandle = () => {
    dialogAddVisible.value = true;
};
const dialogAddVisibleReturn = () => {
    dialogAddVisible.value = false;
}
const dialogAddVisibleConfirm = async () => {
    console.log(state.ruleForm, 'ruleForm')
    let result = await productCategory().SaveCategory(state.ruleForm); // 调用存储菜单方法
    // dialogAddVisible.value = false;
}
//编辑详情
const handleEdit = () => {
    dialogEditVisible.value = true
}
const handleDetail = () => {
    dialogEditVisible.value = true
}
const dialogEditDetailVisibleReturn = () => {
    dialogEditVisible.value = false
}
const dialogEditDetailVisibleConfirm = () => {
    dialogEditVisible.value = false
}
const value = ref()
//获取分类数据
const getCategory = async () => {
    let result = await productCategory().GetCategoryList(); // 调用存储菜单方法
    console.log(result, 'result')
    tableData.value = result
}
onMounted(() => {
    getCategory()
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 40px 44px;

    .right-content-header {
        width: 100%;
        height: 103px;
        background-color: #3A58CF;
        display: flex;
        align-items: center;
        padding: 0 59px;
        box-sizing: border-box;

        .productName {
            font-size: 36px;
            color: #fff;
            margin-right: 44px;
        }

        .enterName {
            width: 514px;
            height: 65px;
            padding: 15px;
            background-color: #D2E0FB;
            font-size: 30px;
            color: #808080;
            border: none;
            outline: none;
        }

        .search {
            width: 122px;
            height: 46px;
            border-radius: 35px;
            background-color: #fff;
            display: flex;
            align-items: center;
            margin-left: 166px;
            cursor: pointer;
            transition: all 0.3s;
            justify-content: flex-start;
            padding-left: 12px;
            border: none;

            &:hover {
                background-color: #f0f0f0;
            }

            .search-icon {
                font-size: 20px;
                color: #3A58CF;
            }
        }
    }
}


.main {
    margin-top: 20px;
    width: 100%;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        float: right;

        .deliveryList {
            font-size: 24px;
            font-weight: bold;
            color: #000;
        }

        .newDelivery {
            width: 150px;
            height: 40px;
            background-color: #3A58CF;
            color: white;
            border: none;
            font-size: 16px;

            &:hover {
                background-color: #2a48bf;
            }
        }
    }

    :deep(.table-header) {
        th {
            background-color: #6B95E8 !important;
            color: white;
            font-size: 20px;
            font-weight: bold;
        }
    }

    :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;

        .el-table__cell {
            padding: 15px 0;
            font-size: 18px;
        }

        .el-button {
            width: 100px;
            height: 40px;
            font-size: 16px;
        }
    }
}
</style>
