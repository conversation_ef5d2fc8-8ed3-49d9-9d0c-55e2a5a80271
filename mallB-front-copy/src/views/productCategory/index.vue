<template>
    <div class="container">
        <div class="left-buttons">
            <el-button
                v-for="(item, index) in state.buttonList"
                :key="index"
                class="data-button"
                @click="handleButtonClick(item)"
            >
                {{ item.menuName }}
            </el-button>
        </div>

        <div class="right-content">
            <div class="content-header">
                <div class="action-area">
                    <h2 class="page-title">商品分类</h2>
                    <div class="search-box">
                        <el-input
                            v-model="state.name"
                            clearable
                            placeholder="输入分类名称"
                            style="width: 300px"
                            @keyup.enter="getCategory"
                        >
                            <template #append>
                                <el-button @click="getCategory">
                                    <el-icon>
                                        <Search/>
                                    </el-icon>
                                </el-button>
                            </template>
                        </el-input>
                        <el-button @click="dialogAddVisibleHandle">新增分类</el-button>
                    </div>
                </div>
            </div>

            <div class="main">
                <el-table
                    :data="tableData"
                    :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
                    header-row-class-name="table-header"
                    row-key="id"
                >
                    <el-table-column
                        label="分类名称"
                        prop="name"
                        width="440"
                    />
                    <el-table-column
                        label="分类编码"
                        prop="code"
                        width="440"
                    />
                    <el-table-column
                        label="操作"
                        prop="status"
                        width="440"
                    >
                        <template #default="{ row }">
                            <el-button
                                link
                                size="small"
                                type="warning"
                                @click="handleEdit(row)"
                            >
                                修改
                            </el-button>
                            <el-button
                                link
                                size="small"
                                type="warning"
                                @click="handleDelete(row)"
                            >
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <el-divider/>
            <div class="bottom ">

            </div>
        </div>
    </div>
    <!-- 新增分类 -->
    <el-dialog v-model="dialogAddVisible" title="新增" width="500" @close="close">
        <el-form :model="state.ruleForm">
            <el-form-item :label-width=" formLabelWidth" label="分类名称">
                <el-input v-model="state.ruleForm.name" :precision="0"/>
            </el-form-item>
            <el-form-item :label-width="formLabelWidth" label="分类编码">
                <el-input v-model="state.ruleForm.code" :precision="0"/>
            </el-form-item>
            <el-form-item :label-width="formLabelWidth" label="上级分类">
                <!-- 先判断是否有数据，若没有数据的话，那么默认上一级菜单。若有数据的话，需要重新获取数据 -->
                <el-tree-select
                    v-model="state.ruleForm.parentId"
                    :data="state.parentData"
                    :props="{ checkStrictly: true, value: 'id', label: 'name', children: 'children'}"
                    :render-after-expand="false"
                    check-strictly
                    style="width: 240px"
                />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogAddVisibleReturn">返回</el-button>
                <el-button type="primary" @click="dialogAddVisibleConfirm">确定</el-button>
            </div>
        </template>
    </el-dialog>
    <!-- 修改分类以及详情分类 -->
    <el-dialog v-model="dialogEditVisible" title="修改" width="500">
        <el-form :model="form">
            <el-form-item :label-width=" formLabelWidth" label="分类名称">
                <el-input v-model="state.ruleForm.name" :precision="0"/>
            </el-form-item>
            <el-form-item :label-width="formLabelWidth" label="分类编码">
                <el-input v-model="state.ruleForm.code" :precision="0"/>
            </el-form-item>
            <el-form-item :label-width="formLabelWidth" label="上级分类">
                <el-tree-select
                    v-model="state.ruleForm.parentId"
                    :data="state.parentData"
                    :props="{ checkStrictly: true, value: 'id', label: 'name', children: 'children'}"
                    :render-after-expand="false"
                    check-strictly
                    style="width: 240px"
                />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogEditDetailVisibleReturn">返回</el-button>
                <el-button type="primary" @click="dialogEditDetailVisibleConfirm">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script lang="ts" setup>
import {useRouter} from 'vue-router'
import {productCategory} from '../../stores/productCategory'
import {ElMessage} from 'element-plus';
import {nextTick, onMounted, reactive, ref} from 'vue'
import {Search} from '@element-plus/icons-vue'
import {Session} from '../../utils/storage'

const state = reactive({
    name: '',
    ruleForm: {
        parentId: 0
    },
    parentData: [
        {
            id: 0,
            name: '顶级分类',
            children: []
        }
    ],
    type: 2,
    buttonList: []
});
const router = useRouter()
const tableData = ref([])
const handleButtonClick = (item) => {
    router.push(item.component)
}

//新增数据弹窗
const dialogAddVisible = ref(false)
const dialogEditVisible = ref(false)
const dialogAddVisibleHandle = () => {
    dialogAddVisible.value = true;
};
const dialogAddVisibleReturn = () => {
    dialogAddVisible.value = false;
}
const dialogAddVisibleConfirm = async () => {
    // 调用存储分类方法
    let result = await productCategory().SaveCategory(state.ruleForm);
    if (result.code == 200) {
        dialogAddVisible.value = false;
        getCategory()
        getCategoryTree()
    }
}
//编辑详情
const handleEdit = async (row) => {
    let result = await productCategory().GetCategoryById(row.id);
    state.ruleForm = result.data
    dialogEditVisible.value = true
}
//删除分类
const handleDelete = async (row) => {
    let result = await productCategory().DeleteCategory(row.id);
    if (result.code == 200) {
        dialogEditVisible.value = false
        ElMessage.success(result.msg)
        getCategory()
        getCategoryTree()
    }
}
const close = () => {
    state.ruleForm = {}
    state.ruleForm.parentId = 0
}
const handleDetail = (value) => {
    dialogEditVisible.value = true
}
const dialogEditDetailVisibleReturn = () => {
    dialogEditVisible.value = false
}
const dialogEditDetailVisibleConfirm = async () => {
    let result = await productCategory().UpdateCategory(state.ruleForm);
    if (result.code == 200) {
        dialogEditVisible.value = false
        ElMessage.success(result.msg)
        getCategory()
        getCategoryTree()
    }
}
//获取分类数据
const getCategory = async () => {
    let data = {
        name: state.name
    }
    let result = await productCategory().GetCategoryList(data); // 调用存储分类列表
    tableData.value = result.data
}
//获取分类树数据
const getCategoryTree = async () => {
    let result = await productCategory().GetCategoryTree(); // 调用存储分类列表
    state.parentData[0].children = result.data
}
onMounted(() => {
    getCategoryTree()
    getCategory()
    nextTick(() => {
        let menuList = Session.getMenu2()
        let menuId = Session.get('homeMenuId');
        if (menuId == null) {
            for (let index = 0; index < menuList.length; index++) {
                const element = menuList[index];
                if (element.openType == 2) {
                    if (menuId == null) {
                        menuId = element.menuId
                    }
                }
            }
        }
        for (let index = 0; index < menuList.length; index++) {
            const element = menuList[index];
            if (element.menuId == menuId) {
                state.buttonList = element.children
            }
        }
    })
})
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    display: flex;
    max-height: 100vh;
    box-sizing: border-box;
}

.left-buttons {
    width: 235px;
    height: calc(100vh - 30px);
    overflow-y: scroll;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    position: fixed;

    :deep(.el-button.data-button) {
        width: 100%;
        height: 60px;
        background-color: #3A58CF;
        color: white;
        font-size: 20px;
        border-radius: 0;
        border: none;
        margin: 0;
        padding: 0;
        display: block;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: background-color 0.3s;

        &:hover {
            background-color: #2a48bf;
        }

        &:first-child {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        &:last-child {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom: none;
        }

        &.el-button {
            --el-button-hover-text-color: white;
            --el-button-hover-bg-color: #2a48bf;
            --el-button-active-bg-color: #1a38af;
            --el-button-active-border-color: transparent;
        }
    }
}

.right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 40px 44px;
    margin-left: 200px;

    .content-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;

        .page-title {
            font-size: 24px;
            color: #333;
            margin: 0;
        }

        .action-area {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .search-box {
            display: flex;
            gap: 10px;
            align-items: center;

            :deep(.el-input-group__append) {
                padding: 0;

                .el-button {
                    border: none;
                    border-radius: 0;
                    height: 100%;
                    padding: 0 15px;
                    margin: 0;
                    color: #fff;
                    background-color: #409eff;

                    &:hover {
                        background-color: #66b1ff;
                    }
                }
            }
        }
    }
}


.main {
    margin-top: 20px;
    width: 100%;

    .filter-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 10px 0;
        border-bottom: 1px solid #eee;

        .el-tag {
            font-size: 16px;
            color: #333;
            background-color: #e6f7ff;
            border-color: #91d5ff;
        }

        .filter-dropdown-link {
            font-size: 16px;
            color: #333;
            display: flex;
            align-items: center;
            cursor: pointer;

            &:hover {
                color: #3A58CF;
            }

            .el-icon {
                margin-left: 5px;
                font-size: 16px;
                color: #333;
            }
        }
    }

    :deep(.table-header) {
        th {
            background-color: #f5f7fa !important;
            color: #606266;
            font-size: 14px;
            font-weight: 500;
            height: 50px;
            padding: 6px 0;
        }
    }

    :deep(.el-table__row) {
        height: 50px;
    }

    :deep(.el-table) {
        border-radius: 8px;
        overflow: hidden;

        .el-table__cell {
            padding: 8px 0;
            font-size: 14px;
        }

        .el-button {
            font-size: 14px;
            padding: 4px 8px;
        }
    }
}
</style>
