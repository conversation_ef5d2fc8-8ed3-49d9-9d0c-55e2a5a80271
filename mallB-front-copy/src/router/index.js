import {createRouter, createWebHashHistory} from 'vue-router'
import Login from '../views/login/index.vue'
import ForgotPassword from '../views/forgotPassword/index.vue'
import Home from '../views/home/<USER>'
import ConstructRole from '../views/constructRole/index.vue'
import NormalManager from '../views/normalManager/index.vue'
import ConstructUser from '../views/constructUser/index.vue'
import ConstructCustomer from '../views/constructCustomer/index.vue'
import PlatformManage from '../views/platformManage/index.vue'
import Statistics from '../views/statistics/index.vue'
import Commodity from '../views/commodity/index.vue'
import Quantification from '../views/quantification/index.vue'
import SystemSetting from '../views/systemSetting/index.vue'
import ProductList from '../views/productList/index.vue'
import ProductCategory from '../views/productCategory/index.vue'
import BrandManage from '../views/brandManage/index.vue'
import DeliveryManage from '../views/deliveryManage/index.vue'
import CommentManage from '../views/commentManage/index.vue'
import ReturnAddress from '../views/returnAddress/index.vue'
import CommoditySales from '../views/commoditySales/index.vue'
import ProductLink from '../views/productLink/index.vue'
import BuildProductLink from '../views/buildProductLink/index.vue'
import ProductLinkImport from '../views/productLinkImport/index.vue'
import ProductSellApply from '../views/productSellApply/index.vue'
import AllCustomer from '../views/allCustomer/index.vue'
import MerchantHomeBg from '../views/merchantHomeBg/index.vue'
import CompanyInformation from '../views/companyInformation/index.vue'
import EligibilityApply from '../views/eligibilityApply/index.vue'
import AllOrders from '../views/allOrders/index.vue'
import NotPayOrders from '../views/notPayOrders/index.vue'
import DropShippingOrders from '../views/dropShippingOrders/index.vue'
import NotPendingOrders from '../views/notPendingOrders/index.vue'
import FinishOrders from '../views/finishOrders/index.vue'
import UnusualPay from '../views/unusualPay/index.vue'
import PayCallbackUnu from '../views/payCallbackUnu/index.vue'
import ReturnOrders from '../views/returnOrders/index.vue'
import Refunded from '../views/refunded/index.vue'
import Shipping from '../views/shipping/index.vue'
import Setting from '../views/setting/index.vue'
import ShippingDetail from '../views/shippingDetail/index.vue'
import FinanceSetting from '../views/financeSetting/index.vue'
import PlatformVoucherDetail from '../views/platformVoucherDetail/index.vue'
import PlatformCouponDetail from '../views/platformCouponDetail/index.vue'
import CommissionDetail from '../views/commissionDetail/index.vue'
import LoanDetail from '../views/loanDetail/index.vue'
import VerifyVoucherDetail from '../views/verifyVoucherDetail/index.vue'
import QuantifyEvolutionDetail from '../views/quantifyEvolutionDetail/index.vue'
import CreditEvolutionDetail from '../views/creditEvolutionDetail/index.vue'
import SettlementCount from '../views/SettlementCount/index.vue'
import SettlementRecord from '../views/settlementRecord/index.vue'
import SettlementReview from '../views/settlementReview/index.vue'
import Ad from '../views/ad/index.vue'
import AddAds from '../views/addAds/index.vue'
import AdPayment from '../views/adPayment/index.vue'
import Logout from '../views/logout/index.vue'
import CustomerServer from '../views/customerServer/index.vue'
import AllUser from '../views/allUser/index.vue'
import TechnicalDrainage from '../views/technicalDrainage/index.vue'
import PlatformAgreement from '../views/platformAgreement/index.vue'
import AuthorizationSettings from '../views/authorizationSettings/index.vue'
import SettlePayment from '../views/settlePayment/index.vue'
import PermissionSetting from '../views/permissionSetting/index.vue'
import ConsignmentLevelSetting from '../views/consignmentLevelSetting/index.vue'
import ConsignmentLevelManage from '../views/consignmentLevelManage/index.vue'
import AdvertisingSetting from '../views/advertisingSetting/index.vue'
import RelationshipChainSetting from '../views/relationshipChainSetting/index.vue'
import HeftSetting from '../views/heftSetting/index.vue'
import ProductNote from '../views/productNote/index.vue'
import BusinessSettledNote from '../views/businessSettledNote/index.vue'
import RealNameReview from '../views/realNameReview/index.vue'
import RefundNote from '../views/refundNote/index.vue'
import OrdersNote from '../views/ordersNote/index.vue'
import SettlementNote from '../views/settlementNote/index.vue'
import PromotionFundDetails from '../views/promotionFundDetails/index.vue'
import OfflineCustomer from '../views/offlineCustomer/index.vue'
import UserComponent from '../views/userComponent/index.vue'
import UserAutonymAudit from '../views/userAutonymAudit/index.vue'
import ApplyAuthorizationAudit from '../views/applyAuthorizationAudit/index.vue'
import attentionEnterprise from '../views/attentionEnterprise/index.vue'
import ConfigSetting from '../views/configSetting/index.vue'
//import MenuManager from '../views/menuManager/index.vue2.bak'
import MenuManager from '../views/menuManager/index.vue'

import constructMenu from '../views/constructMenu/index.vue'
import AllCustomertechnicalDrainage from '../views/allCustomertechnicalDrainage/index.vue'
import UserDdministration from '../views/userDdministration/index.vue'
import CreateUser from '../views/createUser/index.vue'
import RoleList from '../views/roleList/index.vue'
import Merchant from '../views/merchant/index.vue'
import MerchantCompany from '../views/merchantCompany/index.vue'
import InformationSettings from '../views/informationSettings/index.vue'
import MerchantOrder from '../views/merchantOrder/index.vue'
import MerchantNotPayOrders from '../views/MerchantNotPayOrders/index.vue'
import UpdateMenu from '../views/updateMenu/index.vue'
import UpdateRole from '../views/updateRole/index.vue'

import UpdateUser from '../views/updateUser/index.vue'
import CommodityReview from '../views/commodityReview/index.vue'
import CommodityAlreadyReview from '../views/commodityAlreadyReview/index.vue'

import OrderGeneration from '../views/OrderGeneration/index.vue'

import ViewHint from '../views/404/index.vue'
// import Province from '../views/province/index.vue2.bak'
import Index from '@/components/Layout.vue'
import ShopAudit from '../views/shopAudit/index.vue'
import ShopAuditRecord from '../views/shopAuditRecord/index.vue'
import AdminQuantifyEvolutionDetail from '../views/adminQuantifyEvolutionDetail/index.vue'
import AdminCreditEvolutionDetail from '../views/adminCreditEvolutionDetail/index.vue'
import LogoutAudit from '../views/logoutAudit/index.vue'
import LogoutRecord from '../views/logoutRecord/index.vue'
import MerchantService from '../views/merchantServer/index.vue'
import PlatformService from '../views/platformServer/index.vue'
import AgreementView from '../views/commodityAlreadyReview/agreementView.vue'
import MerchantPayment from '../views/merchantPayment/index.vue'
import StatisticsVirtualOrder from '../views/statisticsVirtualOrder/index.vue'
import SettingUserVirtualCash from '../views/settingUserVirtualCash/index.vue'

import {Session} from '../utils/storage';

const routes = [
    // 当访问根路径时，重定向到/home
    // {   path: '/',
    //     redirect: '/productCategory'
    // },
    {
        path: '/404',
        component: ViewHint
    },
    {
        path: '/',
        component: Login
    },
    {
        path: '/login',
        component: Login
    },
    {
        path: '/forgotPassword',
        component: ForgotPassword
    },
    {
        path: '/index',
        component: Index,
        children: [
            {
                path: '/systemSetting',
                component: SystemSetting
            },
            {
                path: '/platformManage',
                component: PlatformManage
            },
            {
                path: '/menuManager',
                component: MenuManager
            },
            {
                path: '/statistics',
                component: Statistics
            },
            {
                path: '/quantification',
                component: Quantification
            },
            {
                path: '/commodity',
                component: Commodity
            },
            {
                path: '/productList',
                component: ProductList
            },
            {
                path: '/productCategory',
                component: ProductCategory
            },
            {
                path: '/brandManage',
                component: BrandManage
            },
            {
                path: '/deliveryManage',
                component: DeliveryManage
            },
            {
                path: '/commentManage',
                component: CommentManage
            },
            {
                path: '/returnAddress',
                component: ReturnAddress
            },
            {
                path: '/commoditySales',
                component: CommoditySales
            },
            {
                path: '/productLink',
                component: ProductLink
            },
            {
                path: '/buildProductLink',
                component: BuildProductLink
            },
            {
                path: '/productLinkImport',
                component: ProductLinkImport
            },
            {
                path: '/productSellApply',
                component: ProductSellApply
            },
            // {
            //     path:'/province',
            //     component:Province
            // },
            {
                path: '/orderGeneration',
                component: OrderGeneration
            },
            {
                path: '/allOrders',
                component: AllOrders
            },
            {
                path: '/merchantHomeBg',
                component: MerchantHomeBg
            },
            {
                path: '/settlePayment',
                component: SettlePayment
            },
            {
                path: '/companyInformation',
                component: CompanyInformation
            },
            {
                path: '/allCustomer',
                component: AllCustomer
            },
            {
                path: '/customerServer',
                component: CustomerServer
            },
            {
                path: '/allUser',
                component: AllUser
            },
            {
                path: '/offlineCustomer',
                component: OfflineCustomer
            },
            {
                path: '/EligibilityApply',
                component: EligibilityApply
            },
            {
                path: '/financeSetting',
                component: FinanceSetting
            },
            {
                path: '/authorization',
                component: () => import('@/views/authorization/index.vue'),
                meta: {showSubMenu: true},
                redirect: '/authorization/bPower',
                children: [
                    {
                        path: 'bPower',
                        name: 'bPower',
                        component: () => import('@/views/bPower/index.vue'),
                        meta: {showSubMenu: true},
                        redirect: '/authorization/bPower/bProvince',
                        children: [
                            {
                                path: 'bProvince',
                                name: 'bProvince',
                                component: () => import('@/views/bProvince/index.vue')
                            },
                            {
                                path: 'bCity',
                                name: 'bCity',
                                component: () => import('@/views/bCity/index.vue')
                            },
                            {
                                path: 'bDistrict',
                                name: 'bDistrict',
                                component: () => import('@/views/bDistrict/index.vue')
                            },

                            {
                                path: 'bTown',
                                name: 'bTown',
                                component: () => import('@/views/bTown/index.vue')
                            }
                        ]
                    },
                    {
                        path: 'cPower',
                        name: 'cPower',
                        component: () => import('@/views/cPower/index.vue'),
                        meta: {showSubMenu: true},
                        redirect: '/authorization/cPower/province',
                        children: [
                            {
                                path: 'province',
                                name: 'province',
                                component: () => import('@/views/province/index.vue')
                            },
                            {
                                path: 'city',
                                name: 'city',
                                component: () => import('@/views/city/index.vue')
                            },
                            {
                                path: 'district',
                                name: 'district',
                                component: () => import('@/views/district/index.vue')
                            },

                            {
                                path: 'town',
                                name: 'town',
                                component: () => import('@/views/town/index.vue')
                            }
                        ]
                    }
                ]
            },
            {
                path: '/ad',
                component: Ad
            },
            {
                path: '/addAds',
                component: AddAds
            },
            {
                path: '/adPayment',
                component: AdPayment
            },
            {
                path: '/permissionSetting',
                component: PermissionSetting
            },
            {
                path: '/ConsignmentLevelSetting',
                component: ConsignmentLevelSetting
            },
            {
                path: '/consignmentLevelManage',
                component: ConsignmentLevelManage
            },
            {
                path: '/advertisingSetting',
                component: AdvertisingSetting
            },
            {
                path: '/shopAudit',
                component: ShopAudit
            },
            {
                path: '/shopAuditRecord',
                component: ShopAuditRecord
            },
            {
                path: '/promotionFundDetails',
                component: PromotionFundDetails
            },
            {
                path: '/offlineCustomer',
                component: OfflineCustomer
            },

            {
                path: '/allCustomertechnicalDrainage',
                component: AllCustomertechnicalDrainage
            },
            //菜单管理
            {
                path: '/constructMenu',
                component: constructMenu
            },
            {
                path: '/updateMenu',
                component: UpdateMenu
            },
            //end 菜单管理
            //角色管理
            {
                path: '/roleList',
                component: RoleList
            },
            {
                path: '/constructRole',
                component: ConstructRole
            },
            {
                path: '/updateRole',
                component: UpdateRole
            },
            //end 角色管理
            //用户管理
            {
                path: '/userDdministration',
                component: UserDdministration
            },
            {
                path: '/createUser',
                component: CreateUser
            },
            {
                path: '/updateUser',
                component: UpdateUser
            },
            //end 用户管理
            //商家管理
            {
                path: '/merchant',
                component: Merchant
            },
            {
                path: '/merchantCompany',
                component: MerchantCompany
            },
            {
                path: '/informationSettings',
                component: InformationSettings
            },
            // 订单
            {
                path: '/merchantOrder',
                component: MerchantOrder
            },
            {
                path: '/merchantNotPayOrders',
                component: MerchantNotPayOrders
            },
            //end 商家管理
            //商品审核
            {
                path: '/commodityReview',
                component: CommodityReview
            },
            //商品已审核
            {
                path: '/commodityAlreadyReview',
                component: CommodityAlreadyReview
            },
            {
                path: '/home',
                component: Home
            },
            {
                path: '/normalManager',
                component: NormalManager
            },
            {
                path: '/constructUser',
                component: ConstructUser
            },
            {
                path: '/constructCustomer',
                component: ConstructCustomer
            },
            {
                path: '/notPayOrders',
                component: NotPayOrders
            },
            {
                path: '/dropShippingOrders',
                component: DropShippingOrders
            },
            {
                path: '/notPendingOrders',
                component: NotPendingOrders
            },
            {
                path: '/finishOrders',
                component: FinishOrders
            },
            {
                path: '/unusualPay',
                component: UnusualPay
            },
            {
                path: '/payCallbackUnu',
                component: PayCallbackUnu
            },
            {
                path: '/returnOrders',
                component: ReturnOrders
            },
            {
                path: '/refunded',
                component: Refunded
            },
            {
                path: '/shipping',
                component: Shipping
            },
            {
                path: '/setting',
                component: Setting
            },
            {
                path: '/shippingDetail',
                component: ShippingDetail
            },
            {
                path: '/platformVoucherDetail',
                component: PlatformVoucherDetail
            },
            {
                path: '/platformCouponDetail',
                component: PlatformCouponDetail
            },
            {
                path: '/commissionDetail',
                component: CommissionDetail
            },
            {
                path: '/loanDetail',
                component: LoanDetail
            },
            {
                path: '/verifyVoucherDetail',
                component: VerifyVoucherDetail
            },
            {
                path: '/quantifyEvolutionDetail',
                component: QuantifyEvolutionDetail
            },
            {
                path: '/creditEvolutionDetail',
                component: CreditEvolutionDetail
            },
            {
                path: '/settlementCount',
                component: SettlementCount
            },
            {
                path: '/settlementRecord',
                component: SettlementRecord
            },
            {
                path: '/settlementReview',
                component: SettlementReview
            },
            {
                path: '/logout',
                component: Logout
            },
            {
                path: '/technicalDrainage',
                component: TechnicalDrainage
            },
            {
                path: '/platformAgreement',
                component: PlatformAgreement
            },
            {
                path: '/relationshipChainSetting',
                component: RelationshipChainSetting
            },
            {
                path: '/heftSetting',
                component: HeftSetting
            },
            {
                path: '/authorizationSettings',
                component: AuthorizationSettings
            },
            {
                path: '/productNote',
                component: ProductNote
            },
            {
                path: '/businessSettledNote',
                component: BusinessSettledNote
            },
            {
                path: '/realNameReview',
                component: RealNameReview
            },
            {
                path: '/refundNote',
                component: RefundNote
            },
            {
                path: '/ordersNote',
                component: OrdersNote
            },
            {
                path: '/settlementNote',
                component: SettlementNote
            },
            {
                path: '/adminQuantifyEvolutionDetail',
                component: AdminQuantifyEvolutionDetail
            },
            {
                path: '/adminCreditEvolutionDetail',
                component: AdminCreditEvolutionDetail
            },
            {
                path: '/logoutAudit',
                component: LogoutAudit
            },
            // 账户注销记录
            {
                path: '/logoutRecord',
                component: LogoutRecord
            },
            // 商家客服
            {
                path: '/merchantService',
                component: MerchantService
            },
            // 平台客服
            {
                path: '/platformService',
                component: PlatformService
            },
            // 平台客服
            {
                path: '/userComponent',
                component: UserComponent
            },
            // 平台客服
            {
                path: '/userAutonymAudit',
                component: UserAutonymAudit
            },
            {
                path: '/applyAuthorizationAudit',
                component: ApplyAuthorizationAudit
            },
            {
                path: '/merchantPayment',
                component: MerchantPayment
            },
            // 统计虚拟订单
            {
                path: '/statisticsVirtualOrder',
                component: StatisticsVirtualOrder
            },
            // 管理用户虚拟币
            {
                path: '/settingUserVirtualCash',
                component: SettingUserVirtualCash
            },
            // 关注企业
            {
                path: '/attentionEnterprise',
                component: attentionEnterprise
            },
            // 设置管理
            {
                path: '/configSetting',
                component: ConfigSetting
            }
        ]
    },
    // 协议查看页面
    {
        path: '/agreementView',
        component: AgreementView
    },
    // 不需要菜单的路由
    // 添加通配符路由，捕获所有未匹配的路径
    {
        path: '/:pathMatch(.*)*',
        redirect: '/404'
    }
]

const router = createRouter({
    routes,
    history: createWebHashHistory()
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
    // 检查是否是不需要菜单的页面或404页面
    const noMenuPages = ['/login', '/', '/404', '/forgotPassword'];
    if (noMenuPages.includes(to.path)) {
        next();
        return;
    }
    // 检查是否需要切换布局组件
    const currentMenu = Session.get('currentMenu');
    if (to.path == '/index') {
        // 是否是平台首页
        if (currentMenu == 'platform') {
            Session.remove('homeMenuId');
            // 商家首页
            if (Session.get("userType") == 2) {
                next('/merchantHomeBg');
            } else {
                next('/merchantHomeBg');
            }
        } else {
            // 加载已经有的平台菜单
            let menuList = Session.getMenu()
            for (let i = 0; i < menuList.length; i++) {
                const menu = menuList[i];
                if (menu.menuId != 1929813722135760898) {
                    Session.set('adminMenuId', menu.menuId);
                    next(menu.component);
                    return;
                }
            }
        }
    } else if (to.path === '/commodity' || to.path === '/platformManage' || (to.path !== '/menuManager' && currentMenu === 'platform') || (to.path !== '/merchantHomeBg' && currentMenu === 'platform')) {
        Session.set('currentMenu', 'platform');
    } else {
        Session.set('currentMenu', 'home');
    }
    next();
})

export default router
