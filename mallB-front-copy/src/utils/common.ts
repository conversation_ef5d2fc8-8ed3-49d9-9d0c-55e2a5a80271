import {COS_URL} from '../config/baseUrl'

/**
 * 获取完整的图片/视频URL
 * @param url 原始URL
 * @returns 完整的URL
 */
export const getImageUrl = (url: string): string => {
    if (!url) return '';

    // 如果已经包含完整URL，直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }

    // 添加前缀
    return `${COS_URL}/${url}`;
}

/**
 * 格式化日期
 * @param dateString 日期字符串或日期对象
 * @param format 格式化模板，默认为 yyyy-MM-dd HH:mm:ss
 * @returns 格式化后的日期字符串
 */
export function formatDate(dateString: string | Date | null | undefined, format: string = 'yyyy-MM-dd HH:mm:ss'): string {
    if (!dateString) {
        return '-';
    }

    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;

    if (isNaN(date.getTime())) {
        return '-';
    }

    const o: Record<string, number> = {
        'M+': date.getMonth() + 1, // 月份
        'd+': date.getDate(), // 日
        'H+': date.getHours(), // 小时
        'm+': date.getMinutes(), // 分
        's+': date.getSeconds(), // 秒
        'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
        'S': date.getMilliseconds() // 毫秒
    };

    if (/(y+)/.test(format)) {
        format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }

    for (const k in o) {
        if (new RegExp('(' + k + ')').test(format)) {
            format = format.replace(
                RegExp.$1,
                RegExp.$1.length === 1 ? String(o[k]) : ('00' + o[k]).substr(('' + o[k]).length)
            );
        }
    }

    return format;
}

/**
 * 格式化金额
 * @param amount 金额
 * @param decimals 小数位数，默认为2
 * @returns 格式化后的金额字符串
 */
export function formatAmount(amount: number | string | null | undefined, decimals: number = 2): string {
    if (amount === null || amount === undefined) {
        return '0.00';
    }

    const num = parseFloat(amount as string);
    if (isNaN(num)) {
        return '0.00';
    }

    return num.toFixed(decimals);
}

/**
 * 防抖函数
 * @param fn 要执行的函数
 * @param delay 延迟时间，单位毫秒，默认300ms
 * @returns 防抖处理后的函数
 */
export function debounce(fn: Function, delay: number = 300): Function {
    let timer: number | null = null;

    return function (this: any, ...args: any[]) {
        if (timer) {
            clearTimeout(timer);
        }

        timer = setTimeout(() => {
            fn.apply(this, args);
            timer = null;
        }, delay) as unknown as number;
    };
}

/**
 * 处理文件下载
 * @param res 响应对象
 * @param fileName 文件名，默认从响应头获取或使用默认名称
 */
export function handleDownload(res: any, fileName?: string): void {
    // 创建Blob对象
    const blob = new Blob([res], {
        type: res.type || 'application/octet-stream'
    });

    // 获取文件名
    let downloadFileName = fileName;

    // 如果没有提供文件名，尝试从响应头获取
    if (!downloadFileName && res.headers) {
        const contentDisposition = res.headers['content-disposition'];
        if (contentDisposition) {
            const match = contentDisposition.match(/filename=(.+)/);
            if (match && match[1]) {
                downloadFileName = decodeURIComponent(match[1].replace(/"/g, ''));
            }
        }
    }

    // 如果仍然没有文件名，使用默认名称
    if (!downloadFileName) {
        downloadFileName = `export_${new Date().getTime()}.xlsx`;
    }

    // 创建下载链接并触发下载
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = downloadFileName;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();

    // 清理
    setTimeout(() => {
        URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
    }, 100);
}
