/**
 * 统一错误处理工具函数
 * 用于提取后台返回的错误信息
 */

/**
 * 提取错误信息
 * @param error 错误对象
 * @param defaultMessage 默认错误信息
 * @returns 错误信息字符串
 */
export const getErrorMessage = (error: any, defaultMessage: string = '操作失败'): string => {
    if (!error) {
        return defaultMessage;
    }

    // 如果是字符串，直接返回
    if (typeof error === 'string') {
        return error;
    }

    // 如果是对象，按优先级提取错误信息
    if (typeof error === 'object') {
        // 优先使用 message 字段（后台标准返回）
        if (error.message) {
            return error.message;
        }

        // 其次使用 msg 字段（部分接口可能使用）
        if (error.msg) {
            return error.msg;
        }

        // 如果有 response.data.message（axios 错误格式）
        if (error.response && error.response.data && error.response.data.message) {
            return error.response.data.message;
        }

        // 如果有 response.data.msg
        if (error.response && error.response.data && error.response.data.msg) {
            return error.response.data.msg;
        }
    }

    return defaultMessage;
};
