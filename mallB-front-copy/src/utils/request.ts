import axios, {AxiosInstance, AxiosResponse, InternalAxiosRequestConfig} from 'axios';
import {Session} from './storage';
import qs from 'qs';
import {ElMessage} from 'element-plus';

/**
 * 创建并配置一个 Axios 实例对象
 */
const service: AxiosInstance = axios.create({
    baseURL: "/api",
    // baseURL:import.meta.env.VITE_API_BASE_URL, // 从环境变量读取
    timeout: 50000, // 全局超时时间
    paramsSerializer: {
        serialize: (params: any) => {
            return qs.stringify(params, {arrayFormat: 'repeat'});
        }
    }
});

/**
 * Axios请求拦截器，对请求进行处理
 * 1. 序列化get请求参数
 * 2. 统一增加Authorization和TENANT-ID请求头
 * 3. 自动适配单体、微服务架构不同的URL
 * @param config AxiosRequestConfig对象，包含请求配置信息
 */
service.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        // 统一增加Authorization请求头, skipToken 跳过增加token
        const token = Session.getToken();
        if (token && !config.headers?.skipToken) {
            config.headers[CommonHeaderEnum.AUTHORIZATION] = `Bearer ${token}`;
        }

        // 如果是导出请求，添加特殊标识
        if (config.responseType === 'blob') {
            config.headers['responseType'] = 'blob';
        }

        // 处理完毕，返回config对象
        return config;
    },
    (error) => {
        // 对请求错误进行处理
        ElMessage.error('请求发送失败');
        return Promise.reject(error);
    }
);

/**
 * 响应拦截器处理函数
 * @param response 响应结果
 * @returns 如果响应成功，则返回响应的data属性；否则，抛出错误或者执行其他操作
 */
const handleResponse = (response: AxiosResponse<any>) => {
    // 处理blob类型响应（文件下载）
    if (response.config.responseType === 'blob') {
        return response.data;
    }

    // 后端成功状态码为200，失败时可能是其他值
    if (response.data.code !== 200 && response.data.code !== 0) {
        const code = response.data.code;
        switch (code) {
            case 401:
                Session.clear(); // 清除浏览器全部临时缓存
                window.location.href = '/'; // 去登录页
                break;
            case 403:
                ElMessage.error('没有权限访问该资源');
                break;
            case 500:
                ElMessage.error(response.data.msg || '服务器内部错误');
                break;
            default:
                if (response.data.msg) {
                    ElMessage.error(response.data.msg);
                } else {
                    ElMessage.error('未知错误');
                }
        }
    }

    return response.data;
};

/**
 * 添加 Axios 的响应拦截器，用于全局响应结果处理
 */
service.interceptors.response.use(
    handleResponse,
    (error) => {
        // 处理HTTP错误状态码
        if (error.response) {
            const status = error.response.status;
            switch (status) {
                case 400:
                    ElMessage.error('请求错误');
                    break;
                case 401:
                    Session.clear(); // 清除浏览器全部临时缓存
                    window.location.href = '/'; // 去登录页
                    break;
                case 403:
                    ElMessage.error('没有权限访问该资源');
                    break;
                case 404:
                    ElMessage.error('请求的资源不存在');
                    break;
                case 500:
                    ElMessage.error('服务器内部错误');
                    break;
                default:
                    ElMessage.error(`未知错误: ${status}`);
            }
        } else if (error.request) {
            // 请求已发出但没有收到响应
            if (error.message.includes('timeout')) {
                ElMessage.error('请求超时，请稍后重试');
            } else {
                ElMessage.error('网络错误，请检查您的网络连接');
            }
        } else {
            // 请求配置有误
            ElMessage.error('请求配置错误');
        }

        return Promise.reject(error.response?.data || error);
    }
);

//常用header
export enum CommonHeaderEnum {
    'TENANT_ID' = 'TENANT-ID',
    'ENC_FLAG' = 'Enc-Flag',
    'AUTHORIZATION' = 'Authorization',
}

// 导出 axios 实例
export default service;
