<script setup>
import {computed, defineEmits, nextTick, onMounted, reactive, ref, watch} from 'vue'
import {ElMessage} from 'element-plus'
import {getUserPhone, resetOperationPwd, sendOperatePwdSmsCode} from '@/api/system/user'

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 计算属性，用于v-model绑定
const dialogVisible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
})

// 步骤控制
const activeStep = ref(1)

// 显示的手机号
const maskedPhone = ref('')
// 加载状态
const loading = ref(false)

// 表单数据
const formData = reactive({
    code: '', // 验证码
    operationPassword: '', // 新操作密码
    confirmPassword: '', // 确认操作密码
})

// 倒计时
const countdown = ref(0)
const countdownTimer = ref(null)

// 监听对话框显示状态，显示时获取用户手机号
watch(() => dialogVisible.value, (newVal) => {
    if (newVal) {
        fetchUserPhone()
    }
})

// 获取用户手机号
const fetchUserPhone = async () => {
    try {
        loading.value = true
        const res = await getUserPhone()
        if (res.code === 200) {
            // 处理手机号，显示部分隐藏的号码
            maskedPhone.value = res.data
        }
    } catch (error) {
        ElMessage.error('获取用户信息失败')
        dialogVisible.value = false
    } finally {
        loading.value = false
    }
}

// 组件挂载时获取手机号
onMounted(() => {
    if (dialogVisible.value) {
        nextTick(() => {
            fetchUserPhone()
        })
    }
})

// 获取验证码
const getCode = async () => {
    try {
        loading.value = true
        // 发送验证码请求
        const res = await sendOperatePwdSmsCode()
        // 开始倒计时
        countdown.value = 60
        countdownTimer.value = setInterval(() => {
            countdown.value--
            if (countdown.value <= 0) {
                clearInterval(countdownTimer.value)
            }
        }, 1000)

        if (res.code != 200){
            return
        }
        ElMessage.success('验证码已发送，请注意查收')
    } catch (error) {
        ElMessage.error(error.message || '验证码发送失败')
    } finally {
        loading.value = false
    }
}

// 验证验证码，进入下一步
const verifyCode = () => {
    if (!formData.code) {
        ElMessage.error('请输入验证码')
        return
    }

    // 进入下一步
    activeStep.value = 2
}

// 提交新密码
const submitNewPassword = async () => {
    if (!formData.operationPassword) {
        ElMessage.error('请输入新操作密码')
        return
    }

    if (!formData.confirmPassword) {
        ElMessage.error('请确认新操作密码')
        return
    }

    if (formData.operationPassword !== formData.confirmPassword) {
        ElMessage.error('两次输入的密码不一致')
        return
    }

    // 密码强度校验
    if (formData.operationPassword.length < 6) {
        ElMessage.error('操作密码长度不能小于6位')
        return
    }

    // 建议包含数字和字母的组合
    const passwordPattern = /^(?=.*[a-zA-Z])(?=.*\d).+$/
    if (!passwordPattern.test(formData.operationPassword)) {
        ElMessage.warning('建议密码包含数字和字母的组合，提高安全性')
        return
    }

    try {
        loading.value = true
        // 调用重置操作密码API
        await resetOperationPwd({
            code: formData.code,
            operationPassword: formData.operationPassword
        })

        ElMessage.success('操作密码重置成功')
        emit('success')
        dialogVisible.value = false
        resetForm()
    } catch (error) {
        ElMessage.error(error.message || '操作密码重置失败')
    } finally {
        loading.value = false
    }
}

// 重置表单
const resetForm = () => {
    formData.code = ''
    formData.operationPassword = ''
    formData.confirmPassword = ''
    activeStep.value = 1

    // 清除倒计时
    if (countdownTimer.value) {
        clearInterval(countdownTimer.value)
        countdown.value = 0
    }
}

// 关闭弹窗
const handleClose = () => {
    dialogVisible.value = false
    resetForm()
}
</script>

<template>
    <el-dialog
        v-model="dialogVisible"
        v-loading="loading"
        :close-on-click-modal="false"
        title="重置操作密码"
        width="500px"
        @close="handleClose"
    >
        <el-steps :active="activeStep" finish-status="success" simple>
            <el-step title="身份验证"></el-step>
            <el-step title="设置新密码"></el-step>
        </el-steps>

        <div class="step-content" style="margin-top: 20px;">
            <!-- 步骤1：身份验证 -->
            <div v-if="activeStep === 1">
                <el-form label-width="100px">
                    <el-form-item label="手机号码">
                        <div class="phone-display">{{ maskedPhone }}</div>
                    </el-form-item>
                    <el-form-item label="验证码">
                        <div class="code-container">
                            <el-input
                                v-model="formData.code"
                                placeholder="请输入验证码"
                                style="width: 60%;"
                            />
                            <el-button
                                :disabled="countdown > 0"
                                style="width: 40%; margin-left: 10px;"
                                type="primary"
                                @click="getCode"
                            >
                                {{ countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码' }}
                            </el-button>
                        </div>
                    </el-form-item>
                </el-form>

                <div class="step-actions">
                    <el-button @click="handleClose">取消</el-button>
                    <el-button type="primary" @click="verifyCode">下一步</el-button>
                </div>
            </div>

            <!-- 步骤2：设置新密码 -->
            <div v-else>
                <el-form label-width="100px">
                    <el-form-item label="新操作密码">
                        <el-input
                            v-model="formData.operationPassword"
                            placeholder="请输入新操作密码"
                            show-password
                            type="password"
                        />
                    </el-form-item>
                    <el-form-item label="确认密码">
                        <el-input
                            v-model="formData.confirmPassword"
                            placeholder="请再次输入新操作密码"
                            show-password
                            type="password"
                        />
                    </el-form-item>
                </el-form>

                <div class="step-actions">
                    <el-button @click="activeStep = 1">上一步</el-button>
                    <el-button type="primary" @click="submitNewPassword">确认重置</el-button>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<style scoped>
.code-container {
    display: flex;
    align-items: center;
}

.step-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

.phone-display {
    line-height: 40px;
    font-size: 16px;
}
</style>
