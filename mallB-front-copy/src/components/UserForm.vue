<template>
    <div class="user-form-container">
        <el-form
            ref="formRef"
            v-loading="loading"
            :model="formData"
            :rules="rules"
            label-width="100px"
            status-icon
        >
            <el-form-item label="用户名称" prop="userName">
                <div class="input-group">
                    <el-input
                        v-model="formData.userName"
                        class="form-input"
                        clearable
                        placeholder="请输入用户名称"
                    />
                </div>
            </el-form-item>

            <el-form-item label="用户昵称" prop="nickName">
                <div class="input-group">
                    <el-input
                        v-model="formData.nickName"
                        class="form-input"
                        clearable
                        placeholder="请输入用户昵称"
                    />
                </div>
            </el-form-item>

            <el-form-item label="手机号码" prop="phonenumber">
                <div class="input-group">
                    <el-input
                        v-model="formData.phonenumber"
                        class="form-input"
                        clearable
                        placeholder="请输入手机号码"
                    />
                </div>
            </el-form-item>

            <!-- 密码字段只在创建用户时显示 -->
            <el-form-item v-if="!isEdit" label="密码" prop="password">
                <div class="input-group">
                    <el-input
                        v-model="formData.password"
                        class="form-input"
                        clearable
                        placeholder="请输入密码"
                        show-password
                        type="password"
                    />
                </div>
            </el-form-item>

            <el-form-item label="岗位" prop="postName">
                <div class="input-group">
                    <el-input
                        v-model="formData.postName"
                        class="form-input"
                        clearable
                        placeholder="请输入岗位"
                    />
                </div>
            </el-form-item>

            <el-form-item label="工号" prop="jobNumber">
                <div class="input-group">
                    <el-input
                        v-model="formData.jobNumber"
                        :disabled="isEdit"
                        class="form-input"
                        placeholder="工号"
                    />
                </div>
            </el-form-item>
            <el-form-item label="授权" prop="menuIds">
                <div class="input-group">
                    <el-cascader
                        v-model="selectedMenuIds"
                        :options="menuOptions"
                        :props="cascaderProps"
                        clearable
                        filterable
                        placeholder="请选择权限"
                        style="width: 100%"
                        @change="handleMenuChange"
                    />
                </div>
            </el-form-item>
        </el-form>

        <div class="form-buttons">
            <el-button :loading="loading" class="save-button" type="primary" @click="submitForm">
                {{ isEdit ? '保存修改' : '创建用户' }}
            </el-button>
            <el-button @click="resetForm">重置</el-button>
            <el-button @click="cancel">取消</el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import {defineEmits, defineProps, reactive, ref, watch} from 'vue';
import type {FormInstance} from 'element-plus';

interface UserFormData {
    userId: string;
    userName: string;
    nickName: string;
    phonenumber: string;
    password?: string;
    postName: string;
    jobNumber: string;
    menuIds: (string | number)[];
}

const props = defineProps({
    isEdit: {
        type: Boolean,
        default: false
    },
    initialData: {
        type: Object as () => UserFormData,
        default: () => ({
            userId: '',
            userName: '',
            nickName: '',
            phonenumber: '',
            password: '',
            postName: '',
            jobNumber: '',
            menuIds: []
        })
    },
    menuOptions: {
        type: Array,
        default: () => []
    },
    loading: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['submit', 'cancel', 'reset']);

const formRef = ref<FormInstance | null>(null);
const selectedMenuIds = ref<(string | number)[]>([]);

// 表单数据
const formData = reactive<UserFormData>({
    userId: '',
    userName: '',
    nickName: '',
    phonenumber: '',
    password: '',
    postName: '',
    jobNumber: '',
    menuIds: []
});

// 级联选择器配置
const cascaderProps = {
    multiple: true
};

// 表单验证规则
const rules = {
    userName: [
        {required: true, message: '请输入用户名称', trigger: 'blur'},
        {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
    ],
    nickName: [
        {required: true, message: '请输入用户昵称', trigger: 'blur'}
    ],
    phonenumber: [
        {required: true, message: '请输入手机号码', trigger: 'blur'},
        {pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur'}
    ],
    password: [
        {required: !props.isEdit, message: '请输入密码', trigger: 'blur'},
        {min: 6, message: '密码长度不能少于6个字符', trigger: 'blur'}
    ]
};

// 监听初始数据变化
watch(() => props.initialData, (newVal) => {
    if (newVal) {
        Object.assign(formData, newVal);
        selectedMenuIds.value = newVal.menuIds;
    }
}, {deep: true, immediate: true});

// 处理菜单选择变化
const handleMenuChange = (value: (string | number)[]) => {
    formData.menuIds = value;
};

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return;

    try {
        const valid = await formRef.value.validate();
        if (valid) {
            // 处理菜单ID
            const menuList = []
            // 由于级联选择器设置了emitPath: false，menuIds已经是ID数组
            // 我们直接将这个数组传给后端
            for (let i = 0; i < selectedMenuIds.value.length; i++) {
                const item = selectedMenuIds.value[i];
                if (Array.isArray(item)) {
                    for (let j = 0; j < item.length; j++) {
                        menuList.push(item[j]);
                    }
                } else {
                    menuList.push(item);
                }
            }
            formData.menuIds = menuList

            // 创建一个新对象来提交，这样可以安全地处理可选字段
            const submitData = {...formData};

            // 如果是编辑模式且密码为空，删除密码字段
            if (props.isEdit && !submitData.password) {
                delete submitData.password;
            }

            emit('submit', submitData);
        }
    } catch (error) {
        console.error('表单验证失败:', error);
    }
};

// 重置表单
const resetForm = () => {
    if (formRef.value) {
        formRef.value.resetFields();

        // 如果是编辑模式，重新加载初始数据
        if (props.isEdit) {
            Object.assign(formData, props.initialData);
            selectedMenuIds.value = Array.isArray(props.initialData.menuIds) ? props.initialData.menuIds : [];
        } else {
            selectedMenuIds.value = [];
        }
    }

    emit('reset');
};

// 取消操作
const cancel = () => {
    emit('cancel');
};
</script>

<style lang="scss" scoped>
.user-form-container {
    width: 100%;
}

.input-group {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
}

.form-input {
    width: 100%;

    :deep(.el-input__wrapper) {
        height: 45px;
        font-size: 15px;
    }
}

.form-buttons {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    gap: 15px;
}

.save-button {
    background-color: #14097A;

    &:hover {
        background-color: #1a0da0;
    }

    &:active {
        background-color: #0f0657;
    }
}

:deep(.el-cascader) {
    width: 100%;
}
</style>
