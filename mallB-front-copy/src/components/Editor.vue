<template>
    <div ref="editorContainerRef" class="editor-wrapper">
        <!-- 上传进度提示 -->
        <div v-if="uploadLoading" class="upload-progress-overlay">
            <div class="upload-progress-content">
                <el-progress
                    :percentage="uploadProgress"
                    :stroke-width="8"
                    status="success"
                    :show-text="true"
                />
                <p class="upload-text">正在上传图片...</p>
            </div>
        </div>

        <div v-if="!editorError" class="editor-content">
            <Toolbar
                v-if="editorRef"
                :defaultConfig="toolbarConfig"
                :editor="editorRef"
                :mode="mode"
                style="border-bottom: 1px solid #ccc"
            />
            <Editor
                v-model="valueHtml"
                :defaultConfig="editorConfig"
                :mode="mode"
                :style="{ height: `${height}px`, 'overflow-y': 'hidden' }"
                @onBlur="handleBlur"
                @onCreated="handleCreated"
                @onDestroyed="handleDestroyed"
                @onError="handleError"
                @onFocus="handleFocus"
                @onMaxLength="handleMaxLength"
            />
        </div>
        <div v-else class="editor-error">
            <div class="error-message">
                <p>编辑器加载失败，请刷新页面重试</p>
                <button class="reload-btn" @click="reloadEditor">重新加载编辑器</button>
            </div>
            <textarea
                v-model="backupContent"
                :style="{ height: `${height}px` }"
                class="backup-textarea"
                placeholder="请输入内容..."
                @input="handleBackupInput"
            ></textarea>
        </div>
    </div>
</template>
<script>
import '@wangeditor/editor/dist/css/style.css' // 引入 css
import {nextTick, onBeforeUnmount, onMounted, ref, shallowRef, watch} from 'vue'
import {Editor, Toolbar} from '@wangeditor/editor-for-vue'
import {ElMessage} from 'element-plus';

// 生成UUID的函数
const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
};

const baseUrl = import.meta.env.VITE_IP;
// 上传相关
const RICH_UPLOAD_URL = baseUrl  + '/system/oss/richUpload'

// 使用 defineEmits 定义要触发的事件
export default {
    props: {
        modelValue: {
            type: String,
            default: ''
        },
        height: {
            type: [Number, String],
            default: 500
        }
    },
    emits: ['update:modelValue'],
    components: {Editor, Toolbar},
    setup(props, {emit}) {
        // 编辑器容器引用
        const editorContainerRef = ref(null)

        // 编辑器实例，必须用 shallowRef
        const editorRef = shallowRef()

        // 内容 HTML
        const valueHtml = ref('')

        // 备用纯文本内容
        const backupContent = ref('')

        // 编辑器是否已经初始化
        const isInitialized = ref(false)

        // 编辑器错误状态
        const editorError = ref(false)

        // 上传状态
        const uploadLoading = ref(false)
        const uploadProgress = ref(0)

        // 安全地设置内容
        const safeSetContent = (content) => {
            try {
                if (content === null || content === undefined) {
                    valueHtml.value = ''
                    backupContent.value = ''
                } else {
                    valueHtml.value = content
                    backupContent.value = content.replace(/<[^>]*>/g, '') // 简单移除HTML标签
                }
            } catch (error) {
                console.error('设置内容错误:', error)
                valueHtml.value = ''
                backupContent.value = ''
            }
        }

        // 初始化编辑器内容
        onMounted(() => {
            try {
                // 确保初始值不是null或undefined
                safeSetContent(props.modelValue)

                // 延迟标记初始化完成，避免初始化过程中的错误
                nextTick(() => {
                    isInitialized.value = true
                })
            } catch (error) {
                console.error('编辑器初始化错误:', error)
                editorError.value = true
                safeSetContent('')
            }
        })

        // 监听父组件传入的值变化
        watch(() => props.modelValue, (newValue) => {
            // 如果编辑器还没初始化完成，不进行更新
            if (!isInitialized.value) return

            try {
                // 避免循环更新
                if (newValue !== valueHtml.value) {
                    safeSetContent(newValue)
                }
            } catch (error) {
                console.error('编辑器更新内容错误:', error)
                editorError.value = true
            }
        })

        // 监听编辑器内容变化
        watch(() => valueHtml.value, (newValue) => {
            // 如果编辑器还没初始化完成，不触发更新事件
            if (!isInitialized.value) return

            try {
                // 避免传递null值给父组件
                emit('update:modelValue', newValue || '')
            } catch (error) {
                console.error('编辑器内容变更错误:', error)
            }
        })

        // 处理备用文本框输入
        const handleBackupInput = () => {
            emit('update:modelValue', backupContent.value || '')
        }

        const toolbarConfig = {
            excludeKeys: [
                // 排除插入视频、上传视频、网络图片、插入链接、插入表格、代码块
                'uploadVideo', 'codeBlock',
                'divider',
                'insertVideo',
                'group-video',
                'fontSize',
                'fontFamily',
                'lineHeight',
                'insertImage',
                'todo',
                'insertLink',
                'insertTable',
                'undo', 'redo',
                'fullScreen',
                // 默认字体
            ],
        }

        const editorConfig = {
            placeholder: '请输入内容...',
            autoFocus: false, // 避免自动聚焦可能引起的问题
            MENU_CONF: {
                uploadImage: {
                    fieldName: 'file',
                    maxFileSize: 1024 * 1024,
                    server: RICH_UPLOAD_URL,
                    allowedFileTypes: ['image/*'],
                    // 上传之前触发 - 添加图片类型和大小限制
                    onBeforeUpload: (data) => {
                        try {
                            const file = Object.values(data)[0];
                            // 检查文件大小是否超过1MB
                            const isLt1M = file.size / 1024 / 1024 < 1
                            if (!isLt1M) {
                                ElMessage.error('上传图片大小不能超过 1MB!')
                                return false
                            }

                            // 生成新的文件名：UUID + 原始后缀
                            const originalName = file.name
                            const lastDotIndex = originalName.lastIndexOf('.')
                            const extension = lastDotIndex > -1 ? originalName.substring(lastDotIndex) : ''
                            file.name = generateUUID() + extension

                            // 开始上传，显示 loading
                            uploadLoading.value = true
                            uploadProgress.value = 0
                            return data
                        } catch (error) {
                            console.error('文件预处理失败:', error)
                            ElMessage.error('文件处理失败，请刷新页面后重试')
                            uploadLoading.value = false
                            uploadProgress.value = 0
                            return false
                        }
                    },

                    // 上传成功回调
                    onSuccess: (file, insertFn) => {
                        uploadLoading.value = false
                        uploadProgress.value = 100
                        console.log('上传成功响应:', file)
                        ElMessage.success('图片上传成功！')
                    },

                    // 上传失败回调
                    onFailed: (file, error) => {
                        uploadLoading.value = false
                        uploadProgress.value = 0
                        ElMessage.error(error.message)
                    },

                    // 上传错误回调
                    onError: (file, error, progress) => {
                        // 是否包含错误信息
                        if (error.message.includes("maximum ")){
                            ElMessage.error("上传图片大小不能超过 1MB!")
                            return
                        }
                        uploadLoading.value = false
                        uploadProgress.value = 0
                        ElMessage.error(error.message)
                    },
                },
            },
            // 自定义处理粘贴内容，避免复杂格式导致的错误
            customPaste: (editor, event) => {
                try {
                    // 获取剪切板的文本
                    const text = event.clipboardData.getData('text/plain')
                    if (text) {
                        // 插入纯文本，避免复杂格式
                        editor.insertText(text)
                    }
                    // 阻止默认粘贴行为
                    event.preventDefault()
                    return false
                } catch (error) {
                    console.error('粘贴处理错误:', error)
                    // 出错时也阻止默认行为
                    event.preventDefault()
                    return false
                }
            }
        }

        // 安全销毁编辑器
        const safeDestroyEditor = () => {
            try {
                const editor = editorRef.value
                if (editor) {
                    // 清空历史记录
                    if (editor.history) {
                        editor.history.undos = []
                        editor.history.redos = []
                    }

                    // 重置选区
                    if (editor.selection) {
                        editor.selection = null
                    }

                    // 销毁编辑器
                    editor.destroy()
                    editorRef.value = null
                }
            } catch (error) {
                console.error('编辑器销毁错误:', error)
                editorRef.value = null
            }
        }

        // 组件销毁时，及时销毁编辑器
        onBeforeUnmount(() => {
            safeDestroyEditor()
        })

        const handleCreated = (editor) => {
            try {
                editorRef.value = editor // 记录 editor 实例，重要！

                // 清空历史记录，避免历史记录错误
                setTimeout(() => {
                    if (editor && editor.history) {
                        editor.history.undos = []
                        editor.history.redos = []
                    }
                }, 100)
            } catch (error) {
                console.error('编辑器创建错误:', error)
                editorError.value = true
            }
        }

        const handleError = (error) => {
            console.error('编辑器错误:', error)
            editorError.value = true

            try {
                safeDestroyEditor()
            } catch (e) {
                console.error('编辑器错误处理失败:', e)
            }
        }

        const handleDestroyed = () => {
            editorRef.value = null
        }

        const handleMaxLength = () => {
            console.warn('编辑器内容已达到最大长度')
        }

        const handleFocus = () => {
            // 聚焦时可以做一些处理
        }

        const handleBlur = () => {
            // 失焦时可以做一些处理
        }

        // 重新加载编辑器
        const reloadEditor = () => {
            try {
                // 先安全销毁现有编辑器
                safeDestroyEditor()

                // 重置错误状态
                editorError.value = false

                // 重新初始化
                nextTick(() => {
                    isInitialized.value = true
                    // 保留当前内容
                    const currentContent = backupContent.value
                    if (currentContent) {
                        emit('update:modelValue', currentContent)
                    }
                })
            } catch (error) {
                console.error('重新加载编辑器失败:', error)
                editorError.value = true
            }
        }

        return {
            editorContainerRef,
            editorRef,
            valueHtml,
            backupContent,
            editorError,
            mode: 'default', // 或 'simple'
            toolbarConfig,
            editorConfig,
            handleCreated,
            handleError,
            handleDestroyed,
            handleMaxLength,
            handleFocus,
            handleBlur,
            handleBackupInput,
            reloadEditor,
            uploadLoading,
            uploadProgress
        }
    },
}
</script>

<style scoped>
.editor-wrapper {
    position: relative;
    border: 1px solid #ccc;
}

.editor-content {
    position: relative;
}

/* 上传进度遮罩层 */
.upload-progress-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
    animation: fadeIn 0.3s ease-in-out;
}

.upload-progress-content {
    text-align: center;
    padding: 30px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 300px;
    border: 1px solid #e4e7ed;
    animation: slideIn 0.3s ease-out;
}

.upload-progress-content :deep(.el-progress) {
    margin-bottom: 15px;
}

.upload-text {
    margin: 0;
    color: #606266;
    font-size: 14px;
    font-weight: 500;
}

.editor-error {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.error-message {
    padding: 10px;
    background-color: #fff3f3;
    color: #f56c6c;
    text-align: center;
    border-bottom: 1px solid #ffccc7;
}

.reload-btn {
    margin-top: 8px;
    padding: 5px 15px;
    background-color: #409eff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.reload-btn:hover {
    background-color: #66b1ff;
}

.backup-textarea {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    border: none;
    resize: none;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
}

.backup-textarea:focus {
    outline: none;
    border: none;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .upload-progress-content {
        min-width: 250px;
        padding: 20px;
    }

    .upload-text {
        font-size: 13px;
    }
}
</style>
