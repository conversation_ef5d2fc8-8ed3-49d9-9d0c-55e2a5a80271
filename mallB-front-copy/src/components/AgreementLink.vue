<template>
    <el-link
        :type="type"
        :underline="underline"
        class="agreement-link"
        @click="viewAgreement"
    >
        {{ title }}
    </el-link>
</template>

<script setup>
import {useRouter} from 'vue-router'

const props = defineProps({
    // 协议标题，用于在查看页面中查找对应的协议
    title: {
        type: String,
        required: true
    },
    // 链接显示的文本，默认使用协议标题
    text: {
        type: String,
        default: ''
    },
    // 链接类型
    type: {
        type: String,
        default: 'primary'
    },
    // 是否显示下划线
    underline: {
        type: Boolean,
        default: true
    }
})

const router = useRouter()

// 跳转到协议查看页面
const viewAgreement = () => {
    router.push({
        path: '/agreementView',
        query: {title: props.title}
    })
}
</script>

<style scoped>
.agreement-link {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
}
</style>
