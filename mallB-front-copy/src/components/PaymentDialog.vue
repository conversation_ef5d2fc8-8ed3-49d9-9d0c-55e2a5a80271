<template>
    <el-dialog v-model="dialogVisible" width="1200" @close="handleClose">
        <template #title>
            <div class="dialog-title">
                本页面显示信息仅供参考
            </div>
        </template>
        <div class="dialog-content">
            <el-table :data="tableData" style="width: 100%">
                <el-table-column label="费用类型" prop="productName"></el-table-column>
                <el-table-column label="金额" prop="amount">
                    <template #default="scope">
                        <div>
                            {{ Number(scope.row.amount) - Number(scope.row.serviceFee) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="第三方支付手续费" prop="serviceFee"></el-table-column>
                <el-table-column label="订单编码" prop="orderNo"></el-table-column>
                <el-table-column label="备注" prop="remark"></el-table-column>
            </el-table>

            <!-- 核销方式选择 -->
            <div v-if="isShop" class="payment-methods">
                <h3>核销方式</h3>
                <el-radio-group v-model="selectedPaymentMethod">
                    <el-radio :label="5">平台促销金核销</el-radio>
                    <el-radio :label="2">其他核销方式</el-radio>
                </el-radio-group>
                <div v-if="selectedPaymentMethod === 5" class="shop-balance">
                    <p>当前平台促销金余额: {{ shopBalance ? shopBalance : 0 }} 元</p>
                </div>
            </div>

            <div class="cancel-order">
                <el-row :gutter="24">
                    <el-col :offset="6" :span="6">
                        <el-button @click="cancelOrder">取消订单</el-button>
                    </el-col>
                    <el-col :span="6"><p class="amount">金额 {{ orderAmount }}</p></el-col>
                    <el-col :span="6">
                        <el-button v-if="selectedPaymentMethod === 5 && isShop" type="primary" @click="handlePay">
                            平台促销金核销
                        </el-button>
                        <el-button v-else type="text">前往APP支付</el-button>
                    </el-col>
                </el-row>
            </div>
        </div>
    </el-dialog>

    <!-- 自定义密码输入对话框 -->
    <el-dialog
        v-model="showPasswordDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        append-to-body
        class="password-input-dialog"
        title="请输入操作密码"
        width="400px"
    >
        <div class="password-form">
            <el-input
                v-model="passwordInput"
                :autocomplete="false"
                autocomplete="off"
                clearable
                data-1p-ignore="true"
                data-form-type="other"
                data-lpignore="true"
                maxlength="20"
                placeholder="请输入操作密码"
                readonly
                show-password
                type="password"
                @blur="$event.target.setAttribute('readonly', true)"
                @focus="$event.target.removeAttribute('readonly')"
            />
            <div class="password-hint">密码长度必须介于 5 和 20 之间</div>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="cancelPasswordInput">取消</el-button>
                <el-button type="primary" @click="confirmPassword">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import {computed, defineEmits, defineProps, nextTick, ref, watch} from 'vue'
import {orderGeneration} from '../stores/orderGeneration'
import {Session} from '../utils/storage'
import {getPromotionBalance} from '../api/shop/index'
import {ElMessage} from 'element-plus'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    orderData: {
        type: Object,
        default: () => ({})
    }
})

const emit = defineEmits(['update:visible', 'cancel-success'])

// 对话框可见状态
const dialogVisible = ref(false)

// 订单数据
const tableData = ref([])
const orderAmount = ref(0)
const orderNo = ref('')

// 核销方式 (5: 商家抵扣金支付, 2: 支付宝支付)
const selectedPaymentMethod = ref(5)

// 商家抵扣金余额 (实际应该从API获取)
const shopBalance = ref(0)

// 判断当前用户是否是商家
const isShop = computed(() => {
    return Session.get('userType') === '2'
})

// 监听visible属性变化
watch(() => props.visible, (newValue) => {
    dialogVisible.value = newValue
    if (newValue && isShop.value) {
        // 如果是商家用户，获取商家抵扣金余额
        getShopBalance()
    }
})

// 监听dialogVisible变化
watch(dialogVisible, (newValue) => {
    emit('update:visible', newValue)
})

// 监听orderData变化
watch(() => props.orderData, (newValue) => {
    if (newValue && Object.keys(newValue).length > 0) {
        tableData.value = [newValue]
        orderAmount.value = Number(newValue.amount)
        orderNo.value = newValue.orderNo
    }
})

// 获取商家平台促销金余额
const getShopBalance = async () => {
    try {
        const response = await getPromotionBalance();
        if (response && response.code === 200) {
            shopBalance.value = response.data;
        } else {
            console.error('获取平台促销金余额失败:', response?.msg);
            ElMessage.error(response?.msg || '获取平台促销金余额失败');
        }
    } catch (error) {
        console.error('获取商家平台促销金余额失败:', error)
        ElMessage.error('获取商家平台促销金余额失败')
    }
}

// 显示密码输入对话框的状态
const showPasswordDialog = ref(false)
const passwordInput = ref('')

// 使用平台促销金核销
const handlePay = async () => {
    if (!orderNo.value) {
        ElMessage.warning('订单号不能为空')
        return
    }

    if (selectedPaymentMethod.value !== 5) {
        ElMessage.warning('请选择核销方式')
        return
    }

    // 显示自定义密码输入对话框
    showPasswordDialog.value = true
    passwordInput.value = ''

    // 延迟聚焦，避免自动填充
    await nextTick()
    setTimeout(() => {
        const input = document.querySelector('.password-input-dialog .el-input__inner')
        if (input) {
            input.removeAttribute('readonly')
            input.focus()
        }
    }, 100)
}

// 确认密码输入
const confirmPassword = async () => {
    if (!passwordInput.value) {
        ElMessage.warning('请输入操作密码')
        return
    }

    if (passwordInput.value.length < 5 || passwordInput.value.length > 20) {
        ElMessage.warning('用户密码长度必须介于 5 和 20 之间')
        return
    }

    try {
        // 调用商家平台促销金核销API
        const data = {
            orderNo: orderNo.value,
            password: passwordInput.value,
            // 5表示平台促销金核销
            payType: '5'
        }
        const response = await orderGeneration().Payment(data)
        if (response && response.code === 200) {
            ElMessage.success('平台促销金核销成功')
            showPasswordDialog.value = false
            dialogVisible.value = false
            emit('cancel-success') // 使用同样的事件通知支付成功
        }
    } catch (error) {
        console.error('平台促销金核销失败:', error)
        ElMessage.error('平台促销金核销失败，请稍后再试')
    }
}

// 取消密码输入
const cancelPasswordInput = () => {
    showPasswordDialog.value = false
    passwordInput.value = ''

}

// 取消订单
const cancelOrder = async () => {
    if (!orderNo.value) {
        ElMessage.warning('订单号不能为空')
        return
    }

    try {
        const result = await orderGeneration().GetcancelTheOrder(orderNo.value)
        if (result.code == 200) {
            ElMessage.success('取消订单成功')
            dialogVisible.value = false
            emit('cancel-success')
        } else {
            ElMessage.error(result.msg || '取消订单失败')
        }
    } catch (error) {
        console.error('取消订单失败:', error)
        ElMessage.error('取消订单失败，请稍后再试')
    }
}

// 关闭对话框
const handleClose = () => {
    dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.dialog-title {
    font-size: 16px;
    color: #f56c6c;
    font-weight: bold;
}

.dialog-content {
    padding: 15px 0;
}

.payment-methods {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f8f8;
    border-radius: 4px;

    h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 16px;
    }

    .shop-balance {
        margin-top: 10px;
        color: #409EFF;
        font-weight: bold;
    }
}

.cancel-order {
    margin-top: 20px;
    text-align: center;
}

.amount {
    font-size: 18px;
    font-weight: bold;
    color: #f56c6c;
    line-height: 40px;
}

/* 自定义密码输入对话框样式 */
.password-input-dialog {
    /* 确保对话框层级足够高 */
    z-index: 3000 !important;

    :deep(.el-dialog__body) {
        padding: 20px;
    }
}

.password-form {
    margin-bottom: 50px;

    .password-hint {
        font-size: 12px;
        color: #909399;
        margin-top: 8px;
        line-height: 1.4;
    }

    /* 防止浏览器自动填充 */
    :deep(.el-input__inner) {
        background-color: #fff !important;

        /* 防止自动填充下拉框遮挡 */
        &::-webkit-credentials-auto-fill-button {
            visibility: hidden;
            display: none !important;
            pointer-events: none;
            height: 0;
            width: 0;
            margin: 0;
        }

        /* 移除自动填充的背景色 */
        &:-webkit-autofill,
        &:-webkit-autofill:hover,
        &:-webkit-autofill:focus,
        &:-webkit-autofill:active {
            -webkit-box-shadow: 0 0 0 30px white inset !important;
            -webkit-text-fill-color: #606266 !important;
            transition: background-color 5000s ease-in-out 0s;
        }

        /* 禁用自动完成 */
        &[autocomplete="off"] {
            background-color: #fff !important;
        }
    }
}
</style>
