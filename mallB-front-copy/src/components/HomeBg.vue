<template>
    <div class="common-layout">
        <el-container>
            <el-aside class="aside" width="263px">
                <!-- 公司昵称 -->
                <div class="asideTitle">
                    <p class="name">
                        <span class="name">
                            中南惠
                        </span>
                    </p>
                </div>
                <!-- 菜单按钮 -->
                <div class="menu-container">
                    <div v-for="item in state.menuList" :key="item.id" class="menu-button" @click="toPath(item)">
                        {{ item.menuName }}
                    </div>
                </div>
            </el-aside>
            <el-container>
                <el-header>
                    <div class="header-content">
                        <div class="header-top">
                            <div class="header-actions">
                                {{ user?.phonenumber }}
                                <el-button class="logout-btn" type="danger" @click="handleLogout">
                                    <el-icon>
                                        <SwitchButton/>
                                    </el-icon>
                                    退出登录
                                </el-button>
                                <el-icon class="setting-icon" @click="handleToSetting">
                                    <Setting/>
                                </el-icon>
                            </div>
                        </div>
                        <!--                        <div class="header-line">-->
                        <!--                            &lt;!&ndash; 新增状态信息显示区域 &ndash;&gt;-->
                        <!--                            <div v-if="statusInfo.userCategory" class="status-info-container">-->
                        <!--                                <el-tag :type="statusInfo.status === '0' ? 'success' : 'danger'" class="status-tag">-->
                        <!--                                    状态: {{ statusInfo.status === '0' ? '正常' : '停用' }}-->
                        <!--                                </el-tag>-->
                        <!--                                <el-tag type="warning" class="jurisdiction-tag">-->
                        <!--                                    权限: {{ formatJurisdiction(statusInfo.jurisdiction) }}-->
                        <!--                                </el-tag>-->
                        <!--                            </div>-->
                        <!--                        </div>-->
                    </div>
                </el-header>
                <el-main>
                    <slot>

                    </slot>
                </el-main>
            </el-container>
        </el-container>
    </div>
</template>
<!-- lang="ts" setup-->
<script>
import {nextTick, onMounted, reactive} from 'vue'
import {useRouter} from 'vue-router'
import {Session} from '../utils/storage';
import {ElMessage, ElMessageBox} from 'element-plus';
import {logout} from '../api/login/index';

import {getStatusAndJurisdiction} from '@/api/user/userStatus'


export default {
    setup() {
        // 新增状态信息响应式对象
        const statusInfo = reactive({
            status: '',
            jurisdiction: '',
            userCategory: null
        })

        // 新增权限格式化方法
        const formatJurisdiction = (jurisdiction) => {
            const map = {'1': '权限1', '2': '权限2', '3': '权限3'}
            return map[jurisdiction] || '无权限'
        }

        const router = useRouter()
        const state = reactive({
            menuList: []
        })
        const user = Session.get('user');
        const handleToSetting = () => {
            router.push('/setting')
        };

        // 退出登录
        const handleLogout = () => {
            ElMessageBox.confirm('确定要退出登录吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // 调用退出登录API
                logout().then(() => {
                    ElMessage.success('退出登录成功');
                    // 清除所有会话数据
                    Session.clear();
                    // 跳转到登录页
                    router.push('/login');
                }).catch(error => {
                    console.error('退出登录失败:', error);
                    ElMessage.error('退出登录失败，请重试');
                });
            }).catch(() => {
                // 取消退出
            });
        };

        const toPath = (item) => {
            if (item.menuId == '1929813722135760898') {
                Session.set('currentMenu', 'platform');
                nextTick(() => {
                    // router.push('/commodity');
                    router.push('/platformManage');
                    //router.push('/platformManage');
                });
            } else if (item.menuType == 'M') {
                Session.set('currentMenu', 'home');
                router.push(item.component);
                Session.set('adminMenuId', item.menuId);
            }
            if (item.menuType == 'C') {
                Session.set('currentMenu', 'home');
                router.push(item.component);
                Session.set('adminMenuId', item.menuId);
            }
        }
        onMounted(() => {
            //获取菜单数据
            state.menuList = Session.get('menus1');
            // 新增：获取状态和权限信息
            getStatusAndJurisdiction().then(res => {
                if (res.code === 200) {
                    Object.assign(statusInfo, res.data)
                }
            })
        })
        return {
            user,
            state,
            statusInfo,
            formatJurisdiction,
            handleToSetting,
            handleLogout,
            toPath
        }
    }
}
</script>
<style lang="scss" scoped>
.common-layout {
    display: flex;
    height: 100vh;

    .aside {
        background: #fff;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2; /* 确保侧边栏在背景图上方 */

        .asideTitle {
            background-color: #D1D9F7;
            width: 263px;
            height: 204px;
            border-bottom: 5px solid #0525F7;

            .name {
                height: 234px;
                width: 100%;
                color: #0525F7;
                font-size: 80px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .CompanyName {
                margin-left: 45px;
                margin-top: 47px;
            }
        }

        .menu-container {
            width: 100%;
            padding: 30px;
            position: relative;
            z-index: 2;

            .menu-button1, .menu-button, .menu-button4 {
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                cursor: pointer;
                background-color: #3A5BDE;
                transition: all 0.3s;
            }

            .menu-button1 {
                margin-top: 0;
            }

            .menu-button4 {
                margin-top: 63px;
                border-radius: 15px;
            }

            .menu-button {
                margin-top: 63px;

                &:hover {
                    background: #2c4ac7;
                }

                &.active {
                    background: #D1D9F7;
                    color: #333;
                    font-weight: 500;
                }
            }
        }
    }

    .el-header {
        background: #fff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 0;
        display: block;
        height: 122px;
        position: relative;
        z-index: 2; /* 确保头部在背景图上方 */

        .header-content {
            display: flex;
            flex-direction: column;

            .header-top {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                padding: 10px 33px 10px 0;

                .header-actions {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                }

                .logout-btn {
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    gap: 5px;
                }

                .setting-icon {
                    font-size: 20px;
                    color: #606266;
                    cursor: pointer;

                    &:hover {
                        color: #3A5BDE;
                    }
                }
            }

            .header-line {
                /*width: 100%;
                height: 81px;
                background-color: #D2E0FB;*/
                width: 100%;
                height: 81px;
                background-color: #D2E0FB;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }

    .el-main {
        padding: 0;
    }
}

/* 新增状态信息样式 */
.status-info-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;

    .status-tag,
    .jurisdiction-tag {
        font-size: 14px;
        padding: 6px 12px;
        border-radius: 4px;
    }
}
</style>
