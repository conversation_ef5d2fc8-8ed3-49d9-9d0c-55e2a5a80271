<template>
    <component :is="currentLayout">
        <router-view></router-view>
    </component>
</template>
<script>
import {computed, onMounted, ref, watch} from 'vue'
import {useRoute} from 'vue-router'
import HomeBg from '@/components/HomeBg.vue'
import ManageBg from '@/components/ManageBg.vue'
import {Session} from '@/utils/storage'

export default {
    components: {
        HomeBg,
        ManageBg
    },
    setup() {
        const route = useRoute()
        const layoutType = ref(Session.get('currentMenu') || 'platform')

        // 监听路由变化
        watch(() => route.path, (newPath) => {
            const noMenuPages = ['/login', '/', '/404']
            if (noMenuPages.includes(newPath)) {
                return null
            }
            // 更新布局类型
            layoutType.value = Session.get('currentMenu') || 'platform'
        }, {immediate: true})

        // 创建一个定时器来检查 Session 中的值变化
        const checkSession = () => {
            const currentMenu = Session.get('currentMenu')
            if (currentMenu !== layoutType.value) {
                layoutType.value = currentMenu
            }
        }

        onMounted(() => {
            // route.push('/quantification')
            // 每 100ms 检查一次 Session 的变化
            const timer = setInterval(checkSession, 100)
            // 组件卸载时清除定时器
            return () => clearInterval(timer)
        })

        const currentLayout = computed(() => {
            return layoutType.value === 'platform' ? ManageBg : HomeBg
        })

        return {
            currentLayout
        }
    }
}
</script>

<style scoped>

</style>
