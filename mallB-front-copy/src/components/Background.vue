<template>
    <div class="background-container">
        <img alt="background" class="main-background" src="../images/bigBackground.png"/>
        <div class="content-wrapper">
            <slot></slot>
        </div>
    </div>
</template>

<script>

</script>

<style scoped>
.background-container {
    position: fixed;
    width: 100vw;
    height: 100vh;
    overflow: auto;
}

.main-background {
    position: fixed;
    object-fit: cover;
    z-index: 1;
}

.top-line {
    position: fixed;
    top: 87px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #fff;
    z-index: 2;

    @media (max-width: 768px) {
        top: 60px;
        height: 1px;
    }
}

.content-wrapper {
    position: relative;
    z-index: 3;
    min-height: 100vh;
}
</style>
