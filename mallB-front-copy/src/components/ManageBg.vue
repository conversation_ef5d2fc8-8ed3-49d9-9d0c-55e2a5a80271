<template>
    <div class="common-layout">
        <el-container>
            <el-aside class="aside" width="263px">
                <div v-if="userType == 1" class="asideTitle">
                    <p class="name">
                        <span class="name">
                            中南惠
                        </span>
                    </p>
                </div>
                <div v-if="userType == 2 || userType == 3" class="asideTitle">
                    <p class="businessName">
                        <el-image v-if="user.shopLogo" :src="getImageUrl(user.shopLogo)"
                                  style="height: 233px"></el-image>
                        <span v-else-if="user.shopName" class="businessName">
                            {{ user.businessName }}
                        </span>
                    </p>
                </div>
                <div class="menu-container">
                    <div v-for="item in state.menuList" :key="item.id" class="menu-button" @click="toPath(item)">
                        {{ item.menuName }}
                    </div>
                </div>
            </el-aside>
            <el-container>
                <el-header>
                    <div class="header-content">
                        <div class="header-top">
                            <div class="header-actions">
                                {{ user.phonenumber }}
                                <el-button class="logout-btn" type="danger" @click="handleLogout">
                                    <el-icon>
                                        <SwitchButton/>
                                    </el-icon>
                                    退出登录
                                </el-button>
                                <el-icon class="setting-icon" @click="handleToSetting">
                                    <Setting/>
                                </el-icon>
                            </div>
                        </div>
                        <div class="header-line">
                            <div class="header-buttons">
<!--                                <el-tag v-if="userType == 1" @click="handleToconstructCustomer">返回平台管理</el-tag>-->

                                <el-button style="width: 170px; font-size: 24px;  height: 50px;" type="primary" v-if="userType == 1" @click="handleToconstructCustomer">返回平台管理</el-button>

                            </div>
                            <!-- 新增状态信息显示区域 -->
                            <div v-if="statusInfo.userCategory && userType != 1" class="status-info-container">
                                <el-tag :type="statusInfo.status === '0' ? 'success' : 'danger'" class="status-tag">
                                    状态: {{ statusInfo.status === '0' ? '正常' : '停用' }}
                                </el-tag>
                                <el-tag class="jurisdiction-tag" type="warning">
                                    权限: {{ formatJurisdiction(statusInfo.jurisdiction) }}
                                </el-tag>
                            </div>
                        </div>
                    </div>
                </el-header>
                <el-main>
                    <slot></slot>
                </el-main>
            </el-container>
        </el-container>
    </div>
</template>

<script>
import {nextTick, onMounted, reactive} from 'vue'
import {useRouter} from 'vue-router'
import {Session} from '../utils/storage'
import {ElMessage, ElMessageBox} from 'element-plus'
import {logout} from '../api/login/index'
import {getImageUrl} from "@/utils/common"
import {getStatusAndJurisdiction} from '@/api/user/userStatus'

export default {
    setup() {
        // 新增状态信息响应式对象
        const statusInfo = reactive({
            status: '',
            jurisdiction: '',
            userCategory: null
        })

        // 新增权限格式化方法
        const formatJurisdiction = (jurisdiction) => {
            const map = {'1': '权限1', '2': '权限2', '3': '权限3'}
            return map[jurisdiction] || '无权限'
        }

        // 原有变量定义保持不变
        const user = Session.get('user')
        const router = useRouter()
        const state = reactive({
            menuList: []
        })
        const userType = Session.get('userType')

        // 原有方法保持不变
        const handleToSetting = () => {
            router.push('/setting')
        }

        const handleLogout = () => {
            ElMessageBox.confirm('确定要退出登录吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                logout().then(() => {
                    ElMessage.success('退出登录成功')
                    Session.clear()
                    router.push('/login')
                }).catch(error => {
                    console.error('退出登录失败:', error)
                    ElMessage.error('退出登录失败，请重试')
                })
            }).catch(() => {
                // 取消退出
            })
        }

        const handleToconstructCustomer = () => {
            Session.set('currentMenu', 'home')
            nextTick(() => {
                router.push('/menuManager')
            })
        }

        const toPath = (item) => {
            Session.set('currentMenu', 'platform')
            router.push(item.component)
            Session.set('homeMenuId', item.menuId)
        }

        onMounted(() => {
            state.menuList = Session.get('menus2')
            // 新增：获取状态和权限信息
            getStatusAndJurisdiction().then(res => {
                if (res.code === 200) {
                    Object.assign(statusInfo, res.data)
                }
            })
        })

        return {
            user,
            state,
            userType,
            statusInfo,
            formatJurisdiction,
            handleToSetting,
            handleLogout,
            handleToconstructCustomer,
            toPath,
            getImageUrl
        }
    }
}
</script>

<style lang="scss" scoped>
/* 原有样式保持不变 */
.common-layout {
    display: flex;
    height: 100vh;

    .aside {
        background: #fff;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;

        .asideTitle {
            background-color: #D1D9F7;
            width: 263px;
            border-bottom: 5px solid #0525F7;
            display: flex;
            justify-content: center;
            align-items: center;

            .name {
                height: 234px;
                width: 100%;
                color: #0525F7;
                font-size: 80px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .businessName {
                height: 234px;
                width: 100%;
                text-align: center;
                color: #0525F7;
                font-size: 50px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

        .menu-container {
            width: 100%;
            padding: 30px;
            position: relative;
            z-index: 2;

            .menu-button {
                font-size: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #3A58CF;
                padding: 12px 20px;
                border-radius: 15px;
                cursor: pointer;
                background-color: #fff;
                transition: all 0.3s;
                border: 1px solid #3A58CF;
                width: 153px;
                height: 65px;
                margin: 0 auto 15px;
                box-sizing: border-box;

                &:hover {
                    background-color: #f0f4ff;
                }
            }
        }
    }

    .el-header {
        background: #fff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 0;
        display: block;
        height: 122px;
        position: relative;
        z-index: 2;

        .header-content {
            display: flex;
            flex-direction: column;

            .header-top {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                padding: 10px 33px 10px 0;

                .header-actions {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                }

                .logout-btn {
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    gap: 5px;
                }

                .setting-icon {
                    font-size: 20px;
                    color: #606266;
                    cursor: pointer;

                    &:hover {
                        color: #3A5BDE;
                    }
                }
            }

            .header-line {
                /*width: 100%;
                height: 81px;
                background-color: #D2E0FB;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;*/
                width: 100%;
                height: 81px;
                background-color: #D2E0FB;
                display: flex;
                flex-direction: row;
                align-items: center;
                position: relative; /* 添加相对定位 */
                padding-left: 20px;
                padding-right: 20px;

            }
        }
    }

    .el-main {
        padding: 0;
        margin-top: 10px;
    }
}

.header-buttons {
    /*  margin-top: 26px;
      cursor: pointer;*/

    margin: 0;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    position: absolute; /* 保持绝对定位 */
    left: 20px; /* 固定在左侧 */

}

/* 新增状态信息样式 */
.status-info-container {
    display: flex;
    align-items: center;
    gap: 12px;
    position: absolute; /* 绝对定位 */
    left: 50%; /* 水平居中 */
    transform: translateX(-50%); /* 精确居中 */

    /* .status-tag,
     .jurisdiction-tag {
       font-size: 14px;
       padding: 6px 12px;
       border-radius: 4px;
     }*/

    .status-tag,
    .jurisdiction-tag {
        font-size: 14px;
        padding: 6px 12px;
        border-radius: 4px;
        white-space: nowrap; /* 防止文字换行 */
    }
}
</style>
