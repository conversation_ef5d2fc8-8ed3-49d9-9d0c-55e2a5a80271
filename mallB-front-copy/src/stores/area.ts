import {defineStore} from 'pinia';
import {getAreasByParentId, getCities, getDistricts, getProvinces, getTowns} from '../api/common/area';
import {ElMessage} from 'element-plus';

// 定义响应类型接口
interface ApiResponse<T> {
    code: number;
    msg: string;
    data?: T;
}

/**
 * 地区数据Store
 */
export const useAreaStore = defineStore('area', {
    state: () => ({
        provinces: [],
        cities: {},
        districts: {},
        towns: {},
        loading: false
    }),
    actions: {
        /**
         * 获取省份列表
         */
        async getProvinces() {
            if (this.provinces.length > 0) {
                return this.provinces;
            }

            this.loading = true;
            try {
                const response = await getProvinces();
                const res = response as ApiResponse<any>;
                if (res.code === 200) {
                    this.provinces = res.data || [];
                    return this.provinces;
                } else {
                    return [];
                }
            } catch (error) {
                return [];
            } finally {
                this.loading = false;
            }
        },

        /**
         * 获取城市列表
         * @param provinceCode 省份编码
         */
        async getCities(provinceCode) {
            if (this.cities[provinceCode]) {
                return this.cities[provinceCode];
            }

            this.loading = true;
            try {
                const response = await getCities(provinceCode);
                const res = response as ApiResponse<any>;
                if (res.code === 200) {
                    this.cities[provinceCode] = res.data || [];
                    return this.cities[provinceCode];
                } else {
                    ElMessage.error(res.msg || '获取城市数据失败');
                    return [];
                }
            } catch (error) {
                ElMessage.error('获取城市数据失败');
                return [];
            } finally {
                this.loading = false;
            }
        },

        /**
         * 获取区县列表
         * @param cityCode 城市编码
         */
        async getDistricts(cityCode) {
            if (this.districts[cityCode]) {
                return this.districts[cityCode];
            }

            this.loading = true;
            try {
                const response = await getDistricts(cityCode);
                const res = response as ApiResponse<any>;
                if (res.code === 200) {
                    this.districts[cityCode] = res.data || [];
                    return this.districts[cityCode];
                } else {
                    ElMessage.error(res.msg || '获取区县数据失败');
                    return [];
                }
            } catch (error) {
                ElMessage.error('获取区县数据失败');
                return [];
            } finally {
                this.loading = false;
            }
        },

        /**
         * 获取街道列表
         * @param districtCode 区县编码
         */
        async getTowns(districtCode) {
            if (this.towns[districtCode]) {
                return this.towns[districtCode];
            }

            this.loading = true;
            try {
                const response = await getTowns(districtCode);
                const res = response as ApiResponse<any>;
                if (res.code === 200) {
                    this.towns[districtCode] = res.data || [];
                    return this.towns[districtCode];
                } else {
                    ElMessage.error(res.msg || '获取街道数据失败');
                    return [];
                }
            } catch (error) {
                ElMessage.error('获取街道数据失败');
                return [];
            } finally {
                this.loading = false;
            }
        },

        /**
         * 获取街道列表
         */
        async GetAreasByParentId(pId) {
            this.loading = true;
            try {
                return await getAreasByParentId(pId)
            } catch (error) {
                ElMessage.error('获取街道数据失败');
                return [];
            } finally {
                this.loading = false;
            }
        }
    }
});
