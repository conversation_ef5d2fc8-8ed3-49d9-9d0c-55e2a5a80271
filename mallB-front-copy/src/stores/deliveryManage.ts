import {defineStore} from 'pinia';
import {getDeliveryManage} from '../api/deliveryManage/deliveryManage'

/**
 * @function deliveryManage
 * @returns {deliveryManage}
 */
export const deliveryManage = defineStore('deliveryManage', {
    state: () => ({}),
    actions: {
        /**
         * 获取列表数据
         */
        async GetDeliveryManage() {
            return new Promise((resolve, reject) => {
                getDeliveryManage().then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        }
    }
});
