import {getUserInfo} from '../api/userInfo/index';
import {defineStore} from 'pinia'

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const useUserInfo = defineStore('userInfo', {
    state: () => ({}),

    actions: {
        /**
         * 获取用户信息方法
         * @function setUserInfos
         * @async
         */
        async setUserInfos() {
            console.log('setUserInfos')
            await getUserInfo().then((res) => {
                console.log(res, 'res')
            });
        },
        async delCachedView() {
            console.log('setUserInfos')
        }
    }
});
