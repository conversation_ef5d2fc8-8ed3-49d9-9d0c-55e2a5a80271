import {defineStore} from 'pinia';
import {
    getMenuTreeList,
    getRoleInformation,
    getRoleList,
    getRoleUpdate,
    getTreeList,
    save
} from '../api/platformRole/index';

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const platformRole = defineStore('platformRole', {
    state: () => ({}),
    actions: {
        /**
         * 创建菜单方法
         * @function login
         * @async
         * @param {Object} data - 创建菜单数据
         * @returns {Promise<Object>}
         */
        async SaveRole(data: any) {
            return new Promise((resolve, reject) => {
                save(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 创建菜单树的方法
         * @function GetTreeList
         * @async
         * @param {Object} data - 创建菜单树的数据
         * @returns {Promise<Object>}
         */
        async GetTreeList(data: any) {
            return new Promise((resolve, reject) => {
                getTreeList(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        /**
         * 创建角色数据的方法
         * @function GetRoleList
         * @async
         * @param {Object} data - 创建角色的数据
         * @returns {Promise<Object>}
         */
        async GetRoleList(data: any) {
            return new Promise((resolve, reject) => {
                getRoleList().then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        /**
         * 创建角色信息的方法
         * @function getRoleInformation
         * @async
         * @param {Object} data - 创建角色信息的数据
         * @returns {Promise<Object>}
         */
        async GetRoleInformation(data: any) {
            return new Promise((resolve, reject) => {
                getRoleInformation(data).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },

        /**
         * 修改更新角色数据的方法
         * @function getRoleUpdate
         * @async
         * @param {Object} data - 创建角色的数据
         * @returns {Promise<Object>}
         */
        async GetRoleUpdate(data: any) {
            return new Promise((resolve, reject) => {
                getRoleUpdate(data).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },


        /**
         * 角色数据的方法
         * @function GetMenuTreeList
         * @async
         * @param {Object} data - 创建角色的数据
         * @returns {Promise<Object>}
         */
        async GetMenuTreeList() {
            return new Promise((resolve, reject) => {
                getMenuTreeList().then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        }

    },
});
