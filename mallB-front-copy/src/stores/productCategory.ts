import {defineStore} from 'pinia';
import {
    deleteCategory,
    getCategoryById,
    getCategoryList,
    getCategoryTree,
    getShopDefaultCategory,
    publishProduct,
    save,
    updateCategory
} from '../api/productCategory/productCategory';

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const productCategory = defineStore('productCategory', {
    state: () => ({}),
    actions: {
        /**
         * 创建菜单方法
         * @function SaveCategory
         * @async
         * @param {Object} data - 创建菜单数据
         * @returns {Promise<Object>}
         */
        async SaveCategory(data: any) {
            return new Promise((resolve, reject) => {
                save(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 查询用户数据的方法
         * @function GetUserList
         * @async
         * @param {Object} data - 用户数据
         * @returns {Promise<Object>}
         */
        async GetCategoryList(data) {
            return new Promise((resolve, reject) => {
                getCategoryList(data).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },

        /**
         * 查询用户数据的方法
         * @function GetUserList
         * @async
         * @param {Object} data - 用户数据
         * @returns {Promise<Object>}
         */
        async GetCategoryById(data: any) {
            return new Promise((resolve, reject) => {
                getCategoryById(data).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },

        /**
         * 查询商品分类树数据的方法
         * @function GetUserList
         * @async
         * @param {Object} data - 商品分类树数据
         * @returns {Promise<Object>}
         */
        async GetCategoryTree() {
            return new Promise((resolve, reject) => {
                getCategoryTree().then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        async GetShopDefaultCategory(){
            return new Promise((resolve, reject) => {
                getShopDefaultCategory().then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },

        /**
         * 修改商品分类数据的方法
         * @function GetUserList
         * @async
         * @returns {Promise<Object>}
         */
        async UpdateCategory(data) {
            return new Promise((resolve, reject) => {
                updateCategory(data).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        /**
         * 修改商品分类数据的方法
         * @function GetUserList
         * @async
         * @returns {Promise<Object>}
         */
        async DeleteCategory(id) {
            return new Promise((resolve, reject) => {
                deleteCategory(id).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },

        // 发布商品数据的方法
        // * @function GetUserList
        // * @async
        // * @returns {Promise<Object>}
        // */
        async PublishProduct(data) {
            return new Promise((resolve, reject) => {
                publishProduct(data).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        }
    }
});
