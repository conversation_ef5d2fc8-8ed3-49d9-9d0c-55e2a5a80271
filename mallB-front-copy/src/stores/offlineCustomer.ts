import {defineStore} from 'pinia';
import {getDrainageRecords, rechargeDrainage} from '../api/offlineCustomer/index';

/**
 * @function offlineCustomer
 * @returns {offlineCustomer}
 */
export const offlineCustomer = defineStore('offlineCustomer', {
    state: () => ({}),
    actions: {
        /**
         * 线下充值客户技术引流次数
         * @function RechargeDrainage
         * @async
         * @param {Object} data - 充值数据
         * @param {number} data.shopId - 商家ID
         * @param {number} data.count - 充值数量
         * @param {string} data.operationPassword - 操作密码
         * @param {string} data.remark - 备注信息
         * @returns {Promise<Object>}
         */
        async RechargeDrainage(data) {
            return new Promise((resolve, reject) => {
                rechargeDrainage(data).then((res) => {
                    resolve(res);
                }).catch((err) => {
                    reject(err);
                });
            });
        },

        /**
         * 查询线下充值技术引流次数记录
         * @function GetDrainageRecords
         * @async
         * @param {Object} params - 查询参数
         * @returns {Promise<Object>}
         */
        async GetDrainageRecords(params) {
            return new Promise((resolve, reject) => {
                getDrainageRecords(params).then((res) => {
                    resolve(res);
                }).catch((err) => {
                    reject(err);
                });
            });
        }
    }
});
