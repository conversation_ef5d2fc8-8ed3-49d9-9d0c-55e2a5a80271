import {defineStore} from 'pinia';
import {getConsignmentCount, getEditDaixiao, getProductById, updateProduct} from '../api/commodity/index';

/**
 * @function commodity
 * @returns {commodity}
 */
export const commodity = defineStore('commodity', {
    state: () => ({}),
    actions: {
        /**
         * 查询用户数据的方法
         * @function salesData
         * @async
         * @returns {Promise<Object>}
         */
        async GetsalesData() {
            return new Promise((resolve, reject) => {
                getConsignmentCount().then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        //保存代销
        async GetEditDaixiao(data) {
            return new Promise((resolve, reject) => {
                getEditDaixiao(data).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        // 获取商品详情
        async GetProductById(id) {
            return new Promise((resolve, reject) => {
                getProductById(id).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        // 更新商品信息
        async UpdateProduct(data) {
            return new Promise((resolve, reject) => {
                updateProduct(data).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        }
    },
});
