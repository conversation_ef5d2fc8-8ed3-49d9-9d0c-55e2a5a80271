import {defineStore} from 'pinia';
import {
    cancelTheOrder,
    createVirtualOrder,
    deleteOrder,
    getConsignmentPermissionInfo,
    getLoginVirtualOrder,
    payment
} from '../api/orderGeneration/index';
import {ElMessage} from 'element-plus';

// 定义接口类型
interface OrderParams {
    pageNum: number;
    pageSize: number;
    productName?: string;
    orderNo?: string;
    status?: string;
}

/**
 * @function orderGeneration
 * @returns {orderGeneration}
 */
export const orderGeneration = defineStore('orderGeneration', {
    state: () => ({}),
    actions: {
        /**
         * 查询订单数据信息
         * @function GetLoginVirtualOrder
         * @async
         * @param {OrderParams} data - 查询参数
         * @returns {Promise<Object>}
         */
        async GetLoginVirtualOrder(data: OrderParams) {
            return new Promise((resolve, reject) => {
                getLoginVirtualOrder(data).then((res) => {
                    // 存储数据信息,返回数据
                    resolve(res);
                }).catch((err) => {
                    ElMessage.error(err?.msg || '获取订单数据失败');
                    reject(err);
                });
            });
        },
        /**
         * 取消订单
         * @function GetcancelTheOrder
         * @async
         * @param {string} orderNo - 订单编号
         * @returns {Promise<Object>}
         */
        async GetcancelTheOrder(orderNo: string) {
            return new Promise((resolve, reject) => {
                cancelTheOrder(orderNo).then((res) => {
                    // 存储数据信息,返回数据
                    ElMessage.success('订单取消成功');
                    resolve(res);
                }).catch((err) => {
                    ElMessage.error(err?.msg || '取消订单失败');
                    reject(err);
                });
            });
        },
        /**
         * 删除订单
         * @function GetcancelTheOrder
         * @async
         * @param {string} orderNo - 订单编号
         * @returns {Promise<Object>}
         */
        async DeleteOrder(orderNo: string) {
            return new Promise((resolve, reject) => {
                deleteOrder(orderNo).then((res) => {
                    // 存储数据信息,返回数据
                    ElMessage.success('订单取消成功');
                    resolve(res);
                }).catch((err) => {
                    ElMessage.error(err?.msg || '取消订单失败');
                    reject(err);
                });
            });
        },
        /**
         * 删除订单
         * @function GetcancelTheOrder
         * @async
         * @param {string} orderNo - 订单编号
         * @returns {Promise<Object>}
         */
        async Payment(data) {
            return new Promise((resolve, reject) => {
                payment(data).then((res) => {
                    // 存储数据信息,返回数据
                    resolve(res);
                }).catch((err) => {
                    ElMessage.error(err?.msg || '支付失败');
                    reject(err);
                });
            });
        },

        /**
         * 获取代销权限信息
         * @function GetConsignmentPermissionInfo
         * @async
         * @returns {Promise<Object>}
         */
        async GetConsignmentPermissionInfo() {
            return new Promise((resolve, reject) => {
                getConsignmentPermissionInfo().then((res) => {
                    resolve(res);
                }).catch((err) => {
                    ElMessage.error(err?.msg || '获取权限信息失败');
                    reject(err);
                });
            });
        },

        /**
         * 创建虚拟订单
         * @function CreateVirtualOrder
         * @async
         * @param {Object} data - 订单数据
         * @returns {Promise<Object>}
         */
        async CreateVirtualOrder(data) {
            return new Promise((resolve, reject) => {
                createVirtualOrder(data).then((res) => {
                    resolve(res);
                }).catch((err) => {
                    ElMessage.error(err?.msg || '创建订单失败');
                    reject(err);
                });
            });
        }
    }
});
