import {defineStore} from 'pinia'
import {ref} from 'vue'
import {ElMessage} from 'element-plus'
import {
    freezeMerchantAmount,
    getMerchantByPhone,
    getShopFreezeRecords,
    unfreezeMerchantAmount
} from '../api/merchantPayment'

// 商家信息接口
interface Merchant {
    id: number;
    name: string;
    phone: string;
    balance: number;
}

// 冻结记录接口
interface FreezeRecord {
    id: number;
    merchantId: number;
    merchantName: string;
    freezeAmount: number;
    reason: string;
    freezeTime: string;
    status: 'frozen' | 'unfrozen';
}

export const useMerchantPaymentStore = defineStore('merchantPayment', () => {
    // 电话号码
    const phone = ref('')

    // 商家信息
    const merchantInfo = ref<Merchant | null>(null)

    // 冻结记录列表
    const freezeRecords = ref<FreezeRecord[]>([])

    // 分页信息
    const pagination = ref({
        total: 0,
        pageNum: 1,
        pageSize: 10
    })

    // 搜索条件
    const searchParams = ref({
        shopName: '',
        shopPhone: '',
        status: ''
    })

    // 加载状态
    const loading = ref(false)

    // 根据电话号码查询商家
    const searchMerchantByPhone = async (phone: string) => {
        loading.value = true
        try {
            const response = await getMerchantByPhone(phone)

            if (response.code === 200 && response.data) {
                merchantInfo.value = response.data
                return true
            } else {
                return false
            }
        } catch (error) {
            console.error('查询商家信息出错:', error)
            ElMessage.error('查询商家信息失败')
            return false
        } finally {
            loading.value = false
        }
    }

    // 获取冻结记录（支持分页和搜索）
    const getFreezeRecords = async (params?: any) => {
        loading.value = true
        try {
            const requestParams = {
                ...searchParams.value,
                pageNum: pagination.value.pageNum,
                pageSize: pagination.value.pageSize,
                ...params
            }

            const response = await getShopFreezeRecords(requestParams)
            console.log("response", response)

            if (response.code === 200) {
                freezeRecords.value = response.rows || []
                pagination.value.total = response.total || 0
                return true
            } else {
                return false
            }
        } catch (error) {
            console.error('获取冻结记录出错:', error)
            ElMessage.error('获取冻结记录失败')
            return false
        } finally {
            loading.value = false
        }
    }

    // 冻结货款
    const freezeAmount = async (data: { shopId: number, amount: number, reason: string }) => {
        loading.value = true
        try {
            const response = await freezeMerchantAmount(data)

            if (response.code === 200) {
                ElMessage.success('货款冻结成功')
                // 刷新冻结记录
                await getFreezeRecords()
                return true
            } else {
                return false
            }
        } catch (error) {
            console.error('冻结货款出错:', error)
            ElMessage.error('冻结货款失败')
            return false
        } finally {
            loading.value = false
        }
    }

    // 解冻货款
    const unfreezeAmount = async (freezeId: number) => {
        loading.value = true
        try {
            const response = await unfreezeMerchantAmount(freezeId)

            if (response.code === 200) {
                ElMessage.success('货款解冻成功')
                // 刷新当前搜索结果
                await getFreezeRecords()
                return true
            } else {
                return false
            }
        } catch (error) {
            console.error('解冻货款出错:', error)
            ElMessage.error('解冻货款失败')
            return false
        } finally {
            loading.value = false
        }
    }

    // 设置搜索条件
    const setSearchParams = (params: any) => {
        searchParams.value = {...searchParams.value, ...params}
    }

    // 重置搜索条件
    const resetSearchParams = () => {
        searchParams.value = {
            shopName: '',
            shopPhone: '',
            status: ''
        }
        pagination.value.pageNum = 1
    }

    // 设置分页
    const setPagination = (pageNum: number, pageSize?: number) => {
        pagination.value.pageNum = pageNum
        if (pageSize) {
            pagination.value.pageSize = pageSize
        }
    }

    return {
        phone,
        merchantInfo,
        freezeRecords,
        pagination,
        searchParams,
        loading,
        searchMerchantByPhone,
        getFreezeRecords,
        freezeAmount,
        unfreezeAmount,
        setSearchParams,
        resetSearchParams,
        setPagination
    }
})
