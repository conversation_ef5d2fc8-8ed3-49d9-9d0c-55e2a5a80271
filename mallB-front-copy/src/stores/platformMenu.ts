import {defineStore} from 'pinia';
import {
    getDeleteMenu,
    getIndependenceMenu,
    getMenuList,
    getTreeList,
    save,
    updateMenu
} from '../api/platformMenu/index';

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const platformMenu = defineStore('platformMenu', {
    state: () => ({}),
    actions: {
        /**
         * 创建菜单方法
         * @function login
         * @async
         * @param {Object} data - 创建菜单数据
         * @returns {Promise<Object>}
         */
        async SaveMenu(data: any) {
            return new Promise((resolve, reject) => {
                save(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 创建菜单方法
         * @function GetMenuList
         * @async
         * @param {Object} data - 创建菜单数据
         * @returns {Promise<Object>}
         */
        async GetMenuList(data: any) {
            return new Promise((resolve, reject) => {
                getMenuList(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                }).catch((err) => {
                    // useMessage().error(err?.msg || '系统异常请联系管理员');
                    // reject(err);
                });
            });
        },

        /**
         * 创建菜单树的方法
         * @function GetTreeList
         * @async
         * @param {Object} data - 创建菜单树的数据
         * @returns {Promise<Object>}
         */
        async GetTreeList(data: any) {
            return new Promise((resolve, reject) => {
                getTreeList(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                }).catch((err) => {

                });
            });
        },


        //修改菜单功能
        /**
         * @function GetIndependenceMenu
         * @async
         * @param {Object} data - 修改菜单树的数据
         * @returns {Promise<Object>}
         */
        async GetIndependenceMenu(data: any) {
            return new Promise((resolve, reject) => {
                getIndependenceMenu(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        /**
         * 更新菜单的方法
         *
         * @function UpdataData
         * @async
         * @param {Object} data - 更新菜单的数据
         * @returns {Promise<Object>}
         */
        async UpdataData(data: any) {
            return new Promise((resolve, reject) => {
                console.log(data, 'data')
                updateMenu(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        //修改菜单功能
        /**
         * @function GetIndependenceMenu
         * @async
         * @param {Object} data - 修改菜单树的数据
         * @returns {Promise<Object>}
         */
        async deleteMenu(data: any) {
            return new Promise((resolve, reject) => {
                getDeleteMenu(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
    },
});
