import {defineStore} from 'pinia';
import {getCode, getUserInfo, getUserMessage, login} from '../api/login/index';
import {useMessage} from '../hooks/message';

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const useUserInfoLogin = defineStore('userInfo', {
    state: () => ({}),
    actions: {
        /**
         * 获取验证码
         */
        async getCode(randomStr) {
            return new Promise((resolve, reject) => {
                getCode(randomStr).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        useMessage().error(err?.msg || '系统异常请联系管理员');
                        reject(err);
                    });
            });
        },
        /**
         * 登录方法
         * @function login
         * @async
         * @param {Object} data - 登录数据
         * @returns {Promise<Object>}
         */
        async login(data: any) {
            return new Promise((resolve, reject) => {
                login(data).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        useMessage().error(err?.msg || '系统异常请联系管理员');
                        reject(err);
                    });
            });
        },
        /**
         * 获取用户菜单数据
         */
        async getUserMessage(type) {
            return new Promise((resolve, reject) => {
                getUserMessage(type).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        useMessage().error(err?.msg || '系统异常请联系管理员');
                        reject(err);
                    });
            });
        },
        /**
         * 获取用户菜单数据
         */
        async GetUserInfo() {
            return new Promise((resolve, reject) => {
                getUserInfo().then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        useMessage().error(err?.msg || '系统异常请联系管理员');
                        reject(err);
                    });
            });
        },
    },
});
