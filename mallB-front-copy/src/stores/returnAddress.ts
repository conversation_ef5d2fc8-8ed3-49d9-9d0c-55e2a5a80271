import {defineStore} from 'pinia';
import {
    addReturnAddress,
    deleteReturnAddress,
    getReturnAddressDetail,
    getReturnAddressList,
    setDefaultReturnAddress,
    updateReturnAddress
} from '../api/returnOrders/index';
import {ElMessage} from 'element-plus';

// 定义响应类型接口
interface ApiResponse<T> {
    code: number;
    msg: string;
    rows?: T[];
    total?: number;
    data?: T;
}

/**
 * 退货地址管理Store
 */
export const useReturnAddressStore = defineStore('returnAddress', {
    state: () => ({
        addressList: [],
        currentAddress: null,
        loading: false,
        pagination: {
            total: 0,
            pageNum: 1,
            pageSize: 10
        },
        searchParams: {
            receiverName: '',
            receiverPhone: '',
            isDefault: ''
        }
    }),
    actions: {
        /**
         * 获取退货地址列表
         */
        async getAddressList() {
            this.loading = true;
            try {
                const params = {
                    pageNum: this.pagination.pageNum,
                    pageSize: this.pagination.pageSize,
                    ...this.searchParams
                };

                const res = await getReturnAddressList(params);
                if (res.code === 200) {
                    this.addressList = res.rows || [];
                    this.pagination.total = res.total || 0;
                    return res.rows;
                } else {
                    return [];
                }
            } catch (error) {
                return [];
            } finally {
                this.loading = false;
            }
        },

        /**
         * 设置分页信息
         */
        setPagination(pageInfo) {
            this.pagination = {
                ...this.pagination,
                ...pageInfo
            };
        },

        /**
         * 设置搜索参数
         */
        setSearchParams(params) {
            this.searchParams = {
                ...this.searchParams,
                ...params
            };
        },

        /**
         * 重置搜索参数
         */
        resetSearchParams() {
            this.searchParams = {
                receiverName: '',
                receiverPhone: '',
                isDefault: ''
            };
            this.pagination.pageNum = 1;
        },

        /**
         * 获取退货地址详情
         * @param id 地址ID
         */
        async getAddressDetail(id) {
            this.loading = true;
            try {
                const res = await getReturnAddressDetail(id);
                if (res.code === 200) {
                    this.currentAddress = res.data;
                    return res.data;
                } else {
                    return null;
                }
            } catch (error) {
                return null;
            } finally {
                this.loading = false;
            }
        },

        /**
         * 添加退货地址
         * @param address 退货地址数据
         */
        async addAddress(address) {
            this.loading = true;
            try {
                const res = await addReturnAddress(address);
                if (res.code === 200) {
                    ElMessage.success('添加退货地址成功');
                    return true;
                } else {
                    return false;
                }
            } catch (error) {
                return false;
            } finally {
                this.loading = false;
            }
        },

        /**
         * 更新退货地址
         * @param address 退货地址数据
         */
        async updateAddress(address) {
            this.loading = true;
            try {
                const res = await updateReturnAddress(address);
                if (res.code === 200) {
                    ElMessage.success('更新退货地址成功');
                    return true;
                } else {
                    return false;
                }
            } catch (error) {
                return false;
            } finally {
                this.loading = false;
            }
        },

        /**
         * 删除退货地址
         * @param id 地址ID
         */
        async deleteAddress(id) {
            this.loading = true;
            try {
                const res = await deleteReturnAddress(id);
                if (res.code === 200) {
                    ElMessage.success('删除退货地址成功');
                    return true;
                } else {
                    return false;
                }
            } catch (error) {
                return false;
            } finally {
                this.loading = false;
            }
        },

        /**
         * 设置默认退货地址
         * @param id 地址ID
         */
        async setDefaultAddress(id) {
            this.loading = true;
            try {
                const res = await setDefaultReturnAddress(id);
                if (res.code === 200) {
                    ElMessage.success('设置默认退货地址成功');
                    return true;
                } else {
                    return false;
                }
            } catch (error) {
                return false;
            } finally {
                this.loading = false;
            }
        }
    }
});
