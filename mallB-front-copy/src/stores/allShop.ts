import {defineStore} from 'pinia';
import {disableEnableFun, exportShopData, getShopDetail, getShopList} from '../api/allCustomer/index';

/**
 * @function allShop
 * @returns {allShop}
 */
export const allShop = defineStore('allShop', {
    state: () => ({}),
    actions: {
        /**
         * 查询店铺数据信息
         * @function GetShopList
         * @async
         * @returns {Promise<Object>}
         */
        async GetShopList(data) {
            return new Promise((resolve, reject) => {
                getShopList(data).then((res) => {
                    // 存储数据信息
                    resolve(res);
                }).catch((err) => {
                    // useMessage().error(err?.msg || '系统异常请联系管理员');
                    reject(err);
                });
            });
        },
        /**
         * 禁用/启用店铺
         * @function DisableEnableFun
         * @async
         * @returns {Promise<Object>}
         */
        async DisableEnableFun(data) {
            return new Promise((resolve, reject) => {
                disableEnableFun(data).then((res) => {
                    // 存储数据信息
                    resolve(res);
                }).catch((err) => {
                    // useMessage().error(err?.msg || '系统异常请联系管理员');
                    reject(err);
                });
            });
        },
        /**
         * 获取商家详情
         * @function GetShopDetail
         * @async
         * @param {number} id 商家ID
         * @returns {Promise<Object>}
         */
        async GetShopDetail(id) {
            return new Promise((resolve, reject) => {
                getShopDetail(id).then((res) => {
                    resolve(res);
                }).catch((err) => {
                    reject(err);
                });
            });
        },
        /**
         * 导出商家数据
         * @function ExportShopData
         * @async
         * @param {Object} data 查询参数
         * @returns {Promise<Blob>}
         */
        async ExportShopData(data) {
            return new Promise((resolve, reject) => {
                exportShopData(data).then((res) => {
                    resolve(res);
                }).catch((err) => {
                    reject(err);
                });
            });
        }
    }
});
