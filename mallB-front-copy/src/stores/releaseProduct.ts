import {defineStore} from 'pinia';
import {getCategoryByParentId} from '../api/releaseProduct/releaseProduct';

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const releaseProduct = defineStore('releaseProduct', {
    state: () => ({}),
    actions: {
        /**
         * 查询用户数据的方法
         * @function GetCategoryByParentId
         * @async
         * @param {Object} data - 用户数据
         * @returns {Promise<Object>}
         */
        async GetCategoryByParentId(data: any) {
            return new Promise((resolve, reject) => {
                getCategoryByParentId(data).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        }
    },
});
