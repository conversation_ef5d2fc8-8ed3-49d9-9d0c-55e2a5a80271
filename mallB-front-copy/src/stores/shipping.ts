import {defineStore} from 'pinia';
import {
    createDrainageOrder,
    deliveryCompany,
    donatePromotionGold,
    getDonationRecords,
    getDrainageCount,
    getShopHomeInfo,
    getShopInfo,
    saveShopBankAccount,
    shipmentOder,
    updateShop,
    updateShopBankAccount
} from '../api/shipping/shipping';

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const shipping = defineStore('shipping', {
    state: () => ({}),
    actions: {
        /**
         * 查收方法
         * @function shipping
         * @async
         * @param {Object} data - 创建快递公司数据
         * @returns {Promise<Object>}
         */
        async DeliveryCompany(data: any) {
            return new Promise((resolve, reject) => {
                deliveryCompany(data).then((res) => {
                    // 存储数据信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 查收方法
         * @function shipping
         * @async
         * @param {Object} data - 创建快递公司数据
         * @returns {Promise<Object>}
         */
        async ShipmentOder(data: any) {
            return new Promise((resolve, reject) => {
                shipmentOder(data).then((res) => {
                    // 存储数据信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 获取商家的技术引流次数
         */
        async GetDrainageCount() {
            return new Promise((resolve, reject) => {
                getDrainageCount().then((res) => {
                    // 存储数据信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 获取商家的技术引流次数
         */
        async CreateDrainageOrder(data) {
            return new Promise((resolve, reject) => {
                createDrainageOrder(data).then((res) => {
                    // 存储数据信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 获取商家的首页信息
         */
        async GetShopHomeInfo() {
            return new Promise((resolve, reject) => {
                getShopHomeInfo().then((res) => {
                    // 存储数据信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 获取商家信息
         */
        async GetShopInfo() {
            return new Promise((resolve, reject) => {
                getShopInfo().then((res) => {// 存储数据信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 保存银行卡信息
         */
        async SaveShopBankAccount(data) {
            return new Promise((resolve, reject) => {
                saveShopBankAccount(data).then((res) => {// 存储数据信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 修改银行卡信息
         */
        async UpdateShopBankAccount(data) {
            return new Promise((resolve, reject) => {
                updateShopBankAccount(data).then((res) => {// 存储数据信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 修改店铺信息
         */
        async UpdateShop(data) {
            return new Promise((resolve, reject) => {
                updateShop(data).then((res) => {// 存储数据信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 商家平台促销金赠送给用户
         */
        async DonatePromotionGold(data) {
            return new Promise((resolve, reject) => {
                donatePromotionGold(data).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        /**
         * 查询商家赠送记录
         */
        async GetDonationRecords(params) {
            return new Promise((resolve, reject) => {
                getDonationRecords(params).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        reject(err);
                    });
            });
        }
    }
});
