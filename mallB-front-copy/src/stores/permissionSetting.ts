import {defineStore} from 'pinia';
import {configurationData, getThroughList} from '../api/permissionSetting/index';

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const permissionSetting = defineStore('permissionSetting', {
    state: () => ({}),
    actions: {
        /**
         * 权限配置
         * @function permissionSetting
         * @async
         * @param {Object} data -
         * @param index
         * @returns {Promise<Object>}
         */
        async GetThroughList(data: any, index) {
            return new Promise((resolve, reject) => {
                getThroughList(data, index).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 查询权限配置信息
         * @function permissionSetting
         * @async
         * @returns {Promise<Object>}
         */
        async ConfigurationData(index) {
            return new Promise((resolve, reject) => {
                configurationData(index).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        }
    }
});
