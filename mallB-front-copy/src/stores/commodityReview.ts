import {defineStore} from 'pinia';
import {batchAudit, getThroughList} from '../api/commodityReview/index';

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const commodityReview = defineStore('commodityReview', {
    state: () => ({}),
    actions: {
        /**
         * 通过和驳回方法
         * @function SaveCategory
         * @async
         * @param {Object} data - 通过和驳回数据
         * @returns {Promise<Object>}
         */
        async GetThroughList(data: any) {
            return new Promise((resolve, reject) => {
                getThroughList(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 通过和驳回方法
         * @function SaveCategory
         * @async
         * @param {Object} data - 通过和驳回数据
         * @returns {Promise<Object>}
         */
        async BatchAudit(data: any) {
            return new Promise((resolve, reject) => {
                batchAudit(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        }

    }
});
