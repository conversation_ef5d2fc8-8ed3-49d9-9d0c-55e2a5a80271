import {defineStore} from 'pinia';
import {
    deleteUser,
    getMenuTreeList,
    getUserDetail,
    getUserList,
    resetUserPwd,
    save,
    saveUpdateUser
} from '../api/platformUser/index';
import {getRoleList} from "../api/platformRole";

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const platformUser = defineStore('platformUser', {
    state: () => ({}),
    actions: {
        /**
         * 创建菜单方法
         * @function login
         * @async
         * @param {Object} data - 创建菜单数据
         * @returns {Promise<Object>}
         */
        async SaveUser(data: any) {
            return new Promise((resolve, reject) => {
                save(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 创建角色数据的方法
         * @function GetRoleList
         * @async
         * @param {Object} data - 创建角色的数据
         * @returns {Promise<Object>}
         */
        async GetRoleList(data: any) {
            return new Promise((resolve, reject) => {
                getRoleList().then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        /**
         * 查询用户数据的方法
         * @function GetUserList
         * @async
         * @param {Object} data - 用户数据
         * @returns {Promise<Object>}
         */
        async GetUserList(data: any) {
            return new Promise((resolve, reject) => {
                getUserList(data).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        /**
         * 根据用户id值查询用户信息
         * @function GetUserList
         * @async
         * @param {Object} data - 用户数据
         * @returns {Promise<Object>}
         */
        async GetUserDetail(data: any) {
            return new Promise((resolve, reject) => {
                getUserDetail(data).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        /**
         * 修改用户保存方法
         * @function login
         * @async
         * @param {Object} data - 创建菜单数据
         * @returns {Promise<Object>}
         */
        async SaveUpdateUser(data: any) {
            return new Promise((resolve, reject) => {
                saveUpdateUser(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },

        /**
         * 删除用户方法
         * @function login
         * @async
         * @param {Object} data - 创建菜单数据
         * @returns {Promise<Object>}
         */
        async deleteUser(data: any) {
            return new Promise((resolve, reject) => {
                deleteUser(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 菜单数据方法
         * @function GetMenuTreeList
         * @async
         * @param {Object} data - 创建菜单的数据
         * @returns {Promise<Object>}
         */
        async GetMenuTreeList() {
            return new Promise((resolve, reject) => {
                getMenuTreeList().then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        },
        /**
         * 重置密码
         */
        async resetUserPwd(data) {
            return new Promise((resolve, reject) => {
                resetUserPwd(data).then((res) => {
                    resolve(res);
                }).catch((err) => {

                });
            });
        }
    },
});
