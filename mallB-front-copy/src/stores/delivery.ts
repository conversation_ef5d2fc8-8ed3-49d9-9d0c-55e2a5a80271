import {defineStore} from 'pinia';
import {add, deleteById, getInfo, list, update} from '../api/delivery/index'

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const delivery = defineStore('delivery', {
    state: () => ({}),
    actions: {
        /**
         * 获取列表数据
         */
        async List(data) {
            return new Promise((resolve, reject) => {
                list(data).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 查看详情数据
         */
        async GetInfo(id) {
            return new Promise((resolve, reject) => {
                getInfo(id).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 新增
         */
        async Add(data) {
            return new Promise((resolve, reject) => {
                add(data).then((data) => {
                    resolve(data);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 修改
         */
        async Update(data) {
            return new Promise((resolve, reject) => {
                update(data).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 删除
         */
        async Delete(id) {
            return new Promise((resolve, reject) => {
                deleteById(id).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },

    }
});
