import {defineStore} from 'pinia';
import {
    getOrderRefund,
    getOrderRefundList,
    getProcessRefund,
    getRejectRefund,
    updateAmount
} from '../api/returnOrders/returnOrders';

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const returnOrders = defineStore('returnOrders', {
    state: () => ({}),
    actions: {
        /**
         * 获取退款列表数据
         * @function GetOrderRefundList
         * @async
         * @param {Object} data - 创建数据
         * @returns {Promise<Object>}
         */
        async GetOrderRefundList(data: any) {
            return new Promise((resolve, reject) => {
                getOrderRefundList(data).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 查看退款详情数据
         * @function GetOrderRefund
         * @async
         * @param {Object} data - 创建数据
         * @returns {Promise<Object>}
         */
        async GetOrderRefund(refundId) {
            return new Promise((resolve, reject) => {
                getOrderRefund(refundId).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 同意退款
         * @function GetOrderRefund
         * @async
         * @param {Object} data - 创建数据
         * @returns {Promise<Object>}
         */
        async GetagreeFun(refundId) {
            return new Promise((resolve, reject) => {
                getProcessRefund(refundId).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 拒绝退款
         * @function GetOrderRefund
         * @async
         * @param {Object} data - 创建数据
         * @returns {Promise<Object>}
         */
        async GetrefuseFun(refundId, rejectMessage) {
            return new Promise((resolve, reject) => {
                getRejectRefund(refundId, rejectMessage).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 修改退款金额
         * @param refundId
         * @param refundAmount 修改后的退款金额
         * @constructor
         */
        async UpdateAmount(refundId, data) {
            return new Promise((resolve, reject) => {
                updateAmount(refundId, data).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        console.log(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        }
    }
});
