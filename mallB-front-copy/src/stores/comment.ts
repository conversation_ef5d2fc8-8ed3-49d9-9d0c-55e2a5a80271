import {defineStore} from 'pinia';
import {deleteComment, getCommentDetail, getCommentList, replyComment, reviewComment} from '../api/comment/index';
import {ElMessage} from 'element-plus';

// 定义响应类型接口
interface ApiResponse<T> {
    code: number;
    msg: string;
    rows?: T[];
    total?: number;
    data?: T;
}

/**
 * 评论管理Store
 */
export const useCommentStore = defineStore('comment', {
    state: () => ({
        commentList: [],
        pagination: {
            total: 0,
            pageSize: 10,
            currentPage: 1
        },
        currentComment: null,
        loading: false,
        searchParams: {
            productName: '',
            status: '',
            beginTime: '',
            endTime: ''
        }
    }),
    actions: {
        /**
         * 获取评论列表
         */
        async getComments() {
            this.loading = true;
            try {
                const params = {
                    pageNum: this.pagination.currentPage,
                    pageSize: this.pagination.pageSize,
                    productName: this.searchParams.productName || undefined,
                    status: this.searchParams.status || undefined,
                    beginTime: this.searchParams.beginTime || undefined,
                    endTime: this.searchParams.endTime || undefined
                };

                const res = await getCommentList(params);
                if (res.code === 200) {
                    this.commentList = res.rows || [];
                    this.pagination.total = res.total || 0;
                    return res.rows;
                } else {
                    return [];
                }
            } catch (error) {
                return [];
            } finally {
                this.loading = false;
            }
        },

        /**
         * 审核评论
         * @param id 评论ID
         * @param status 状态（1:通过，2:拒绝）
         */
        async reviewComment(id, status) {
            try {
                const res = await reviewComment(id, status);
                if (res.code === 200) {
                    ElMessage.success(status === '1' ? '评论已通过审核' : '评论已拒绝');
                    return true;
                } else {
                    return false;
                }
            } catch (error) {
                return false;
            }
        },

        /**
         * 回复评论
         * @param id 评论ID
         * @param reply 回复内容
         */
        async replyComment(id, reply) {
            try {
                const res = await replyComment(id, reply);
                if (res.code === 200) {
                    ElMessage.success('回复评论成功');
                    return true;
                } else {
                    return false;
                }
            } catch (error) {
                return false;
            }
        },

        /**
         * 删除评论
         * @param ids 评论ID数组
         */
        async deleteComment(ids) {
            try {
                const res = await deleteComment(ids);
                if (res.code === 200) {
                    ElMessage.success('删除评论成功');
                    return true;
                } else {
                    return false;
                }
            } catch (error) {
                return false;
            }
        },

        /**
         * 获取评论详情
         * @param id 评论ID
         */
        async getCommentDetail(id) {
            try {
                const res = await getCommentDetail(id);
                if (res.code === 200) {
                    this.currentComment = res.data;
                    return res.data;
                } else {
                    return null;
                }
            } catch (error) {
                return null;
            }
        },

        /**
         * 设置搜索参数
         * @param params 搜索参数
         */
        setSearchParams(params) {
            this.searchParams = {...this.searchParams, ...params};
        },

        /**
         * 重置搜索参数
         */
        resetSearchParams() {
            this.searchParams = {
                productName: '',
                status: '',
                beginTime: '',
                endTime: ''
            };
        }
    }
});
