import {defineStore} from 'pinia';
import {
    exportOrderList,
    getOrderDetail,
    getOrderList,
    UpdateOrderAddress,
    updateOrderPrice
} from '../api/allOrders/index';

/**
 * @function allOrders
 * @returns {allOrders}
 */
export const allOrders = defineStore('allOrders', {
    state: () => ({}),
    actions: {
        /**
         * 获取订单详情
         * @function allOrders
         * @async
         * @returns {Promise<Object>}
         */
        async GetOrderList(data) {
            return new Promise((resolve, reject) => {
                getOrderList(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                }).catch((err) => {
                    // useMessage().error(err?.msg || '系统异常请联系管理员');
                    // reject(err);
                });
            });
        },
        /**
         * 导出订单数据
         * @function ExportOrderList
         * @async
         * @param {Object} data - 查询参数
         * @returns {Promise<Blob>}
         */
        async ExportOrderList(data) {
            return new Promise((resolve, reject) => {
                exportOrderList(data).then((res) => {
                    resolve(res);
                }).catch((err) => {
                    // reject(err);
                });
            });
        },
        async UpdateOrderAddress(data) {
            return new Promise((resolve, reject) => {
                UpdateOrderAddress(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                }).catch((err) => {
                    // useMessage().error(err?.msg || '系统异常请联系管理员');
                    // reject(err);
                });
            });
        },
        /**
         * 修改订单价格
         * @function UpdateOrderPrice
         * @async
         * @param {Object} data - 修改价格参数
         * @returns {Promise<Object>}
         */
        async UpdateOrderPrice(data) {
            return new Promise((resolve, reject) => {
                updateOrderPrice(data).then((res) => {
                    resolve(res);
                }).catch((err) => {
                    // useMessage().error(err?.msg || '系统异常请联系管理员');
                    // reject(err);
                });
            });
        },
        /**
         * 获取订单详情
         * @function GetOrderDetail
         * @async
         * @param {string} orderNo - 订单编号
         * @returns {Promise<Object>}
         */
        async GetOrderDetail(orderNo) {
            return new Promise((resolve, reject) => {
                getOrderDetail(orderNo).then((res) => {
                    resolve(res);
                }).catch((err) => {
                    // reject(err);
                });
            });
        }
    }
});
