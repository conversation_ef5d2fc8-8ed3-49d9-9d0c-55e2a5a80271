import {defineStore} from 'pinia';
import {activateThepermission, getVirtualOrderDetail, openpermission} from '../api/productSellApply/index';

/**
 * @function jurisdiction
 * @returns {jurisdiction}
 */
export const jurisdiction = defineStore('jurisdiction', {
    state: () => ({}),
    actions: {
        /**
         * 查询权限数据信息
         * @function jurisdiction
         * @async
         * @returns {Promise<Object>}
         */
        async GetActivateThepermission(data) {
            return new Promise((resolve, reject) => {
                activateThepermission(data).then((res) => {
                    // 存储数据信息,返回数据
                    resolve(res);
                }).catch((err) => {
                    // useMessage().error(err?.msg || '系统异常请联系管理员');
                    // reject(err);
                });
            });
        },
        /**
         * 开通权限(类型)
         * @function Open
         * @async
         * @returns {Promise<Object>}
         */
        async Openpermission(data) {
            return new Promise((resolve, reject) => {
                openpermission(data).then((res) => {
                    // 存储数据信息,返回数据
                    resolve(res);
                }).catch((err) => {
                    // useMessage().error(err?.msg || '系统异常请联系管理员');
                    // reject(err);
                });
            });
        },
        /**
         * 权限详情(类型)
         * @function Open
         * @async
         * @returns {Promise<Object>}
         */
        async GetVirtualOrderDetail(data) {
            return new Promise((resolve, reject) => {
                getVirtualOrderDetail(data).then((res) => {
                    // 存储数据信息,返回数据
                    resolve(res);
                }).catch((err) => {
                    // useMessage().error(err?.msg || '系统异常请联系管理员');
                    // reject(err);
                });
            });
        }
    }
});
