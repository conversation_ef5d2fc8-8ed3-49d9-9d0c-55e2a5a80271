import {defineStore} from 'pinia';
import {add, deleteById, getInfo, getInfoByTitle, list, update} from '../api/platformAgreement/index'

export const platformAgreement = defineStore('platformAgreement', {
    state: () => ({}),
    actions: {
        /**
         * 获取列表数据
         */
        async List() {
            return new Promise((resolve, reject) => {
                list().then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 查看详情数据
         */
        async GetInfo(id: Number) {
            return new Promise((resolve, reject) => {
                getInfo(id).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 新增
         */
        async Add(data: any) {
            return new Promise((resolve, reject) => {
                add(data).then((data) => {
                    resolve(data);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 修改
         */
        async Update(data: any) {
            return new Promise((resolve, reject) => {
                update(data).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 删除
         */
        async Delete(id: Number) {
            return new Promise((resolve, reject) => {
                deleteById(id).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 根据title获取协议内容
         */
        async GetInfoByTitle(title: String) {
            return new Promise((resolve, reject) => {
                getInfoByTitle(title).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },

    }
});
