import {defineStore} from 'pinia';
import {
    batchgetshelves,
    exportProductList,
    getdelete,
    getProductDetail,
    getProductList,
    getSale,
    getSales,
    getshelves
} from '../api/productList/productList';

/**
 * @function useUserInfo
 * @returns {UserInfosStore}
 */
export const productList = defineStore('productList', {
    state: () => ({}),
    actions: {
        /**
         * 查询产品数据
         * @function SaveCategory
         * @async
         * @param {Object} data - 查询产品数据
         * @returns {Promise<Object>}
         */
        async GetProductList(data: any) {
            return new Promise((resolve, reject) => {
                getProductList(data).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 上架方法
         * @function SaveCategory
         * @async
         * @param {Object} data - 上架
         * @returns {Promise<Object>}
         */
        async GetSale(data: any) {
            return new Promise((resolve, reject) => {
                getSale(data).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 批量上架方法
         * @function SaveCategory
         * @async
         * @param {Object} data - 批量上架
         * @returns {Promise<Object>}
         */
        async GetSales(data: any) {
            return new Promise((resolve, reject) => {
                getSales(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 下架方法
         * @function Getshelves
         * @async
         * @param {Object} data - 下架
         * @returns {Promise<Object>}
         */
        async Getshelves(data: any) {
            return new Promise((resolve, reject) => {
                getshelves(data).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         *
         * @function Batchgetshelves
         * @async
         * @param {Object} data - 批量下架
         * @returns {Promise<Object>}
         */
        async Batchgetshelves(data: any) {
            return new Promise((resolve, reject) => {
                batchgetshelves(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         *
         * @function Getdelete
         * @async
         * @param {Object} data - 删除
         * @returns {Promise<Object>}
         */
        async Getdelete(data: any) {
            return new Promise((resolve, reject) => {
                getdelete(data).then((res) => {
                    // 存储token 信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 导出商品列表
         * @function ExportProductList
         * @async
         * @param {Object} data - 导出条件
         * @returns {Promise<Blob>}
         */
        async ExportProductList(data: any) {
            return new Promise((resolve, reject) => {
                exportProductList(data).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 获取商品详情
         * @function getProductDetail
         * @async
         * @param {number} productId - 商品ID
         * @returns {Promise<Object>}
         */
        async GetProductDetail(productId: number) {
            return new Promise((resolve, reject) => {
                getProductDetail(productId).then((res) => {
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        reject(err);
                    });
            });
        }
    }
});
