import {defineStore} from 'pinia';
import {cancelConsignmentProduct, getDaiXiaoProductList, insertConsignmentProduct} from '../api/commoditySales/index';

/**
 * @function commoditySales
 * @returns {commoditySales}
 */
export const commoditySales = defineStore('commoditySales', {
    state: () => ({}),
    actions: {
        /**
         * 查询代销能代销的商品
         * @function getDaiXiaoProductList
         * @async
         * @param {Object} data - 代销数据
         * @returns {Promise<Object>}
         */
        async GetDaiXiaoProductList(data: any) {
            return new Promise((resolve, reject) => {
                getDaiXiaoProductList(data).then((res) => {
                    // 返回信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 添加代销能代销的商品
         *
         */
        async InsertConsignmentProduct(productId: String) {
            return new Promise((resolve, reject) => {
                insertConsignmentProduct(productId).then((res) => {
                    // 返回信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 取消代销能代销的商品
         */
        async CancelConsignmentProduct(productId: String) {
            return new Promise((resolve, reject) => {
                cancelConsignmentProduct(productId).then((res) => {
                    // 返回信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        }
    }
});
