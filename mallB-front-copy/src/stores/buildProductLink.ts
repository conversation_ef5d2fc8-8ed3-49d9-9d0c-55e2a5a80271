import {defineStore} from 'pinia';
import {
    cancelConsignment,
    editDaiXiaoProductUrl,
    getShopDaiXiaoProductList
} from '../api/buildProductLink/buildProductLink';

/**
 * @function buildProductLink
 * @returns {buildProductLink}
 */
export const buildProductLink = defineStore('buildProductLink', {
    state: () => ({}),
    actions: {
        /**
         * 商家查询代销商品
         * @function getShopDaiXiaoProductList
         * @async
         * @param {Object} data - 商品链接生成
         * @returns {Promise<Object>}
         */
        async GetShopDaiXiaoProductList(data: any) {
            return new Promise((resolve, reject) => {
                getShopDaiXiaoProductList(data).then((res) => {
                    // 返回信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },

        /**
         * 商家取消代销商品
         */
        async CancelConsignment(productId: String) {
            return new Promise((resolve, reject) => {
                cancelConsignment(productId).then((res) => {
                    // 返回信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        },
        /**
         * 查询商品链接
         * @function EditDaiXiaoProductUrl
         * @async
         * @param {Object} data - 商品链接生成
         * @returns {Promise<Object>}
         */
        async EditDaiXiaoProductUrl(data: any) {
            return new Promise((resolve, reject) => {
                editDaiXiaoProductUrl(data).then((res) => {
                    // 返回信息
                    resolve(res);
                })
                    .catch((err) => {
                        // useMessage().error(err?.msg || '系统异常请联系管理员');
                        // reject(err);
                    });
            });
        }
    }
});
