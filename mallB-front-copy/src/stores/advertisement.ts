import {defineStore} from 'pinia'
import {ref} from 'vue'
import {ElMessage} from 'element-plus'
import {
    addAdvertisement,
    Advertisement,
    auditAdvertisement,
    batchAuditAdvertisement,
    cancelVirtualOrder,
    createAdPayOrder,
    deleteAdvertisement,
    getAdvertisementDetail,
    getAdvertisementList,
    getVirtualOrderDetail,
    updateAdvertisement
} from '../api/shop/advertisement'
import {Session} from '../utils/storage'

// 获取token
const getToken = () => {
    return Session.getToken()
}

// 分页数据结构
interface PageData<T> {
    rows: T[];
    total: number;
}

// 定义修复响应类型的接口
interface ApiResponse<T> {
    code: number;
    msg: string;
    data: T;
}

export const useAdvertisementStore = defineStore('advertisement', () => {
    // 广告列表
    const adList = ref<Advertisement[]>([])

    // 加载状态
    const loading = ref(false)

    // 分页信息
    const pagination = ref({
        total: 0,
        pageSize: 10,
        pageNum: 1
    })

    // 搜索参数
    const searchParams = ref({
        adName: '',
        status: ''
    })

    // 设置搜索参数
    const setSearchParams = (params) => {
        // 确保日期不是undefined或null
        const sanitizedParams = {
            ...params
        }

        searchParams.value = {
            ...searchParams.value,
            ...sanitizedParams
        }
    }

    // 重置搜索参数
    const resetSearchParams = () => {
        searchParams.value = {
            adName: '',
            status: ''
        }
    }

    // 获取广告列表
    const getAdvertisementListData = async () => {
        loading.value = true
        try {
            const response = await getAdvertisementList({
                pageNum: pagination.value.pageNum,
                pageSize: pagination.value.pageSize,
                ...searchParams.value
            })

            if (response.code === 200) {
                // 从响应中提取rows和total
                const {rows, total} = response

                adList.value = rows || []
                pagination.value.total = total || 0

                // 添加类型和状态文本
                adList.value.forEach(ad => {
                    // 设置广告类型文本
                    if (ad.adType === '1') {
                        ad.adTypeText = '图片'
                    } else if (ad.adType === '2') {
                        ad.adTypeText = '视频'
                    } else {
                        ad.adTypeText = '未知'
                    }

                    // 设置状态文本
                    if (ad.status === '0') {
                        ad.statusText = '待审核'
                    } else if (ad.status === '1') {
                        ad.statusText = '待支付'
                    } else if (ad.status === '2') {
                        ad.statusText = '已生效'
                    } else if (ad.status === '3') {
                        ad.statusText = '拒绝'
                    } else {
                        ad.statusText = '未知'
                    }

                    // 设置广告位置文本
                    if (ad.type === '0') {
                        ad.typeText = '商城首页'
                    } else if (ad.type === '1') {
                        ad.typeText = '分享广告'
                    } else if (ad.type === '2') {
                        ad.typeText = '商区广告'
                    } else {
                        ad.typeText = '未知位置'
                    }
                })
            }
        } catch (error) {
            console.error('获取广告列表出错:', error)
        } finally {
            loading.value = false
        }
    }

    // 添加广告
    const addAdvertisementData = async (adData) => {
        loading.value = true
        try {
            const response = await addAdvertisement(adData)

            if (response.code === 200) {
                return true
            }
        } catch (error) {
            console.error('添加广告出错:', error)
            return false
        } finally {
            loading.value = false
        }
    }

    // 更新广告
    const updateAdvertisementData = async (adData) => {
        loading.value = true
        try {
            const response = await updateAdvertisement(adData)

            if (response.code === 200) {
                return true
            }
        } catch (error) {
            console.error('更新广告出错:', error)
            ElMessage.error('更新广告失败')
            return false
        } finally {
            loading.value = false
        }
    }

    // 删除广告
    const deleteAdvertisementData = async (id) => {
        loading.value = true
        try {
            const response = await deleteAdvertisement(id)

            if (response.code === 200) {
                return true
            }
        } catch (error) {
            ElMessage.error('删除广告失败')
            return false
        } finally {
            loading.value = false
        }
    }

    // 获取广告详情
    const getAdvertisementDetailData = async (id) => {
        loading.value = true
        try {
            const response = await getAdvertisementDetail(id)

            if (response.code === 200) {
                return response.data.data
            }
        } catch (error) {
            console.error('获取广告详情出错:', error)
            ElMessage.error('获取广告详情失败')
            return null
        } finally {
            loading.value = false
        }
    }

    // 审核广告
    const auditAdvertisementData = async (id, status, remark) => {
        loading.value = true
        try {
            const response = await auditAdvertisement(id, status, remark)

            if (response.code === 200) {
                ElMessage.success(status === '1' ? '广告审核通过' : '广告审核拒绝')
                return true
            }
        } catch (error) {
            console.error('审核广告出错:', error)
            ElMessage.error('审核广告失败: ' + error.message || '未知错误')
            return false
        } finally {
            loading.value = false
        }
    }

    // 批量审核广告
    const batchAuditAdvertisementData = async (ids, status, remark) => {
        loading.value = true
        try {
            const response = await batchAuditAdvertisement(ids, status, remark)

            if (response.code === 200) {
                ElMessage.success(status === '1' ? '广告批量审核通过' : '广告批量审核拒绝')
                return true
            }
        } catch (error) {
            console.error('批量审核广告出错:', error)
            ElMessage.error('批量审核广告失败: ' + error.message || '未知错误')
            return false
        } finally {
            loading.value = false
        }
    }

    return {
        adList,
        loading,
        pagination,
        searchParams,
        setSearchParams,
        resetSearchParams,
        getAdvertisementList: getAdvertisementListData,
        addAdvertisement: addAdvertisementData,
        updateAdvertisement: updateAdvertisementData,
        deleteAdvertisement: deleteAdvertisementData,
        getAdvertisementDetail: getAdvertisementDetailData,

        /**
         * 创建广告支付订单
         * @param adId 广告ID
         * @returns 订单号
         */
        async createAdPayOrder(adId: string | number) {
            loading.value = true
            try {
                const response = await createAdPayOrder(adId);
                if (response.code === 200) {
                    return response.data;
                }
            } catch (error) {
                console.error('创建广告支付订单失败:', error);
                ElMessage.error('系统错误，请稍后再试');
                return null;
            } finally {
                loading.value = false
            }
        },

        /**
         * 获取虚拟订单详情
         * @param orderNo 订单号
         * @returns 订单详情
         */
        async getVirtualOrderDetail(orderNo: string) {
            loading.value = true
            try {
                const response = await getVirtualOrderDetail(orderNo);
                console.log("response", response)
                if (response.code === 200) {
                    return response.data;
                }
            } catch (error) {
                console.error('获取订单详情失败:', error);
                return null;
            } finally {
                loading.value = false
            }
        },

        /**
         * 取消虚拟订单
         * @param orderNo 订单号
         * @returns 是否成功
         */
        async cancelOrder(orderNo: string) {
            loading.value = true
            try {
                const response = await cancelVirtualOrder(orderNo);
                if (response.code === 200) {
                    ElMessage.success('订单已取消');
                    return true;
                }
            } catch (error) {
                console.error('取消订单失败:', error);
                ElMessage.error('系统错误，请稍后再试');
                return false;
            } finally {
                loading.value = false
            }
        },

        /**
         * 检查广告支付状态
         * @param orderNo 订单号
         * @returns 是否成功
         */
        async checkAdPayStatus(orderNo: string) {
            loading.value = true
            try {
                // 获取订单详情
                const orderDetail = await this.getVirtualOrderDetail(orderNo);

                // 如果订单状态不是待支付，则表示支付成功
                if (orderDetail && orderDetail.status !== '0') {
                    // 刷新广告列表以获取最新状态
                    await this.getAdvertisementList();
                    ElMessage.success('订单支付成功，广告状态已更新');
                    return true;
                }

                return false;
            } catch (error) {
                console.error('检查广告支付状态失败:', error);
                return false;
            } finally {
                loading.value = false
            }
        },

        auditAdvertisement: auditAdvertisementData,
        batchAuditAdvertisement: batchAuditAdvertisementData,

        /**
         * 创建商家平台促销金核销
         * @param orderNo 订单号
         * @returns 是否成功
         */
        async createShopDeductionPayment(orderNo: string) {
            loading.value = true
            try {
                // 调用商家平台促销金核销接口
                const response = await fetch('/app/virtual/payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Authorization': `Bearer ${getToken()}`
                    },
                    body: new URLSearchParams({
                        orderNo: orderNo,
                        payType: '5' // 5表示商家平台促销金核销
                    })
                }).then(res => res.json());

                if (response.code === 200) {
                    ElMessage.success('商家平台促销金核销成功');
                    // 刷新广告列表
                    await this.getAdvertisementList();
                    return true;
                } else {
                    ElMessage.error(response.msg || '支付失败');
                    return false;
                }
            } catch (error) {
                console.error('商家平台促销金核销失败:', error);
                ElMessage.error('系统错误，请稍后再试');
                return false;
            } finally {
                loading.value = false
            }
        }
    }
})
