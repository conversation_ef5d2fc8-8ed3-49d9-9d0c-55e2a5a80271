# MallB 电商平台前端

## 项目介绍

MallB 前端项目是基于Vue 3、Vite构建的现代化电商平台前端应用，提供完整的电商业务操作界面，包括用户端和管理端功能，与后端API接口交互实现完整的电商业务流程。

## 技术栈

- **核心框架**: Vue 3
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **HTTP客户端**: Axios
- **UI组件库**: Element Plus
- **国际化**: vue-i18n
- **富文本编辑器**: TinyMCE

## 目录结构

- **src/api**: 接口请求模块，按业务分类管理
- **src/assets**: 静态资源文件
- **src/components**: 通用组件
- **src/config**: 全局配置
- **src/hooks**: 复用的组合式函数
- **src/http**: HTTP请求封装
- **src/i18n**: 国际化配置
- **src/images**: 图片资源
- **src/router**: 路由配置
- **src/stores**: Pinia状态管理
- **src/utils**: 工具函数
- **src/views**: 页面组件，按业务模块组织

## 主要功能模块

- **用户认证**: 登录、注册、找回密码
- **商品管理**: 商品列表、详情、分类管理
- **订单管理**: 订单列表、详情、状态流转
- **用户中心**: 个人信息、收货地址、账户安全
- **购物流程**: 购物车、结算、支付
- **营销活动**: 优惠券、限时活动
- **店铺管理**: 店铺信息、商品管理、订单处理
- **系统设置**: 权限设置、角色管理、菜单配置

## 平台协议功能

### 协议编辑功能

平台协议编辑页面支持富文本编辑，可以创建、编辑和删除各类平台协议。

特点：

- 支持富文本编辑，可以添加格式化文本、图片、表格等
- 简单易用的协议管理界面
- 实时保存协议内容

### 协议查看功能

系统提供了专门的协议查看页面，支持从任何地方通过链接跳转查看指定协议。

特点：

- 通过URL参数传递协议标题，自动匹配并显示对应协议
- 友好的错误提示，当协议不存在时给出清晰反馈
- 美观的富文本显示，支持各种格式化内容

### 使用方法

1. 协议链接组件

   使用`AgreementLink`组件在任何页面中添加协议链接：

   ```vue
   <template>
     <div>
       请阅读并同意我们的 <AgreementLink title="用户协议" />
     </div>
   </template>
   
   <script setup>
   import AgreementLink from '@/components/AgreementLink.vue'
   </script>
   ```

2. 直接URL访问

   可以通过URL直接访问协议页面：
   ```
   #/agreementView?title=用户协议
   ```

3. 协议管理

   平台管理员可以在"平台协议"页面中管理所有协议内容。

## 开发指南

### 环境准备

- Node.js >= 16.x
- npm >= 8.x

### 安装依赖

```bash
npm install
```

### 开发运行

```bash
npm run dev
```

### 生产构建

```bash
npm run build
```

### 代码规范

- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 遵循组件化开发原则
- API请求统一管理

## 接口配置

在 src/config/baseUrl.js 中配置API接口地址：

```javascript
// 开发环境
const dev = {
  baseUrl: 'http://localhost:8080/api'
};

// 生产环境
const prod = {
  baseUrl: 'https://api.example.com/api'
};

export default process.env.NODE_ENV === 'development' ? dev : prod;
```

## 国际化支持

系统支持多语言配置，语言文件位于 src/i18n/lang 目录下：

- zh-CN: 简体中文
- en-US: 英文

## 自定义主题

可通过修改Element Plus的样式变量自定义主题，配置文件位于styles目录下。
