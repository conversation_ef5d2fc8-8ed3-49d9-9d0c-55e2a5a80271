# ruoyi-job 定时任务执行模块

## 模块介绍

`ruoyi-job` 是MallB电商平台的任务执行器模块，负责执行系统中的各类定时任务和批处理任务。该模块作为XXL-Job的执行器端，接收来自任务调度中心的指令并执行相应的业务逻辑。

## 主要功能

- 订单状态自动更新任务
- 支付超时处理任务
- 库存定时盘点任务
- 数据统计分析任务
- 系统缓存定时刷新
- 定时报表生成
- 用户积分过期处理
- 商品上下架定时处理
- 活动自动开始/结束
- 日志定期清理任务

## 任务类型

- CRON表达式定时任务
- 固定频率任务
- 一次性任务
- 分片任务(数据分片处理)
- 子任务(任务链)

## 技术实现

- XXL-Job执行器
- 注解式任务定义
- 日志记录与追踪
- 失败重试机制
- 任务超时控制

## 使用方式

1. 定义任务执行类：
   ```java
   @Component
   public class OrderStatusJob {
       
       @XxlJob("orderStatusJobHandler")
       public ReturnT<String> execute(String param) {
           // 任务执行逻辑
           return ReturnT.SUCCESS;
       }
   }
   ```

2. 任务调度中心配置：
    - 创建执行器：应用名为ruoyi-job
    - 新建任务：绑定执行方法，设置调度策略

## 开发指南

1. 长耗时任务需考虑超时控制
2. 大批量数据处理建议使用分片任务
3. 任务执行应考虑幂等性
4. 关键任务应设置重试机制 