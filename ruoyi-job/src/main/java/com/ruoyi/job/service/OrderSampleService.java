package com.ruoyi.job.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.enums.OrderStatusEnum;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.Order;
import com.ruoyi.mall.enums.TradeMethodEnum;
import com.ruoyi.mall.service.IOrderCommissionService;
import com.ruoyi.mall.service.IOrderService;
import com.ruoyi.mall.service.IShopWalletService;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.service.IPayService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单定时处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderSampleService {

    private final IOrderService orderService;
    private final IPayService payService;
    private final IShopWalletService shopWalletService;
    private final IOrderCommissionService commissionService;

    /**
     * 自动取消超时未支付订单
     * 建议配置每5分钟执行一次
     */
    // @Scheduled(cron = "0 */5***?")
    // @Transactional(rollbackFor = Exception.class)
    @XxlJob("autoCancelUnpaidOrdersHandler")
    public void autoCancelUnpaidOrders() {
        try {
            XxlJobHelper.log("开始执行超时未支付订单自动取消任务");

            // 查询超过30分钟待支付订单
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                // 待支付状态
                .eq(Order::getStatus, OrderStatusEnum.PENDING_PAYMENT.getCode())
                .lt(Order::getCreateTime, new Date(System.currentTimeMillis() - 30 * 60 * 1000)); // 创建时间超过30分钟

            List<Order> orders = orderService.list(queryWrapper);

            if (orders.isEmpty()) {
                XxlJobHelper.log("没有需要自动取消的订单");
                return;
            }

            // 3. 批量取消超时订单
            for (Order order : orders) {
                try {
                    orderService.cancelOrder(order.getOrderNo());
                    XxlJobHelper.log("自动取消超时订单成功,订单号:{}", order.getOrderNo());
                } catch (Exception e) {
                    XxlJobHelper.log("自动取消超时订单失败,订单号:{}", order.getOrderNo(), e);
                }
            }
            XxlJobHelper.log("自动取消订单处理完成，处理订单数：" + orders.size());

        } catch (Exception e) {
            XxlJobHelper.log("自动取消订单处理异常：" + e.getMessage());
            XxlJobHelper.handleFail();
        }
    }

    /**
     * 自动确认收货
     * 建议配置每天凌晨执行一次
     */
    @XxlJob("autoConfirmReceiptHandler")
    @Transactional(rollbackFor = Exception.class)
    public void autoConfirmReceipt() {
        try {
            XxlJobHelper.log("开始执行自动确认收货任务");

            // 查询发货超过15天的订单
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                // 待收货状态
                .eq(Order::getStatus, OrderStatusEnum.PENDING_RECEIVE.getCode())
                .eq(Order::getDelFlag, "0") // 未删除
                .lt(Order::getDeliveryTime, new Date(System.currentTimeMillis() - 15 * 24 * 60 * 60 * 1000)); // 发货时间超过15天

            List<Order> orders = orderService.list(queryWrapper);

            if (orders.isEmpty()) {
                XxlJobHelper.log("没有需要自动确认收货的订单");
                return;
            }

            // 批量更新订单状态为已完成
            LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                .set(Order::getStatus, OrderStatusEnum.COMPLETED.getCode()) // 已完成状态
                .set(Order::getReceiveTime, new Date())
                .in(Order::getId, orders.stream().map(Order::getId).collect(Collectors.toList()));

            boolean success = orderService.update(updateWrapper);

            // 处理商家钱包入账
            if (success) {
                for (Order order : orders) {
                    try {
                        if (order.getShopId() != null && order.getPayAmount() != null) {

                            //商家能获取的订单金额（需要考虑抵扣金组合支付的情况）
                            BigDecimal amount = calculateShopIncomeAmount(order);

                            //判断订单是否有代销商家
                            if(StringUtils.isNotBlank(order.getConsignmentId().toString())||order.getConsignmentId()!=null){
                                //获取代销商家总结算多少佣金
                                BigDecimal consignmentAmount=commissionService.getCommissionAmount(order.getId(),order.getConsignmentId());
                                //商家最终能获取的金额
                                amount=amount.subtract(consignmentAmount);
                            }

                            // 调用商家钱包服务，将订单金额转入商家钱包
                            shopWalletService.income(
                                order.getShopId(),
                                amount,
                                TradeMethodEnum.SALES_INCOME, // 交易方式：销售收入
                                order.getOrderNo(),
                                "订单自动确认收货：" + order.getOrderNo()
                            );
                            XxlJobHelper.log("订单{}商家钱包入账成功", order.getOrderNo());
                        }
                    } catch (Exception e) {
                        log.error("订单{}商家钱包入账异常", order.getOrderNo(), e);
                        XxlJobHelper.log("订单{}商家钱包入账异常: {}", order.getOrderNo(), e.getMessage());
                    }
                }
            }

            XxlJobHelper.log("自动确认收货处理完成，处理订单数：" + orders.size() + "，处理" + (success ? "成功" : "失败"));

        } catch (Exception e) {
            log.error("自动确认收货任务执行异常", e);
            XxlJobHelper.log("自动确认收货处理异常：" + e.getMessage());
            XxlJobHelper.handleFail();
        }
    }

    /**
     * 自动查询订单支付状态
     * 建议配置每1分钟执行一次
     * 主要用于处理支付中状态的订单，向第三方支付平台查询实际支付状态
     */
    @XxlJob("autoQueryOrderStatusHandler")
    public void autoQueryOrderStatus() {
        try {
            XxlJobHelper.log("开始执行订单状态查询任务");

            // 查询支付中的订单（状态为支付中）
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                // 支付中状态
                .eq(Order::getStatus, OrderStatusEnum.DURING_PAYMENT.getCode())
                // 未删除
                .eq(Order::getDelFlag, "0")
                .last("LIMIT 100"); // 每次最多处理100条，避免单次处理太多

            List<Order> orders = orderService.list(queryWrapper);

            if (orders.isEmpty()) {
                XxlJobHelper.log("没有需要查询状态的订单");
                return;
            }

            int successCount = 0;
            int failCount = 0;

            // 遍历订单查询实际支付状态
            for (Order order : orders) {
                try {
                    PayOrder payOrder = payService.getByOrderNo(order.getOrderNo());
                    orderService.queryPayStatus(payOrder.getPaymentNo());
                    successCount++;
                } catch (Exception e) {
                    log.error("查询订单{}支付状态异常", order.getId(), e);
                    failCount++;
                }
            }

            XxlJobHelper.log("订单状态查询完成，共处理{}笔订单。支付成功：{}笔，支付失败：{}笔",
                orders.size(), successCount, failCount);

        } catch (Exception e) {
            log.error("订单状态查询任务执行异常", e);
            XxlJobHelper.log("订单状态查询任务异常：" + e.getMessage());
            XxlJobHelper.handleFail();
        }
    }

    /**
     * 计算商家应收入的金额
     * 对于抵扣金组合支付，需要考虑实际支付金额的构成
     */
    private BigDecimal calculateShopIncomeAmount(Order order) {
        BigDecimal amount = order.getPayAmount();

        // 如果是抵扣金组合支付，需要特殊处理
        if (PayOrder.PAY_TYPE_DEDUCTION.equals(order.getPayType())) {
            // 抵扣金组合支付的情况下：
            // order.getPayAmount() 可能包含了抵扣金金额 + 手续费
            // 但商家应该获得的是完整的商品金额（不包括手续费）

            if (order.getDeductionAmount() != null && order.getDeductionAmount().compareTo(BigDecimal.ZERO) > 0) {
                // 如果有抵扣金使用记录，说明是组合支付
                // 商家收入 = 抵扣金金额（商品金额部分）
                // 手续费部分不给商家
                amount = order.getDeductionAmount();

                log.info("抵扣金组合支付订单商家收入计算 - 订单号: {}, 订单总金额: {}元, 抵扣金: {}元, 商家实际收入: {}元",
                        order.getOrderNo(), order.getPayAmount(), order.getDeductionAmount(), amount);
            }
        }

        return amount;
    }
}
