package com.ruoyi.job.service;

import cn.hutool.json.JSONObject;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.entity.UserDeductionMoneyLimitRecord;
import com.ruoyi.mall.enums.UserDeductionMoneyLimitEnum;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.service.ITzUserDeductionMoneyLimitRecordService;
import com.ruoyi.mall.service.ITzUserService;
import com.ruoyi.mall.service.IUserComponentRecordService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 计算分量服务
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class ComputeWeightSampleService {

    @Autowired
    private final IConfigSettingService configSettingService;
    @Autowired
    private final ITzUserService userService;
    @Autowired
    private final IUserComponentRecordService userComponentRecordService;
    @Autowired
    private final ITzUserDeductionMoneyLimitRecordService deductionMoneyLimitRecordService;

    @XxlJob("monthComputeWeight")
    public void monthComputeWeight() {

        XxlJobHelper.log("开始执行每月计算分量任务");

        //获取分量配置
        JSONObject weightConfig = configSettingService.getConfigValue("configFour");
        if (weightConfig.isEmpty()) {
            XxlJobHelper.log("分量配置值不存在");
            return;
        }

        //获取多少平台补贴金上限额度
        Double dailyThresholdReward = weightConfig.getDouble("dailyThresholdReward");
        //获取每月技术引流需要推荐商家人数
        int monthDrainageNum = weightConfig.getInt("monthlyTechThreshold");
        //获取推荐人数大于或者等于每月技术引流需要推荐商家人数，获取的补贴金上限额度
        Double monthExchangeMoneyLimit = weightConfig.getDouble("monthlyTechRewardGold");
        //获取每月无效技术引流商家人数
        int monthNoDrainageNum = weightConfig.getInt("monthlyNoTechThreshold");
        //获取无效引流下降补贴金
        Double monthNoExchangeMoneyDecline = weightConfig.getDouble("monthlyNoTechPenaltyGold");

        //获取所有的消费者
        TzUser u = new TzUser();
        u.setUserType("C");
        u.setIsFirst("1");
        List<TzUser> userList = userService.selectTzUserList(u);
        for (TzUser user : userList) {
            if (user.getIsFirst().equals("1")) {

                //默认为增加
                String status=null;
                //默认为额度上限为0
                Double deductionMoneyLimit =0.0;
                String type=null;
                String description=null;

                Map<String, Object> map = userService.countMonthlyRecommendedShop(user.getUserId());

                //获取用户上个月邀请用户成为商家人数
                int UserMonthDrainageNum= (int) map.get("monthDrainageNum");
                if(UserMonthDrainageNum>0){
                    //有效引流商家人数
                    int drainageCount = (int) map.get("drainageCount");
                    //无效引流商家人数
                    int noDrainageCount = (int) map.get("noDrainageCount");

                    TzUser tzUser = new TzUser();
                    tzUser.setUserId(user.getUserId());
                    Double ExchangeMoneyLimit = user.getDeductionMoneyLimit();

                    if (drainageCount >= monthDrainageNum) {//当用户上月总引流次数大于/等于某个值，则获得补贴金的上限度
                        ExchangeMoneyLimit +=(drainageCount / monthDrainageNum)*monthExchangeMoneyLimit;

                        status="0";
                        type= UserDeductionMoneyLimitEnum.SHOP_EFFECTIVE_DRAINAGE.getNumber();
                        description= UserDeductionMoneyLimitEnum.SHOP_EFFECTIVE_DRAINAGE.getName();
                        deductionMoneyLimit= (drainageCount / monthDrainageNum)*monthExchangeMoneyLimit;
                    }if (noDrainageCount >= monthNoDrainageNum&&ExchangeMoneyLimit>0) {//当用户上月总无效引流次数大于/等于指标指，则下降补贴金额度上限,最低为最初额度
                        ExchangeMoneyLimit = Math.max(0, ExchangeMoneyLimit -
                            (noDrainageCount/monthNoDrainageNum)*monthNoExchangeMoneyDecline) < dailyThresholdReward ?
                            Math.max(dailyThresholdReward, 0) :
                            Math.max(ExchangeMoneyLimit -(noDrainageCount/monthNoDrainageNum)*monthNoExchangeMoneyDecline, 0);

                        status="1";
                        type=UserDeductionMoneyLimitEnum.SHOP_INVALID_DRAINAGE.getNumber();
                        description=UserDeductionMoneyLimitEnum.SHOP_INVALID_DRAINAGE.getName();
                        deductionMoneyLimit=Math.max(0, ExchangeMoneyLimit -
                            (noDrainageCount/monthNoDrainageNum)*monthNoExchangeMoneyDecline);
                    }

                    tzUser.setDeductionMoneyLimit(ExchangeMoneyLimit);

                    if(userService.updateTzUser(tzUser)>0){
                        UserDeductionMoneyLimitRecord record = new UserDeductionMoneyLimitRecord();
                        record.setUserId(tzUser.getUserId());
                        record.setDeductionMoneyLimit(deductionMoneyLimit);
                        record.setStatus(status);
                        record.setType(type);
                        record.setDescription(description);

                        if(deductionMoneyLimitRecordService.save(record)){
                            XxlJobHelper.log("用户userId:{},记录用户抵扣金兑换额度上限修改", tzUser.getUserId());
                        }else{
                            XxlJobHelper.log("用户userId:{},记录用户抵扣金兑换额度上限失败",tzUser.getUserId());
                        }
                    }
                }
                XxlJobHelper.log("当前用户:{},上个月邀请用户成为商家数量为0", user.getUserId());
            }
        }
    }

    @XxlJob("EveryDayComputeWeight")
    public void EveryDayComputeWeight() {

        XxlJobHelper.log("每天计算分量任务开始-----");

        //获取分量配置
        JSONObject componentConfig = configSettingService.getConfigValue("configFive");
        if (componentConfig.isEmpty()) {
            XxlJobHelper.log("分量配置值不存在");
            return;
        }

        //每天获取多少总分量维持达标
        BigDecimal dailyThreshold = (BigDecimal) componentConfig.get("dailyThreshold");
        //每天新增多少总分量为上限
        BigDecimal dailyExtraQuantityUnit = (BigDecimal) componentConfig.get("dailyExtraQuantityUnit");
//        //每天新增总分量能兑换的补贴金
//        BigDecimal dailyExtraRewardGold = (BigDecimal) componentConfig.get("dailyExtraRewardGold");
        //分量达标能获取多少平台补贴金上限额度
        Double dailyThresholdReward = (Double) componentConfig.get("dailyThresholdReward");

        //获取所有已经达到初始累计分量的消费者
        TzUser u = new TzUser();
        u.setUserType("C");
        u.setIsFirst("1");
        List<TzUser> userList = userService.selectTzUserList(u);
        for (TzUser user : userList) {
            try {
                XxlJobHelper.log("开始处理用户：" + user.getUsername());
//                //获取用户平台补贴金
//                BigDecimal userExchangeMoney = user.getDeductionMoney();
                //获取平台补贴金上限额度
                Double userExchangeMoneyLimit = user.getDeductionMoneyLimit();

                // 获取昨日零点时间
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, -1); // 减1天得到昨天
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                Date yesterdayStart = calendar.getTime();

                // 可以同时获取昨日结束时间(今天零点)
                calendar.add(Calendar.DATE, 1); // 加1天回到今天
                Date yesterdayEnd = calendar.getTime();

                TzUser tzUser = new TzUser();
                tzUser.setUserId(user.getUserId());

                //获取用户昨天总分量
                BigDecimal yesterdayTotalComponent = userComponentRecordService.sumUserComponent(user.getUserId(), yesterdayStart, yesterdayEnd);
               //当分量达标时
                if(yesterdayTotalComponent.compareTo(dailyThreshold)>=0){
                   if(yesterdayTotalComponent.compareTo(dailyThreshold.add(dailyExtraQuantityUnit))>=0){
                       userExchangeMoneyLimit+=dailyThresholdReward;
                   }
                }

//                //当昨天总分量大于/等于指标值
//                if (yesterdayTotalComponent.compareTo(dailyThreshold) >= 0) {
//                    //当昨天总分量大于/等于每天分量上限兑换的平台补贴金
//                    if (yesterdayTotalComponent.compareTo(dailyExtraQuantityUnit) >= 0) {
//                        //当用户补贴金上限额度大于/等于每天平台补贴金额度上限
//                        if (new BigDecimal(userExchangeMoneyLimit).compareTo(dailyExtraRewardGold) >= 0) {
//                            userExchangeMoney.add(dailyExtraRewardGold);
//                        } else {//小于时
//                            userExchangeMoney = dailyExtraRewardGold;
//                        }
//                    }
//                }
//                //计算用户当日的补贴金上限额度
//                userExchangeMoneyLimit = dailyThreshold.divide(new BigDecimal(dailyThresholdReward), 0, RoundingMode.HALF_UP).subtract(yesterdayTotalComponent).doubleValue();
//
////                user.setDeductionMoneyLimit(userExchangeMoneyLimit);
////                userService.updateTzUser(tzUser);
            } catch (Exception e) {
                XxlJobHelper.log("每天计算用户平台金兑换额度异常", user);
            }
        }
    }

}
