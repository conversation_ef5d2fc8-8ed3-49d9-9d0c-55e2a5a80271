package com.ruoyi.job.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.enums.TzUserStatus;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.service.ITzUserService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户睡眠状态定时处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TzUserSampleService {

    private final ITzUserService tzUserService;

    /**
     * 自动将三天未登录的用户设置为睡眠状态
     * 建议配置每天凌晨执行一次
     */
    @XxlJob("autoSetUserSleepStatusHandler")
    @Transactional(rollbackFor = Exception.class)
    public void autoSetUserSleepStatus() {
        try {
            XxlJobHelper.log("开始执行用户睡眠状态设置任务");

            // 计算三天前的时间
            Date threeDaysAgo = new Date(System.currentTimeMillis() - 3 * 24 * 60 * 60 * 1000);

            // 查询符合条件的用户：
            // 1. 状态为正常
            // 2. 最后登录时间在三天前或者为空
            // 3. 未被删除
            LambdaQueryWrapper<TzUser> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                .eq(TzUser::getStatus, TzUserStatus.OK.getCode()) // 正常状态
                .and(wrapper -> wrapper
                    .lt(TzUser::getLoginTime, threeDaysAgo) // 登录时间在三天前
                    .or()
                    .isNull(TzUser::getLoginTime) // 或者从未登录过
                )
                .eq(TzUser::getDelFlag, "0"); // 未删除

            List<TzUser> users = tzUserService.list(queryWrapper);

            if (users.isEmpty()) {
                XxlJobHelper.log("没有需要设置睡眠状态的用户");
                return;
            }

            // 获取用户ID列表
            List<Long> userIds = users.stream().map(TzUser::getUserId).collect(Collectors.toList());

            // 批量更新用户状态为睡眠状态
            LambdaUpdateWrapper<TzUser> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                .set(TzUser::getStatus, TzUserStatus.SLEEP.getCode()) // 设置为睡眠状态
                .in(TzUser::getUserId, userIds);

            boolean success = tzUserService.update(updateWrapper);

            XxlJobHelper.log("用户睡眠状态设置完成，处理用户数：" + users.size() + "，处理" + (success ? "成功" : "失败"));

        } catch (Exception e) {
            log.error("用户睡眠状态设置任务执行异常", e);
            XxlJobHelper.log("用户睡眠状态设置任务异常：" + e.getMessage());
            XxlJobHelper.handleFail();
        }
    }
}
