package com.ruoyi.job.service;


import com.ruoyi.mall.service.IConsignmentService;
import com.ruoyi.mall.service.IShopDrainageService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商家技术引流相关定时任务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopDrainageJobService {

    private final IShopDrainageService shopDrainageService;
    private final IConsignmentService consignmentService;


    /**
     * 检查商家七天内是否有开通技术引流，没有开通过的设置为失效状态
     */
    @XxlJob("shopDrainageStatusHandler")
    public void checkAndSetShopDrainageStatus() {
        try {
            XxlJobHelper.log("开始执行商家技术引流状态检查任务");

            int failureCount = shopDrainageService.checkAndSetShopDrainageStatus();

            XxlJobHelper.log("商家技术引流状态检查任务完成，设置失效状态的商家数量: " + failureCount);
        } catch (Exception e) {
            log.error("商家技术引流状态检查任务执行异常", e);
            XxlJobHelper.log("商家技术引流状态检查任务异常：" + e.getMessage());
            XxlJobHelper.handleFail();
        }
    }

    /**
     * 检查代销商家代销时间是否过期，过期的设置为失效状态
     */
    @XxlJob("consignmentConfigTime")
    public void checkConsignmentConfig() {
        try {
            XxlJobHelper.log("开始执行代销商家代销时间检查任务");
            int count= consignmentService.checkConsignmentConfig();

           XxlJobHelper.log("代销商家代销时间检查任务完成，设置失效状态的代销商家数量: " + count);
        } catch (Exception e) {
            log.error("代销商家代销时间检查任务执行异常", e);

        }
    }

    /**
     * 检查商家代销功能是否过期
     */
    @XxlJob("checkShopConsignmentConfig")
    public void checkShopConsignmentConfig(){
        log.info("开始检查店铺代销权限时间是否过期任务");
        try {
            XxlJobHelper.log("开始检查店铺代销权限时间是否过期任务");
            consignmentService.checkShopConsignmentConfig();
        }catch(Exception e){
            log.error("检查店铺代销权限时间是否过期任务异常",e);

            XxlJobHelper.log("代销商家代销时间检查任务异常：" + e.getMessage());
        }

    }
}
