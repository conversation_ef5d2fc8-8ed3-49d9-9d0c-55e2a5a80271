package com.ruoyi.job.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.enums.OrderStatusEnum;
import com.ruoyi.common.enums.OrderVirtualStatusEnum;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.service.IVirtualOrderService;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.service.IPayService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 虚拟订单定时处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VirtualOrderSampleService {

    private final IVirtualOrderService virtualOrderService;
    private final IPayService payService;

    /**
     * 自动取消30分钟未支付的虚拟订单
     * 每5分钟执行一次
     */
//    @Scheduled(cron = "0 */5 * * * ?")
//    @Transactional(rollbackFor = Exception.class)
    @XxlJob("autoCancelTimeoutVirtualOrders")
    public void autoCancelTimeoutVirtualOrders() {
        try {
            XxlJobHelper.log("开始执行超时未支付虚拟订单自动取消任务");

            // 2. 查询超时未支付的虚拟订单
            LambdaQueryWrapper<VirtualOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VirtualOrder::getStatus, OrderVirtualStatusEnum.PENDING_PAYMENT.getCode())
                .lt(VirtualOrder::getCreateTime, new Date(System.currentTimeMillis() - 30 * 60 * 1000))
                .eq(VirtualOrder::getDelFlag, "0");

            List<VirtualOrder> timeoutOrders = virtualOrderService.list(queryWrapper);
            if (timeoutOrders.isEmpty()) {
                XxlJobHelper.log("没有需要自动取消的订单");
                return;
            }

            // 3. 批量取消超时订单
            for (VirtualOrder order : timeoutOrders) {
                try {
                    // 取消关联的支付单
                    PayOrder payOrder = payService.getByOrderNo(order.getOrderNo());
                    if (payOrder != null) {
                        payService.cancelPayment(payOrder.getPaymentNo());
                    }

                    // 更新订单状态为已取消
                    order.setStatus(OrderVirtualStatusEnum.PENDING_RECEIVE.getCode());
                    order.setCancelTime(new Date());
                    order.setCancelReason("超时未支付自动取消");
                    virtualOrderService.updateById(order);

                    XxlJobHelper.log("自动取消超时虚拟订单成功,订单号:{}", order.getOrderNo());
                } catch (Exception e) {
                    XxlJobHelper.log("自动取消超时虚拟订单失败,订单号:{}", order.getOrderNo(), e);
                    XxlJobHelper.handleFail();
                }
            }
            XxlJobHelper.log("自动取消订单处理完成，处理订单数：" + timeoutOrders.size());
        } catch (Exception e) {
            XxlJobHelper.log("自动取消订单处理异常：" + e.getMessage());
            XxlJobHelper.handleFail();
        }
    }

    /**
     * 自动查询虚拟订单支付状态
     * 建议配置每1分钟执行一次
     * 主要用于处理支付中状态的订单，向第三方支付平台查询实际支付状态
     */
    @XxlJob("autoQueryVirtualOrderStatusHandler")
    public void autoQueryOrderStatus() {
        try {
            XxlJobHelper.log("开始执行虚拟订单状态查询任务");

            // 查询支付中的订单（状态为支付中），且创建时间在24小时以内的订单
            LambdaQueryWrapper<VirtualOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                // 支付中状态
                .eq(VirtualOrder::getStatus, OrderStatusEnum.DURING_PAYMENT.getCode())
                // 未删除
                .eq(VirtualOrder::getDelFlag, "0")
                .last("LIMIT 100"); // 每次最多处理100条，避免单次处理太多

            List<VirtualOrder> orders = virtualOrderService.list(queryWrapper);

            if (orders.isEmpty()) {
                XxlJobHelper.log("没有需要查询状态的订单");
                return;
            }

            int successCount = 0;
            int failCount = 0;

            // 遍历订单查询实际支付状态
            for (VirtualOrder virtualOrder : orders) {
                try {
                    PayOrder payOrder = payService.getByOrderNo(virtualOrder.getOrderNo());
                    virtualOrderService.queryOrderStatus(payOrder.getPaymentNo());
                    successCount++;
                } catch (Exception e) {
                    log.error("查询订单{}支付状态异常", virtualOrder.getId(), e);
                    failCount++;
                }
            }

            XxlJobHelper.log("订单状态查询完成，共处理{}笔订单。支付成功：{}笔，支付失败：{}笔",
                orders.size(), successCount, failCount);

        } catch (Exception e) {
            log.error("订单状态查询任务执行异常", e);
            XxlJobHelper.log("订单状态查询任务异常：" + e.getMessage());
            XxlJobHelper.handleFail();
        }
    }

}
