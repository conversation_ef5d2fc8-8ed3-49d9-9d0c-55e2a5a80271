package com.ruoyi.job.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.mall.domain.entity.OrderRefund;
import com.ruoyi.mall.enums.ReturnMoneyStsEnum;
import com.ruoyi.mall.service.IOrderRefundService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 订单退款定时处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderRefundSampleService {

    private final IOrderRefundService orderRefundService;

    /**
     * 自动同意超过7天未处理的退款申请
     * 建议配置每天凌晨执行一次
     * // @Scheduled(cron = "0 0 1 * * ?")
     */
    @XxlJob("autoAgreeTimeoutRefundHandler")
    public void autoAgreeTimeoutRefund() {
        try {
            XxlJobHelper.log("开始执行自动同意超时退款申请任务");

            // 计算7天前的时间点
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -7);
            Date timeoutDate = calendar.getTime();

            // 查询超过7天未处理的退款申请
            LambdaQueryWrapper<OrderRefund> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                // 申请状态
                .eq(OrderRefund::getReturnMoneySts, ReturnMoneyStsEnum.APPLY.value())
                // 申请时间早于7天前
                .lt(OrderRefund::getApplyTime, timeoutDate);

            List<OrderRefund> timeoutRefunds = orderRefundService.list(queryWrapper);

            if (timeoutRefunds.isEmpty()) {
                XxlJobHelper.log("没有需要自动同意的退款申请");
                return;
            }

            int successCount = 0;
            int failCount = 0;

            // 批量处理超时退款申请
            for (OrderRefund refund : timeoutRefunds) {
                try {
                    // 调用处理退款的方法（同意退款）
                    boolean result = orderRefundService.processRefund(refund.getRefundId());
                    if (result) {
                        XxlJobHelper.log("自动同意超时退款申请成功，退款ID: {}, 订单ID: {}", refund.getRefundId(), refund.getOrderId());
                        successCount++;
                    } else {
                        XxlJobHelper.log("自动同意超时退款申请失败，退款ID: {}, 订单ID: {}", refund.getRefundId(), refund.getOrderId());
                        failCount++;
                    }
                } catch (Exception e) {
                    XxlJobHelper.log("处理退款申请异常，退款ID: {}, 错误信息: {}", refund.getRefundId(), e.getMessage());
                    failCount++;
                }
            }

            XxlJobHelper.log("自动同意退款申请处理完成，总数: {}, 成功: {}, 失败: {}",
                timeoutRefunds.size(), successCount, failCount);

        } catch (Exception e) {
            log.error("自动同意退款申请任务执行异常", e);
            XxlJobHelper.log("自动同意退款申请任务异常：" + e.getMessage());
            XxlJobHelper.handleFail();
        }
    }
}
