# ruoyi-framework 框架核心模块

## 模块介绍

`ruoyi-framework` 是MallB电商平台的核心框架模块，提供系统的基础架构支撑，包括安全控制、缓存配置、数据源管理、AOP切面等核心功能，是连接各业务模块和基础设施的中间层。

## 主要功能

- 安全框架配置与集成
- 数据源管理与事务控制
- 缓存框架配置与管理
- 全局异常处理机制
- Web请求拦截与处理
- AOP切面定义与管理
- 系统启动加载配置
- 权限控制与验证
- 国际化支持
- 多环境配置支持

## 核心组件

- 安全配置：集成Spring Security，提供身份认证和授权
- 数据源配置：支持多数据源、动态数据源
- Redis缓存：集成Redis，支持缓存和分布式锁
- 权限控制：自定义权限注解和处理器
- 会话管理：用户会话状态维护
- 系统拦截器：请求日志、参数验证等
- 线程池配置：异步任务执行
- 定时任务支持：与任务调度集成

## 开发指南

1. 业务模块需依赖该框架模块
2. 安全控制通过注解或配置方式实现
3. 缓存操作通过CacheManager或直接Redis操作
4. 异常处理通过全局异常处理器配置

## 扩展点

- 自定义权限验证器
- 自定义缓存管理策略
- 自定义数据源选择器
- 自定义AOP切面 