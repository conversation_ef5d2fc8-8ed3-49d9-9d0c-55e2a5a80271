# ruoyi-mall-server 商城核心服务模块

## 模块介绍

`ruoyi-mall-server` 是MallB电商平台的核心业务服务模块，封装了电商系统的所有核心业务逻辑和数据处理功能。该模块不直接对外提供接口，而是作为核心服务被其他模块调用，确保业务逻辑的一致性和可复用性。

## 核心功能

- 商品管理核心
    - 商品信息处理
    - SKU与规格管理
    - 库存管理
    - 商品审核逻辑
    - 商品分类体系
- 订单中心
    - 订单创建逻辑
    - 订单状态流转
    - 库存锁定与释放
    - 价格计算规则
    - 优惠计算引擎
- 用户中心
    - 会员体系
    - 积分规则
    - 用户等级逻辑
    - 账户余额管理
- 营销中心
    - 优惠券处理逻辑
    - 活动规则处理
    - 满减逻辑
    - 折扣计算
- 支付体系
    - 支付流程处理
    - 退款处理逻辑
    - 支付方式管理
- 结算中心
    - 商家结算规则
    - 佣金计算
    - 分账处理

## 设计原则

- 高内聚低耦合：独立封装业务逻辑
- 分层设计：DTO、VO、Entity分离
- 事务一致性：保证数据完整性
- 可扩展性：支持业务规则灵活配置
- 可测试性：便于单元测试和集成测试

## 核心对象模型

- 商品(Product)：包含基本信息、规格、库存等
- 订单(Order)：订单信息、状态、支付信息等
- 用户(User)：用户信息、等级、积分等
- 店铺(Shop)：店铺信息、评分、结算信息等
- 交易(Transaction)：支付、退款等交易信息

## 服务调用方式

其他模块通过依赖注入调用本模块服务：

```java
@Autowired
private ProductService productService;

public void someMethod() {
    ProductDTO product = productService.getProductById(productId);
    // 进行业务处理
}
``` 