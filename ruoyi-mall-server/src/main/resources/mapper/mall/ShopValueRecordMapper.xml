<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.ShopValueRecordMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.ShopValueRecord" id="ShopValueRecordResult">
        <id property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="operationType" column="operation_type"/>
        <result property="beforeQuantificationValue" column="before_quantification_value"/>
        <result property="afterQuantificationValue" column="after_quantification_value"/>
        <result property="quantificationValueAmount" column="quantification_value_amount"/>
        <result property="beforeUserFaith" column="before_user_faith"/>
        <result property="afterUserFaith" column="after_user_faith"/>
        <result property="userFaithAmount" column="user_faith_amount"/>
        <result property="beforePromotionGold" column="before_promotion_gold"/>
        <result property="afterPromotionGold" column="after_promotion_gold"/>
        <result property="promotionGoldAmount" column="promotion_gold_amount"/>
        <result property="conversionRate" column="conversion_rate"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

</mapper>
