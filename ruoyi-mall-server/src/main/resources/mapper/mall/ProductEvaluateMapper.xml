<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.ProductEvaluateMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.ProductEvaluate" id="ProductEvaluateResult">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="productId" column="product_id"/>
        <result property="orderId" column="order_id"/>
        <result property="img" column="img"/>
        <result property="video" column="video"/>
        <result property="content" column="content"/>
        <result property="level" column="level"/>
        <result property="isShow" column="is_show"/>
        <result property="status" column="status"/>
        <result property="reply" column="reply"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selProductEvaluate" resultType="com.ruoyi.mall.domain.vo.EvaluateVo">
        SELECT
        e.id,
        u.nickname username,
        u.avatar,
        u.user_id,
        e.img,
        e.video,
        e.content,
        e.reply,
        e.is_show,
        e.level,
        e.id AS evaluate_id,
        e.product_id,
        e.order_id,
        e.create_time,
        i.product_name as name,
        i.product_image as cover,
        i.sku_spec,
        i.product_price AS price
        FROM mall_product_evaluate e
        INNER JOIN mall_product p ON e.product_id = p.id
        LEFT JOIN tz_user u ON e.user_id = u.user_id
        LEFT JOIN mall_order o ON o.id = e.order_id
        LEFT JOIN mall_order_item i ON i.id = e.item_id
        WHERE e.product_id= #{productId}
        AND e.status = '1'
        AND e.del_flag = '0'
        <choose>
            <when test="type == 1">

            </when>
            <when test="type == 2">
                AND e.level IN (4, 5)
            </when>
            <when test="type ==3">
                AND e.level = 3
            </when>
            <when test="type == 4">
                AND e.level IN (1, 2)
            </when>
            <when test="type == 5">
                AND e.level = 5 AND (e.img IS NOT NULL OR e.video IS NOT NULL)
            </when>
        </choose>
        ORDER BY e.create_time DESC
    </select>

    <select id="ExamineEvaluate" resultType="Integer">
        update mall_product_evaluate
        set status=#{status}
        where id = #{id}
    </select>

    <delete id="deleteProductEvaluateByIds" parameterType="String">
        delete from mall_product_evaluate where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
