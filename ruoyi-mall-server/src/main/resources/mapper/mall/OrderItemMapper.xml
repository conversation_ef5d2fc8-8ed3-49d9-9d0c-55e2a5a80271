<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.OrderItemMapper">

    <insert id="insertBatch">
        insert into mall_order_item (
        order_id,
        order_no,
        product_id,
        sku_id,
        user_id,
        shop_id,
        product_name,
        product_image,
        sku_spec,
        product_price,
        quantity,
        total_amount,
        pay_amount,
        discount_amount,
        comment_status,
        refund_status,
        del_flag,
        create_by,
        create_time,
        update_by,
        update_time,
        remark
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.orderId},
            #{item.orderNo},
            #{item.productId},
            #{item.skuId},
            #{item.userId},
            #{item.shopId},
            #{item.productName},
            #{item.productImage},
            #{item.skuSpec},
            #{item.productPrice},
            #{item.quantity},
            #{item.totalAmount},
            #{item.payAmount},
            #{item.discountAmount},
            #{item.commentStatus},
            #{item.refundStatus},
            #{item.delFlag},
            #{item.createBy},
            sysdate(),
            #{item.updateBy},
            sysdate(),
            #{item.remark}
            )
        </foreach>
    </insert>

</mapper>
