<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.ProductMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.Product" id="ProductResult">
        <id property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="name" column="name"/>
        <result property="cover" column="cover"/>
        <result property="images" column="images"/>
        <result property="price" column="price"/>
        <result property="originalPrice" column="original_price"/>
        <result property="stock" column="stock"/>
        <result property="sales" column="sales"/>
        <result property="unit" column="unit"/>
        <result property="description" column="description"/>
        <result property="content" column="content"/>
        <result property="status" column="status"/>
        <result property="isRecommend" column="is_recommend"/>
        <result property="isNew" column="is_new"/>
        <result property="isHot" column="is_hot"/>
        <result property="sort" column="sort"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <association property="shop" javaType="com.ruoyi.mall.domain.entity.Shop">
            <id property="id" column="shop_id"/>
            <result property="name" column="shop_name"/>
            <result property="logo" column="shop_logo"/>
            <result property="status" column="shop_status"/>
        </association>
        <association property="category" javaType="com.ruoyi.mall.domain.entity.Category">
            <id property="id" column="category_id"/>
            <result property="name" column="category_name"/>
            <result property="icon" column="category_icon"/>
            <result property="status" column="category_status"/>
        </association>
    </resultMap>

    <resultMap type="com.ruoyi.mall.domain.entity.Shop" id="ShopResult">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="logo" column="logo"/>
        <result property="introduction" column="introduction"/>
        <result property="address" column="address"/>
        <result property="phone" column="phone"/>
        <result property="businessHours" column="business_hours"/>
        <result property="type" column="type"/>
        <result property="authStatus" column="auth_status"/>
        <result property="businessStatus" column="business_status"/>
        <result property="status" column="status"/>
        <result property="shopCategoryId" column="shop_category_id"/>
        <result property="shopCategoryName" column="shop_category_name"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectProductVo">
        select p.*,
               s.name as shop_name,
               s.logo as shop_logo,
               s.status as shop_status,
               c.name as category_name,
               c.icon as category_icon,
               c.status as category_status
        from mall_product p
                 left join mall_shop s on s.id = p.shop_id
                 left join mall_category c on c.id = p.category_id
    </sql>

    <select id="selectProductList" parameterType="com.ruoyi.mall.domain.entity.Product" resultMap="ProductResult">
        <include refid="selectProductVo"/>
        <where>
            <if test="shopId != null">
                AND p.shop_id = #{shopId}
            </if>
            <if test="categoryId != null">
                AND p.category_id = #{categoryId}
            </if>
            <if test="name != null and name != ''">
                AND p.name like concat('%', #{name}, '%')
            </if>
            <if test="status != null and status != ''">
                AND p.status = #{status}
            </if>
            AND p.del_flag = '0'
        </where>
        order by p.sort asc
    </select>

    <select id="selectProductBySales" resultType="com.ruoyi.mall.domain.vo.ProductVo">
        select *
        from ((SELECT t.id, t1.consignment_shop as shopId, t.name, t.cover, t.price, t.sales, t.is_daixiao
               FROM mall_product t
                        RIGHT JOIN mall_consignment_product t1 ON t1.product_id = t.id
                        LEFT JOIN mall_shop t2 ON t2.id = t.shop_id
                        LEFT JOIN mall_shop t3 ON t3.id = t1.consignment_shop
               WHERE t.del_flag = '0'
                 AND t1.del_flag = '0'
                 AND t2.del_flag = '0'
                 AND t3.del_flag = '0'
                 AND t.status = '0'
                 AND t.audit_status = '1'
                 AND t.status = '1'
                 AND t.is_daixiao = 1
                 AND t2.status = '0'
                 AND t2.auth_status = '1'
                 AND t3.status = '0'
                 AND t3.type = '0'
                 AND DATE(t2.jurisdiction_expire_time) >= DATE(NOW())
                 AND t3.consignment_permission = 1
                 AND DATE(t3.consignment_expire_time) >= DATE(NOW())
               GROUP BY t1.id)
              UNION ALL
              (SELECT t.id, t.shop_id, t.name, t.cover, t.price, t.sales, t.is_daixiao
               FROM mall_product t
                        LEFT JOIN mall_shop t1 ON t1.id = t.shop_id
               WHERE t.del_flag = '0'
                 AND t1.del_flag = '0'
                 AND t.audit_status = '1'
                 AND t.status = '1'
                 AND t1.status = '0'
                 AND t1.auth_status = '1'
               GROUP BY t.id)) as a
        order by sales desc
    </select>

    <select id="selectProductByBrowse" resultType="com.ruoyi.mall.domain.vo.ProductVo">
        select *
        from ((SELECT t.id, t1.consignment_shop as shopId, t.name, t.cover, t.price, t.sales, t.is_daixiao
               FROM mall_product t
                        RIGHT JOIN mall_consignment_product t1 ON t1.product_id = t.id
                        LEFT JOIN mall_shop t2 ON t2.id = t.shop_id
                        LEFT JOIN mall_shop t3 ON t3.id = t1.consignment_shop
                        left join mall_shop_browse t4 on t4.shop_id = t2.id
               WHERE t.del_flag = '0'
                 AND t1.del_flag = '0'
                 AND t2.del_flag = '0'
                 AND t3.del_flag = '0'
                 AND t4.del_flag = '0'
                 AND t.audit_status = '1'
                 AND t.status = '1'
                 AND t.is_daixiao = 1
                 AND t1.status = '0'
                 AND t2.status = '0'
                 AND t3.status = '0'
                 AND t3.type = '0'
                 AND t4.user_id = #{userId}
                 AND DATE(t2.jurisdiction_expire_time) >= DATE(NOW())
                 AND t3.consignment_permission = 1
                 AND DATE(t3.consignment_expire_time) >= DATE(NOW())
                 AND t.create_time BETWEEN (CURDATE() - INTERVAL 30 DAY) AND (CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND)
               GROUP BY t1.id)
              UNION ALL
              (SELECT t.id, t.shop_id, t.name, t.cover, t.price, t.sales, t.is_daixiao
               FROM mall_product t
                        LEFT JOIN mall_shop t1 ON t1.id = t.shop_id
                        left join mall_shop_browse t2 on t2.shop_id = t1.id
               WHERE t.del_flag = '0'
                 AND t1.del_flag = '0'
                 AND t2.del_flag = '0'
                 AND t.audit_status = '1'
                 AND t.status = '1'
                 AND t1.status = '0'
                 AND t2.user_id = #{userId}
                 AND t.create_time BETWEEN (CURDATE() - INTERVAL 30 DAY) AND (CURDATE() + INTERVAL 1 DAY - INTERVAL 1 SECOND)
               GROUP BY t.id)) as a
    </select>

    <select id="UserBrowseLikeProduct" resultType="com.ruoyi.mall.domain.vo.ProductVo">
        select *
        from mall_product
        where del_flag = '0'
          and status = '1'
          and audit_status = '1'
    </select>

    <!-- 根据ID查询店铺信息 -->
    <select id="selectShopById" parameterType="Long" resultMap="ShopResult">
        select id,
               name,
               logo,
               introduction,
               address,
               phone,
               business_hours,
               type,
               auth_status,
               business_status,
               status,
               shop_category_id,
               shop_category_name,
               del_flag
        from mall_shop
        where id = #{shopId}
          and del_flag = '0'
    </select>

    <!-- 更新店铺信息 -->
    <update id="updateShop" parameterType="com.ruoyi.mall.domain.entity.Shop">
        update mall_shop
        <set>
            <if test="shopCategoryId != null">shop_category_id = #{shopCategoryId},</if>
            <if test="shopCategoryName != null and shopCategoryName != ''">shop_category_name = #{shopCategoryName},
            </if>
            update_time = sysdate()
        </set>
        where id = #{id}
    </update>

    <select id="getRandomConsignmentProductPage" resultType="com.ruoyi.mall.domain.vo.ProductVo">
        SELECT t.id, t1.consignment_shop as shopId, t.name, t.cover, t.price, t.sales, t.is_daixiao
        FROM mall_product t
                 RIGHT JOIN mall_consignment_product t1 ON t1.product_id = t.id
                 LEFT JOIN mall_shop t2 ON t2.id = t.shop_id
        WHERE t.del_flag = '0'
          AND t1.del_flag = '0'
          AND t2.del_flag = '0'
          AND t.audit_status = '1'
          AND t.status = '1'
          AND t.is_daixiao = 1
          AND t1.status = '0'
          AND t2.status = '0'
          AND t2.type = '0'
          AND DATE(t2.consignment_expire_time) >= DATE(NOW())
        ORDER BY t1.id, RAND()
    </select>

    <select id="getRandomProductPage" resultType="com.ruoyi.mall.domain.vo.ProductVo">
        select *
        from ((SELECT t.id,
                      t1.consignment_shop as shopId,
                      t.name,
                      t.cover,
                      t.price,
                      t.sales,
                      t.is_daixiao,
                      "1"                 as type
               FROM mall_product t
                        RIGHT JOIN mall_consignment_product t1 ON t1.product_id = t.id
                        LEFT JOIN mall_shop t2 ON t2.id = t.shop_id
                        LEFT JOIN mall_shop t3 ON t3.id = t1.consignment_shop
               WHERE t.del_flag = '0'
                 AND t1.del_flag = '0'
                 AND t2.del_flag = '0'
                 AND t3.del_flag = '0'
                 AND t.audit_status = '1'
                 AND t.status = '1'
                 AND t.is_daixiao = 1
                 AND t1.status = '0'
                 AND t2.status = '0'
                 AND t3.status = '0'
                 AND DATE(t2.jurisdiction_expire_time) >= DATE(NOW())
                 AND t3.consignment_permission = 1
                 AND DATE(t3.consignment_expire_time) >= DATE(NOW())
               ORDER BY t1.id, RAND())
              UNION ALL
              (SELECT t.id,
                      t.shop_id,
                      t.name,
                      t.cover,
                      t.price,
                      t.sales,
                      t.is_daixiao,
                      "2" as type
               FROM mall_product t
                        LEFT JOIN mall_shop t1 ON t1.id = t.shop_id
               WHERE t.del_flag = '0'
                 AND t1.del_flag = '0'
                 AND t.audit_status = '1'
                 AND t.status = '1'
                 AND t1.status = '0'
               ORDER BY t.id, RAND())) as a
        order by type = 1 desc
    </select>

    <select id="getProductByCategoryIds" resultType="com.ruoyi.mall.domain.vo.ProductVo">
        SELECT * FROM (
        -- 第一个子查询：代销商品
        SELECT
        t.id,
        t1.consignment_shop as shopId,
        t.name,
        t.cover,
        t.price,
        t.sales,
        t.is_daixiao,
        "1" as type,
        t.category_name,
        t.description
        FROM mall_product t
        INNER JOIN mall_consignment_product t1 ON t1.product_id = t.id
        INNER JOIN mall_shop t2 ON t2.id = t.shop_id
        INNER JOIN mall_shop t3 ON t3.id = t1.consignment_shop
        WHERE t.del_flag = '0'
        AND t1.del_flag = '0'
        AND t2.del_flag = '0'
        AND t3.del_flag = '0'
        AND t.audit_status = '1'
        AND t.status = '1'
        AND t.is_daixiao = 1
        AND t1.status = '0'
        AND t2.status = '0'
        AND t3.status = '0'
        AND t2.jurisdiction_expire_time >= CURDATE()
        AND t3.consignment_permission = 1
        AND t3.consignment_expire_time >= CURDATE()
        AND t2.consignment_expire_time >= CURDATE()
        AND t.category_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY t1.id
        UNION ALL
        -- 第二个子查询：普通商品
        SELECT
        t.id,
        t.shop_id as shopId,
        t.name,
        t.cover,
        t.price,
        t.sales,
        t.is_daixiao,
        "2" as type,
        t.category_name,
        t.description
        FROM mall_product t
        INNER JOIN mall_shop t1 ON t1.id = t.shop_id
        WHERE t.del_flag = '0'
        AND t1.del_flag = '0'
        AND t.audit_status = '1'
        AND t.status = '1'
        AND t1.status = '0'
        AND t.category_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY t.id
        ) AS combined_results
        <where>
            <if test="keyword != null and keyword != ''">
                AND (
                name LIKE CONCAT('%', #{keyword}, '%')
                OR category_name LIKE CONCAT('%', #{keyword}, '%')
                OR description LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        ORDER BY type = 1 DESC
    </select>

    <select id="getProductByKeyword" resultType="com.ruoyi.mall.domain.vo.ProductVo">
        select * from ((
        SELECT t.id, t1.consignment_shop as shopId, t.name, t.cover, t.price, t.sales, t.is_daixiao,"1" as type
        FROM mall_product t
        RIGHT JOIN mall_consignment_product t1 ON t1.product_id = t.id
        LEFT JOIN mall_shop t2 ON t2.id = t.shop_id
        LEFT JOIN mall_shop t3 ON t3.id = t1.consignment_shop
        <where>
            t.del_flag = '0'
            AND t1.del_flag = '0'
            AND t2.del_flag = '0'
            AND t3.del_flag = '0'
            AND t.audit_status = '1'
            AND t.status = '1'
            AND t.is_daixiao = 1
            AND t1.status= '0'
            AND t2.status = '0'
            AND t3.status = '0'
            AND DATE(t2.jurisdiction_expire_time) >= DATE(NOW())
            AND t3.consignment_permission=1
            AND DATE(t3.consignment_level_expire_time) >= DATE(NOW())
        </where>
        ORDER BY t1.id,RAND() )
        UNION ALL
        (SELECT t.id, t.shop_id, t.name, t.cover, t.price, t.sales, t.is_daixiao,"2" as type
        FROM mall_product t
        LEFT JOIN mall_shop t1 ON t1.id = t.shop_id
        <where>
            t.del_flag = '0'
            AND t1.del_flag = '0'
            AND t.audit_status = '1'
            AND t.status = '1'
            AND t1.status = '0'
            <if test="keyword != null and keyword != ''">
                AND t.name like concat('%', #{keyword}, '%')
                OR t.category_name like concat('%', #{keyword}, '%')
                OR t.description like concat('%', #{keyword}, '%')
            </if>
        </where>
        ORDER BY t.id,RAND())
        )as a order by type=1 desc
    </select>

    <!-- 优化的代销商品查询 - 支持商品编号、商品名称、企业手机号码、代销状态的搜索 -->
    <select id="selectConsignmentProductsOptimized" resultType="com.ruoyi.mall.domain.vo.DaiXiaoInfoVo">
        SELECT
        p.id AS productId,
        p.shop_id AS shopId,
        p.name,
        p.cover,
        p.jump_url AS jumpUrl,
        p.status,
        p.price,
        p.daixiao_type,
        p.daixiao_type_value,
        s.phone,
        cp.status AS daiXiaoStatus,
        p.create_time AS createTime
        FROM mall_product p
        INNER JOIN (
        -- 先筛选符合条件的店铺，减少连接数据量
        SELECT id, phone
        FROM mall_shop
        WHERE auth_status = '1'
        AND status = '0'
        AND del_flag = '0'
        <if test="shopIds != null and shopIds.size() > 0">
            AND id IN
            <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>
        <if test="query.phone != null and query.phone != ''">
            AND phone LIKE CONCAT('%', #{query.phone}, '%')
        </if>
        ) s ON p.shop_id = s.id
        LEFT JOIN mall_consignment_product cp ON (
        cp.product_id = p.id
        AND cp.shop_id = s.id
        AND cp.del_flag = '0'
        )
        WHERE p.is_daixiao = 1
        AND p.audit_status = '1'
        AND p.status = '1'
        AND p.del_flag = '0'
        <!-- 商品编号搜索 -->
        <if test="query.productId != null and query.productId != ''">
            AND p.id LIKE CONCAT('%', #{query.productId}, '%')
        </if>
        <!-- 商品名称搜索 -->
        <if test="query.productName != null and query.productName != ''">
            AND p.name LIKE CONCAT('%', #{query.productName}, '%')
        </if>
        <!-- 代销状态搜索 -->
        <if test="query.daiXiaoStatus != null and query.daiXiaoStatus != ''">
            <choose>
                <!-- 处理未代销状态（'0'） -->
                <when test='query.daiXiaoStatus.equals("0")'>
                    AND (cp.status IS NULL OR cp.status = '0' OR cp.status = 0)
                </when>
                <!-- 处理其他代销状态 -->
                <otherwise>
                    AND cp.status = #{query.daiXiaoStatus}
                </otherwise>
            </choose>
        </if>
        ORDER BY COALESCE(cp.create_time, p.create_time) DESC
    </select>


</mapper>
