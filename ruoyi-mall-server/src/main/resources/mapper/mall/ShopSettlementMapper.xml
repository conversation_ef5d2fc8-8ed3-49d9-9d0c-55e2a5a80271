<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.ShopSettlementMapper">


    <!-- 结算列表 -->
    <select id="selectListSettlementStatsByPage" resultType="com.ruoyi.mall.domain.vo.SettlementStatsVO">
        SELECT
        m.id,
        s.update_time AS settlement_date,
        m.name AS shop_name,
        m.phone AS phone,
        s.actual_amount AS bank_card_actual_amount,
        SUM(s.actual_amount) OVER (PARTITION BY s.shop_id,DATE(s.update_time)) AS total_amount
        FROM mall_shop_settlement s
        JOIN mall_shop m ON s.shop_id = m.id
        <where>
            s.del_flag = '0' AND m.del_flag = '0' and s.status = '4'
            <if test="dto.phone != null and dto.phone != ''">
                AND m.phone = #{dto.phone}
            </if>
            <if test="dto.beginTime != null">
                <![CDATA[
                 AND s.update_time>= #{dto.beginTime}
             ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[
                 AND s.update_time<= #{dto.endTime}
                 ]]>
            </if>
        </where>
        ORDER BY s.update_time DESC
        <!--SELECT
        ms.id AS shop_id,
        ms.name AS shop_name,
        ms.phone ,
        DATE(ms_settle.update_time) AS settlement_date,
        SUM(ms_settle.actual_amount) AS bank_card_actual_amount,
        SUM(SUM(ms_settle.actual_amount)) OVER (PARTITION BY DATE(ms_settle.update_time)) AS total_amount
        FROM
        mall_shop ms
        JOIN
        mall_shop_settlement ms_settle ON ms.id = ms_settle.shop_id
        <where>
            ms_settle.status = '4' &#45;&#45; 假设只统计已打款的结算单
            AND ms_settle.del_flag = '0'
            AND ms.del_flag = '0'
            <if test="dto.phone != null and dto.phone != ''">
                AND m.phone = #{dto.phone}
            </if>
            <if test="dto.beginTime != null">
                <![CDATA[
                AND s.update_time>= #{dto.beginTime}
            ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[
                AND s.update_time<= #{dto.endTime}
                ]]>
            </if>
        </where>
        GROUP BY
        ms.id, ms.name, ms.phone, DATE(ms_settle.update_time)
        ORDER BY
        settlement_date DESC, ms.id DESC-->
    </select>

    <!-- 结算列表 (不分页) -->
    <select id="selectSettlementStatsList" resultType="com.ruoyi.mall.domain.vo.SettlementStatsVO">
        SELECT
        m.id,
        s.update_time AS settlement_date,
        m.name AS shop_name,
        m.phone AS phone,
        s.actual_amount AS bank_card_actual_amount,
        SUM(s.actual_amount) OVER (PARTITION BY s.shop_id,DATE(s.update_time)) AS total_amount
        FROM mall_shop_settlement s
        JOIN mall_shop m ON s.shop_id = m.id
        <where>
            s.del_flag = '0' AND m.del_flag = '0' and s.status = '4'
            <if test="dto.phone != null and dto.phone != ''">
                AND m.phone = #{dto.phone}
            </if>
            <if test="dto.beginTime != null">
                <![CDATA[
                AND s.update_time>= #{dto.beginTime}
            ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[
                AND s.update_time<= #{dto.endTime}
                ]]>
            </if>
        </where>
        ORDER BY s.update_time DESC
        <!-- SELECT
         ms.id AS shop_id,
         ms.name AS shop_name,
         ms.phone ,
         DATE(ms_settle.update_time) AS settlement_date,
         SUM(ms_settle.actual_amount) AS bank_card_actual_amount,
         SUM(SUM(ms_settle.actual_amount)) OVER (PARTITION BY DATE(ms_settle.update_time)) AS total_amount
         FROM
         mall_shop ms
         JOIN
         mall_shop_settlement ms_settle ON ms.id = ms_settle.shop_id
         <where>
             ms_settle.status = '4' &#45;&#45; 假设只统计已打款的结算单
             AND ms_settle.del_flag = '0'
             AND ms.del_flag = '0'
             <if test="dto.phone != null and dto.phone != ''">
                 AND m.phone = #{dto.phone}
             </if>
             <if test="dto.beginTime != null">
                 <![CDATA[
                 AND s.update_time>= #{dto.beginTime}
             ]]>
             </if>
             <if test="dto.endTime != null">
                 <![CDATA[
                 AND s.update_time<= #{dto.endTime}
                 ]]>
             </if>
         </where>
         GROUP BY
         ms.id, ms.name, ms.phone, DATE(ms_settle.update_time)
         ORDER BY
         settlement_date DESC, ms.id DESC-->
    </select>


    <!-- 商家结算记录列表 (分页) -->

    <!-- 定义 SQL 片段 -->
    <sql id="selectSettlementRecords">
        SELECT
        s.account_name,
        m.legal_person_phone,
        s.update_time settlement_time,
        s.settlement_no,
        s.account_no ,
        m.business_name ,
        /*s.settlement_type,*/
        s.actual_amount,
        s.status
        FROM mall_shop_settlement s
        JOIN mall_shop m ON s.shop_id = m.id
        <where>
            s.del_flag = '0'
            AND m.del_flag = '0'
            <if test="dto.accountName != null and dto.accountName != ''">
                AND s.account_name LIKE CONCAT('%', #{dto.accountName}, '%')
            </if>
            <if test="dto.legalPersonPhone != null and dto.legalPersonPhone != ''">
                AND m.legal_person_phone LIKE CONCAT('%', #{dto.legalPersonPhone}, '%')
            </if>
            <!--<if test="dto.settlementType != null and dto.settlementType != ''">
                AND s.settlement_type = #{dto.settlementType}
            </if>-->
            <if test="dto.status != null and dto.status != ''">
                AND s.status = #{dto.status}
            </if>
            <if test="dto.beginTime != null">
                <![CDATA[
                AND s.update_time>= #{dto.beginTime}
            ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[
                AND s.update_time<= #{dto.endTime}
                ]]>
            </if>
        </where>
        ORDER BY s.update_time DESC
    </sql>
    <select id="selectSettlementRecordPage" resultType="com.ruoyi.mall.domain.vo.SettlementRecordVO">
        <!--SELECT
        s.account_name,
        m.legal_person_phone,
        s.update_time settlement_time,
        s.settlement_no,
        s.account_no ,
        m.business_name ,
        /*s.settlement_type,*/
        s.actual_amount,
        s.status
        FROM mall_shop_settlement s
        JOIN mall_shop m ON s.shop_id = m.id
        <where>
            s.del_flag = '0'
            AND m.del_flag = '0'
            <if test="dto.accountName != null and dto.accountName != ''">
                AND s.account_name LIKE CONCAT('%', #{dto.accountName}, '%')
            </if>
            <if test="dto.legalPersonPhone != null and dto.legalPersonPhone != ''">
                AND m.legal_person_phone LIKE CONCAT('%', #{dto.legalPersonPhone}, '%')
            </if>
            &lt;!&ndash;<if test="dto.settlementType != null and dto.settlementType != ''">
                AND s.settlement_type = #{dto.settlementType}
            </if>&ndash;&gt;
            <if test="dto.status != null and dto.status != ''">
                AND s.status = #{dto.status}
            </if>
            <if test="dto.beginTime != null">
                <![CDATA[
                AND s.update_time>= #{dto.beginTime}
            ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[
                AND s.update_time<= #{dto.endTime}
                ]]>
            </if>
        </where>
        ORDER BY s.update_time DESC-->
        <include refid="selectSettlementRecords"/>
    </select>
    <select id="selectSettlementRecordList" resultType="com.ruoyi.mall.domain.vo.SettlementRecordVO">
        <!-- <include refid="selectSettlementRecords"/>-->
        SELECT
        s.account_name,
        m.legal_person_phone,
        s.update_time as settlement_time,
        s.settlement_no,
        s.account_no ,
        m.business_name ,
        /*s.settlement_type,*/
        s.actual_amount,
        /* s.status*/
        CASE s.status
        WHEN '0' THEN '待审核'
        WHEN '1' THEN '审核通过'
        WHEN '2' THEN '审核拒绝'
        WHEN '3' THEN '已打款'
        WHEN '4' THEN '已完成'
        ELSE '未知状态'
        END AS status
        FROM mall_shop_settlement s
        JOIN mall_shop m ON s.shop_id = m.id
        <where>
            s.del_flag = '0'
            AND m.del_flag = '0'
            <if test="dto.accountName != null and dto.accountName != ''">
                AND s.account_name LIKE CONCAT('%', #{dto.accountName}, '%')
            </if>
            <if test="dto.legalPersonPhone != null and dto.legalPersonPhone != ''">
                AND m.legal_person_phone LIKE CONCAT('%', #{dto.legalPersonPhone}, '%')
            </if>
            <!-- <if test="dto.settlementType != null and dto.settlementType != ''">
                 AND s.settlement_type = #{dto.settlementType}
             </if>-->
            <if test="dto.status != null and dto.status != ''">
                AND s.status = #{dto.status}
            </if>
            <if test="dto.beginTime != null">
                <![CDATA[
                AND s.update_time>= #{dto.beginTime}
            ]]>
            </if>
            <if test="dto.endTime != null">
                <![CDATA[
                AND s.update_time<= #{dto.endTime}
                ]]>
            </if>
        </where>
        ORDER BY s.update_time DESC
    </select>


</mapper>
