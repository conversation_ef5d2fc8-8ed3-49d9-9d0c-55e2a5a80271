<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.ShopReturnAddressMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.ShopReturnAddress" id="ShopReturnAddressResult">
        <result property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverPhone" column="receiver_phone"/>
        <result property="provinceCode" column="province_code"/>
        <result property="provinceName" column="province_name"/>
        <result property="cityCode" column="city_code"/>
        <result property="cityName" column="city_name"/>
        <result property="districtCode" column="district_code"/>
        <result property="districtName" column="district_name"/>
        <result property="detailAddress" column="detail_address"/>
        <result property="postCode" column="post_code"/>
        <result property="isDefault" column="is_default"/>
        <result property="status" column="status"/>
        <result property="addressRemark" column="address_remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


</mapper>
