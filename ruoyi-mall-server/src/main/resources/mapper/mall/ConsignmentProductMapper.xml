<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.ConsignmentProductMapper">

    <select id="selectCountGroupByProductId" resultType="map">
        SELECT product_id AS productId,
        count(*) AS count
        FROM mall_consignment_product
        WHERE del_flag = '0'
        AND (product_id IN
        <foreach collection="productIds" item="productId" open="(" close=")" separator=",">
            #{productId}
        </foreach>
        AND STATUS = '0')
        GROUP BY product_id
    </select>

</mapper>
