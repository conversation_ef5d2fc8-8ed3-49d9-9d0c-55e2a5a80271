<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.ShopOrderDetailMapper">


    <resultMap id="ShopOrderDetailResult" type="com.ruoyi.mall.domain.vo.ShopOrderDetailVO">
        <id property="receiveTime" column="receive_time" javaType="java.time.LocalDateTime"/><!--java.util.Date-->
        <result property="shopName" column="name"/>
        <result property="phone" column="phone"/>
        <result property="orderNo" column="order_no"/>
        <result property="orderName" column="order_name"/>
        <result property="payAmount" column="pay_amount" javaType="java.math.BigDecimal"/>
        <result property="dayTotalAmount" column="day_total_amount" javaType="java.math.BigDecimal"/>
    </resultMap>


    <select id="selectMallShopOrderDetails" resultMap="ShopOrderDetailResult">

        <!-- SELECT
            o.receive_time,
            s.name,
            s.phone,
            o.order_no,
            o.order_name,
            o.pay_amount,
            t.day_total_amount
            FROM mall_order o
            JOIN mall_shop s ON o.shop_id = s.id
            LEFT JOIN (
            SELECT
            shop_id,
            DATE_FORMAT(receive_time, '%Y-%m-%d') AS order_date,
            SUM(pay_amount) AS day_total_amount
            FROM mall_order
            WHERE del_flag = '0'
            GROUP BY shop_id, order_date
            ) t ON t.shop_id = o.shop_id AND t.order_date = DATE_FORMAT(o.receive_time, '%Y-%m-%d')
            <where>
                o.del_flag = '0'
                AND s.del_flag = '0'
                and o.status = '4'
                <if test="query.phone != null and query.phone != ''">
                    AND s.phone = #{query.phone}
                </if>
                <if test="query.beginTime != null">
                    <![CDATA[
            AND o.receive_time >= #{query.beginTime}
            ]]>
                </if>
                <if test="query.endTime != null">
                    <![CDATA[
            AND o.receive_time <= #{query.endTime}
            ]]>
                </if>
                <if test="query.shopName != null and query.shopName != ''">
                    AND s.name LIKE concat('%',#{query.shopName},'%')
                </if>
            </where>
            ORDER BY o.receive_time DESC-->
        SELECT
        receive_time,
        name,
        phone,
        order_no,
        order_name,
        pay_amount,
        CASE WHEN rn = 1 THEN day_total_amount ELSE NULL END AS day_total_amount
        FROM (
        SELECT
        o.receive_time,
        s.name,
        s.phone,
        o.order_no,
        o.order_name,
        o.pay_amount,
        SUM(o.pay_amount) OVER (PARTITION BY s.id, DATE(o.receive_time)) AS day_total_amount,
        ROW_NUMBER() OVER (PARTITION BY s.id, DATE(o.receive_time) ORDER BY o.receive_time DESC) AS rn,
        MAX(o.receive_time) OVER (PARTITION BY s.id) AS max_receive_time
        FROM mall_order o
        JOIN mall_shop s ON o.shop_id = s.id
        WHERE o.del_flag = '0'
        AND s.del_flag = '0'
        AND o.status = '4'
        <!-- 手机号精确查询 -->
        <if test="query.phone != null and query.phone != ''">
            AND s.phone = #{query.phone}
        </if>
        <!-- 时间范围过滤 -->
        <if test="query.beginTime != null">
            <![CDATA[
                  AND o.receive_time >= #{query.beginTime}
              ]]>
        </if>
        <if test="query.endTime != null">
            <![CDATA[
                  AND o.receive_time <= #{query.endTime}
              ]]>
        </if>
        <!-- 商家名称模糊查询 -->
        <if test="query.shopName != null and query.shopName != ''">
            AND s.name LIKE CONCAT('%', #{query.shopName}, '%')
        </if>
        ) AS subquery
        ORDER BY max_receive_time DESC, name, receive_time DESC
    </select>


    <select id="selectMallShopOrderDetailsForExport" resultMap="ShopOrderDetailResult">
        <!--  SELECT
         o.receive_time,
         s.name,
         s.phone,
         o.order_no,
         o.order_name,
         o.pay_amount,
         t.day_total_amount
         FROM mall_order o
         JOIN mall_shop s ON o.shop_id = s.id
         LEFT JOIN (
         SELECT
         shop_id,
         DATE_FORMAT(receive_time, '%Y-%m-%d') AS order_date,
         SUM(pay_amount) AS day_total_amount
         FROM mall_order
         WHERE del_flag = '0'
         GROUP BY shop_id, order_date
         ) t ON t.shop_id = o.shop_id AND t.order_date = DATE_FORMAT(o.receive_time, '%Y-%m-%d')-->
        <!-- <where>
             o.del_flag = '0'
             AND s.del_flag = '0'
             and o.status = '4'
             <if test="query.phone != null and query.phone != ''">
                 AND s.phone = #{query.phone}
             </if>
             <if test="query.beginTime != null">
                 <![CDATA[
         AND o.receive_time >= #{query.beginTime}
         ]]>
             </if>
             <if test="query.endTime != null">
                 <![CDATA[
         AND o.receive_time <= #{query.endTime}
         ]]>
             </if>
             <if test="query.shopName != null and query.shopName != ''">
                 AND s.name LIKE concat('%',#{query.shopName},'%')
             </if>
         </where>
         ORDER BY o.receive_time DESC-->
        SELECT
        receive_time,
        name,
        phone,
        order_no,
        order_name,
        pay_amount,
        CASE WHEN rn = 1 THEN day_total_amount ELSE NULL END AS day_total_amount
        FROM (
        SELECT
        o.receive_time,
        s.name,
        s.phone,
        o.order_no,
        o.order_name,
        o.pay_amount,
        SUM(o.pay_amount) OVER (PARTITION BY s.id, DATE(o.receive_time)) AS day_total_amount,
        ROW_NUMBER() OVER (PARTITION BY s.id, DATE(o.receive_time) ORDER BY o.receive_time DESC) AS rn,
        MAX(o.receive_time) OVER (PARTITION BY s.id) AS max_receive_time
        FROM mall_order o
        JOIN mall_shop s ON o.shop_id = s.id
        WHERE o.del_flag = '0'
        AND s.del_flag = '0'
        AND o.status = '4'
        <!-- 手机号精确查询 -->
        <if test="query.phone != null and query.phone != ''">
            AND s.phone = #{query.phone}
        </if>
        <!-- 时间范围过滤 -->
        <if test="query.beginTime != null">
            <![CDATA[
                  AND o.receive_time >= #{query.beginTime}
              ]]>
        </if>
        <if test="query.endTime != null">
            <![CDATA[
                  AND o.receive_time <= #{query.endTime}
              ]]>
        </if>
        <!-- 商家名称模糊查询 -->
        <if test="query.shopName != null and query.shopName != ''">
            AND s.name LIKE CONCAT('%', #{query.shopName}, '%')
        </if>
        ) AS subquery
        ORDER BY max_receive_time DESC, name, receive_time DESC
    </select>
</mapper>
