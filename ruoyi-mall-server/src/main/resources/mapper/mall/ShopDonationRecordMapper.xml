<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.ShopDonationRecordMapper">

    <resultMap type="com.ruoyi.mall.domain.vo.ShopDonationRecordVo" id="ShopDonationRecordResult">
        <id property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="userId" column="user_id"/>
        <result property="receiverPhone" column="receiver_phone"/>
        <result property="receiverNickname" column="receiver_nickname"/>
        <result property="amount" column="amount"/>
        <result property="status" column="status"/>
        <result property="statusName" column="status_name"/>
        <result property="donationType" column="donation_type"/>
        <result property="donationTypeName" column="donation_type_name"/>
        <result property="donationTime" column="donation_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <select id="selectDonationRecordPage" resultMap="ShopDonationRecordResult">
        SELECT
        r.id,
        r.shop_id,
        s.name as shop_name,
        r.user_id,
        r.receiver_phone,
        u.nickname as receiver_nickname,
        r.amount,
        r.loss_amount,
        r.actual_amount,
        r.status,
        CASE r.status WHEN '0' THEN '失败' WHEN '1' THEN '成功' ELSE '未知' END as status_name,
        r.donation_type,
        CASE r.donation_type WHEN '1' THEN '商家赠送' WHEN '2' THEN '代销赠送' ELSE '未知' END as donation_type_name,
        r.donation_time,
        r.remark
        FROM
        mall_shop_donation_record r
        LEFT JOIN mall_shop s ON r.shop_id = s.id
        LEFT JOIN tz_user u ON r.user_id = u.user_id
        WHERE
        r.del_flag = '0'
        AND r.shop_id = #{shopId}
        <if test="beginTime != null ">
            AND r.donation_time &gt;= #{beginTime}
        </if>
        <if test="endTime != null ">
            AND r.donation_time &lt; #{endTime}
        </if>
        <if test="phone != null and phone != ''">
            AND r.receiver_phone LIKE CONCAT('%', #{phone}, '%')
        </if>
        <if test="donationType != null and donationType != ''">
            AND r.donation_type = #{donationType}
        </if>
        ORDER BY
        r.donation_time DESC
    </select>
</mapper>
