<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.AuthorizationAreaMapper">

    <select id="queryAreaAuthorizeByShop" resultType="Map">
        SELECT u.id,
               u.phone,
               s.business_name as username,
               a.`name`        AS areaName,
               u.start_time    AS startTime,
               u.end_time      AS endTime,
               u.value
        FROM authorization_area AS u
                 LEFT JOIN area AS a ON u.area_id = a.`code`
                 LEFT JOIN mall_shop AS s ON s.phone = u.phone
        WHERE u.type = #{type}
          AND a.`level` = #{level}
        ORDER BY u.update_time DESC
    </select>

    <select id="queryAreaAuthorizeByUser" resultType="Map">

        SELECT u.id,
               u.phone,
               us.username  as username,
               a.`name`     AS areaName,
               u.start_time AS startTime,
               u.end_time   AS endTime,
               u.value
        FROM authorization_area AS u
                 LEFT JOIN area AS a ON u.area_id = a.`code`
                 LEFT JOIN tz_user AS us ON us.phone = u.phone
        WHERE u.type = #{type}
          AND a.`level` = #{level}
        ORDER BY u.update_time DESC
    </select>
</mapper>
