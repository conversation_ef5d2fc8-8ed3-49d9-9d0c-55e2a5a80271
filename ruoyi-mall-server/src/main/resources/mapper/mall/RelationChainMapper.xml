<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.RelationChainMapper">

    <!-- 关系链视图对象结果映射 -->
    <resultMap type="com.ruoyi.mall.domain.vo.RelationChainVO" id="RelationChainResult">
        <result property="userId" column="user_id"/>
        <result property="parentNusername" column="parent_username"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="jurisdiction" column="jurisdiction"/>
        <result property="username" column="username"/>
        <result property="fans" column="fans"/>
        <result property="flag" column="flag"/>
        <result property="deductionMoneyLimit" column="deduction_money_limit"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="town" column="town"/>
        <result property="address" column="address"/>
        <result property="loginAddress" column="login_address"/>
        <result property="isMerchant" column="is_merchant"/>
    </resultMap>

    <!-- 查询关系链列表 -->
    <select id="selectRelationChainList" parameterType="com.ruoyi.mall.domain.dto.RelationChainQueryParam"
            resultMap="RelationChainResult">
        SELECT
        u.user_id AS user_id,
        u.phone AS phone,
        pu.username AS parent_username,
        u.STATUS AS `STATUS`,
        u.create_time,
        if(s.jurisdiction_expire_time > NOW(), s.jurisdiction , NULL ) AS jurisdiction,
        IF( su.is_shop = 1, s.business_name, u.username ) AS username,
        IFNULL( s.fans, NULL ) AS fans,
        if(su.is_shop = 1 or su.is_consignment = 1, null ,
        if(u.is_first = 0, 0,
        CASE
        WHEN SUM( t5.component_value ) > #{sum} THEN 2
        WHEN SUM( t5.component_value ) > #{dailyThreshold} THEN 1
        ELSE 0
        END ) ) AS flag,
        IF(su.is_shop = 1 or su.is_consignment = 1, NULL,
        CASE
        WHEN (
        CASE
        WHEN SUM( t5.component_value ) > #{sum} THEN 2
        WHEN SUM( t5.component_value ) > #{dailyThreshold} THEN 1
        ELSE 0
        END
        ) = 2 THEN u.deduction_money_limit
        WHEN (
        CASE
        WHEN SUM( t5.component_value ) > #{sum} THEN 2
        WHEN SUM( t5.component_value ) > #{dailyThreshold} THEN 1
        ELSE 0
        END
        ) = 1 THEN #{dailyThresholdReward}
        WHEN (
        CASE
        WHEN SUM( t5.component_value ) > #{sum} THEN 2
        WHEN SUM( t5.component_value ) > #{dailyThreshold} THEN 1
        ELSE 0
        END
        ) = 0 THEN '0'
        ELSE u.deduction_money_limit
        END
        ) AS deduction_money_limit,
        IF( su.is_shop = 1, s.province, u.province ) AS province,
        IF( su.is_shop = 1, s.city, u.city ) AS city,
        IF( su.is_shop = 1, s.district, u.district ) AS district,
        IF( su.is_shop = 1, s.town, u.town ) AS town,
        IF( su.is_shop = 1, s.address, u.address ) AS address,
        u.login_address,
        CASE
        WHEN s.id IS NOT NULL THEN 1
        ELSE 0
        END AS is_merchant
        FROM tz_user u
        LEFT JOIN tz_user pu ON u.parent_id = pu.user_id and pu.del_flag = '0'
        LEFT JOIN sys_user AS su ON su.tz_user_id = u.user_id and su.del_flag = '0'
        LEFT JOIN mall_shop s ON su.user_id = s.user_id and s.del_flag = '0'
        LEFT JOIN mall_user_component_record t5 ON t5.user_id = u.user_id AND DATE( t5.create_time ) = DATE_SUB(
        CURDATE(), INTERVAL 1 DAY )
        where u.del_flag = '0'
        <if test="queryParam.phone != null and queryParam.phone != ''">
            AND u.phone LIKE CONCAT('%', #{queryParam.phone}, '%')
        </if>
        <if test="queryParam.inviter != null and queryParam.inviter != ''">
            AND pu.username LIKE CONCAT('%', #{queryParam.inviter}, '%')
        </if>
        <if test="queryParam.jurisdiction != null and queryParam.jurisdiction != ''">
            AND s.jurisdiction = #{queryParam.jurisdiction} and s.jurisdiction_expire_time > NOW()
        </if>

        GROUP BY
        u.user_id,
        u.username,
        u.user_type,
        u.chain_type,
        u.`status`,
        u.create_time,
        u.deduction_money_limit,
        s.id,
        su.`status`,
        su.is_shop,
        su.is_consignment,
        u.province,
        u.city,
        u.district,
        u.town,
        u.address,
        u.login_address
        ORDER BY u.create_time DESC
    </select>

    <!-- 检查用户是否为商家 -->
    <select id="checkUserIsMerchant" resultType="Boolean">
        SELECT CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END
        FROM mall_shop s
                 LEFT JOIN sys_user su ON su.user_id = s.user_id
                 left JOIN tz_user u on su.tz_user_id = u.user_id
        WHERE u.user_id = #{userId}
    </select>


    <!-- 结果映射配置 -->
    <resultMap id="StatusAndJurisdictionResult" type="com.ruoyi.mall.domain.vo.UserStatusJurisdictionVO">
        <result property="userId" column="user_id"/>
        <result property="phone" column="phone"/>
        <result property="status" column="status"/>
        <result property="userCategory" column="userCategory"/>
        <result property="jurisdiction" column="jurisdiction"/>
    </resultMap>
    <!-- 获取用户状态权限 -->
    <select id="selectStatusAndJurisdiction" resultMap="StatusAndJurisdictionResult">
        SELECT su.user_id,
               su.phonenumber                                               AS phone,
               CASE
                   WHEN su.is_shop = 1 OR su.is_consignment = 1 THEN su.STATUS
                   ELSE u.STATUS
                   END                                                      AS STATUS,
               CASE
                   WHEN su.is_shop = 1 THEN 1
                   WHEN su.is_consignment = 1 THEN 2
                   WHEN u.user_id IS NOT NULL THEN 3
                   ELSE NULL
                   END                                                      AS userCategory,
               IF(s.jurisdiction_expire_time > NOW(), s.jurisdiction, NULL) AS jurisdiction
        FROM sys_user su
                 LEFT JOIN mall_shop s ON su.user_id = s.user_id
                 LEFT JOIN tz_user u ON su.tz_user_id = u.user_id
        where su.user_id = #{userId}
    </select>


    <!-- 更新系统用户状态（商家） -->
    <update id="updateSysUserStatus">
        UPDATE
            sys_user su
        SET su.status = #{status}
        WHERE su.tz_user_id = #{userId}
    </update>

    <!-- 更新用户状态 -->
    <update id="updateUserStatus">
        UPDATE
        tz_user
        SET
        status = #{status}
        <if test="status == 0">
            ,login_time = now()
        </if>
        WHERE
        user_id = #{userId}
    </update>
</mapper>
