<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.OrderMapper">

    <!-- 统计指定店铺在指定日期范围内的成功订单数量 -->
    <select id="countSuccessfulOrdersByShopIdAndDateRange" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM mall_order
        WHERE status = '3' <!-- 3:已完成 -->
        AND create_time BETWEEN #{startDate} AND #{endDate}
        AND del_flag = '0'
        AND shop_id = #{shopId}
    </select>

    <!-- 统计指定店铺的所有成功订单数量 -->
    <select id="countSuccessfulOrdersByShopId" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM mall_order
        WHERE shop_id = #{shopId}
        AND status = '3' <!-- 3:已完成 -->
        AND del_flag = '0'
    </select>

    <!-- 统计指定店铺在指定日期范围内的所有订单数量（不论状态） -->
    <select id="countAllOrdersByShopIdAndDateRange" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM mall_order
        WHERE shop_id = #{shopId}
          AND create_time BETWEEN #{startDate} AND #{endDate}
          AND del_flag = '0'
    </select>

    <!-- 统计指定店铺特定状态的订单数量 -->
    <select id="countOrdersByShopIdAndStatus" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM mall_order
        WHERE shop_id = #{shopId}
          AND status = #{status}
          AND del_flag = '0'
    </select>

    <!-- 统计指定店铺的退货待处理订单数量 -->
    <select id="countReturnOrdersByShopId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM mall_order_refund
        WHERE shop_id = #{shopId}
          and return_money_sts = #{status}
    </select>

</mapper>
