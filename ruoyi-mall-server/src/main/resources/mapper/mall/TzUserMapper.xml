<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.TzUserMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.TzUser" id="TzUserResult">
        <result property="userId" column="user_id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="nickname" column="nickname"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="avatar" column="avatar"/>
        <result property="sex" column="sex"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="deductionMoney" column="deduction_money"/>
        <result property="component" column="component"/>
        <result property="deductionMoneyLimit" column="deduction_money_limit"/>
    </resultMap>

    <sql id="selectTzUserVo">
        SELECT user_id,
               username,
               PASSWORD,
               nickname,
               phone,
               email,
               avatar,
               sex,
               STATUS,
               del_flag,
               create_by,
               create_time,
               update_by,
               update_time,
               deduction_money,
               deduction_money_limit,
               binding_invitation_code,
               remark
        FROM tz_user
    </sql>

    <select id="selectTzUserList" parameterType="com.ruoyi.mall.domain.entity.TzUser" resultMap="TzUserResult">
        <include refid="selectTzUserVo"/>
        <where>
            <if test="username != null  and username != ''">and username like concat('%', #{username}, '%')</if>
            <if test="nickname != null  and nickname != ''">and nickname like concat('%', #{nickname}, '%')</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="email != null  and email != ''">and email = #{email}</if>
            <if test="sex != null  and sex != ''">and sex = #{sex}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
        </where>
    </select>

    <select id="selectTzUserByUserId" parameterType="Long" resultMap="TzUserResult">
        <include refid="selectTzUserVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertTzUser" parameterType="com.ruoyi.mall.domain.entity.TzUser" useGeneratedKeys="true"
            keyProperty="userId">
        insert into tz_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="username != null">username,</if>
            <if test="password != null">password,</if>
            <if test="nickname != null">nickname,</if>
            <if test="phone != null">phone,</if>
            <if test="email != null">email,</if>
            <if test="avatar != null">avatar,</if>
            <if test="sex != null">sex,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="username != null">#{username},</if>
            <if test="password != null">#{password},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="phone != null">#{phone},</if>
            <if test="email != null">#{email},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="sex != null">#{sex},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateTzUser" parameterType="com.ruoyi.mall.domain.entity.TzUser">
        update tz_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="component !=null">component = #{component},</if>
            <if test="deductionMoney!=null">deduction_money = #{deductionMoney},</if>
            <if test="deductionMoneyLimit!=null">deduction_money_limit = #{deductionMoneyLimit},</if>
            <if test="loginTime!=null">login_time = #{loginTime},</if>
        </trim>
        <where>
            user_id = #{userId}
        </where>
    </update>

    <delete id="deleteTzUserByUserId" parameterType="Long">
        delete
        from tz_user
        where user_id = #{userId}
    </delete>

    <delete id="deleteTzUserByUserIds" parameterType="String">
        delete from tz_user where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <select id="getRelationChain" resultType="com.ruoyi.mall.domain.bo.RelationChainBo">
        SELECT t.user_id                                                         AS id,
               t.username,
               t.user_type,
               t.chain_type,
               t.`status`,
               t.create_time,
               t.component,
               SUM(t5.component_value)                                           AS todayComponent,
               t.login_address,
               t.deduction_money_limit,
               t2.business_name,
               IF(t1.is_shop = 1 OR t1.is_consignment = 1, t2.phone, t.phone) AS phone,
               t2.business_license_number                                     AS businessLicense,
               t2.fans                                                           AS fans,
               if(t1.is_shop = 1, t2.province_code, t.province_code)          as provinceCode,
               if(t1.is_shop = 1, t2.province, t.province)                    as province,
               if(t1.is_shop = 1, t2.city_code, t.city_code)                  as cityCode,
               if(t1.is_shop = 1, t2.city, t.city)                            as city,
               if(t1.is_shop = 1, t2.district_code, t.district_code)          as districtCode,
               if(t1.is_shop = 1, t2.district, t.district)                    as district,
               if(t1.is_shop = 1, t2.town_code, t.town_code)                  as townCode,
               if(t1.is_shop = 1, t2.town, t.town)                            as town,
               if(t1.is_shop = 1, t2.address, t.address)                      as address,
               MAX(
                   IF
                   (t2.jurisdiction_expire_time > now(), t2.jurisdiction, NULL)) AS jurisdiction,
               t4.username                                                       AS parentNusername,
               t4.user_id                                                        AS parentId,
               CASE

                   WHEN SUM(t5.component_value) > #{dailyThresholdReward} THEN
                       2
                   WHEN SUM(t5.component_value) > #{sum} THEN
                       1
                   ELSE 0
                   END                                                           AS flag
        FROM tz_user t
                 LEFT JOIN sys_user t1 ON t1.tz_user_id = t.user_id
            AND t1.del_flag = '0'
                 LEFT JOIN mall_shop t2 ON t2.user_id = t1.user_id
            AND t2.del_flag = '0'
                 LEFT JOIN mall_user_component_record t5 ON t5.user_id = t.user_id
            AND DATE(t5.create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                 LEFT JOIN tz_user t4 ON t4.user_id = t.parent_id
        WHERE t.del_flag = '0'
        GROUP BY t.user_id,
                 t.username,
                 t.user_type,
                 t.chain_type,
                 t.`status`,
                 t.create_time,
                 t.deduction_money_limit,
                 t1.is_shop,
                 t1.is_consignment,
                 t2.business_name,
                 t2.phone,
                 t2.business_license_number,
                 t2.fans,
                 t2.province_code,
                 t2.province,
                 t2.city_code,
                 t2.city,
                 t2.district_code,
                 t2.district,
                 t2.town_code,
                 t2.town,
                 t2.address,
                 t4.username
    </select>

</mapper>
