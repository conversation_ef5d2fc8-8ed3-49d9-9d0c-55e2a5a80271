<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.CategoryMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.Category" id="CategoryResult">
        <id property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="name" column="name"/>
        <result property="icon" column="icon"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="visible" column="visible"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <association property="shop" javaType="com.ruoyi.mall.domain.entity.Shop">
            <id property="id" column="shop_id"/>
            <result property="name" column="shop_name"/>
            <result property="logo" column="shop_logo"/>
            <result property="status" column="shop_status"/>
            <result property="phone" column="shop_phone"/>
            <result property="address" column="shop_address"/>
        </association>
    </resultMap>

    <sql id="selectCategoryVo">
        select c.id,
               c.parent_id,
               c.shop_id,
               c.name,
               c.icon,
               c.sort,
               c.status,
               c.visible,
               c.del_flag,
               c.create_by,
               c.create_time,
               c.update_by,
               c.update_time,
               c.remark,
               s.name    as shop_name,
               s.logo    as shop_logo,
               s.status  as shop_status,
               s.phone   as shop_phone,
               s.address as shop_address
        from mall_category c
                 left join mall_shop s on s.id = c.shop_id
    </sql>

    <select id="selectCategoryList" parameterType="com.ruoyi.mall.domain.entity.Category" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        <where>
            <if test="shopId != null">
                AND c.shop_id = #{shopId}
            </if>
            <if test="name != null and name != ''">
                AND c.name like concat('%', #{name}, '%')
            </if>
            <if test="status != null and status != ''">
                AND c.status = #{status}
            </if>
            <if test="visible != null and visible != ''">
                AND c.visible = #{visible}
            </if>
            AND c.del_flag = '0'
        </where>
        order by c.sort
    </select>

    <select id="getCategoryIdByCategoryId" resultType="Long">
        WITH RECURSIVE cte AS (SELECT id, parent_id
                               FROM mall_category
                               WHERE id = #{categoryId}
                               UNION ALL
                               SELECT c.id, c.parent_id
                               FROM mall_category c
                                        INNER JOIN cte ON c.parent_id = cte.id)
        SELECT id
        FROM cte;
    </select>

</mapper>
