<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.OfflineDrainageRecordMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.OfflineDrainageRecord" id="OfflineDrainageRecordResult">
        <id property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="shopName" column="shop_name"/>
        <result property="count" column="count"/>
        <result property="beforeCount" column="before_count"/>
        <result property="afterCount" column="after_count"/>
        <result property="operatorId" column="operator_id"/>
        <result property="operatorName" column="operator_name"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

</mapper>
