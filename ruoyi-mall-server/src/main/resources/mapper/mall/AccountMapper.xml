<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.AccountMapper">


    <resultMap id="mallShopResultMap" type="com.ruoyi.mall.domain.entity.Shop">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="brand" column="brand" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="CHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="businessHours" column="business_hours" jdbcType="VARCHAR"/>
        <result property="introduction" column="introduction" jdbcType="VARCHAR"/>
        <result property="shopCategoryId" column="shop_category_id" jdbcType="BIGINT"/>
        <result property="shopCategoryName" column="shop_category_name" jdbcType="VARCHAR"/>
        <result property="logo" column="logo" jdbcType="VARCHAR"/>
        <result property="businessName" column="business_name" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="legalPerson" column="legal_person" jdbcType="VARCHAR"/>
        <result property="legalPersonPhone" column="legal_person_phone" jdbcType="VARCHAR"/>
        <result property="legalPersonIdCard" column="legal_person_id_card" jdbcType="VARCHAR"/>
        <result property="socialCreditCode" column="social_credit_code" jdbcType="VARCHAR"/>
        <result property="provinceCode" column="province_code" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="cityCode" column="city_code" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="districtCode" column="district_code" jdbcType="VARCHAR"/>
        <result property="district" column="district" jdbcType="VARCHAR"/>
        <result property="townCode" column="town_code" jdbcType="VARCHAR"/>
        <result property="town" column="town" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="businessLicense" column="business_license" jdbcType="VARCHAR"/>
        <result property="businessLicenseNumber" column="business_license_number" jdbcType="VARCHAR"/>
        <result property="businessLicenseExpireTime" column="business_license_expire_time" jdbcType="VARCHAR"/>
        <result property="otherCertificate" column="other_certificate" jdbcType="VARCHAR"/>
        <result property="idCardFront" column="id_card_front" jdbcType="VARCHAR"/>
        <result property="idCardBack" column="id_card_back" jdbcType="VARCHAR"/>
        <result property="idCardHand" column="id_card_hand" jdbcType="VARCHAR"/>
        <result property="authStatus" column="auth_status" jdbcType="CHAR"/>
        <result property="businessStatus" column="business_status" jdbcType="CHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="DATE"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="DATE"/>
        <result property="drainage" column="drainage" jdbcType="INTEGER"/>
        <result property="fans" column="fans" jdbcType="INTEGER"/>
        <result property="jurisdiction" column="jurisdiction" jdbcType="CHAR"/>
        <result property="jurisdictionExpireTime" column="jurisdiction_expire_time" jdbcType="DATE"/>
        <result property="consignmentPermission" column="consignment_permission" jdbcType="CHAR"/>
        <result property="consignmentExpireTime" column="consignment_expire_time" jdbcType="DATE"/>
        <result property="deductionMoney" column="deduction_money" jdbcType="DECIMAL"/>
        <result property="quantificationValue" column="quantification_value" jdbcType="DECIMAL"/>
        <result property="altogetherQuantificationValue" column="altogether_quantification_value" jdbcType="DECIMAL"/>
        <result property="userFaith" column="user_faith" jdbcType="DECIMAL"/>
        <result property="altogetherUserFaith" column="altogether_user_faith" jdbcType="DECIMAL"/>
        <result property="platformPromotionGold" column="platform_promotion_gold" jdbcType="DECIMAL"/>
        <result property="quantificationRate" column="quantification_rate" jdbcType="DECIMAL"/>
    </resultMap>


    <select id="existsByUserId" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM mall_user_account_status
        WHERE user_id = #{userId}
          AND del_flag = '0'
    </select>


    <!--计算未完成订单数量-->
    <select id="countUnfinishedOrder" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM mall_order
        WHERE user_id = #{userId}
          AND status != #{status}
    </select>


    <!--计算全部订单完成后距离现在时间的差值-->
    <!--    <select id="countOrderCompletedTimeDifference" resultType="java.lang.Integer">
            /*SELECT DATEDIFF(sysdate(), MAX(o.receive_time))
            FROM tz_user u
                     JOIN mall_order o ON u.user_id = o.user_id
            WHERE o.user_id = #{userId}
              AND o.status = 4
              AND u.del_flag = '0'
              AND o.del_flag = '0'*/
            /*   SELECT ifnull(DATEDIFF(sysdate(), MAX(o.receive_time)), 0) as diffDays
               FROM sys_user u
                        JOIN mall_order o ON u.user_id = o.user_id
               WHERE o.user_id = #{userId}
                 AND o.`status` = '4'
                 AND u.del_flag = '0'
                 AND o.del_flag = '0'*/
            SELECT ifnull(DATEDIFF(sysdate(), MAX(receive_time)), 0) as diffDays
            FROM mall_order
            WHERE user_id = #{userId}
              AND status = #{status}
        </select>-->


    <!--计算商家B和代销商CB未完成结算的数量-->
    <select id="countUnsettledOrder" resultType="java.lang.Integer">
        select count(*)
        from mall_shop s
                 join mall_shop_settlement ss on s.id = ss.shop_id
        where s.user_id = #{userId}
          and s.del_flag = '0'
          and ss.del_flag = '0'
          and ss.status != '4'
    </select>


    <!--计算代销商没有取消商品的数量-->
    <select id="countNotCancelAllProducts" resultType="java.lang.Integer">
        select count(*)
        from mall_shop s
                 join mall_consignment_product cp on s.id = cp.shop_id
        where s.user_id = #{userId}
          and s.del_flag = '0'
          and cp.del_flag = '0'
          and cp.status = '0'
    </select>


</mapper>
