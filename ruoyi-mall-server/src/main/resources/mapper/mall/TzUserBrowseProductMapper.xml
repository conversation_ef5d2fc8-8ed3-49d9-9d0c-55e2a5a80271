<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.TzUserBrowseProductMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.TzUserBrowseProduct" id="TzUserBrowseProductResult">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="productId" column="shop_id"/>
        <result property="createTime" column="create_time"/>
        <result property="number" column="number"/>
        <result property="lastTime" column="last_time"/>
    </resultMap>
    <select id="selectUserBrowseTime" parameterType="Long" resultType="String">
        select DATE_FORMAT(last_time, '%Y-%m-%d')
        from mall_user_browse_product
        where user_id = #{userId}
        group by time
        order by time desc
    </select>

    <select id="selectUserBrowseByTime" resultMap="TzUserBrowseProductResult">
        select b.*, p.name, p.cover, p.price, p.shop_id
        from mall_user_browse_product b
                 left join mall_product p on p.id = b.product_id
        where b.user_id = #{userId}
          AND DATE(b.last_time) = #{createTime}
          AND p.status = '0'
          AND p.del_flag = '0'
    </select>
</mapper>
