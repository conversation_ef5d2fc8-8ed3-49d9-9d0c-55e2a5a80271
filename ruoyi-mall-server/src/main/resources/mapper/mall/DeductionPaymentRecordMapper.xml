<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.DeductionPaymentRecordMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.DeductionPaymentRecord" id="DeductionPaymentRecordResult">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="paymentNo" column="payment_no"/>
        <result property="orderNo" column="order_no"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="feeAmount" column="fee_amount"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="payTime" column="pay_time"/>
        <result property="status" column="status"/>
        <result property="payType" column="pay_type"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 查询用户已使用平台抵扣金总额 -->
    <select id="selectUserDeductionUsed" resultType="java.util.HashMap">
        SELECT
        user_id as "key",
        SUM(total_amount) as "value"
        FROM
        mall_deduction_payment_record
        WHERE
        user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND status = '0'
        AND del_flag = '0'
        AND pay_type = #{payType}
        GROUP BY
        user_id
    </select>

    <!-- 查询用户昨日抵扣金使用统计 -->
    <select id="selectUserDeductionDailyStat" resultType="java.util.HashMap">
        SELECT u.user_id,
               u.phone,
               -- 昨日使用的抵扣金
               IFNULL(daily.daily_used, 0)                                AS daily_used,
               -- 累计已使用的抵扣金
               IFNULL(total.total_used, 0)                                AS total_used,
               -- 用户总抵扣金
               IFNULL(u.deduction_money, 0)                               AS total_deduction,
               -- 未使用的抵扣金（剩余）
               IFNULL(u.deduction_money, 0) - IFNULL(total.total_used, 0) AS remain_deduction
        FROM tz_user u
                 LEFT JOIN (
            -- 计算昨天使用的抵扣金
            SELECT user_id,
                   SUM(total_amount) AS daily_used
            FROM mall_deduction_payment_record
            WHERE status = '0'
              AND del_flag = '0'
              AND pay_type = #{payType}
              AND pay_time BETWEEN #{startTime} AND #{endTime}
            GROUP BY user_id) daily ON u.user_id = daily.user_id
                 LEFT JOIN (
            -- 计算累计使用的抵扣金
            SELECT user_id,
                   SUM(total_amount) AS total_used
            FROM mall_deduction_payment_record
            WHERE status = '0'
              AND del_flag = '0'
              AND pay_type = #{payType}
            GROUP BY user_id) total ON u.user_id = total.user_id
    </select>


    <!-- 1 查询用户或商家已使用平台抵扣金 (分页) -->
    <resultMap id="UserDeductionRecordResultMap" type="com.ruoyi.mall.domain.vo.UserDeductionRespVO">
        <!--        <id property="userId" column="user_id"/>-->
        <result property="nickname" column="nickname"/>
        <result property="phone" column="phone"/>
        <result property="orderNo" column="order_no"/>
        <result property="payTime" column="pay_time"/>
        <result property="deductionUsed" column="deduction_used"/>
        <result property="totalDeductionUsed" column="total_deduction_used"/>
    </resultMap>
    <select id="selectUserDeductionDetailInfoPage" resultMap="UserDeductionRecordResultMap">
        <!--SELECT u.user_id,
        u.nickname,
        u.phone,
        m.order_no,
        m.pay_time,
        m.total_amount AS deduction_used,
        SUM(m.total_amount) OVER (PARTITION BY u.user_id) AS total_deduction_used
        FROM tz_user u
        INNER JOIN
        mall_deduction_payment_record m ON u.user_id = m.user_id
        <where>
            m.status = '0'
            AND m.del_flag = '0'
            AND m.pay_type = #{payType}
            &lt;!&ndash; 手机号精确查询 &ndash;&gt;
            <if test="vo.phone != null and vo.phone != ''">
                AND u.phone = #{vo.phone}
            </if>
            &lt;!&ndash; 用户名模糊查询 &ndash;&gt;
            <if test="vo.nickname != null and vo.nickname != ''">
                AND u.nickname LIKE CONCAT('%', #{vo.nickname}, '%')
            </if>
            <if test="vo.beginTime != null">
                <![CDATA[
                     AND m.pay_time >= #{vo.beginTime}
                ]]>
            </if>
            <if test="vo.endTime != null">
                <![CDATA[
                    AND m.pay_time <= #{vo.endTime}
                ]]>
            </if>
        </where>-->
        SELECT
        user_id,
        nickname,
        phone,
        order_no,
        pay_time,
        deduction_used,
        CASE WHEN rn = 1 THEN total_deduction_used ELSE NULL END AS total_deduction_used
        FROM (
        SELECT
        u.user_id,
        u.nickname,
        u.phone,
        m.order_no,
        m.pay_time,
        m.total_amount AS deduction_used,
        SUM(m.total_amount) OVER (PARTITION BY u.user_id ORDER BY DATE(m.pay_time)) AS total_deduction_used,
        ROW_NUMBER() OVER (PARTITION BY u.user_id, DATE(m.pay_time) ORDER BY m.pay_time DESC) AS rn,
        MAX(m.pay_time) OVER (PARTITION BY u.user_id) AS max_pay_time_for_user
        FROM tz_user u
        INNER JOIN mall_deduction_payment_record m ON u.user_id = m.user_id
        <where>
            m.status = '0'
            AND m.del_flag = '0'
            AND m.pay_type = #{payType}
            <!-- 手机号精确查询 -->
            <if test="vo.phone != null and vo.phone != ''">
                AND u.phone = #{vo.phone}
            </if>
            <!-- 用户名模糊查询 -->
            <if test="vo.nickname != null and vo.nickname != ''">
                AND u.nickname LIKE CONCAT('%', #{vo.nickname}, '%')
            </if>
            <if test="vo.beginTime != null">
                <![CDATA[
                     AND m.pay_time >= #{vo.beginTime}
                ]]>
            </if>
            <if test="vo.endTime != null">
                <![CDATA[
                    AND m.pay_time <= #{vo.endTime}
                ]]>
            </if>
        </where>
        ) AS subquery
        ORDER BY max_pay_time_for_user DESC, user_id, pay_time DESC
    </select>

    <!-- 查询用户抵扣金明细列表(不分页) -->
    <select id="selectUserDeductionDetailInfoList" resultType="com.ruoyi.mall.domain.vo.UserDeductionRespVO">
        <!--SELECT u.user_id,
        u.nickname,
        u.phone,
        m.order_no,
        m.pay_time,
        m.total_amount AS deduction_used,
        SUM(m.total_amount) OVER (PARTITION BY u.user_id) AS total_deduction_used
        FROM tz_user u
        INNER JOIN
        mall_deduction_payment_record m ON u.user_id = m.user_id
        <where>
            m.status = '0'
            AND m.del_flag = '0'
            AND m.pay_type = #{payType}
            &lt;!&ndash; 手机号精确查询 &ndash;&gt;
            <if test="vo.phone != null and vo.phone != ''">
                AND u.phone = #{vo.phone}
            </if>
            &lt;!&ndash; 用户名模糊查询 &ndash;&gt;
            <if test="vo.nickname != null and vo.nickname != ''">
                AND u.nickname LIKE CONCAT('%', #{vo.nickname}, '%')
            </if>
            <if test="vo.beginTime != null">
                <![CDATA[
                     AND m.pay_time >= #{vo.beginTime}
                ]]>
            </if>
            <if test="vo.endTime != null">
                <![CDATA[
                    AND m.pay_time <= #{vo.endTime}
                ]]>
            </if>
        </where>-->
        SELECT
        user_id,
        nickname,
        phone,
        order_no,
        pay_time,
        deduction_used,
        CASE WHEN rn = 1 THEN total_deduction_used ELSE NULL END AS total_deduction_used
        FROM (
        SELECT
        u.user_id,
        u.nickname,
        u.phone,
        m.order_no,
        m.pay_time,
        m.total_amount AS deduction_used,
        SUM(m.total_amount) OVER (PARTITION BY u.user_id ORDER BY DATE(m.pay_time)) AS total_deduction_used,
        ROW_NUMBER() OVER (PARTITION BY u.user_id, DATE(m.pay_time) ORDER BY m.pay_time DESC) AS rn,
        MAX(m.pay_time) OVER (PARTITION BY u.user_id) AS max_pay_time_for_user
        FROM tz_user u
        INNER JOIN mall_deduction_payment_record m ON u.user_id = m.user_id
        <where>
            m.status = '0'
            AND m.del_flag = '0'
            AND m.pay_type = #{payType}
            <!-- 手机号精确查询 -->
            <if test="vo.phone != null and vo.phone != ''">
                AND u.phone = #{vo.phone}
            </if>
            <!-- 用户名模糊查询 -->
            <if test="vo.nickname != null and vo.nickname != ''">
                AND u.nickname LIKE CONCAT('%', #{vo.nickname}, '%')
            </if>
            <if test="vo.beginTime != null">
                <![CDATA[
                     AND m.pay_time >= #{vo.beginTime}
                ]]>
            </if>
            <if test="vo.endTime != null">
                <![CDATA[
                    AND m.pay_time <= #{vo.endTime}
                ]]>
            </if>
        </where>
        ) AS subquery
        ORDER BY max_pay_time_for_user DESC, user_id, pay_time DESC
    </select>


    <!-- 2 查询商家已使用促销金明细 (分页) -->
    <resultMap id="UserPromotionRecordResultMap" type="com.ruoyi.mall.domain.vo.ShopPromotionRespVO">
        <!--        <id property="userId" column="user_id"/>-->
        <result property="name" column="name"/>
        <result property="phone" column="phone"/>
        <result property="orderNo" column="order_no"/>
        <result property="payTime" column="pay_time"/>
        <result property="promotionUsed" column="promotion_used"/>
        <result property="totalPromotionUsed" column="total_promotion_used"/>
    </resultMap>
    <select id="selectUserPromotionDetailInfoPage" resultMap="UserPromotionRecordResultMap">
        <!-- SELECT s.user_id,
         s.name,
         s.phone,
         /* s.platform_promotion_gold,*/
         m.total_amount AS promotion_used,
         m.order_no,
         m.pay_time,
         SUM(m.total_amount) OVER (PARTITION BY s.user_id) AS total_promotion_used
         FROM mall_shop s
         INNER JOIN
         mall_deduction_payment_record m ON s.user_id = m.user_id
         <where>
             m.status = '0'
             AND m.del_flag = '0'
             AND m.pay_type = #{payType}
             &lt;!&ndash; 手机号精确查询 &ndash;&gt;
             <if test="vo.phone != null and vo.phone != ''">
                 AND s.phone = #{vo.phone}
             </if>
             &lt;!&ndash; 商家名称模糊查询 &ndash;&gt;
             <if test="vo.name != null and vo.name != ''">
                 AND s.name LIKE CONCAT('%', #{vo.name}, '%')
             </if>
             <if test="vo.beginTime != null">
                 <![CDATA[
                      AND m.pay_time >= #{vo.beginTime}
                 ]]>
             </if>
             <if test="vo.endTime != null">
                 <![CDATA[
                     AND m.pay_time <= #{vo.endTime}
                 ]]>
             </if>
         </where>-->
        SELECT
        user_id,
        name,
        phone,
        order_no,
        pay_time,
        promotion_used,
        CASE WHEN rn = 1 THEN total_promotion_used ELSE NULL END AS total_promotion_used
        FROM (
        SELECT
        s.user_id,
        s.name,
        s.phone,
        m.order_no,
        m.pay_time,
        m.total_amount AS promotion_used,
        SUM(m.total_amount) OVER (PARTITION BY s.user_id ORDER BY m.pay_time) AS total_promotion_used,
        ROW_NUMBER() OVER (PARTITION BY s.user_id, DATE(m.pay_time) ORDER BY m.pay_time DESC) AS rn
        FROM mall_shop s
        INNER JOIN mall_deduction_payment_record m ON s.user_id = m.user_id
        <where>
            m.status = '0'
            AND m.del_flag = '0'
            AND m.pay_type = #{payType}
            <!-- 手机号精确查询 -->
            <if test="vo.phone != null and vo.phone != ''">
                AND s.phone = #{vo.phone}
            </if>
            <!-- 商家名称模糊查询 -->
            <if test="vo.name != null and vo.name != ''">
                AND s.name LIKE CONCAT('%', #{vo.name}, '%')
            </if>
            <if test="vo.beginTime != null">
                <![CDATA[
                     AND m.pay_time >= #{vo.beginTime}
                ]]>
            </if>
            <if test="vo.endTime != null">
                <![CDATA[
                    AND m.pay_time <= #{vo.endTime}
                ]]>
            </if>
        </where>
        ) AS subquery
        ORDER BY DATE(pay_time) DESC, user_id, pay_time DESC
    </select>


    <!-- 查询商家已使用促销金明细 (不分页) -->
    <select id="selectUserPromotionDetailInfoList" resultMap="UserPromotionRecordResultMap">
        <!--SELECT s.user_id,
        s.name,
        s.phone,
        /* s.platform_promotion_gold,*/
        m.total_amount AS promotion_used,
        m.order_no,
        m.pay_time,
        SUM(m.total_amount) OVER (PARTITION BY s.user_id) AS total_promotion_used
        FROM mall_shop s
        INNER JOIN
        mall_deduction_payment_record m ON s.user_id = m.user_id
        <where>
            m.status = '0'
            AND m.del_flag = '0'
            AND m.pay_type = #{payType}
            &lt;!&ndash; 手机号精确查询 &ndash;&gt;
            <if test="vo.phone != null and vo.phone != ''">
                AND s.phone = #{vo.phone}
            </if>
            &lt;!&ndash; 商家名称模糊查询 &ndash;&gt;
            <if test="vo.name != null and vo.name != ''">
                AND s.name LIKE CONCAT('%', #{vo.name}, '%')
            </if>
            <if test="vo.beginTime != null">
                <![CDATA[
                     AND m.pay_time >= #{vo.beginTime}
                ]]>
            </if>
            <if test="vo.endTime != null">
                <![CDATA[
                    AND m.pay_time <= #{vo.endTime}
                ]]>
            </if>
        </where>-->
        SELECT
        user_id,
        name,
        phone,
        order_no,
        pay_time,
        promotion_used,
        CASE WHEN rn = 1 THEN total_promotion_used ELSE NULL END AS total_promotion_used
        FROM (
        SELECT
        s.user_id,
        s.name,
        s.phone,
        m.order_no,
        m.pay_time,
        m.total_amount AS promotion_used,
        SUM(m.total_amount) OVER (PARTITION BY s.user_id ORDER BY m.pay_time) AS total_promotion_used,
        ROW_NUMBER() OVER (PARTITION BY s.user_id, DATE(m.pay_time) ORDER BY m.pay_time DESC) AS rn
        FROM mall_shop s
        INNER JOIN mall_deduction_payment_record m ON s.user_id = m.user_id
        <where>
            m.status = '0'
            AND m.del_flag = '0'
            AND m.pay_type = #{payType}
            <!-- 手机号精确查询 -->
            <if test="vo.phone != null and vo.phone != ''">
                AND s.phone = #{vo.phone}
            </if>
            <!-- 商家名称模糊查询 -->
            <if test="vo.name != null and vo.name != ''">
                AND s.name LIKE CONCAT('%', #{vo.name}, '%')
            </if>
            <if test="vo.beginTime != null">
                <![CDATA[
                     AND m.pay_time >= #{vo.beginTime}
                ]]>
            </if>
            <if test="vo.endTime != null">
                <![CDATA[
                    AND m.pay_time <= #{vo.endTime}
                ]]>
            </if>
        </where>
        ) AS subquery
        ORDER BY DATE(pay_time) DESC, user_id, pay_time DESC
    </select>


    <!-- 查询用户最近使用平台抵扣金时间 -->
    <select id="selectUserLastUsedTime" resultType="java.util.HashMap">
        SELECT
        t.user_id as "key",
        t.pay_time as "value"
        FROM
        mall_deduction_payment_record t
        JOIN (
        SELECT
        user_id,
        MAX(pay_time) as max_pay_time
        FROM
        mall_deduction_payment_record
        WHERE
        user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND status = '0'
        AND del_flag = '0'
        GROUP BY
        user_id
        ) m ON t.user_id = m.user_id AND t.pay_time = m.max_pay_time
    </select>


</mapper>
