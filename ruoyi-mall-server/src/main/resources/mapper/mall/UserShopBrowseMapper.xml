<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.UserShopBrowseMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.UserShopBrowse" id="UserShopBrowseResult">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <insert id="saveUserShopBrowse" parameterType="com.ruoyi.mall.domain.entity.UserShopBrowse" useGeneratedKeys="true"
            keyProperty="id">
        insert into mall_shop_browse
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId !=null">user_id,</if>
            <if test="shopId !=null">shop_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId !=null">#{userId},</if>
            <if test="shopId !=null">#{shopId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>

    </insert>


    <select id="findUserShopBrowse" resultType="Integer">
        select IFNULL(count(*), 0)
        from mall_shop_browse
        where user_id = #{userId}
          AND shop_id = #{shopId}
    </select>

    <select id="selectShopIds" resultType="com.ruoyi.mall.domain.vo.ShopVo">
        select id,
               name,
               logo,
               drainage,
               fans,
               1 as is_click,
               business_name,
               sales
        from mall_shop_browse b
                 left join mall_shop s on s.id = b.shop_id
        where b.user_id = #{userId}
        order by b.create_time

    </select>


</mapper>
