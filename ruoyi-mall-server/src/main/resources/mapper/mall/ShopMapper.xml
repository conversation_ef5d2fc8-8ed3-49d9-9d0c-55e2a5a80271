<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.ShopMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.Shop" id="ShopResult">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="logo" column="logo"/>
        <result property="introduction" column="introduction"/>
        <result property="address" column="address"/>
        <result property="phone" column="phone"/>
        <result property="businessHours" column="business_hours"/>
        <result property="type" column="type"/>
        <result property="authStatus" column="auth_status"/>
        <result property="businessStatus" column="business_status"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectHomeShopList" resultType="com.ruoyi.mall.domain.vo.ShopVo">
        select id,name,logo,drainage,fans,business_name,'true' as is_click from mall_shop
        <where>
            auth_status='1'
            AND status='0'
            AND del_flag = '0'
            AND drainage>0
            AND id not in(select shop_id from mall_shop_browse where user_id=#{userId} and DATE(create_time)= CURDATE())
        </where>
        order by drainage desc
    </select>

    <select id="selectUserShopList" resultType="com.ruoyi.mall.domain.vo.ShopVo">
        select id,name,logo,drainage,fans,business_name,
        IF(
        id IN (
        SELECT shop_id
        FROM mall_shop_browse
        WHERE user_id = #{userId}
        AND DATE(create_time) = CURDATE()
        )
        OR drainage = 0,
        'false',
        'true'
        ) AS is_click from mall_shop
        <where>
            auth_status='1'
            AND status='0'
            AND del_flag = '0'
        </where>
        order by is_click='true' desc,RAND()
    </select>

    <select id="selectShopListByKeyWord" resultType="com.ruoyi.mall.domain.vo.ShopVo">
        select id,name,logo,drainage,fans,business_name,
        IF(
        id IN (
        SELECT shop_id
        FROM mall_shop_browse
        WHERE user_id = #{userId}
        AND DATE(create_time) = CURDATE()
        )
        OR drainage = 0,
        'false',
        'true'
        ) AS is_click from mall_shop
        <where>
            auth_status='1'
            AND status='0'
            AND del_flag = '0'
            <if test="keyword != null and keyword != ''">
                AND business_name like concat('%', #{keyword}, '%')
            </if>
        </where>
        order by is_click='true' desc
    </select>

    <select id="selectShopByPhone" resultType="com.ruoyi.mall.domain.entity.Shop">
        SELECT *
        FROM mall_shop
        WHERE phone = #{phone}
          AND del_flag = '0'
        LIMIT 1
    </select>

</mapper>
