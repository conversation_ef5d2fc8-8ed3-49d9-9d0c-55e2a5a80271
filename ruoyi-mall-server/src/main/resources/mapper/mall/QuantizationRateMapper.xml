<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.QuantizationRateMapper">

    <select id="getQuantizationRate" resultType="com.ruoyi.mall.domain.entity.QuantizationRate">
        SELECT quantify_date,
               quantify_rate
        FROM quantization_rate
        WHERE DATE_FORMAT(quantify_date, '%Y-%m') = DATE_FORMAT(#{searchMonth}, '%Y-%m')
        GROUP BY quantify_date, quantify_rate;
    </select>

</mapper>
