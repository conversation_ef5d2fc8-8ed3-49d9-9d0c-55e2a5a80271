<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.CartMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.Cart" id="CartResult">
        <id property="id" column="id"/>
        <result property="productId" column="product_id"/>
        <result property="userId" column="user_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="quantity" column="quantity"/>
        <result property="selected" column="selected"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <select id="getUserCartInfo" resultType="com.ruoyi.mall.domain.vo.CartVo">
        SELECT
        s.status shopStatus,
        c.id AS cartId,
        c.quantity,
        c.shop_id,
        c.product_id,
        c.update_time,
        u.name AS skuName,
        u.value AS skuValue,
        u.price,
        u.stock,
        u.status AS skuStatus,
        p.cover AS productImage,
        p.name AS productName,
        p.fee_bearer,
        p.status AS productStatus,
        u.price * c.quantity AS totalAmount
        FROM tz_cart c
        LEFT JOIN mall_shop s ON s.id = c.shop_id
        LEFT JOIN mall_product p ON p.id = c.product_id
        LEFT JOIN mall_sku u ON u.id = c.sku_id
        WHERE c.shop_id IN (
        SELECT shop_id
        FROM (
        SELECT
        c.shop_id,
        MAX(c.update_time) AS max_update_time
        FROM tz_cart c
        LEFT JOIN mall_shop s ON s.id = c.shop_id
        LEFT JOIN mall_product p ON p.id = c.product_id
        LEFT JOIN mall_sku u ON u.id = c.sku_id
        WHERE c.user_id=#{userId}
        AND c.del_flag='0'
        <if test="query != null and query != ''">
            and (p.name like concat('%',#{query},'%') or u.name like concat('%',#{query},'%'))
            or u.value like concat('%',#{query},'%')
            or s.name like concat('%',#{query},'%')
        </if>
        GROUP BY c.shop_id
        ORDER BY max_update_time DESC
        LIMIT #{pageNum}, #{pageSize}
        ) AS shop_page
        )
        AND c.user_id=#{userId}
        AND c.del_flag='0'
        <if test="query != null and query != ''">
            and (p.name like concat('%',#{query},'%') or u.name like concat('%',#{query},'%'))
            or u.value like concat('%',#{query},'%')
            or s.name like concat('%',#{query},'%')
        </if>
        ORDER BY
        (SELECT MAX(update_time) FROM tz_cart WHERE shop_id = c.shop_id) DESC,
        c.update_time DESC

    </select>

    <select id="getUserCartCount" resultType="int">
        select count(*) from
        (SELECT
        s.id
        FROM tz_cart c
        LEFT JOIN mall_shop s ON s.id = c.shop_id
        LEFT JOIN mall_product p ON p.id = c.product_id
        LEFT JOIN mall_sku u ON u.id = c.sku_id
        WHERE c.user_id = #{userId}
        AND c.del_flag = '0'
        <if test="query != null and query != ''">
            and p.name like concat('%',#{query},'%')
            or u.name like concat('%',#{query},'%')
            or u.value like concat('%',#{query},'%')
            or s.name like concat('%',#{query},'%')
        </if>
        group by s.id
        ) as a

    </select>

    <select id="getCartInfoByCartIds" resultType="com.ruoyi.mall.domain.vo.CartVo">
        SELECT
        s.status shopStatus,
        c.id AS cartId,
        c.quantity,
        c.shop_id,
        c.product_id,
        c.update_time,
        u.name AS skuName,
        u.value AS skuValue,
        u.price,
        u.stock,
        u.status AS skuStatus,
        p.cover AS productImage,
        p.name AS productName,
        p.fee_bearer,
        p.status AS productStatus,
        u.price * c.quantity AS totalAmount
        FROM tz_cart c
        LEFT JOIN mall_shop s ON s.id = c.shop_id
        LEFT JOIN mall_product p ON p.id = c.product_id
        LEFT JOIN mall_sku u ON u.id = c.sku_id
        WHERE c.id IN
        <foreach item="id" collection="cartIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND c.user_id = #{userId}
        AND c.del_flag = '0'
        AND s.del_flag = '0'
        AND s.status = '0'
        AND p.del_flag = '0'
        AND p.status = '1'
        AND u.stock >= c.quantity
        ORDER BY
        (SELECT MAX(update_time) FROM tz_cart WHERE shop_id = c.shop_id) DESC,
        c.update_time DESC
    </select>

</mapper>
