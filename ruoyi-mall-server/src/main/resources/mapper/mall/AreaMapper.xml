<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mall.mapper.AreaMapper">

    <resultMap type="com.ruoyi.mall.domain.entity.Area" id="AreaResult">
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="level" column="level"/>
        <result property="parentId" column="parent_id"/>
        <result property="initial" column="initial"/>
        <result property="pinyin" column="pinyin"/>
    </resultMap>

    <!-- 获取所有省份列表 -->
    <select id="selectProvinces" resultMap="AreaResult">
        select code, name, level, parent_id, initial, pinyin
        from area
        where level = 1
        order by code
    </select>

    <!-- 获取指定省份下的城市列表 -->
    <select id="selectCitiesByProvince" resultMap="AreaResult">
        select code, name, level, parent_id, initial, pinyin
        from area
        where parent_id = #{parentId}
          and level = 2
        order by code
    </select>

    <!-- 获取指定城市下的区县列表 -->
    <select id="selectDistrictsByCity" resultMap="AreaResult">
        select code, name, level, parent_id, initial, pinyin
        from area
        where parent_id = #{parentId}
          and level = 3
        order by code
    </select>

    <!-- 根据编码获取地区信息 -->
    <select id="selectByCode" resultMap="AreaResult">
        select code, name, level, parent_id, initial, pinyin
        from area
        where code = #{code}
    </select>

    <!-- 根据父ID查询下级地区列表 -->
    <select id="selectByParentId" resultMap="AreaResult">
        select code, name, level, parent_id, initial, pinyin
        from area
        where parent_id = #{parentId}
        order by code
    </select>
</mapper>
