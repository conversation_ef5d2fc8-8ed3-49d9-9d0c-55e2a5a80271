-- ----------------------------
-- 创建广告表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `tz_advertisement`
(
    `id`            bigint(20)     NOT NULL AUTO_INCREMENT COMMENT '广告ID',
    `shop_id`       bigint(20)     NOT NULL COMMENT '商家ID',
    `title`         varchar(100)   NOT NULL COMMENT '广告标题',
    `image`         varchar(255)   NOT NULL COMMENT '广告图片URL',
    `price`         decimal(10, 2) NOT NULL COMMENT '广告价格',
    `website`       varchar(255)   NOT NULL COMMENT '跳转链接',
    `description`   varchar(500)            DEFAULT NULL COMMENT '广告描述',
    `sort`          int(11)                 DEFAULT '0' COMMENT '排序',
    `start_date`    date           NOT NULL COMMENT '开始日期',
    `end_date`      date           NOT NULL COMMENT '结束日期',
    `status`        char(1)        NOT NULL DEFAULT '0' COMMENT '状态：0-待支付，1-审核中，2-已上线，3-已下线，4-已拒绝',
    `pay_status`    char(1)        NOT NULL DEFAULT '0' COMMENT '支付状态：0-未支付，1-已支付',
    `pay_time`      datetime                DEFAULT NULL COMMENT '支付时间',
    `pay_method`    varchar(50)             DEFAULT NULL COMMENT '支付方式：alipay-支付宝，wechat-微信，bank-银行卡',
    `pay_order_no`  varchar(50)             DEFAULT NULL COMMENT '支付订单号',
    `reviewer_id`   bigint(20)              DEFAULT NULL COMMENT '审核人ID',
    `review_time`   datetime                DEFAULT NULL COMMENT '审核时间',
    `review_remark` varchar(500)            DEFAULT NULL COMMENT '审核备注',
    `create_by`     varchar(64)             DEFAULT '' COMMENT '创建者',
    `create_time`   datetime                DEFAULT NULL COMMENT '创建时间',
    `update_by`     varchar(64)             DEFAULT '' COMMENT '更新者',
    `update_time`   datetime                DEFAULT NULL COMMENT '更新时间',
    `remark`        varchar(500)            DEFAULT NULL COMMENT '备注',
    `del_flag`      char(1)                 DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (`id`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_status` (`status`),
    KEY `idx_pay_status` (`pay_status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='商家广告表';
