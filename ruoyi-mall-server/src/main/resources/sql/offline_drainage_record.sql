-- ----------------------------
-- 线下充值技术引流次数记录表
-- ----------------------------
DROP TABLE IF EXISTS `mall_offline_drainage_record`;
CREATE TABLE `mall_offline_drainage_record`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `shop_id`       bigint(20) NOT NULL COMMENT '商家ID',
    `shop_name`     varchar(100) DEFAULT NULL COMMENT '商家名称',
    `count`         int(11)    NOT NULL COMMENT '充值数量',
    `before_count`  int(11)      DEFAULT '0' COMMENT '操作前技术引流次数',
    `after_count`   int(11)      DEFAULT '0' COMMENT '操作后技术引流次数',
    `operator_id`   bigint(20) NOT NULL COMMENT '操作人ID',
    `operator_name` varchar(50)  DEFAULT NULL COMMENT '操作人名称',
    `remark`        varchar(500) DEFAULT NULL COMMENT '备注',
    `status`        char(1)      DEFAULT '0' COMMENT '状态（0成功 1失败）',
    `create_by`     varchar(64)  DEFAULT '' COMMENT '创建者',
    `create_time`   datetime     DEFAULT NULL COMMENT '创建时间',
    `update_by`     varchar(64)  DEFAULT '' COMMENT '更新者',
    `update_time`   datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_shop_id` (`shop_id`),
    KEY `idx_operator_id` (`operator_id`),
    KEY `idx_create_time` (`create_time`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='线下充值技术引流次数记录表';
