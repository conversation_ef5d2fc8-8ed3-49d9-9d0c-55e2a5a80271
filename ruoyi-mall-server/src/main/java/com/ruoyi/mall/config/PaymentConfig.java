package com.ruoyi.mall.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "mall.payment")
public class PaymentConfig {
    /**
     * 微信支付配置
     */
    private WechatPay wechatPay = new WechatPay();

    /**
     * 支付宝配置
     */
    private Alipay alipay = new Alipay();

    @Data
    public static class WechatPay {
        /**
         * 应用ID
         */
        private String appId;
        /**
         * 商户号
         */
        private String mchId;
        /**
         * API密钥
         */
        private String apiKey;
        /**
         * 回调地址
         */
        private String notifyUrl;
        /**
         * 证书路径
         */
        private String certPath;
    }

    @Data
    public static class Alipay {
        /**
         * 应用ID
         */
        private String appId;
        /**
         * 商户私钥
         */
        private String privateKey;
        /**
         * 支付宝公钥
         */
        private String publicKey;
        /**
         * 回调地址
         */
        private String notifyUrl;
        /**
         * 网关地址
         */
        private String gatewayUrl = "https://openapi.alipay.com/gateway.do";
    }
}
