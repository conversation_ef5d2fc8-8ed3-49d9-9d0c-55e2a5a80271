package com.ruoyi.mall.enums;

public enum UserRoleEnum {

    /**
     * 用户角色
     */
    IS_USER(1),
    /**
     * 店铺角色
     */
    IS_SHOP(2),
    /**
     * 代销角色
     */
    IS_CONSIGNMENT(3),

    /**
     * 游客角色
     */
    IS_VISITOR(4);

    private final Integer num;

    UserRoleEnum(Integer num) {
        this.num = num;
    }

    public static UserRoleEnum valueOf(Integer value) {
        for (UserRoleEnum type : UserRoleEnum.values()) {
            if (type.value().equals(value)) {
                return type;
            }
        }
        return null;
    }

    public Integer value() {
        return num;
    }
}
