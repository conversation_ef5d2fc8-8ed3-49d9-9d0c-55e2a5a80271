package com.ruoyi.mall.enums;

import lombok.Getter;

/**
 * 钱包交易方式枚举
 */
@Getter
public enum TradeMethodEnum {

    SALES_INCOME(1, "销售收入"),
    WITHDRAWAL(2, "提现"),
    REFUND(3, "退款"),
    OTHER(4, "其他");

    private final Integer value;
    private final String info;

    TradeMethodEnum(Integer value, String info) {
        this.value = value;
        this.info = info;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static TradeMethodEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (TradeMethodEnum method : values()) {
            if (method.getValue().equals(value)) {
                return method;
            }
        }
        return null;
    }
}
