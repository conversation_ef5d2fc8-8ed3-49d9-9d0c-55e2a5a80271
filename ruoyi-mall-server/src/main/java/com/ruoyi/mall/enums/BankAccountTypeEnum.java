package com.ruoyi.mall.enums;

/**
 * 银行卡账户类型枚举
 */
public enum BankAccountTypeEnum {

    /**
     * 个人账户
     */
    PERSONAL("1"),

    /**
     * 企业账户
     */
    ENTERPRISE("2");

    private final String value;

    BankAccountTypeEnum(String value) {
        this.value = value;
    }

    public static BankAccountTypeEnum getByValue(String value) {
        if (value == null) {
            return null;
        }
        for (BankAccountTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }
}
