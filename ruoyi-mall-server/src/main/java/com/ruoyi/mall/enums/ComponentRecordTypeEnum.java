package com.ruoyi.mall.enums;

/**
 * 分量记录类型枚举
 */
public enum ComponentRecordTypeEnum {

    /**
     * 签到获得
     */
    SIGN_IN(1),

    /**
     * 管理员调整
     */
    ADMIN_ADJUST(2),

    /**
     * 商铺引流，点击商铺获得
     */
    CLICK_SHOP(3);

    private final Integer value;

    ComponentRecordTypeEnum(Integer value) {
        this.value = value;
    }

    public static ComponentRecordTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (ComponentRecordTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }

    public Integer getValue() {
        return value;
    }
}
