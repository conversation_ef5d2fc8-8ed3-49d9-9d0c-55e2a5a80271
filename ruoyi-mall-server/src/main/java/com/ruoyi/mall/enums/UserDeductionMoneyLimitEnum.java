package com.ruoyi.mall.enums;

public enum UserDeductionMoneyLimitEnum {


    /**
     * 初始
     */
    Initial("0", "初始获取兑换额度"),

    /*
     * 推荐用户成为商家奖励
     */
    SHOP_RECOMMEND("1", "推荐用户成为商家奖励额度"),

    /**
     * 商家有效技术引流奖励
     */
    SHOP_EFFECTIVE_DRAINAGE("2", "商家有效技术引流奖励额度"),
    /**
     * 商家无效技术引流下降
     */
    SHOP_INVALID_DRAINAGE("3", "商家无效技术引流降低额度");


    private final String number;
    private final String name;

    UserDeductionMoneyLimitEnum(String number, String name) {
        this.number = number;
        this.name = name;
    }

    public String getNumber() {
        return number;
    }

    public String getName() {
        return name;
    }

}
