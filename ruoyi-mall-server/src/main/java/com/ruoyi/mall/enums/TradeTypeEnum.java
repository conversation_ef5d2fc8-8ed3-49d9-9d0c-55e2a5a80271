package com.ruoyi.mall.enums;

import lombok.Getter;

/**
 * 钱包交易类型枚举
 */
@Getter
public enum TradeTypeEnum {

    INCOME(1, "入账"),
    EXPENSE(2, "出账");

    private final Integer value;
    private final String info;

    TradeTypeEnum(Integer value, String info) {
        this.value = value;
        this.info = info;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static TradeTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (TradeTypeEnum type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
