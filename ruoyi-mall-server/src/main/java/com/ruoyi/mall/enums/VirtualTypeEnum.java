package com.ruoyi.mall.enums;

public enum VirtualTypeEnum {

    /**
     * 商品代销
     */
    DAIXOAO_ONE(1, "权限一", "一个月权限一的费用"),
    /**
     * 商品代销权限二
     */
    DAIXOAO_TWO(2, "权限二", "一个月权限二的费用"),
    /**
     * 商品代销权限三
     */
    DAIXOAO_THERE(3, "权限三", "一个月权限三的费用"),

    /**
     * CB 代销开启权限
     */
    DAIXIAO_OPEN(4, "代销", "代销的费用"),

    /**
     * 技术引流
     */
    JISHU_DRAINAGE(5, "技术引流", "技术引流的费用"),

    /**
     * 广告投放
     */
    ADVERTISEMENT_PUTTING(6, "广告投放", "广告投放的费用"),

    /**
     * 代销权限一开通
     */
    CONSIGNMENT_LEVEL_ONE(7, "代销权限一", "代销权限一开通费用"),

    /**
     * 代销权限二开通
     */
    CONSIGNMENT_LEVEL_TWO(8, "代销权限二", "代销权限二开通费用"),

    /**
     * 商品推荐开通
     */
    SHOP_PRODUCT_RECOMMEND(9, "商品推荐", "商品推荐开通费用"),
    /**
     * 商品上新开通
     */
    SHOP_PRODUCT_NEW(10,"商品上新","商品上新开通费用"),
    /**
     * 商品热销开通
     */
    SHOP_PRODUCT_HOT(11,"商品热销","商品热销开通费用");

    private final Integer type;

    private final String productName;

    private final String remark;

    VirtualTypeEnum(Integer type, String productName, String remark) {
        this.type = type;
        this.productName = productName;
        this.remark = remark;
    }

    public static VirtualTypeEnum instance(Integer type) {
        VirtualTypeEnum[] enums = values();
        for (VirtualTypeEnum virtualTypeEnum : enums) {
            if (virtualTypeEnum.type().equals(type)) {
                return virtualTypeEnum;
            }
        }
        return null;
    }

    public Integer type() {
        return type;
    }

    public String productName() {
        return productName;
    }

    public String remark() {
        return remark;
    }
}
