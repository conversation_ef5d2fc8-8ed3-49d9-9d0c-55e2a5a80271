package com.ruoyi.mall.enums;

public enum OrderTypeEnum {


    /**
     * 全部
     */
    ALL(1),

    /**
     * 待付款
     */
    PENDING(2),

    /**
     * 待发货
     */
    PAYING(3),

    /**
     * 待收货
     */
    DELIVERING(4),
    /**
     * 已收货
     */
    COMPLETED(5),

    /**
     * 已取消
     */
    CANCELED(6),

    /**
     * 待评价
     */
    RECEIVED(7),
    /**
     * 售后
     */
    REFUND(8);

    private final Integer num;

    OrderTypeEnum(Integer num) {
        this.num = num;
    }

    public Integer value() {
        return num;
    }


}
