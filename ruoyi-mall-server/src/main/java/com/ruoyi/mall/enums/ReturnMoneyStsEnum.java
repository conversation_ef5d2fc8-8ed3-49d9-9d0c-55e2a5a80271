package com.ruoyi.mall.enums;

public enum ReturnMoneyStsEnum {

    /**
     * 退款申请中
     */
    APPLY(1),

    /**
     * 卖家处理退款
     */
    PROCESSING(2),
    /**
     * 退款成功
     */
    SUCCESS(3),

    /**
     * 客户撤回退款申请
     */
    CANCEL(4),

    /**
     * 商家拒绝
     */
    REJECT(5),

    /**
     * 退款关闭
     */
    FAIL(-1);

    private final Integer num;

    ReturnMoneyStsEnum(Integer num) {
        this.num = num;
    }

    public static ReturnMoneyStsEnum instance(Integer value) {
        ReturnMoneyStsEnum[] enums = values();
        for (ReturnMoneyStsEnum statusEnum : enums) {
            if (statusEnum.value().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }

    public Integer value() {
        return num;
    }
}
