package com.ruoyi.mall.enums;

import lombok.Getter;

/**
 * 实名认证状态枚举
 */
@Getter
public enum RealNameStatusEnum {

    PENDING("0", "审核中"),
    APPROVED("1", "审核通过"),
    REJECTED("2", "审核拒绝"),
    RESUBMIT("3", "重新提交");

    private final String code;
    private final String info;

    RealNameStatusEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    /**
     * 根据状态码获取枚举
     */
    public static RealNameStatusEnum getByCode(String code) {
        for (RealNameStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
