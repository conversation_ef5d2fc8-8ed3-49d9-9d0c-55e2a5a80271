package com.ruoyi.mall.enums;

/**
 * 支付方式枚举
 */

public enum PayTypeEnum {


    /**
     * 微信支付
     */
    WECHAT_PAY("1", "微信支付"),
    /**
     * 支付宝
     */
    ALI_PAY("2", "支付宝"),
    /**
     * 抵扣金支付
     */
    DEDUCTION_PAY("3", "抵扣金支付"),
    /**
     * 银联
     */
    UNION_PAY("4", "银联"),
    /**
     * 组合支付
     */
    COMPOSE_PAY("5", "组合支付"),
    /**
     * 线下支付
     */
    OFFLINE_PAY("6", "线下支付");

    private final String number;
    private final String name;

    PayTypeEnum(String number, String name) {
        this.number = number;
        this.name = name;
    }

    public String getNumber() {
        return number;
    }

    public String getName() {
        return name;
    }
}
