package com.ruoyi.mall.factory;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.mall.strategy.VirtualPaymentSuccess.PaymentSuccessStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付成功处理策略工厂
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PaymentSuccessFactory {

    private final List<PaymentSuccessStrategy> paymentSuccessStrategies;

    private final Map<Integer, PaymentSuccessStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        paymentSuccessStrategies.forEach(strategy ->
            strategyMap.put(strategy.getProductType(), strategy)
        );
        log.info("支付成功处理策略初始化完成，共加载{}个策略", strategyMap.size());
    }

    /**
     * 获取支付成功处理策略
     *
     * @param productType 产品类型
     * @return 支付成功处理策略
     */
    public PaymentSuccessStrategy getPaymentSuccessStrategy(Integer productType) {
        PaymentSuccessStrategy strategy = strategyMap.get(productType);
        if (strategy == null) {
            throw new ServiceException("未找到产品类型对应的支付成功处理策略: " + productType);
        }
        return strategy;
    }
}
