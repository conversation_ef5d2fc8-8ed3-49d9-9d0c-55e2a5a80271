package com.ruoyi.mall.strategy.ReceptionA;

import com.ruoyi.mall.strategy.ReceptionA.impl.ComponentStrategy;
import com.ruoyi.mall.strategy.ReceptionA.impl.DrainageStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 接收A系统推送数据策略工厂
 */
@Component
@RequiredArgsConstructor
public class ReceptionAFactory {

    private final ComponentStrategy componentStrategy;
    private final DrainageStrategy drainageStrategy;

    /**
     * 虚拟商品策略
     *
     * @param type 支付方式：1-分量配置配置，2-单次引流配置，
     */
    public ReceptionAStrategy getReceptionStrategy(Integer type) {
        switch (type) {
            case 1:
                return componentStrategy;
            case 2:
                return drainageStrategy;
            default:
                throw new RuntimeException("不支持的推送类型");
        }
    }
}
