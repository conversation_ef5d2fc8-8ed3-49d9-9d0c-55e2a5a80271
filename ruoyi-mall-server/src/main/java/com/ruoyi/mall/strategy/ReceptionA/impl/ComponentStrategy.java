package com.ruoyi.mall.strategy.ReceptionA.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.mall.domain.bo.ConfigSettingBo;
import com.ruoyi.mall.domain.bo.ReceptionABo;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.vo.ConfigSettingVo;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.strategy.ReceptionA.ReceptionAStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 分量策略实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ComponentStrategy implements ReceptionAStrategy {

    private final static Integer index = 4;
    private final IConfigSettingService configSettingService;
    private final TzUserMapper tzUserMapper;

    @Override
    public Boolean setValue(ReceptionABo receptionABo) {
        String key = ConfigSettingConstants.config.get(index);
        ConfigSettingVo configSettingVo = configSettingService.getConfigByKey(key);
        ConfigSettingBo configSetting = new ConfigSettingBo();
        configSetting.setConfigKey(ConfigSettingConstants.config.get(index));
        configSetting.setConfigName(ConfigSettingConstants.configName.get(index));
        configSetting.setConfigValue(receptionABo.getJsonObject());

        // 查看传输的值
        JSONObject jsonObject = receptionABo.getJsonObject();
        // 获取新的值
        Double dailyThresholdReward = jsonObject.getDouble("dailyThresholdReward");

        // 获取数据的值
        JSONObject dbConfigValue = configSettingVo.getConfigValue();
        Double dbMonthlyNoTechThreshold = dbConfigValue.getDouble("dailyThresholdReward");
        if (!dbMonthlyNoTechThreshold.equals(dailyThresholdReward)) {
            // 更新用户的抵扣金上限额度
            double v = dailyThresholdReward - dbMonthlyNoTechThreshold;
            tzUserMapper.update(
                new LambdaUpdateWrapper<TzUser>()
                    .setSql("deduction_money_limit = CASE " +
                        "    WHEN (deduction_money_limit + " + v + ") <= 0 THEN 0.01" +
                        "    ELSE deduction_money_limit + " + v +
                        "END")
            );

        }

        if (configSettingVo == null) {
            return configSettingService.saveConfig(configSetting);
        } else {
            return configSettingService.updateConfig(configSetting);
        }
    }

    @Override
    public ConfigSettingVo getValue() {
        String key = ConfigSettingConstants.config.get(index);
        return configSettingService.getConfigByKey(key);
    }
}
