package com.ruoyi.mall.strategy.VirtualPaymentSuccess.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.TzUserStatus;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.enums.VirtualTypeEnum;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.mall.strategy.VirtualPaymentSuccess.AbstractPaymentSuccessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 技术引流支付成功处理策略
 */
@Slf4j
@Component
public class DrainagePaymentSuccessStrategy extends AbstractPaymentSuccessStrategy {

    @Autowired
    private TzUserMapper tzUserMapper;

    @Override
    public boolean handlePaymentSuccess(VirtualOrder order) {
        SysUser sysUser = getSysUser(order.getUserId());
        if (sysUser == null) {
            return false;
        }

        Shop shop = getShopByUserId(order.getUserId());
        if (shop == null) {
            return false;
        }

        // 获取技术引流的次数
        Integer quantity = order.getQuantity();
        shop.setDrainage(shop.getDrainage() + quantity);
        shopMapper.updateById(shop);
        log.info("技术引流支付成功，增加技术引流次数: {}，shopId: {}", quantity, shop.getId());

        // 检查对应的商城用户状态，如果是失效状态则改为正常状态
        Long tzUserId = sysUser.getTzUserId();
        if (tzUserId != null) {
            TzUser tzUser = tzUserMapper.selectOne(
                new LambdaQueryWrapper<TzUser>()
                    .eq(TzUser::getUserId, tzUserId)
                    .eq(TzUser::getDelFlag, "0")
            );

            if (tzUser == null) {
                log.error("技术引流支付成功，但商城用户不存在，tzUserId: {}", tzUserId);
            } else if (TzUserStatus.FAILURE.getCode().equals(tzUser.getStatus())) {
                // 如果用户是失效状态，则更新为正常状态
                tzUser.setStatus(TzUserStatus.OK.getCode());
                tzUserMapper.updateById(tzUser);
                log.info("技术引流支付成功，商家用户状态从失效改为正常，tzUserId: {}", tzUserId);
            }
        } else {
            log.error("技术引流支付成功，但系统用户没有关联商城用户ID，sysUserId: {}", sysUser.getUserId());
        }

        return true;
    }

    @Override
    public Integer getProductType() {
        return VirtualTypeEnum.JISHU_DRAINAGE.type();
    }
}
