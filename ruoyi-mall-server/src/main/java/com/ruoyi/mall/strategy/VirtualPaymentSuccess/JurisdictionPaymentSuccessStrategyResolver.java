package com.ruoyi.mall.strategy.VirtualPaymentSuccess;

import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.enums.VirtualTypeEnum;
import com.ruoyi.mall.strategy.VirtualPaymentSuccess.impl.JurisdictionPaymentSuccessStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 权限类策略解析器，处理权限类型1-3
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JurisdictionPaymentSuccessStrategyResolver {

    private final JurisdictionPaymentSuccessStrategy jurisdictionPaymentSuccessStrategy;

    /**
     * 解析权限类型并处理支付成功逻辑
     *
     * @param order 虚拟订单
     * @return 处理结果
     */
    public boolean resolveAndHandle(VirtualOrder order) {
        Integer productType = order.getProductType();

        // 检查是否为权限类型1-3
        if (Objects.equals(productType, VirtualTypeEnum.DAIXOAO_ONE.type()) ||
            Objects.equals(productType, VirtualTypeEnum.DAIXOAO_TWO.type()) ||
            Objects.equals(productType, VirtualTypeEnum.DAIXOAO_THERE.type())) {

            return jurisdictionPaymentSuccessStrategy.handlePaymentSuccess(order);
        }

        return false;
    }
}
