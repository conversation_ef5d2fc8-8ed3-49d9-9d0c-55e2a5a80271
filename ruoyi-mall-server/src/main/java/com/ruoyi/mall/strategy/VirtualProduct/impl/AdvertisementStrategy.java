package com.ruoyi.mall.strategy.VirtualProduct.impl;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.strategy.VirtualProduct.VirtualStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 广告投放价格策略
 */
@Component
@RequiredArgsConstructor
public class AdvertisementStrategy implements VirtualStrategy {

    private final static Integer index = 6;
    private final IConfigSettingService configSettingService;

    @Override
    public BigDecimal getAmount() {
        JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(index));
        String str = configValue.getStr("offer");
        return new BigDecimal(str);
    }

    @Override
    public Long getCount() {
        // 广告投放不涉及数量限制，返回0
        return 0L;
    }
}
