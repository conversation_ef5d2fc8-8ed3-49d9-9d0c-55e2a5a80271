package com.ruoyi.mall.strategy.VirtualProduct.impl;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.common.constant.ConsignmentProductLimitConstants;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.strategy.VirtualProduct.VirtualStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 代销权限开通策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DaixaioOpenStrategy implements VirtualStrategy {

    private final static Integer index = 2;
    private final IConfigSettingService configSettingService;

    @Override
    public BigDecimal getAmount() {
        JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(index));
        String str = configValue.getStr("discountPrice");
        return new BigDecimal(str);
    }

    /**
     * 只配置了开通权限的数量，没有配置数量限制，所以返回0
     */
    @Override
    public Long getCount() {
        return ConsignmentProductLimitConstants.ConsignmentProductLimitZero;
    }
}
