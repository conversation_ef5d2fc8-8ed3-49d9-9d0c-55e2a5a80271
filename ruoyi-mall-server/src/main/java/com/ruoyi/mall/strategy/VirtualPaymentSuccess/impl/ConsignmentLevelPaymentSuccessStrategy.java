package com.ruoyi.mall.strategy.VirtualPaymentSuccess.impl;

import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.enums.VirtualTypeEnum;
import com.ruoyi.mall.strategy.VirtualPaymentSuccess.AbstractPaymentSuccessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 代销权限等级支付成功处理策略
 */
@Slf4j
@Component
public class ConsignmentLevelPaymentSuccessStrategy extends AbstractPaymentSuccessStrategy {

    @Override
    public boolean handlePaymentSuccess(VirtualOrder order) {
        log.info("开始处理代销权限等级支付成功，订单号: {}, 产品类型: {}", order.getOrderNo(), order.getProductType());

        Shop shop = getShopByUserId(order.getUserId());
        if (shop == null) {
            log.error("代销权限等级支付成功处理策略，商家不存在，userId: {}", order.getUserId());
            return false;
        }

        // 检查代销权限升级路径：基础代销(0) -> 权限一(1) -> 权限二(2)
        // 注意：能访问到此功能的用户必然已开通基础代销权限，无需额外检查
        String currentLevel = shop.getConsignmentLevel() != null ? shop.getConsignmentLevel() : "0";

        if (VirtualTypeEnum.CONSIGNMENT_LEVEL_ONE.type().equals(order.getProductType())) {
            // 开通代销权限一：必须是基础代销用户
            if (!"0".equals(currentLevel)) {
                log.error("开通代销权限一失败，当前等级: {}，只有基础代销用户才能升级到权限一，userId: {}", currentLevel, order.getUserId());
                return false;
            }
        } else if (VirtualTypeEnum.CONSIGNMENT_LEVEL_TWO.type().equals(order.getProductType())) {
            // 开通代销权限二：可以从基础代销(0)或权限一(1)升级
            if (!"0".equals(currentLevel) && !"1".equals(currentLevel)) {
                log.error("开通代销权限二失败，当前等级: {}，只有基础代销或权限一用户才能升级到权限二，userId: {}", currentLevel, order.getUserId());
                return false;
            }
        }

        // 检查是否为再次开通（等级权限过期后重新开通）
        boolean isRenewal = isRenewalPayment(shop, order.getProductType());

        // 根据产品类型设置代销等级
        if (VirtualTypeEnum.CONSIGNMENT_LEVEL_ONE.type().equals(order.getProductType())) {
            shop.setConsignmentLevel("1");
            if (isRenewal) {
                log.info("再次开通代销权限一，shopId: {}, 过期时间: {}", shop.getId(), order.getExpireTime());
            } else {
                log.info("设置代销权限一，shopId: {}, 过期时间: {}", shop.getId(), order.getExpireTime());
            }
        } else if (VirtualTypeEnum.CONSIGNMENT_LEVEL_TWO.type().equals(order.getProductType())) {
            shop.setConsignmentLevel("2");
            if (isRenewal) {
                log.info("再次开通代销权限二，shopId: {}, 过期时间: {}", shop.getId(), order.getExpireTime());
            } else {
                log.info("设置代销权限二，shopId: {}, 过期时间: {}", shop.getId(), order.getExpireTime());
            }
        } else {
            log.error("不支持的代销权限等级类型: {}", order.getProductType());
            return false;
        }

        // 设置代销等级过期时间
        shop.setConsignmentExpireTime(order.getExpireTime());
        shop.setConsignmentLevelExpireTime(order.getExpireTime());

        boolean result = shopMapper.updateById(shop) > 0;

        if (result) {
            log.info("代销权限等级支付成功处理完成，shopId: {}, 等级: {}", shop.getId(), shop.getConsignmentLevel());
        } else {
            log.error("代销权限等级支付成功处理失败，shopId: {}", shop.getId());
        }

        return result;
    }

    /**
     * 判断是否为再次开通（等级权限过期后重新开通）
     */
    private boolean isRenewalPayment(Shop shop, Integer productType) {
        String currentLevel = shop.getConsignmentLevel();
        Date currentLevelExpireTime = shop.getConsignmentLevelExpireTime();

        String targetLevel = null;
        if (VirtualTypeEnum.CONSIGNMENT_LEVEL_ONE.type().equals(productType)) {
            targetLevel = "1";
        } else if (VirtualTypeEnum.CONSIGNMENT_LEVEL_TWO.type().equals(productType)) {
            targetLevel = "2";
        }

        // 如，果当前等级与要开通的等级相同且等级权限已过期，则认为是再次开通
        if (targetLevel != null && targetLevel.equals(currentLevel) &&
            currentLevelExpireTime != null &&
            currentLevelExpireTime.before(new Date())) {
            return true;
        }

        return false;
    }

    @Override
    public Integer getProductType() {
        // 由于代销权限等级有两种类型(7和8)，这里返回一个默认值
        // 实际匹配会在工厂中根据具体的产品类型进行
        return VirtualTypeEnum.CONSIGNMENT_LEVEL_ONE.type();
    }
}
