package com.ruoyi.mall.strategy.VirtualPaymentSuccess.impl;

import com.ruoyi.mall.domain.entity.ShopAdvertisement;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.enums.VirtualTypeEnum;
import com.ruoyi.mall.mapper.ShopAdvertisementMapper;
import com.ruoyi.mall.strategy.VirtualPaymentSuccess.AbstractPaymentSuccessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 广告投放支付成功处理策略
 */
@Slf4j
@Component
public class AdvertisementPaymentSuccessStrategy extends AbstractPaymentSuccessStrategy {

    @Autowired
    private ShopAdvertisementMapper shopAdvertisementMapper;

    @Override
    public boolean handlePaymentSuccess(VirtualOrder order) {
        // 检查订单使用情况
        if (order == null) {
            log.error("广告投放支付成功处理策略，订单为空");
            return false;
        }

        // 获取广告ID
        Long adId = order.getOrderId();
        if (adId == null) {
            log.error("广告投放支付成功处理策略，广告ID为空");
            return false;
        }

        try {
            // 查询广告信息
            ShopAdvertisement ad = shopAdvertisementMapper.selectById(adId);
            if (ad == null) {
                log.error("广告投放支付成功处理策略，广告不存在，adId: {}", adId);
                return false;
            }

            // 检查广告是否是待支付状态
            if (!"1".equals(ad.getStatus())) {
                log.warn("广告投放支付成功处理策略，广告状态不是待支付(审核通过)，adId: {}, status: {}", adId, ad.getStatus());
                return false;
            }

            // 更新广告状态为已生效(2)
            ad.setStatus("2");
            // 设置广告有效期
            ad.setStartTime(new Date());
            // 结束时间为一个月
            ad.setEndTime(new Date(ad.getStartTime().getTime() + 7L * 24 * 60 * 60 * 1000));
            int result = shopAdvertisementMapper.updateById(ad);

            if (result > 0) {
                log.info("广告投放支付成功，广告状态已更新为已生效，adId: {}", adId);
                return true;
            } else {
                log.error("广告投放支付成功处理策略，更新广告状态失败，adId: {}", adId);
                return false;
            }
        } catch (Exception e) {
            log.error("广告投放支付成功处理策略处理异常，adId: {}", adId, e);
            return false;
        }
    }

    @Override
    public Integer getProductType() {
        return VirtualTypeEnum.ADVERTISEMENT_PUTTING.type();
    }
}
