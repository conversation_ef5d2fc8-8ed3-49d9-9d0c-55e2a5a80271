package com.ruoyi.mall.strategy.VirtualProduct;

import com.ruoyi.mall.strategy.VirtualProduct.impl.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 虚拟商品金额策略工厂
 */
@Component
@RequiredArgsConstructor
public class VirtualFactory {

    private final QuanxianOneStrategy quanxianOneStrategy;
    private final QuanxianTwoStrategy quanxianTwoStrategy;
    private final QuanxianThereStrategy quanxianThereStrategy;
    private final DaixaioOpenStrategy daixaioOpenStrategy;
    private final JishuDrainageStrategy jishuDrainageStrategy;
    private final AdvertisementStrategy advertisementStrategy;
    private final ConsignmentLevelOneStrategy consignmentLevelOneStrategy;
    private final ConsignmentLevelTwoStrategy consignmentLevelTwoStrategy;

    /**
     * 虚拟商品策略
     *
     * @param payType 支付方式：1-权限一，2-权限二，3-权限三，4-开通代销，5-技术引流，6-广告投放，7-代销权限一，8-代销权限二
     * @return 虚拟商品信息策略
     */
    public VirtualStrategy getPaymentStrategy(Integer payType) {
        switch (payType) {
            case 1:
                return quanxianOneStrategy;
            case 2:
                return quanxianTwoStrategy;
            case 3:
                return quanxianThereStrategy;
            case 4:
                return daixaioOpenStrategy;
            case 5:
                return jishuDrainageStrategy;
            case 6:
                return advertisementStrategy;
            case 7:
                return consignmentLevelOneStrategy;
            case 8:
                return consignmentLevelTwoStrategy;
            default:
                throw new RuntimeException("不支持的虚拟商品");
        }
    }
}
