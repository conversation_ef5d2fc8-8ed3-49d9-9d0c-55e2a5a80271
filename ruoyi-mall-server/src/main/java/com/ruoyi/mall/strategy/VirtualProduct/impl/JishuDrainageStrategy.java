package com.ruoyi.mall.strategy.VirtualProduct.impl;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.strategy.VirtualProduct.VirtualStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 技术引流价格策略
 */
@Component
@RequiredArgsConstructor
public class JishuDrainageStrategy implements VirtualStrategy {

    private final static Integer index = 5;
    private final IConfigSettingService configSettingService;

    @Override
    public BigDecimal getAmount() {
        JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(index));
        String str = configValue.getStr("citationValue");
        return new BigDecimal(str);
    }

    @Override
    public Long getCount() {
        // 技术引流不涉及数量限制，返回0
        return 0L;
    }
}
