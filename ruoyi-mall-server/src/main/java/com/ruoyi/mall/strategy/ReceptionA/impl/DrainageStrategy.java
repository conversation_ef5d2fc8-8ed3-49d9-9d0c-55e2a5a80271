package com.ruoyi.mall.strategy.ReceptionA.impl;

import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.mall.domain.bo.ConfigSettingBo;
import com.ruoyi.mall.domain.bo.ReceptionABo;
import com.ruoyi.mall.domain.vo.ConfigSettingVo;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.strategy.ReceptionA.ReceptionAStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 引流策略实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DrainageStrategy implements ReceptionAStrategy {

    private final static Integer index = 5;
    private final IConfigSettingService configSettingService;

    @Override
    public Boolean setValue(ReceptionABo receptionABo) {
        String key = ConfigSettingConstants.config.get(index);
        ConfigSettingVo configSettingVo = configSettingService.getConfigByKey(key);
        ConfigSettingBo configSetting = new ConfigSettingBo();
        configSetting.setConfigKey(ConfigSettingConstants.config.get(index));
        configSetting.setConfigName(ConfigSettingConstants.configName.get(index));
        configSetting.setConfigValue(receptionABo.getJsonObject());
        if (configSettingVo == null) {
            configSetting.setConfigName(ConfigSettingConstants.configName.get(index));
            return configSettingService.saveConfig(configSetting);
        } else {
            return configSettingService.updateConfig(configSetting);
        }
    }

    @Override
    public ConfigSettingVo getValue() {
        String key = ConfigSettingConstants.config.get(index);
        return configSettingService.getConfigByKey(key);
    }
}
