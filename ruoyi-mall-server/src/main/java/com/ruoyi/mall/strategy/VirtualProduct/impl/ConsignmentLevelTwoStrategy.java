package com.ruoyi.mall.strategy.VirtualProduct.impl;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.common.constant.ConsignmentProductLimitConstants;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.strategy.VirtualProduct.VirtualStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 代销权限二开通策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConsignmentLevelTwoStrategy implements VirtualStrategy {

    private final static Integer index = 12;
    private final IConfigSettingService configSettingService;

    @Override
    public BigDecimal getAmount() {
        try {
            JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(index));
            if (configValue != null) {
                String discountPrice = configValue.getStr("discountPrice");
                if (discountPrice != null && !discountPrice.isEmpty()) {
                    return new BigDecimal(discountPrice);
                }
            }
        } catch (Exception e) {
            log.error("获取代销权限二价格配置失败", e);
        }
        // 如果配置不存在或获取失败，返回默认价格
        log.warn("代销权限二配置不存在或获取失败，使用默认价格");
        return new BigDecimal("299.00");
    }

    /**
     * 获取代销权限二可以代销的商品数量
     */
    @Override
    public Long getCount() {
        try {
            JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(index));
            if (configValue != null) {
                Long count = configValue.getLong("maxProducts");
                return count != null ? count : 50L;
            }
        } catch (Exception e) {
            log.error("获取代销权限二商品数量配置失败", e);
        }
        // 如果配置不存在，返回默认数量
        log.warn("代销权限二商品数量配置不存在，使用默认数量");
        return ConsignmentProductLimitConstants.ConsignmentProductLimitTwO;
    }

    /**
     * 检查代销权限二是否启用
     */
    public boolean isEnabled() {
        try {
            JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(index));
            if (configValue != null) {
                Boolean enabled = configValue.getBool("enabled");
                return enabled != null ? enabled : true; // 默认启用
            }
        } catch (Exception e) {
            log.error("获取代销权限二启用状态失败", e);
        }
        // 如果配置不存在，默认启用
        return true;
    }
}
