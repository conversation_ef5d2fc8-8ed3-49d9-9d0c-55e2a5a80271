package com.ruoyi.mall.strategy.VirtualPaymentSuccess.impl;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.strategy.VirtualPaymentSuccess.AbstractPaymentSuccessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 权限类支付成功处理策略
 */
@Slf4j
@Component
public class JurisdictionPaymentSuccessStrategy extends AbstractPaymentSuccessStrategy {

    @Override
    public boolean handlePaymentSuccess(VirtualOrder order) {
        log.info("开始处理权限类支付成功，订单号: {}, 产品类型: {}", order.getOrderNo(), order.getProductType());

        SysUser sysUser = getSysUser(order.getUserId());
        if (sysUser == null) {
            log.error("权限类支付成功处理策略，用户不存在，userId: {}", order.getUserId());
            return false;
        }

        Shop shop = getShopByUserId(order.getUserId());
        if (shop == null) {
            log.error("权限类支付成功处理策略，商家不存在，userId: {}", order.getUserId());
            return false;
        }

        // 检查是否为再次开通（权限过期后重新开通）
        boolean isRenewal = isRenewalPayment(shop, order.getProductType());

        if (isRenewal) {
            log.info("检测到权限类再次开通，shopId: {}, 权限类型: {}", shop.getId(), order.getProductType());
            // 再次开通：更新权限和过期时间，保留原有数据
            shop.setJurisdiction(order.getProductType().toString());
            shop.setJurisdictionExpireTime(order.getExpireTime());

            // 如果商家状态不正常，恢复正常状态
            if (!"0".equals(shop.getStatus())) {
                shop.setStatus("0"); // 恢复正常状态
                log.info("恢复商家正常状态，shopId: {}", shop.getId());
            }
        } else {
            log.info("权限类首次开通或升级，shopId: {}, 权限类型: {}", shop.getId(), order.getProductType());
            // 首次开通或升级：直接设置新权限
            shop.setJurisdiction(order.getProductType().toString());
            shop.setJurisdictionExpireTime(order.getExpireTime());
        }

        boolean result = shopMapper.updateById(shop) > 0;

        if (result) {
            log.info("权限类支付成功处理完成，设置商家权限: {}，过期时间: {}，shopId: {}",
                order.getProductType(), order.getExpireTime(), shop.getId());
        } else {
            log.error("权限类支付成功处理失败，shopId: {}", shop.getId());
        }

        return result;
    }

    /**
     * 判断是否为再次开通（权限过期后重新开通）
     */
    private boolean isRenewalPayment(Shop shop, Integer productType) {
        String currentJurisdiction = shop.getJurisdiction();
        Date currentExpireTime = shop.getJurisdictionExpireTime();

        // 如果当前权限类型与要开通的类型相同，且权限已过期，则认为是再次开通
        if (productType.toString().equals(currentJurisdiction) &&
            currentExpireTime != null &&
            currentExpireTime.before(new Date())) {
            return true;
        }

        return false;
    }

    @Override
    public Integer getProductType() {
        // 返回值为1、2、3中的任一值，工厂会根据订单中的productType查找匹配的策略
        // 由于权限类型有多个，所以这里返回一个通用值，需要在VirtualOrderServiceImpl中特殊处理
        return 1;
    }
}
