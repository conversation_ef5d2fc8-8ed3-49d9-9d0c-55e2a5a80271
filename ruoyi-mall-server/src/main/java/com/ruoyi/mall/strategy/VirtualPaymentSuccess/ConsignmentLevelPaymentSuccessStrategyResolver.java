package com.ruoyi.mall.strategy.VirtualPaymentSuccess;

import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.enums.VirtualTypeEnum;
import com.ruoyi.mall.strategy.VirtualPaymentSuccess.impl.ConsignmentLevelPaymentSuccessStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 代销权限等级策略解析器，处理代销权限等级类型7-8
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConsignmentLevelPaymentSuccessStrategyResolver {

    private final ConsignmentLevelPaymentSuccessStrategy consignmentLevelPaymentSuccessStrategy;

    /**
     * 解析代销权限等级类型并处理支付成功逻辑
     *
     * @param order 虚拟订单
     * @return 处理结果
     */
    public boolean resolveAndHandle(VirtualOrder order) {
        Integer productType = order.getProductType();

        // 检查是否为代销权限等级类型7-8
        if (Objects.equals(productType, VirtualTypeEnum.CONSIGNMENT_LEVEL_ONE.type()) ||
            Objects.equals(productType, VirtualTypeEnum.CONSIGNMENT_LEVEL_TWO.type())) {

            log.info("检测到代销权限等级类型: {}, 订单ID: {}", productType, order.getId());
            return consignmentLevelPaymentSuccessStrategy.handlePaymentSuccess(order);
        }

        return false;
    }
}
