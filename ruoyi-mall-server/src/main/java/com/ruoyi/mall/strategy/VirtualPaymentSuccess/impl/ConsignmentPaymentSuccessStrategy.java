package com.ruoyi.mall.strategy.VirtualPaymentSuccess.impl;

import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.ruoyi.common.constant.MenuConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.TzUserStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.enums.VirtualTypeEnum;
import com.ruoyi.mall.mapper.ShopMapper;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.mall.strategy.VirtualPaymentSuccess.AbstractPaymentSuccessStrategy;
import com.ruoyi.sms.enums.SmsType;
import com.ruoyi.sms.service.SmsLogService;
import com.ruoyi.system.domain.SysUserMenu;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.mapper.SysUserMenuMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 代销支付成功处理策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConsignmentPaymentSuccessStrategy extends AbstractPaymentSuccessStrategy {

    private final TzUserMapper tzUserMapper;
    private final ShopMapper shopMapper;
    private final SysUserMapper sysUserMapper;
    private final SysUserMenuMapper sysUserMenuMapper;
    private final SmsLogService smsLogService;

    public static List<SysUserMenu> setMerchantMenuId(SysUser shopUser, List<Long> merchantMenuIdList) {
        List<SysUserMenu> userMenuList = new ArrayList<>(merchantMenuIdList.size());
        for (Long menuId : merchantMenuIdList) {
            SysUserMenu userMenu = new SysUserMenu();
            userMenu.setUserId(shopUser.getUserId());
            userMenu.setMenuId(menuId);
            userMenuList.add(userMenu);
        }
        return userMenuList;
    }

    @NotNull
    private static Shop getShop(Long userId, TzUser tzUser) {
        Shop shop = new Shop();
        shop.setUserId(userId);
        shop.setName(tzUser.getUsername() + "的店铺");
        shop.setLogo(tzUser.getAvatar());
        shop.setType("0");//店铺类型：代销商家
        shop.setLegalPerson(tzUser.getUsername());
        shop.setPhone(tzUser.getPhone());
        shop.setLegalPersonIdCard(tzUser.getIdCard());
        shop.setIdCardBack(tzUser.getIdCardBack());
        shop.setIdCardFront(tzUser.getIdCardFront());
        shop.setIdCardHand(tzUser.getIdCardHand());
        shop.setProvinceCode(tzUser.getProvinceCode());
        shop.setProvince(tzUser.getProvince());
        shop.setCityCode(tzUser.getCityCode());
        shop.setCity(tzUser.getCity());
        shop.setDistrictCode(tzUser.getDistrictCode());
        shop.setDistrict(tzUser.getDistrict());
        shop.setTownCode(tzUser.getTownCode());
        shop.setTown(tzUser.getTown());
        shop.setAddress(tzUser.getAddress());
        shop.setAuthStatus("1"); // 已认证
        shop.setStatus("0"); // 正常状态
        shop.setDelFlag("0"); // 未删除
        // 代销等级相关字段初始化
        shop.setConsignmentLevel("0"); // 基础代销等级
        shop.setConsignmentLevelExpireTime(null); // 等级权限过期时间初始为空
        return shop;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handlePaymentSuccess(VirtualOrder order) {
        SysUser existingSysUser;
        TzUser tzUser;
        Long userId = order.getUserId();
        // 用户创建的订单
        if ("2".equals(order.getOrderType())) {
            tzUser = tzUserMapper.selectById(userId);
            // 检查是否已存在SysUser账户（再次开通的情况）
            existingSysUser = sysUserMapper.selectOne(
                new LambdaQueryWrapper<SysUser>()
                    .eq(SysUser::getTzUserId, userId)
                    .eq(SysUser::getIsConsignment, "1")
            );
        } else {
            // 检查是否已存在SysUser账户（再次开通的情况）
            existingSysUser = sysUserMapper.selectOne(
                new LambdaQueryWrapper<SysUser>()
                    .eq(SysUser::getUserId, userId)
                    .eq(SysUser::getIsConsignment, "1")
            );
            tzUser = tzUserMapper.selectById(existingSysUser.getTzUserId());
        }

        SysUser sysUser;
        if (existingSysUser != null) {
            // 再次开通：更新现有账户状态
            log.info("用户再次开通代销权限，更新现有账户，userId: {}", userId);
            sysUser = existingSysUser;
            sysUser.setStatus("0"); // 恢复正常状态
            sysUser.setNickName(tzUser.getNickname()); // 更新昵称

            if (sysUserMapper.updateById(sysUser) == 0) {
                throw new ServiceException("更新商家用户账号失败");
            }

            // 通过短信通知用户账户已重新激活
            smsLogService.sendSms(SmsType.SHOP_CHECK, "", sysUser.getPhonenumber(), Maps.newHashMap());

        } else {
            // 首次开通：创建新账户
            log.info("用户首次开通代销权限，创建新账户，userId: {}", userId);
            sysUser = new SysUser();
            sysUser.setTzUserId(tzUser.getUserId());
            sysUser.setNickName(tzUser.getNickname());
            sysUser.setUserType("sys_user");
            sysUser.setUserName(tzUser.getPhone());
            sysUser.setPhonenumber(tzUser.getPhone());
            sysUser.setPassword(BCrypt.hashpw("123456")); // 默认密码
            sysUser.setIsConsignment("1");
            sysUser.setStatus("0");
            sysUser.setDelFlag("0");

            // 通过短信发送密码给用户
            smsLogService.sendSms(SmsType.SHOP_CHECK, "", sysUser.getPhonenumber(), Maps.newHashMap());

            // 保存商家用户
            if (sysUserMapper.insert(sysUser) == 0) {
                throw new ServiceException("创建商家用户账号失败");
            }
        }

        // 处理用户菜单权限
        handleUserMenuPermissions(sysUser, existingSysUser != null);

        // 处理店铺信息
        handleShopInfo(sysUser, tzUser, order, existingSysUser != null);

        // 设置关系链（只在首次开通时设置）
        if (existingSysUser == null) {
            tzUser.setChainType("B");
            tzUser.setUserType("CB");
            // 设置用户状态为无效
            tzUser.setStatus(TzUserStatus.INVALID.getCode());
            if (tzUserMapper.updateById(tzUser) == 0) {
                throw new ServiceException("设置关系链失败");
            }
        }

        return true;
    }

    /**
     * 处理用户菜单权限
     */
    private void handleUserMenuPermissions(SysUser sysUser, boolean isRenewal) {
        if (isRenewal) {
            // 再次开通：检查并更新菜单权限（如果需要的话）
            log.info("再次开通代销权限，检查菜单权限，userId: {}", sysUser.getUserId());
            // 这里可以根据需要更新菜单权限，目前保持现有权限不变
        } else {
            // 首次开通：分配代销菜单权限
            log.info("首次开通代销权限，分配菜单权限，userId: {}", sysUser.getUserId());
            List<Long> merchantMenuIdList = MenuConstants.consignmentMenuIdList;
            List<SysUserMenu> userMenuList = setMerchantMenuId(sysUser, merchantMenuIdList);
            sysUserMenuMapper.insertBatch(userMenuList);
        }
    }

    /**
     * 处理店铺信息
     */
    private void handleShopInfo(SysUser sysUser, TzUser tzUser, VirtualOrder order, boolean isRenewal) {
        Shop existingShop = shopMapper.selectOne(
            new LambdaQueryWrapper<Shop>()
                .eq(Shop::getUserId, sysUser.getUserId())
                .eq(Shop::getType, "0") // 代销商家
        );

        if (existingShop != null) {
            // 再次开通：更新现有店铺的代销权限
            log.info("更新现有店铺的代销权限，shopId: {}", existingShop.getId());
            // 开通代销权限
            existingShop.setConsignmentPermission("1"); //
            existingShop.setConsignmentExpireTime(order.getExpireTime()); // 更新过期时间
            existingShop.setConsignmentLevel("0");
            existingShop.setConsignmentLevelExpireTime(order.getExpireTime());
            existingShop.setStatus("0"); // 恢复正常状态
            existingShop.setAuthStatus("1"); // 保持认证状态

            if (shopMapper.updateById(existingShop) == 0) {
                throw new ServiceException("更新代销商家信息失败");
            }
        } else {
            // 首次开通：创建新店铺
            log.info("创建新的代销店铺，userId: {}", sysUser.getUserId());
            Shop shop = getShop(sysUser.getUserId(), tzUser);
            shop.setConsignmentPermission("1"); // 开通代销权限
            shop.setConsignmentExpireTime(order.getExpireTime()); // 设置过期时间
            shop.setConsignmentLevel("0");
            shop.setConsignmentLevelExpireTime(order.getExpireTime());

            if (shopMapper.insert(shop) == 0) {
                throw new ServiceException("创建代销商家账号失败");
            }
        }
    }

    @Override
    public Integer getProductType() {
        return VirtualTypeEnum.DAIXIAO_OPEN.type();
    }
}
