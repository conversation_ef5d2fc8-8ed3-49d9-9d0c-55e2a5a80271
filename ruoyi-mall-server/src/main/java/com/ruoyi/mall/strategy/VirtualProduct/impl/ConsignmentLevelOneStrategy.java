package com.ruoyi.mall.strategy.VirtualProduct.impl;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.common.constant.ConsignmentProductLimitConstants;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.strategy.VirtualProduct.VirtualStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 代销权限一开通策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConsignmentLevelOneStrategy implements VirtualStrategy {

    private final static Integer index = 11;
    private final IConfigSettingService configSettingService;

    @Override
    public BigDecimal getAmount() {
        try {
            JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(index));
            if (configValue != null) {
                String discountPrice = configValue.getStr("discountPrice");
                if (discountPrice != null && !discountPrice.isEmpty()) {
                    return new BigDecimal(discountPrice);
                }
            }
        } catch (Exception e) {
            log.error("获取代销权限一价格配置失败", e);
        }
        // 如果配置不存在或获取失败，返回默认价格
        log.warn("代销权限一配置不存在或获取失败，使用默认价格");
        return new BigDecimal("99.00");
    }

    /**
     * 获取代销权限一可以代销的商品数量
     */
    @Override
    public Long getCount() {
        try {
            JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(index));
            if (configValue != null) {
                Long count = configValue.getLong("maxProducts");
                return count != null ? count : 10L;
            }
        } catch (Exception e) {
            log.error("获取代销权限一商品数量配置失败", e);
        }
        // 如果配置不存在，返回默认数量
        log.warn("代销权限一商品数量配置不存在，使用默认数量");
        return ConsignmentProductLimitConstants.ConsignmentProductLimitOne;
    }
}
