package com.ruoyi.mall.strategy.VirtualPaymentSuccess;

import com.ruoyi.mall.domain.entity.VirtualOrder;

import java.util.Map;

/**
 * 支付成功处理策略接口
 */
public interface PaymentSuccessStrategy {

    /**
     * 处理支付成功逻辑
     *
     * @param order 虚拟订单
     * @return 处理结果
     */
    boolean handlePaymentSuccess(VirtualOrder order);

    /**
     * 处理支付成功逻辑（带参数）
     *
     * @param order  虚拟订单
     * @param params 额外参数
     * @return 处理结果
     */
    boolean handlePaymentSuccess(VirtualOrder order, Map<String, Object> params);

    /**
     * 获取策略类型
     *
     * @return 产品类型
     */
    Integer getProductType();
}
