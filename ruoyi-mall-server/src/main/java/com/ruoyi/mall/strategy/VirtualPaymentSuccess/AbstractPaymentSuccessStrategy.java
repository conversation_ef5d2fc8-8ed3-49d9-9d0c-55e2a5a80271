package com.ruoyi.mall.strategy.VirtualPaymentSuccess;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.mapper.ShopMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * 抽象支付成功处理策略
 */
@Slf4j
public abstract class AbstractPaymentSuccessStrategy implements PaymentSuccessStrategy {

    @Autowired
    protected SysUserMapper sysUserMapper;

    @Autowired
    protected ShopMapper shopMapper;


    /**
     * 带参数的处理支付成功逻辑的默认实现
     * 子类可以根据需要重写该方法
     *
     * @param order  虚拟订单
     * @param params 额外参数
     * @return 处理结果
     */
    @Override
    public boolean handlePaymentSuccess(VirtualOrder order, Map<String, Object> params) {
        // 默认实现直接调用无参数的方法
        log.info("使用默认参数处理方式，忽略附加参数: {}", params);
        return handlePaymentSuccess(order);
    }

    /**
     * 获取用户对应的店铺信息
     *
     * @param userId 用户ID
     * @return 店铺信息
     */
    protected Shop getShopByUserId(Long userId) {
        SysUser sysUser = sysUserMapper.selectById(userId);
        if (sysUser == null) {
            log.error("支付成功处理，但系统用户不存在，userId: {}", userId);
            return null;
        }

        Shop shop = shopMapper.selectOne(
            new LambdaQueryWrapper<Shop>()
                .eq(Shop::getUserId, sysUser.getUserId())
        );

        if (shop == null) {
            log.error("支付成功处理，但商家店铺不存在，sysUserId: {}", sysUser.getUserId());
            return null;
        }

        return shop;
    }

    /**
     * 获取系统用户信息
     *
     * @param userId 用户ID
     * @return 系统用户
     */
    protected SysUser getSysUser(Long userId) {
        SysUser sysUser = sysUserMapper.selectById(userId);
        if (sysUser == null) {
            log.error("支付成功处理，但系统用户不存在，userId: {}", userId);
            return null;
        }
        return sysUser;
    }
}
