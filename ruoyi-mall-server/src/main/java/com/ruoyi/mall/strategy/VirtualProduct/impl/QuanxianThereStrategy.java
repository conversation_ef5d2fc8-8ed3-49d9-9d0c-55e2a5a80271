package com.ruoyi.mall.strategy.VirtualProduct.impl;

import cn.hutool.json.JSONObject;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.strategy.VirtualProduct.VirtualStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 权限三策略实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QuanxianThereStrategy implements VirtualStrategy {

    private final static Integer index = 1;
    private final IConfigSettingService configSettingService;

    @Override
    public BigDecimal getAmount() {
        JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(index));
        String str = configValue.getJSONObject("permission3").getStr("discountPrice");
        return new BigDecimal(str);
    }

    /**
     * 获取权限二可以代销的数量
     */
    @Override
    public Long getCount() {
        JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(1));
        return Long.valueOf(configValue.getStr("authorityThree"));
    }

}
