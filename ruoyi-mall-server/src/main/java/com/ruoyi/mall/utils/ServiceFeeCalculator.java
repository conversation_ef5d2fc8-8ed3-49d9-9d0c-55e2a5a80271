package com.ruoyi.mall.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 手续费计算工具类
 */
public class ServiceFeeCalculator {

    /**
     * 虚拟订单手续费率（千分之六）
     */
    private static final BigDecimal VIRTUAL_ORDER_FEE_RATE = new BigDecimal("0.006");

    /**
     * 计算虚拟订单手续费
     *
     * @param originalAmount 原始金额
     * @return 手续费金额
     */
    public static BigDecimal calculateVirtualOrderServiceFee(BigDecimal originalAmount) {
        if (originalAmount == null || originalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        return originalAmount.multiply(VIRTUAL_ORDER_FEE_RATE)
            .setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算包含手续费的总金额
     *
     * @param originalAmount 原始金额
     * @return 包含手续费的总金额
     */
    public static BigDecimal calculateTotalAmountWithServiceFee(BigDecimal originalAmount) {
        if (originalAmount == null || originalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal serviceFee = calculateVirtualOrderServiceFee(originalAmount);
        return originalAmount.add(serviceFee);
    }

    /**
     * 获取虚拟订单手续费率
     *
     * @return 手续费率
     */
    public static BigDecimal getVirtualOrderFeeRate() {
        return VIRTUAL_ORDER_FEE_RATE;
    }

    /**
     * 获取虚拟订单手续费率（百分比形式）
     *
     * @return 手续费率百分比
     */
    public static String getVirtualOrderFeeRatePercent() {
        return VIRTUAL_ORDER_FEE_RATE.multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP) + "%";
    }
}
