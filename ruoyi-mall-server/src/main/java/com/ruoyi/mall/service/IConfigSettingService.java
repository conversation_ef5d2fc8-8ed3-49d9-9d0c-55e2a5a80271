package com.ruoyi.mall.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.mall.domain.bo.ConfigSettingBo;
import com.ruoyi.mall.domain.entity.ConfigSetting;
import com.ruoyi.mall.domain.vo.ConfigSettingVo;

/**
 * 定制化配置服务接口
 */
public interface IConfigSettingService extends IService<ConfigSetting> {

    /**
     * 根据配置键获取配置值
     *
     * @param key 配置键
     * @return 配置值
     */
    JSONObject getConfigValue(String key);

    /**
     * 根据配置键获取配置对象
     *
     * @param key 配置键
     * @return 配置视图对象
     */
    ConfigSettingVo getConfigByKey(String key);

    /**
     * 保存配置
     *
     * @param configBo 配置业务对象
     * @return 配置ID
     */
    Boolean saveConfig(ConfigSettingBo configBo);

    /**
     * 更新配置
     *
     * @param configBo 配置业务对象
     * @return 是否成功
     */
    Boolean updateConfig(ConfigSettingBo configBo);
}
