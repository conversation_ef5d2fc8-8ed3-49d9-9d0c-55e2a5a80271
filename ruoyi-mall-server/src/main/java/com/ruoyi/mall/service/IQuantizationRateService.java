package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.mall.domain.bo.ReceiveQuantizedBo;
import com.ruoyi.mall.domain.entity.QuantizationRate;

import java.util.Date;
import java.util.List;

public interface IQuantizationRateService extends IService<QuantizationRate> {


    void receiveQuantizedData(ReceiveQuantizedBo receiveQuantizedBo);

    List<QuantizationRate> getQuantizationRate(Date searchMonth);
}
