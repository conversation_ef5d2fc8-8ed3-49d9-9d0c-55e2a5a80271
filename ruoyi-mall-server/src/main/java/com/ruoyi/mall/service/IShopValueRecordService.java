package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.FaithStatisticsDTO;
import com.ruoyi.mall.domain.dto.QuantificationStatisticsDTO;
import com.ruoyi.mall.domain.entity.ShopValueRecord;

import java.util.List;
import java.util.Map;

/**
 * 商家量化值转换记录 Service 接口
 */
public interface IShopValueRecordService extends IService<ShopValueRecord> {
    /**
     * 超级管理员量化值转换为量化
     *
     * @param shopId               商家ID
     * @param quantificationAmount 要转换的量化值数量
     * @param rate                 转换率
     * @return 转换结果记录
     */
    ShopValueRecord convertQuantificationToFaith(Double quantificationAmount, Double rate);

    /**
     * 量化值转换为量化
     *
     * @param shopId               商家ID
     * @param quantificationAmount 要转换的量化值数量
     * @param rate                 转换率
     * @return 转换结果记录
     */
    ShopValueRecord convertQuantificationToFaith(Long shopId, Double quantificationAmount, Double rate);

    /**
     * 超级管理员量化转换为平台促销金
     *
     * @param shopId      商家ID
     * @param faithAmount 要转换的量化数量
     * @param rate        转换率
     * @return 转换结果记录
     */
    ShopValueRecord convertFaithToPromotionGold(Double faithAmount, Double rate);

    /**
     * 量化转换为平台促销金
     *
     * @param shopId      商家ID
     * @param faithAmount 要转换的量化数量
     * @param rate        转换率
     * @return 转换结果记录
     */
    ShopValueRecord convertFaithToPromotionGold(Long shopId, Double faithAmount, Double rate);

    /**
     * 按时间统计量化值转化数据（分页版本）
     *
     * @param date          日期
     * @param phone         电话号码
     * @param operationType 操作类型
     * @param pageQuery     分页参数
     * @return 分页统计数据
     */
    TableDataInfo<QuantificationStatisticsDTO> statisticsQuantificationByTimePage(String date, String phone, Integer operationType, PageQuery pageQuery);

    /**
     * 导出量化值统计数据
     *
     * @param date          日期
     * @param phone         电话号码
     * @param operationType 操作类型
     * @return 用于导出的统计数据列表
     */
    List<QuantificationStatisticsDTO> exportQuantificationStatistics(String date, String phone, Integer operationType);

    /**
     * 按时间统计量化转化数据（分页版本）
     *
     * @param date          日期
     * @param phone         电话号码
     * @param operationType 操作类型
     * @param pageQuery     分页参数
     * @return 分页统计数据
     */
    TableDataInfo<FaithStatisticsDTO> statisticsFaithByTimePage(String date, String phone, Integer operationType, PageQuery pageQuery);

    /**
     * 导出量化统计数据
     *
     * @param date          日期
     * @param phone         电话号码
     * @param operationType 操作类型
     * @return 用于导出的统计数据列表
     */
    List<FaithStatisticsDTO> exportFaithStatistics(String date, String phone, Integer operationType);


    /**
     * 按时间统计admin量化值转化数据
     *
     * @param date          日期
     * @param phone         电话号码
     * @param operationType 操作类型
     * @return 分页统计数据
     */
    QuantificationStatisticsDTO adminQuantification(String date, String phone, Integer operationType);

    /**
     * 按时间统计admin量化转化数据
     *
     * @param date          日期
     * @param phone         电话号码
     * @param operationType 操作类型
     * @return 分页统计数据
     */
    FaithStatisticsDTO adminFaith(String date, String phone, Integer operationType);

    /**
     * 获取指定日期量化值进化统计
     *
     * @param date 日期字符串，格式：yyyy-MM-dd，如果为空则查询当天
     * @return 统计结果
     */
    Map<String, Object> getDateQuantificationStats(String date);

    /**
     * 获取指定日期量化进化统计
     *
     * @param date 日期字符串，格式：yyyy-MM-dd，如果为空则查询当天
     * @return 统计结果
     */
    Map<String, Object> getDateFaithStats(String date);
}
