package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.mall.domain.entity.ShopAddress;

import java.util.List;

public interface IShopAddressService extends IService<ShopAddress> {

    /**
     * 新增商家地址
     *
     * @param shopAddress 商家地址
     * @return
     */
    Integer insertShopAddress(ShopAddress shopAddress);

    /**
     * 根据ID查询商家地址
     *
     * @param id 地址ID
     * @return
     */
    ShopAddress selectShopAddressById(Long id);

    /**
     * 查询商家地址列表
     *
     * @param shopAddress
     * @param pageQuery
     * @return
     */
    List<ShopAddress> selectShopAddressList(ShopAddress shopAddress, PageQuery pageQuery);

    /**
     * 修改商家地址
     *
     * @param shopAddress
     * @return
     */
    Integer updateShopAddress(ShopAddress shopAddress);

    /**
     * 根据ID删除商家地址
     *
     * @param id
     * @return
     */
    Integer deleteShopAddressById(Long id);

    /**
     * 批量删除商家地址
     *
     * @param ids
     * @return
     */
    Integer deleteShopAddressByIds(Long[] ids);
}
