package com.ruoyi.mall.service;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.ShopWalletRecord;
import com.ruoyi.mall.domain.query.QueryShopWalletRecordDTO;
import com.ruoyi.mall.enums.TradeMethodEnum;
import com.ruoyi.mall.enums.TradeTypeEnum;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商家钱包交易记录服务接口
 */
public interface IShopWalletRecordService {

    /**
     * 查询商家钱包交易记录列表
     *
     * @param shopWalletRecord 商家钱包交易记录
     * @param pageQuery        分页对象
     * @return 商家钱包交易记录分页数据
     */
    TableDataInfo<ShopWalletRecord> queryPageList(ShopWalletRecord shopWalletRecord, PageQuery pageQuery);

    /**
     * 获取商家钱包交易记录详细信息
     *
     * @param id 记录ID
     * @return 商家钱包交易记录
     */
    ShopWalletRecord getById(Long id);

    /**
     * 新增商家钱包交易记录
     *
     * @param shopWalletRecord 商家钱包交易记录
     * @return 结果
     */
    boolean save(ShopWalletRecord shopWalletRecord);

    /**
     * 删除商家钱包交易记录
     *
     * @param id 记录ID
     * @return 结果
     */
    boolean removeById(Long id);

    /**
     * 创建交易记录
     *
     * @param walletId      钱包ID
     * @param shopId        商家ID
     * @param amount        交易金额
     * @param tradeType     交易类型
     * @param tradeMethod   交易方式
     * @param beforeBalance 交易前余额
     * @param afterBalance  交易后余额
     * @param orderNo       关联订单号
     * @param remark        备注
     * @return 结果
     */
    boolean createRecord(Long walletId, Long shopId, BigDecimal amount, TradeTypeEnum tradeType,
                         TradeMethodEnum tradeMethod, BigDecimal beforeBalance, BigDecimal afterBalance,
                         String orderNo, String remark);

    /**
     * 查询商家交易记录历史
     *
     * @param queryShopWalletRecordDTO 参数条件
     * @return
     */
    List<ShopWalletRecord> getShopWalletRecordList(QueryShopWalletRecordDTO queryShopWalletRecordDTO);
}
