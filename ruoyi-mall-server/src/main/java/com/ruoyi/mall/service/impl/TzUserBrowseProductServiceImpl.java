package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.mall.domain.entity.TzUserBrowseProduct;
import com.ruoyi.mall.mapper.TzUserBrowseProductMapper;
import com.ruoyi.mall.service.ITzUserBrowseProductService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.ruoyi.common.helper.LoginHelper.getUserId;

@Service
@RequiredArgsConstructor
public class TzUserBrowseProductServiceImpl extends ServiceImpl<TzUserBrowseProductMapper, TzUserBrowseProduct> implements ITzUserBrowseProductService {

    @Autowired
    private TzUserBrowseProductMapper userBrowseProductMapper;

    @Override
    public Integer addUserBrowseProduct(Long productId) {
        Integer count;
        Long userId = getUserId();

        LambdaQueryWrapper<TzUserBrowseProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(TzUserBrowseProduct::getProductId, productId)
            .eq(TzUserBrowseProduct::getUserId, userId);

        TzUserBrowseProduct userBrowseProduct = userBrowseProductMapper.selectOne(queryWrapper);
        if (userBrowseProduct != null) {
            LambdaUpdateWrapper<TzUserBrowseProduct> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                .set(TzUserBrowseProduct::getLastTime, new Date())
                .set(TzUserBrowseProduct::getNumber, userBrowseProduct.getNumber() + 1)
                .eq(TzUserBrowseProduct::getId, userBrowseProduct.getId());

            count = userBrowseProductMapper.update(updateWrapper);
        } else {
            TzUserBrowseProduct tzUserBrowseProduct = new TzUserBrowseProduct();
            tzUserBrowseProduct.setProductId(productId);
            tzUserBrowseProduct.setCreateTime(new Date());
            tzUserBrowseProduct.setUserId(userId);
            tzUserBrowseProduct.setNumber(1);
            tzUserBrowseProduct.setLastTime(new Date());
            count = userBrowseProductMapper.insert(tzUserBrowseProduct);
        }
        return count;
    }

    @Override
    public List<Map<String, Object>> getUserBrowseProductList(PageQuery pageQuery) {
        Long userId = getUserId();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); //时间格式（年-月-日）

        LambdaQueryWrapper<TzUserBrowseProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(TzUserBrowseProduct::getUserId, userId)
            .groupBy(TzUserBrowseProduct::getLastTime, TzUserBrowseProduct::getId)
            .orderByDesc(TzUserBrowseProduct::getLastTime);

        List<TzUserBrowseProduct> timeList = userBrowseProductMapper.selectPage(new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize()), queryWrapper).getRecords();
        List<Map<String, Object>> result = new ArrayList<>();

        for (TzUserBrowseProduct u : timeList) {
            Map<String, Object> map = new HashMap<>();

            MPJLambdaWrapper<TzUserBrowseProduct> wrapper = new MPJLambdaWrapper<>();
            wrapper
                .select("t.*,p.name,p.cover,p.price,p.shop_id")
                .leftJoin("mall_product p on p.id=t.product_id")
                .eq(TzUserBrowseProduct::getUserId, userId)
                .eq(TzUserBrowseProduct::getLastTime, u.getLastTime())
                .orderByDesc(TzUserBrowseProduct::getLastTime);

            List<TzUserBrowseProduct> userBrowseProductList = userBrowseProductMapper.selectList(wrapper);
            map.put("time", sdf.format(u.getLastTime()));
            map.put("productList", userBrowseProductList);
            result.add(map);
        }
        return result;
    }
}
