package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.bo.VirtualOrderBo;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.domain.vo.VirtualOrderVo;
import com.ruoyi.mall.enums.VirtualTypeEnum;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 虚拟订单服务接口
 */
public interface IVirtualOrderService extends IService<VirtualOrder> {

    /**
     * 创建虚拟订单
     *
     * @param virtualOrderBo  订单业务对象
     * @param virtualTypeEnum 虚拟类型枚举
     */
    String createVirtualOrder(VirtualOrderBo virtualOrderBo, VirtualTypeEnum virtualTypeEnum);

    /**
     * 创建支付订单
     *
     * @param id      订单ID
     * @param orderNo 订单号
     * @param userId  用户ID
     * @param amount  金额
     * @param payType 支付类型
     */
    Map<String, String> createPayment(Long id, String orderNo, Long userId, BigDecimal amount, String payType);

    /**
     * 使用商家平台促销金支付虚拟订单
     *
     * @param id      订单ID
     * @param orderNo 订单号
     * @param userId  商家用户ID
     * @param amount  支付金额
     * @return 支付结果
     */
    Map<String, String> payWithShopDeduction(Long id, String orderNo, Long userId, BigDecimal amount);

    /**
     * 获取虚拟订单详情
     *
     * @param orderNo 订单ID
     * @return 订单视图对象
     */
    VirtualOrderVo getVirtualOrderDetail(String orderNo);

    /**
     * 取消虚拟订单
     *
     * @param orderId 订单ID
     * @param userId  用户ID
     * @return 是否成功
     */
    boolean cancelVirtualOrder(String orderId, Long userId);

    /**
     * 处理支付成功的订单
     *
     * @param payType 支付类类型
     * @param map     银行返回的东西
     * @return 是否处理成功
     */
    boolean handlePaymentSuccess(String payType, Map<String, String> map);

    Long getConsignmentCount(Long userId, Long shopId);

    TableDataInfo<VirtualOrder> getLoginVirtualOrder(VirtualOrder virtualOrder, PageQuery pageQuery);

    void queryOrderStatus(String paymentNo);

    boolean deleteVirtualOrder(String orderNo, Long userId);

    /**
     * 获取用户虚拟订单
     *
     * @param userId
     * @return
     */
    List<VirtualOrder> getUserVirtualOrderList(Long userId, PageQuery pageQuery);
}
