package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.ShopProductAttributeConfig;

public interface IShopProductAttributeConfigService  extends IService<ShopProductAttributeConfig> {
    /**
     * 新增商品属性配置
     *
     * @param shopProductAttributeConfig 商品属性配置
     * @return 结果
     */
    Integer insertShopProductAttributeConfig(ShopProductAttributeConfig shopProductAttributeConfig);

    /**
     * 查询商品属性配置列表
     * @param shopProductAttributeConfig
     * @param pageQuery
     * @return
     */
    TableDataInfo<ShopProductAttributeConfig> selectShopProductAttributeConfigList(ShopProductAttributeConfig shopProductAttributeConfig, PageQuery pageQuery);

    /**
     * 修改商品属性配置
     * @param productId
     * @param shopId
     * @param status
     * @return
     */
    Integer updateShopProductAttributeConfig(Long productId,Long  shopId,String status);
}
