package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.vo.ShopPromotionReqVO;
import com.ruoyi.mall.domain.vo.ShopPromotionRespVO;
import com.ruoyi.mall.domain.vo.ShopPromotionVO;
import com.ruoyi.mall.mapper.DeductionPaymentRecordMapper;
import com.ruoyi.mall.mapper.ShopMapper;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.mall.service.IUserPromotionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商家平台促销金Service实现
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class UserPromotionServiceImpl implements IUserPromotionService {


    private final IShopService shopService;
    private final DeductionPaymentRecordMapper deductionPaymentRecordMapper;
    private final ShopMapper shopMapper;


    @Override
    public TableDataInfo<ShopPromotionVO> queryUserPromotionList(ShopPromotionVO shopPromotion, PageQuery pageQuery) {

        // 查询商家信息
        LambdaQueryWrapper<Shop> shopWrapper = buildShopQueryWrapper(shopPromotion);
        Page<Shop> page = shopService.page(pageQuery.build(), shopWrapper);

        List<ShopPromotionVO> shopPromotionList = new ArrayList<>();

        if (!page.getRecords().isEmpty()) {
            // 获取商家ID列表
            List<Long> shopIds = page.getRecords().stream()
                .map(Shop::getUserId)
                .collect(Collectors.toList());

            // 查询商家促销记录
            Map<Long, BigDecimal> userUsedMap = getUserDeductionUsed(shopIds, 2);

            // 查询商家最近消费时间
            Map<Long, Date> userLastUsedTimeMap = getUserLastUsedTime(shopIds);

            // 组装数据
            for (Shop shop : page.getRecords()) {
                ShopPromotionVO vo = new ShopPromotionVO();
                vo.setUserId(shop.getUserId());
                vo.setName(shop.getName());
                vo.setPhone(shop.getPhone());
                vo.setCreateTime(shop.getCreateTime());
                vo.setIntroduction(shop.getIntroduction());

                // 平台促销金情况

                /*
                 * 已使用平台促销金 promotionUsed
                 */
                BigDecimal promotionUsed = userUsedMap.getOrDefault(shop.getUserId(), BigDecimal.ZERO);
                if (promotionUsed == null) {
                    promotionUsed = BigDecimal.ZERO; // 默认值
                }
                vo.setPromotionUsed(promotionUsed);


                /*
                 *  平台剩余促销金 platformPromotionGold
                 */
                LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Shop::getUserId, shop.getUserId());
                Shop dbShop = shopMapper.selectOne(queryWrapper);
                BigDecimal platformPromotionGold = dbShop.getPlatformPromotionGold();
                if (platformPromotionGold == null) {
                    platformPromotionGold = BigDecimal.ZERO; // 默认值
                }
                vo.setPlatformPromotionGold(platformPromotionGold);

                /*
                 * 平台抵促销总额 promotionTotal
                 */
                BigDecimal promotionTotal = promotionUsed.add(platformPromotionGold);
                vo.setPromotionTotal(promotionTotal);

                /*
                 * 最近使用时间 lastUsedTime
                 */
                vo.setLastUsedTime(userLastUsedTimeMap.get(shop.getUserId()));

                shopPromotionList.add(vo);
            }

        }


        TableDataInfo<ShopPromotionVO> rspData = new TableDataInfo<>();
        rspData.setCode(200);
        rspData.setMsg("查询成功");
        rspData.setRows(shopPromotionList);
        rspData.setTotal(page.getTotal());
        return rspData;

    }

    @Override
    public TableDataInfo<ShopPromotionRespVO> pageUserPromotionDetail(ShopPromotionReqVO shopPromotion, PageQuery pageQuery) {

        return TableDataInfo.build(deductionPaymentRecordMapper.selectUserPromotionDetailInfoPage(shopPromotion, 2, pageQuery.build()));

    }

    @Override
    public List<ShopPromotionRespVO> listUserPromotionDetail(ShopPromotionReqVO shopPromotion) {
        return deductionPaymentRecordMapper.selectUserPromotionDetailInfoList(shopPromotion, 2);
    }


    /**
     * 获取商家已使用平台促销金
     */
    private Map<Long, BigDecimal> getUserDeductionUsed(List<Long> userIds, Integer payType) {
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<Long, BigDecimal> resultMap = new HashMap<>();
        List<Map<String, Object>> resultList = deductionPaymentRecordMapper.selectUserDeductionUsed(userIds, payType);

        if (resultList != null && !resultList.isEmpty()) {
            for (Map<String, Object> map : resultList) {
                if (map.containsKey("key") && map.containsKey("value")) {
                    Long userId = Long.valueOf(map.get("key").toString());
                    BigDecimal usedAmount = new BigDecimal(map.get("value").toString());
                    resultMap.put(userId, usedAmount);
                }
            }
        }

        return resultMap;
    }


    /**
     * 获取商家最近使用平台促销金时间
     */
    private Map<Long, Date> getUserLastUsedTime(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<Long, Date> resultMap = new HashMap<>();
        List<Map<String, Object>> resultList = deductionPaymentRecordMapper.selectUserLastUsedTime(userIds);

        if (resultList != null && !resultList.isEmpty()) {
            for (Map<String, Object> map : resultList) {
                if (map.containsKey("key") && map.containsKey("value")) {
                    Long userId = Long.valueOf(map.get("key").toString());
                    Object timeValue = map.get("value");

                    // 处理不同类型的时间值
                    if (timeValue != null) {
                        Date lastUsedTime = null;
                        if (timeValue instanceof Date) {
                            lastUsedTime = (Date) timeValue;
                        } else if (timeValue instanceof java.time.LocalDateTime) {
                            java.time.LocalDateTime localDateTime = (java.time.LocalDateTime) timeValue;
                            lastUsedTime = java.util.Date.from(localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
                        } else {
                            try {
                                // 尝试解析字符串格式的时间
                                lastUsedTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(timeValue.toString());
                            } catch (Exception e) {
                                log.error("Failed to parse date value: {}", timeValue, e);
                            }
                        }

                        if (lastUsedTime != null) {
                            resultMap.put(userId, lastUsedTime);
                        }
                    }
                }
            }
        }

        return resultMap;
    }


    /**
     * 构建商家查询条件
     */
    private LambdaQueryWrapper<Shop> buildShopQueryWrapper(ShopPromotionVO shopPromotion) {
        LambdaQueryWrapper<Shop> lqw = Wrappers.lambdaQuery();
        lqw.eq(shopPromotion.getUserId() != null, Shop::getUserId, shopPromotion.getUserId());
        lqw.like(StringUtils.isNotBlank(shopPromotion.getName()), Shop::getName, shopPromotion.getName());
        lqw.like(StringUtils.isNotBlank(shopPromotion.getPhone()), Shop::getPhone, shopPromotion.getPhone());
        lqw.orderByDesc(Shop::getCreateTime);
        return lqw;
    }


}
