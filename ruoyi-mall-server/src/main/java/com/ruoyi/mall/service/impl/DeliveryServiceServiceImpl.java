package com.ruoyi.mall.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.mall.domain.bo.ConfigSettingBo;
import com.ruoyi.mall.domain.bo.ReceiveQuantizedBo;
import com.ruoyi.mall.domain.entity.QuantizationRate;
import com.ruoyi.mall.domain.vo.ConfigSettingVo;
import com.ruoyi.mall.mapper.QuantizationRateMapper;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.service.IQuantizationRateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service
@RequiredArgsConstructor
public class DeliveryServiceServiceImpl extends ServiceImpl<QuantizationRateMapper, QuantizationRate> implements IQuantizationRateService {

    private final static Integer index = 8;
    private final IConfigSettingService configSettingService;

    @Override
    public void receiveQuantizedData(ReceiveQuantizedBo receiveQuantizedBo) {
        QuantizationRate quantizationRate = new QuantizationRate();
        quantizationRate.setQuantifyDate(receiveQuantizedBo.getQuantifyDate());
        quantizationRate.setQuantifyRate(receiveQuantizedBo.getQuantifyRate());
        this.baseMapper.insert(quantizationRate);

        String key = ConfigSettingConstants.config.get(index);
        ConfigSettingBo configSetting = new ConfigSettingBo();
        configSetting.setConfigKey(ConfigSettingConstants.config.get(index));
        configSetting.setConfigName(ConfigSettingConstants.configName.get(index));
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("creditToCoupon", receiveQuantizedBo.getCreditToCoupon());
        jsonObject.set("quantifyToCredit", receiveQuantizedBo.getQuantifyToCredit());
        configSetting.setConfigValue(jsonObject);

        ConfigSettingVo configSettingVo = configSettingService.getConfigByKey(key);
        if (configSettingVo == null) {
            configSetting.setConfigName(ConfigSettingConstants.configName.get(index));
            configSettingService.saveConfig(configSetting);
        } else {
            configSettingService.updateConfig(configSetting);
        }
    }

    @Override
    public List<QuantizationRate> getQuantizationRate(Date searchMonth) {
        return this.baseMapper.getQuantizationRate(searchMonth);
    }

}
