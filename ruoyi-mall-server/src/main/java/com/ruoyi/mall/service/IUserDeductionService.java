package com.ruoyi.mall.service;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.vo.UserDeductionReqVO;
import com.ruoyi.mall.domain.vo.UserDeductionRespVO;
import com.ruoyi.mall.domain.vo.UserDeductionVO;

import java.util.List;

/**
 * 用户平台抵扣金Service接口
 */
public interface IUserDeductionService {

    /**
     * 查询用户平台抵扣金列表
     *
     * @param userDeduction 查询条件
     * @param pageQuery     分页参数
     * @return 用户平台抵扣金分页列表
     */
    TableDataInfo<UserDeductionVO> queryUserDeductionList(UserDeductionVO userDeduction, PageQuery pageQuery);

    /**
     * 查询用户每条平台抵扣金详细列表
     *
     * @param pageQuery 分页参数
     * @return 用户平台抵扣金分页列表
     */
    TableDataInfo<UserDeductionRespVO> pageUserDeductionDetail(UserDeductionReqVO userDeduction, PageQuery pageQuery);


    /**
     * 查询用户每条平台抵扣金详细列表(不分页)
     *
     * @return 用户平台抵扣金分页列表
     */
    List<UserDeductionRespVO> listUserDeductionDetail(UserDeductionReqVO userDeduction);


    /**
     * 查询用户平台抵扣金列表（不分页）
     *
     * @param userDeduction 查询条件
     * @return 用户平台抵扣金列表
     */
    List<UserDeductionVO> queryUserDeductionList(UserDeductionVO userDeduction);

    /**
     * 获取用户平台抵扣金详细信息
     *
     * @param userId 用户ID
     * @return 用户平台抵扣金信息
     */
    UserDeductionVO getUserDeductionInfo(Long userId);
}
