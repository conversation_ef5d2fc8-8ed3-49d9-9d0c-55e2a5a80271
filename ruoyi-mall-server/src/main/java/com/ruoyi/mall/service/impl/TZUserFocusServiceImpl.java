package com.ruoyi.mall.service.impl;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.TZUserFocus;
import com.ruoyi.mall.mapper.ShopMapper;
import com.ruoyi.mall.mapper.TZUserFocusMapper;
import com.ruoyi.mall.service.ITZUserFocusService;
import com.ruoyi.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class TZUserFocusServiceImpl extends MPJBaseServiceImpl<TZUserFocusMapper, TZUserFocus> implements ITZUserFocusService {

    @Autowired
    TZUserFocusMapper mapper;

    @Autowired
    ISysUserService sysUserService;
    @Autowired
    ShopMapper shopMapper;

    @Override
    public Integer saveUserFocus(Long shopId, Long userId, String status) {

        if (shopId == null) {
            throw new ServiceException("店铺ID不能为空");
        }

        if (userId == null) {
            throw new ServiceException("用户ID不能为空");
        }
        if (status == null) {
            throw new ServiceException("关注状态不能为空");
        }

        Shop shop = shopMapper.selectById(shopId);
        if (shop == null) {
            throw new ServiceException("店铺不存在");
        }
        Integer fans = shop.getFans();

        LambdaQueryWrapper<TZUserFocus> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .eq(TZUserFocus::getShopId, shopId)
            .eq(TZUserFocus::getUserId, userId);

        TZUserFocus tzUserFocus = getOne(wrapper);

        if (tzUserFocus != null) {
            if (status.equals("0")) {
                fans += 1;
            } else if (status.equals("1")) {
                fans -= 1;
                status = "0";
            }
            tzUserFocus.setStatus(status);
            mapper.updateById(tzUserFocus);
        } else {
            TZUserFocus userFocus = new TZUserFocus();
            userFocus.setCreateTime(new Date());
            userFocus.setUserId(userId);
            userFocus.setShopId(shopId);
            userFocus.setStatus("0");
            if (mapper.insert(userFocus) > 0) {
                fans += 1;
            }
        }

        LambdaUpdateWrapper<Shop> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .set(Shop::getFans, fans)
            .eq(Shop::getId, shopId);

        return shopMapper.update(updateWrapper);
    }


    @Override
    public List<TZUserFocus> getUserFocusList(Long userId, PageQuery pageQuery) {

        MPJLambdaWrapper<TZUserFocus> wrapper = new MPJLambdaWrapper<>();
        wrapper
            .selectAll(TZUserFocus.class)
            .selectAs(Shop::getName, TZUserFocus::getShopName)
            .selectAs(Shop::getLogo, TZUserFocus::getShopLogo)
            .leftJoin(Shop.class, Shop::getId, TZUserFocus::getShopId)
            .eq(Shop::getDelFlag, "0")
            .eq(TZUserFocus::getStatus, "0")
            .eq(TZUserFocus::getUserId, userId);
        Page<TZUserFocus> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        return page(page, wrapper).getRecords();
    }

    @Override
    public TableDataInfo<TZUserFocus> getUserFocusPage(TZUserFocus focus, PageQuery pageQuery) {
        MPJLambdaWrapper<TZUserFocus> wrapper = new MPJLambdaWrapper<>();
        wrapper
            .selectAll(TZUserFocus.class)
            .selectAs(Shop::getName, TZUserFocus::getShopName)
            .selectAs(Shop::getLogo, TZUserFocus::getShopLogo)
            .leftJoin(Shop.class, Shop::getId, TZUserFocus::getShopId)
            .eq(TZUserFocus::getUserId, focus.getUserId())

            .like(StrUtil.isNotBlank(focus.getShopName()), Shop::getName, focus.getShopName())
            .eq(StrUtil.isNotBlank(focus.getStatus()), TZUserFocus::getStatus, focus.getStatus())

            .orderByDesc(TZUserFocus::getCreateTime);
        return TableDataInfo.build(mapper.selectPage(pageQuery.build(), wrapper));
    }

    @Override
    public boolean cancelUserFocus(Long focusId) {
        try {
            // 获取当前登录的代销商家用户
            Long currentSysUserId = LoginHelper.getUserId();
            SysUser currentSysUser = sysUserService.getById(currentSysUserId);

            if (currentSysUser == null || currentSysUser.getTzUserId() == null) {
                log.error("当前用户不存在或未关联TZ用户，无法执行取消关注操作");
                return false;
            }

            // 更新关注状态为取消关注
            LambdaUpdateWrapper<TZUserFocus> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                .set(TZUserFocus::getStatus, "1") // 1表示取消关注
                .set(TZUserFocus::getUpdateTime, new Date())
                .eq(TZUserFocus::getId, focusId);

            return update(updateWrapper);

        } catch (Exception e) {
            log.error("取消用户关注操作异常", e);
            return false;
        }
    }

    @Override
    public String getUserIsFocus(Long shopId, Long userId) {
        LambdaQueryWrapper<TZUserFocus> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .eq(TZUserFocus::getShopId, shopId)
            .eq(TZUserFocus::getUserId, userId)
            .orderByDesc(TZUserFocus::getCreateTime)
            .last("limit 1")
        ;
        TZUserFocus userFocus = getOne(wrapper);
        return userFocus == null ? "1" : userFocus.getStatus();
    }
}
