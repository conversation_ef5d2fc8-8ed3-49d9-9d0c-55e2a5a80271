package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.ShopFreezeRecord;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 店铺支付服务接口
 */
public interface IShopPaymentService extends IService<ShopFreezeRecord> {

    /**
     * 根据电话号码查询店铺信息
     *
     * @param phone 店铺电话
     * @return 店铺信息
     */
    Map<String, Object> getShopByPhone(String phone);

    /**
     * 获取店铺货款冻结记录
     *
     * @return 冻结记录列表
     */
    TableDataInfo<ShopFreezeRecord> getShopFreezeRecords(PageQuery pageQuery, Long shopId, String shopName, String shopPhone, String status);

    /**
     * 冻结店铺货款
     *
     * @param shopId 店铺ID
     * @param amount 金额
     * @param reason 原因
     * @return 结果
     */
    Integer freezeShopAmount(Long shopId, BigDecimal amount, String reason);

    /**
     * 解冻店铺货款
     *
     * @param freezeId 冻结记录ID
     * @return 结果
     */
    Integer unfreezeShopAmount(Long freezeId);
}
