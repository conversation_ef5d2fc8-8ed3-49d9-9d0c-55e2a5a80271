package com.ruoyi.mall.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.UserDeductionMoneyLimitRecord;
import com.ruoyi.mall.mapper.TzUserDeductionMoneyLimitRecordMapper;
import com.ruoyi.mall.service.ITzUserDeductionMoneyLimitRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ITzUserDeductionMoneyLimitRecordServiceImpl extends ServiceImpl<TzUserDeductionMoneyLimitRecordMapper, UserDeductionMoneyLimitRecord> implements ITzUserDeductionMoneyLimitRecordService {
    @Override
    public Boolean insertTzUserDeductionMoneyLimitRecord(UserDeductionMoneyLimitRecord tzUserDeductionMoneyLimitRecord) {
        return save(tzUserDeductionMoneyLimitRecord);
    }

    @Override
    public TableDataInfo<UserDeductionMoneyLimitRecord> selectTzUserDeductionMoneyLimitRecordPage(UserDeductionMoneyLimitRecord tzUserDeductionMoneyLimitRecord, PageQuery pageQuery) {

        LambdaQueryWrapper<UserDeductionMoneyLimitRecord> lqw = new LambdaQueryWrapper<>();
        lqw
            .eq(StringUtils.isNotEmpty(tzUserDeductionMoneyLimitRecord.getUserId().toString()), UserDeductionMoneyLimitRecord::getUserId, tzUserDeductionMoneyLimitRecord.getUserId())
            .orderByDesc(UserDeductionMoneyLimitRecord::getCreateTime);

        return TableDataInfo.build(page(pageQuery.build(), lqw));
    }
}
