package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.entity.UserShopBrowse;
import com.ruoyi.mall.mapper.UserShopBrowseMapper;
import com.ruoyi.mall.service.IUserShopBrowseService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UserShopBrowseServiceImpl extends MPJBaseServiceImpl<UserShopBrowseMapper, UserShopBrowse> implements IUserShopBrowseService {


    @Autowired
    private UserShopBrowseMapper userShopBrowseMapper;

    @Override
    public TableDataInfo<UserShopBrowse> getShopUserBrowseList(Long shopId, PageQuery pageQuery) {

        MPJLambdaWrapper<UserShopBrowse> wrapper = new MPJLambdaWrapper<>();
        wrapper
            .selectAll(UserShopBrowse.class)
            .selectAs(TzUser::getNickname, UserShopBrowse::getUserName)
            .selectAs(TzUser::getAvatar, UserShopBrowse::getUserAvatar)
            .leftJoin(TzUser.class, TzUser::getUserId, UserShopBrowse::getUserId)
            .eq(UserShopBrowse::getShopId, shopId);

        TableDataInfo<UserShopBrowse> tableDataInfo = TableDataInfo.build(selectJoinListPage(pageQuery.build(), UserShopBrowse.class, wrapper));

        return tableDataInfo;
    }

    @Override
    public Integer saveUserShopBrowse(UserShopBrowse userShopBrowse) {
        return userShopBrowseMapper.saveUserShopBrowse(userShopBrowse);
    }

    @Override
    public Integer findUserShopBrowse(Long userId, Long shopId) {
        LambdaQueryWrapper<UserShopBrowse> userMapper = new LambdaQueryWrapper<>();
        userMapper
            .eq(UserShopBrowse::getUserId, userId)
            .eq(UserShopBrowse::getShopId, shopId);

        return userShopBrowseMapper.selectCount(userMapper).intValue();
    }

    @Override
    public Integer todayUserShopBrowseIsClick(Long userId, Long shopId) {
        LambdaQueryWrapper<UserShopBrowse> userMapper = new LambdaQueryWrapper<>();
        userMapper
            .eq(UserShopBrowse::getUserId, userId)
            .eq(UserShopBrowse::getShopId, shopId)
            .last(" and  DATE(create_time)= CURDATE() ");

        return userShopBrowseMapper.selectCount(userMapper).intValue();
    }

}

