package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.OrderNoGenerator;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.dto.SettlementRecordDTO;
import com.ruoyi.mall.domain.dto.SettlementStatsDTO;
import com.ruoyi.mall.domain.entity.ShopSettlement;
import com.ruoyi.mall.domain.entity.ShopWallet;
import com.ruoyi.mall.domain.vo.SettlementRecordVO;
import com.ruoyi.mall.domain.vo.SettlementStatsVO;
import com.ruoyi.mall.enums.TradeMethodEnum;
import com.ruoyi.mall.mapper.ShopSettlementMapper;
import com.ruoyi.mall.service.IShopSettlementService;
import com.ruoyi.mall.service.IShopWalletService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * 商家结算单服务实现类
 */
@RequiredArgsConstructor
@Service
public class ShopSettlementServiceImpl extends ServiceImpl<ShopSettlementMapper, ShopSettlement> implements IShopSettlementService {

    private final ShopSettlementMapper shopSettlementMapper;
    private final IShopWalletService shopWalletService;

    /**
     * 查询商家结算单列表
     *
     * @param shopSettlement 商家结算单
     * @param pageQuery      分页对象
     * @return 商家结算单分页数据
     */
    @Override
    public TableDataInfo<ShopSettlement> queryPageList(ShopSettlement shopSettlement, PageQuery pageQuery) {
        LambdaQueryWrapper<ShopSettlement> lqw = buildQueryWrapper(shopSettlement);
        Page<ShopSettlement> page = shopSettlementMapper.selectPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<ShopSettlement> buildQueryWrapper(ShopSettlement shopSettlement) {
        LambdaQueryWrapper<ShopSettlement> lqw = Wrappers.lambdaQuery();
        lqw.eq(shopSettlement.getShopId() != null, ShopSettlement::getShopId, shopSettlement.getShopId());
        lqw.like(StringUtils.isNotBlank(shopSettlement.getSettlementNo()), ShopSettlement::getSettlementNo, shopSettlement.getSettlementNo());
        lqw.eq(StringUtils.isNotBlank(shopSettlement.getStatus()), ShopSettlement::getStatus, shopSettlement.getStatus());
        lqw.orderByDesc(ShopSettlement::getCreateTime);
        return lqw;
    }

    /**
     * 获取商家结算单详细信息
     *
     * @param id 结算单ID
     * @return 商家结算单
     */
    @Override
    public ShopSettlement getById(Long id) {
        return shopSettlementMapper.selectById(id);
    }

    /**
     * 获取商家结算单详细信息
     *
     * @param settlementNo 结算单号
     * @return 商家结算单
     */
    @Override
    public ShopSettlement getBySettlementNo(String settlementNo) {
        LambdaQueryWrapper<ShopSettlement> lqw = Wrappers.lambdaQuery();
        lqw.eq(ShopSettlement::getSettlementNo, settlementNo);
        return shopSettlementMapper.selectOne(lqw);
    }

    /**
     * 商家申请结算
     *
     * @param shopId         商家ID
     * @param amount         申请结算金额
     * @param accountName    收款账户名
     * @param accountNo      收款账号
     * @param bankName       银行名称
     * @param bankBranchName 开户行
     * @param contactPhone   联系电话
     * @return 结算单
     */
    @Override
    @Transactional
    public ShopSettlement applySettlement(Long shopId, BigDecimal amount, String accountName,
                                          String accountNo, String bankName, String bankBranchName, String contactPhone) {
        // 校验申请金额
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("申请结算金额必须大于0");
        }

        // 获取商家钱包
        ShopWallet wallet = shopWalletService.getByShopId(shopId);
        if (wallet == null) {
            throw new ServiceException("商家钱包不存在");
        }

        // 校验钱包余额
        if (wallet.getBalance().subtract(wallet.getFrozenAmount()).compareTo(amount) < 0) {
            throw new ServiceException("钱包余额不足，无法申请结算");
        }

        // 冻结申请结算的金额
        BigDecimal frozenAmount = wallet.getFrozenAmount().add(amount);
        wallet.setFrozenAmount(frozenAmount);
        boolean updateResult = shopWalletService.updateById(wallet);
        if (!updateResult) {
            throw new ServiceException("冻结资金失败，请稍后重试");
        }

        // 生成结算单
        ShopSettlement settlement = new ShopSettlement();
        settlement.setSettlementNo(generateSettlementNo());
        settlement.setShopId(shopId);
        settlement.setWalletId(wallet.getId());
        settlement.setAmount(amount);
        settlement.setActualAmount(amount);
        settlement.setFee(BigDecimal.ZERO);
        settlement.setAccountName(accountName);
        settlement.setAccountNo(accountNo);
        settlement.setBankName(bankName);
        settlement.setBankBranchName(bankBranchName);
        settlement.setContactPhone(contactPhone);
        settlement.setStatus("0"); // 待审核

        shopSettlementMapper.insert(settlement);

        return settlement;
    }

    /**
     * 生成结算单号
     */
    private String generateSettlementNo() {
        return OrderNoGenerator.generateOrderNo("ST");
    }

    /**
     * 审核结算单
     *
     * @param id          结算单ID
     * @param status      审核状态（1通过 2拒绝）
     * @param auditorId   审核人ID
     * @param auditorName 审核人名称
     * @param auditRemark 审核意见
     * @return 结果
     */
    @Override
    @Transactional
    public boolean auditSettlement(Long id, String status, Long auditorId, String auditorName, String auditRemark) {
        // 获取结算单
        ShopSettlement settlement = getById(id);
        if (settlement == null) {
            throw new ServiceException("结算单不存在");
        }

        // 校验结算单状态
        if (!"0".equals(settlement.getStatus())) {
            throw new ServiceException("结算单已审核，不能重复审核");
        }

        // 更新结算单状态
        ShopSettlement updateSettlement = new ShopSettlement();
        updateSettlement.setId(id);
        updateSettlement.setStatus(status);
        updateSettlement.setAuditorId(auditorId);
        updateSettlement.setAuditorName(auditorName);
        updateSettlement.setAuditTime(new Date());
        updateSettlement.setAuditRemark(auditRemark);

        boolean result = shopSettlementMapper.updateById(updateSettlement) > 0;

        // 如果审核拒绝，解冻资金
        if (result && "2".equals(status)) {
            ShopWallet wallet = shopWalletService.getById(settlement.getWalletId());
            if (wallet != null) {
                wallet.setFrozenAmount(wallet.getFrozenAmount().subtract(settlement.getAmount()));
                shopWalletService.updateById(wallet);
            }
        }

        return result;
    }

    /**
     * 确认打款
     *
     * @param id        结算单ID
     * @param payerId   打款人ID
     * @param payerName 打款人名称
     * @param payProof  打款凭证
     * @param payRemark 打款备注
     * @return 结果
     */
    @Override
    @Transactional
    public boolean confirmPayment(Long id, Long payerId, String payerName, String payProof, String payRemark) {
        // 获取结算单
        ShopSettlement settlement = getById(id);
        if (settlement == null) {
            throw new ServiceException("结算单不存在");
        }

        // 校验结算单状态
        if (!"1".equals(settlement.getStatus())) {
            throw new ServiceException("结算单未审核通过，不能确认打款");
        }

        // 更新结算单状态
        ShopSettlement updateSettlement = new ShopSettlement();
        updateSettlement.setId(id);
        updateSettlement.setStatus("3"); // 已打款
        updateSettlement.setPayerId(payerId);
        updateSettlement.setPayerName(payerName);
        updateSettlement.setPayTime(new Date());
        updateSettlement.setPayProof(payProof);
        updateSettlement.setPayRemark(payRemark);

        return shopSettlementMapper.updateById(updateSettlement) > 0;
    }

    /**
     * 完成结算
     *
     * @param id 结算单ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean completeSettlement(Long id) {
        // 获取结算单
        ShopSettlement settlement = getById(id);
        if (settlement == null) {
            throw new ServiceException("结算单不存在");
        }

        // 校验结算单状态
        if (!"3".equals(settlement.getStatus())) {
            throw new ServiceException("结算单未确认打款，不能完成结算");
        }

        // 更新结算单状态
        ShopSettlement updateSettlement = new ShopSettlement();
        updateSettlement.setId(id);
        updateSettlement.setStatus("4"); // 已完成

        boolean result = shopSettlementMapper.updateById(updateSettlement) > 0;

        // 解冻资金并记录支出
        if (result) {
            ShopWallet wallet = shopWalletService.getById(settlement.getWalletId());
            if (wallet != null) {
                wallet.setFrozenAmount(wallet.getFrozenAmount().subtract(settlement.getAmount()));
                shopWalletService.updateById(wallet);

                // 记录钱包交易记录
                shopWalletService.expense(
                    settlement.getShopId(),
                    settlement.getAmount(),
                    TradeMethodEnum.WITHDRAWAL, // 提现
                    settlement.getSettlementNo(),
                    "商家结算提现"
                );
            }
        }

        return result;
    }

    /**
     * 取消结算单
     *
     * @param id     结算单ID
     * @param remark 取消原因
     * @return 结果
     */
    @Override
    @Transactional
    public boolean cancelSettlement(Long id, String remark) {
        // 获取结算单
        ShopSettlement settlement = getById(id);
        if (settlement == null) {
            throw new ServiceException("结算单不存在");
        }

        // 校验结算单状态，只有待审核和审核通过的结算单可以取消
        if (!"0".equals(settlement.getStatus()) && !"1".equals(settlement.getStatus())) {
            throw new ServiceException("结算单状态不允许取消");
        }

        // 更新结算单状态
        LambdaUpdateWrapper<ShopSettlement> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(ShopSettlement::getId, id);
        updateWrapper.set(ShopSettlement::getStatus, "5");
        updateWrapper.set(ShopSettlement::getAuditRemark, remark);

        boolean result = shopSettlementMapper.update(null, updateWrapper) > 0;

        // 解冻资金
        if (result) {
            ShopWallet wallet = shopWalletService.getById(settlement.getWalletId());
            if (wallet != null) {
                wallet.setFrozenAmount(wallet.getFrozenAmount().subtract(settlement.getAmount()));
//                wallet.setBalance(wallet.getBalance().add(settlement.getAmount()));
                shopWalletService.updateById(wallet);
            }
        }

        return result;
    }


    /**
     * 结算统计
     *
     * @param dto       请求dto
     * @param pageQuery 分页对象
     * @return TableDataInfo<SettlementStatsVO>
     * <AUTHOR>
     */
    @Override
    public TableDataInfo<SettlementStatsVO> pageSettlementStats(SettlementStatsDTO dto, PageQuery pageQuery) {
//        Page<SettlementStatsVO> settlementStatsVOPage = shopSettlementMapper.selectListSettlementStatsByPage(dto, pageQuery.build());

        List<SettlementStatsVO> rawData = shopSettlementMapper.selectSettlementStatsList(dto);

        // Step 1: 按 shopId + date 分组，并计算每个商家每天的 bankCardActualAmount 总和
        Map<String, BigDecimal> dailySumMap = new HashMap<>();
        Map<String, SettlementStatsVO> latestRecordMap = new HashMap<>();

        for (SettlementStatsVO record : rawData) {
            LocalDate date = record.getSettlementDate().toLocalDate();
            String key = record.getId() + "_" + date;

            // 累加每天的金额
            dailySumMap.merge(key, record.getBankCardActualAmount(), BigDecimal::add);

            // 更新最新一条记录
            if (!latestRecordMap.containsKey(key) || record.getSettlementDate().isAfter(latestRecordMap.get(key).getSettlementDate())) {
                latestRecordMap.put(key, record);
            }
        }

        // Step 2: 构建最终结果
        List<SettlementStatsVO> result = new ArrayList<>();
        for (Map.Entry<String, SettlementStatsVO> entry : latestRecordMap.entrySet()) {
            String key = entry.getKey();
            SettlementStatsVO record = entry.getValue();
            BigDecimal sumAmount = dailySumMap.get(key);
            result.add(new SettlementStatsVO(
                record.getId(),
                record.getSettlementDate(),
                record.getShopName(),
                record.getPhone(),
                sumAmount,
                record.getTotalAmount()
            ));
        }

        // Step 3: 按照结算日期倒序排序
        result.sort((a, b) -> b.getSettlementDate().compareTo(a.getSettlementDate()));


//        return TableDataInfo.build(settlementStatsVOPage);

        // Step 4: 内存分页
        int pageNum = pageQuery.getPageNum();
        int pageSize = pageQuery.getPageSize();
        int start = (pageNum - 1) * pageSize;
        int end = Math.min(pageNum * pageSize, result.size());

        List<SettlementStatsVO> pagedList = new ArrayList<>();
        if (start < result.size()) {
            pagedList = result.subList(start, end);
        }

        // 构造新的 Page 对象用于返回
        Page<SettlementStatsVO> finalPage = new Page<>(pageNum, pageSize, result.size());
        finalPage.setRecords(pagedList);

        return TableDataInfo.build(finalPage);

    }

    /**
     * 结算统计 （不分页）
     *
     * @param dto 请求dto
     * @return List<SettlementStatsVO>
     * <AUTHOR>
     */
    @Override
    public List<SettlementStatsVO> listSettlementStats(SettlementStatsDTO dto) {
//        List<SettlementStatsVO> settlementStatsVOS = shopSettlementMapper.selectSettlementStatsList(dto);
//        return settlementStatsVOS;
        List<SettlementStatsVO> rawData = shopSettlementMapper.selectSettlementStatsList(dto);

        // Step 1: 按 shopId + date 分组，并计算每个商家每天的 bankCardActualAmount 总和
        Map<String, BigDecimal> dailySumMap = new HashMap<>();
        Map<String, SettlementStatsVO> latestRecordMap = new HashMap<>();

        for (SettlementStatsVO record : rawData) {
            LocalDate date = record.getSettlementDate().toLocalDate();
            String key = record.getId() + "_" + date;

            // 累加每天的金额
            dailySumMap.merge(key, record.getBankCardActualAmount(), BigDecimal::add);

            // 更新最新一条记录
            if (!latestRecordMap.containsKey(key) || record.getSettlementDate().isAfter(latestRecordMap.get(key).getSettlementDate())) {
                latestRecordMap.put(key, record);
            }
        }

        // Step 2: 构建最终结果
        List<SettlementStatsVO> result = new ArrayList<>();
        for (Map.Entry<String, SettlementStatsVO> entry : latestRecordMap.entrySet()) {
            String key = entry.getKey();
            SettlementStatsVO record = entry.getValue();
            BigDecimal sumAmount = dailySumMap.get(key);
            result.add(new SettlementStatsVO(
                record.getId(),
                record.getSettlementDate(),
                record.getShopName(),
                record.getPhone(),
                sumAmount,
                record.getTotalAmount()
            ));
        }

        // Step 3: 按照结算日期倒序排序
        result.sort((a, b) -> b.getSettlementDate().compareTo(a.getSettlementDate()));
        return result;
    }

    /**
     * 结算记录列表 （分页）
     *
     * @param dto       请求dto
     * @param pageQuery 分页对象
     * @return TableDataInfo<SettlementRecordVO>
     * <AUTHOR>
     */
    @Override
    public TableDataInfo<SettlementRecordVO> pageSettlementRecord(SettlementRecordDTO dto, PageQuery pageQuery) {
        IPage<SettlementRecordVO> settlementRecordVOIPage = shopSettlementMapper.selectSettlementRecordPage(dto, pageQuery.build());
        return TableDataInfo.build(settlementRecordVOIPage);
    }


    /**
     * 结算记录列表 （不分页）
     *
     * @param dto 请求dto
     * @return List<SettlementRecordVO>
     * <AUTHOR>
     */
    @Override
    public List<SettlementRecordVO> listSettlementRecord(SettlementRecordDTO dto) {
        List<SettlementRecordVO> settlementRecordVOS = shopSettlementMapper.selectSettlementRecordList(dto);
        return settlementRecordVOS;
    }
}
