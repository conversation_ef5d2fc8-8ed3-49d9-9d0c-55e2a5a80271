package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.ShopWallet;
import com.ruoyi.mall.enums.TradeMethodEnum;

import java.math.BigDecimal;

/**
 * 商家钱包服务接口
 */
public interface IShopWalletService extends IService<ShopWallet> {

    /**
     * 查询商家钱包列表
     *
     * @param shopWallet 商家钱包
     * @param pageQuery  分页对象
     * @return 商家钱包分页数据
     */
    TableDataInfo<ShopWallet> queryPageList(ShopWallet shopWallet, PageQuery pageQuery);

    /**
     * 获取商家钱包详细信息
     *
     * @param id 钱包ID
     * @return 商家钱包
     */
    ShopWallet getById(Long id);

    /**
     * 获取商家钱包详细信息
     *
     * @param shopId 商家ID
     * @return 商家钱包
     */
    ShopWallet getByShopId(Long shopId);

    /**
     * 新增商家钱包
     *
     * @param shopWallet 商家钱包
     * @return 结果
     */
    boolean save(ShopWallet shopWallet);

    /**
     * 修改商家钱包
     *
     * @param shopWallet 商家钱包
     * @return 结果
     */
    boolean updateById(ShopWallet shopWallet);

    /**
     * 商家钱包入账
     *
     * @param shopId      商家ID
     * @param amount      入账金额
     * @param tradeMethod 交易方式
     * @param orderNo     关联订单号
     * @param remark      备注
     * @return 结果
     */
    boolean income(Long shopId, BigDecimal amount, TradeMethodEnum tradeMethod, String orderNo, String remark);

    /**
     * 商家钱包出账
     *
     * @param shopId      商家ID
     * @param amount      出账金额
     * @param tradeMethod 交易方式
     * @param orderNo     关联订单号
     * @param remark      备注
     * @return 结果
     */
    boolean expense(Long shopId, BigDecimal amount, TradeMethodEnum tradeMethod, String orderNo, String remark);

    /**
     * 创建商家钱包
     *
     * @param shopId 商家ID
     * @return 商家钱包
     */
    ShopWallet createWallet(Long shopId);

    /**
     * 处理订单退款（全额退款给用户，并额外扣除商家0.6%手续费）
     *
     * @param shopId  商家ID
     * @param orderNo 订单号
     * @param amount  退款金额（全额退给用户）
     * @param remark  备注信息
     * @return 结果
     */
    boolean processRefund(Long shopId, String orderNo, BigDecimal amount, String remark);

    /**
     * 处理用户承担手续费的退款
     *
     * @param shopId    商家ID
     * @param orderNo   订单号
     * @param amount    实际退款金额（已扣除手续费）
     * @param feeAmount 手续费金额
     * @param remark    备注信息
     * @return 结果
     */
    boolean processRefundWithUserFee(Long shopId, String orderNo, BigDecimal amount,
                                     BigDecimal feeAmount, String remark);
}
