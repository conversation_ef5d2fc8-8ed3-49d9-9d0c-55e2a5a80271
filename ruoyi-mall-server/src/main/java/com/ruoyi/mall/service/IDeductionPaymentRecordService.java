package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.DeductionPaymentRecord;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 抵扣金支付记录Service接口
 */
public interface IDeductionPaymentRecordService extends IService<DeductionPaymentRecord> {

    /**
     * 查询抵扣金支付记录列表
     *
     * @param deductionPaymentRecord 抵扣金支付记录
     * @param pageQuery              分页参数
     * @return 抵扣金支付记录分页列表
     */
    TableDataInfo<DeductionPaymentRecord> queryPageList(DeductionPaymentRecord deductionPaymentRecord, PageQuery pageQuery);

    /**
     * 记录抵扣金支付
     *
     * @param userId    用户ID
     * @param paymentNo 支付单号
     * @param orderNo   业务订单编号
     * @param payAmount 支付金额
     * @param feeAmount 手续费
     * @param payTime   支付时间
     * @param status    支付状态
     * @param payType   支付类型：1-用户抵扣金支付，2-商家平台兑换金支付
     * @return 是否记录成功
     */
    boolean recordDeductionPayment(Long userId, String paymentNo, String orderNo,
                                   BigDecimal payAmount, BigDecimal feeAmount,
                                   Date payTime, String status, String payType);
}
