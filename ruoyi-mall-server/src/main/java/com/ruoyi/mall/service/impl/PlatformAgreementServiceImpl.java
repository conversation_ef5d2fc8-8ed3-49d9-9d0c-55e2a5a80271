package com.ruoyi.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.bo.PlatformAgreementBo;
import com.ruoyi.mall.domain.entity.PlatformAgreement;
import com.ruoyi.mall.mapper.PlatformAgreementMapper;
import com.ruoyi.mall.service.IPlatformAgreementService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 平台协议配置Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class PlatformAgreementServiceImpl extends ServiceImpl<PlatformAgreementMapper, PlatformAgreement> implements IPlatformAgreementService {

    private final PlatformAgreementMapper baseMapper;

    /**
     * 查询平台协议配置
     */
    @Override
    public PlatformAgreement queryById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询平台协议配置列表
     */
    @Override
    public TableDataInfo<PlatformAgreement> queryPageList(PlatformAgreementBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PlatformAgreement> lqw = buildQueryWrapper(bo);
        lqw.orderByDesc(PlatformAgreement::getCreateTime);
        return TableDataInfo.build(baseMapper.selectPage(pageQuery.build(), lqw));
    }

    /**
     * 查询平台协议配置列表
     */
    @Override
    public List<PlatformAgreement> queryList(PlatformAgreementBo bo) {
        LambdaQueryWrapper<PlatformAgreement> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    private LambdaQueryWrapper<PlatformAgreement> buildQueryWrapper(PlatformAgreementBo bo) {
        LambdaQueryWrapper<PlatformAgreement> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), PlatformAgreement::getName, bo.getName());
        return lqw;
    }

    /**
     * 新增平台协议配置
     */
    @Override
    public Boolean insertByBo(PlatformAgreementBo bo) {
        PlatformAgreement add = BeanUtil.toBean(bo, PlatformAgreement.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改平台协议配置
     */
    @Override
    public Boolean updateByBo(PlatformAgreementBo bo) {
        PlatformAgreement update = BeanUtil.toBean(bo, PlatformAgreement.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PlatformAgreement entity) {
        // 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除平台协议配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
