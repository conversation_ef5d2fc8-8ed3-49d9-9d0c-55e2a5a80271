package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.UserDeductionMoneyLimitRecord;

public interface ITzUserDeductionMoneyLimitRecordService extends IService<UserDeductionMoneyLimitRecord> {

    /**
     * 新增用户扣除金额限制记录
     *
     * @param tzUserDeductionMoneyLimitRecord
     * @return
     */
    Boolean insertTzUserDeductionMoneyLimitRecord(UserDeductionMoneyLimitRecord tzUserDeductionMoneyLimitRecord);

    /**
     * 查询用户扣除金额限制记录列表
     *
     * @param tzUserDeductionMoneyLimitRecord
     * @param pageQuery
     * @return
     */
    TableDataInfo<UserDeductionMoneyLimitRecord> selectTzUserDeductionMoneyLimitRecordPage(UserDeductionMoneyLimitRecord tzUserDeductionMoneyLimitRecord, PageQuery pageQuery);


}
