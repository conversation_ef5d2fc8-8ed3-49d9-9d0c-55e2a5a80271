package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.ProductEvaluate;
import com.ruoyi.mall.domain.vo.EvaluateVo;

public interface IProductEvaluateService extends IService<ProductEvaluate> {

    /**
     * 查询产品的评价
     *
     * @param productId 商品id
     */
    TableDataInfo<EvaluateVo> selProductEvaluate(Long productId, Integer type, PageQuery pageQuery);

    /**
     * 审核评价
     *
     * @param id     评价id
     * @param status 0：审核中，1:审核通过，2：不通过
     */
    Integer ExamineEvaluate(Long id, String status);

    /**
     * 新增商品评价
     */
    Integer saveProductEvaluate(ProductEvaluate productEvaluate);

    /**
     * 根据评价id查询评价详情
     */
    ProductEvaluate getProductEvaluateById(Long id);

    /**
     * 批量删除
     */
    Integer deleteProductEvaluateIds(Long[] ids);

    /**
     * 根据id删除评价
     */
    Integer deleteProductEvaluateId(Long id);

    /**
     * 根据商品项获取评价详情
     *
     * @param itemId
     * @return
     */
    ProductEvaluate getProductEvaluateByItemId(Long itemId);

    /**
     * 商家回复评价
     *
     * @param id    评价ID
     * @param reply 回复内容
     * @return 操作结果
     */
    Integer replyEvaluate(Long id, String reply);

    /**
     * 修改商品评价
     *
     * @param productEvaluate
     * @return
     */
    Integer editProductEvaluate(ProductEvaluate productEvaluate);
}
