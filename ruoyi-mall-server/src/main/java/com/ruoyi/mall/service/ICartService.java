package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.CartListDTO;
import com.ruoyi.mall.domain.dto.OrderPreviewDTO;
import com.ruoyi.mall.domain.entity.Cart;
import com.ruoyi.mall.domain.query.CartQueryParamDTO;
import com.ruoyi.mall.domain.vo.CartInfoVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车Service接口
 */
public interface ICartService extends IService<Cart> {
    /**
     * 查询购物车列表
     */
    List<Cart> selectCartList(Cart cart);

    /**
     * 查询购物车分页列表
     */
    TableDataInfo<CartInfoVo> selectCartPage(CartQueryParamDTO cart, PageQuery pageQuery);

    /**
     * 查询购物车详细信息
     */
    Cart selectCartById(Long id);

    /**
     * 添加购物车
     */
    boolean addCart(Cart cart);

    /**
     * 修改购物车
     */
    boolean updateCart(Cart cart);

    /**
     * 删除购物车
     */
    boolean deleteCartById(Long id);

    /**
     * 批量删除购物车
     */
    boolean deleteCartByIds(Long[] ids);

    /**
     * 清空购物车
     */
    boolean clearCart(Long userId);

    /**
     * 修改购物车商品选中状态
     */
    boolean updateCartSelected(Long id, String selected);

    /**
     * 批量修改购物车商品选中状态
     */
    boolean updateCartSelectedBatch(Long[] ids, String selected);

    /**
     * 按店铺分组查询购物车列表
     *
     * @return 按店铺分组的购物车列表
     * @ param userId 用户ID
     */
    List<CartListDTO> selectCartListByShop(Long userId);

    /**
     * 购物车全选/取消全选
     *
     * @param userId   用户ID
     * @param selected 0未选中，1：选中
     * @return
     */
    Boolean CartAllSelected(Long userId, String selected);

    /**
     * 修改购物车数量
     *
     * @param cartId
     * @param quantity
     * @return
     */
    BigDecimal updateCartQuantity(Long cartId, Integer quantity);

    /**
     * 获取订单预览信息
     *
     * @param cartIds
     * @return
     */
    OrderPreviewDTO getOrderPreview(List<Long> cartIds);


}
