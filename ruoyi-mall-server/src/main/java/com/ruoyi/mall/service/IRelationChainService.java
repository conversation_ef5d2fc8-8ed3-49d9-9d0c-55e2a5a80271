package com.ruoyi.mall.service;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.RelationChainQueryParam;
import com.ruoyi.mall.domain.vo.RelationChainVO;
import com.ruoyi.mall.domain.vo.UserStatusJurisdictionVO;

/**
 * 关系链服务接口
 */
public interface IRelationChainService {

    /**
     * 获取关系链列表
     *
     * @param queryParam 查询参数
     * @return 关系链列表
     */
    TableDataInfo<RelationChainVO> getRelationChainList(PageQuery pageQuery, RelationChainQueryParam queryParam);

    /**
     * 修改用户状态
     *
     * @param userId 用户ID（手机号）
     * @param status 状态
     * @return 结果
     */
    int changeUserStatus(Long userId, Integer status);


    /**
     * 获取用户状态和权限
     *
     * @param
     * @return UserStatusJurisdictionVO
     * <AUTHOR>
     */
    UserStatusJurisdictionVO getStatusAndJurisdiction();

}
