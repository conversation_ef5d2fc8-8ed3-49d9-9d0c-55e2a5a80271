package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.DeductionPaymentRecord;
import com.ruoyi.mall.mapper.DeductionPaymentRecordMapper;
import com.ruoyi.mall.service.IDeductionPaymentRecordService;
import com.ruoyi.pay.enums.DeductionPaymentStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付记录Service实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeductionPaymentRecordServiceImpl extends ServiceImpl<DeductionPaymentRecordMapper, DeductionPaymentRecord>
    implements IDeductionPaymentRecordService, com.ruoyi.pay.service.IDeductionPaymentRecordService {

    @Override
    public TableDataInfo<DeductionPaymentRecord> queryPageList(DeductionPaymentRecord deductionPaymentRecord, PageQuery pageQuery) {
        LambdaQueryWrapper<DeductionPaymentRecord> lqw = buildQueryWrapper(deductionPaymentRecord);
        Page<DeductionPaymentRecord> page = page(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    private LambdaQueryWrapper<DeductionPaymentRecord> buildQueryWrapper(DeductionPaymentRecord deductionPaymentRecord) {
        LambdaQueryWrapper<DeductionPaymentRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(deductionPaymentRecord.getUserId() != null, DeductionPaymentRecord::getUserId, deductionPaymentRecord.getUserId());
        lqw.eq(StringUtils.isNotBlank(deductionPaymentRecord.getPaymentNo()), DeductionPaymentRecord::getPaymentNo, deductionPaymentRecord.getPaymentNo());
        lqw.eq(StringUtils.isNotBlank(deductionPaymentRecord.getOrderNo()), DeductionPaymentRecord::getOrderNo, deductionPaymentRecord.getOrderNo());
        lqw.eq(StringUtils.isNotBlank(deductionPaymentRecord.getStatus()), DeductionPaymentRecord::getStatus, deductionPaymentRecord.getStatus());
        lqw.eq(StringUtils.isNotBlank(deductionPaymentRecord.getPayType()), DeductionPaymentRecord::getPayType, deductionPaymentRecord.getPayType());
        lqw.orderByDesc(DeductionPaymentRecord::getCreateTime);
        return lqw;
    }

    @Override
    public boolean recordDeductionPayment(Long userId, String paymentNo, String orderNo,
                                          BigDecimal payAmount, BigDecimal feeAmount,
                                          Date payTime, String status, String payType) {
        DeductionPaymentRecord record = new DeductionPaymentRecord();
        record.setUserId(userId);
        record.setPaymentNo(paymentNo);
        record.setOrderNo(orderNo);
        record.setPayAmount(payAmount);
        record.setFeeAmount(feeAmount);
        record.setTotalAmount(payAmount.add(feeAmount));
        record.setPayTime(payTime);
        record.setStatus(status);
        record.setPayType(payType);
        record.setDelFlag("0");

        return save(record);
    }

    @Override
    public boolean updateDeductionPaymentStatus(String paymentNo, String status) {
        LambdaUpdateWrapper<DeductionPaymentRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DeductionPaymentRecord::getPaymentNo, paymentNo)
            .eq(DeductionPaymentRecord::getStatus, DeductionPaymentStatusEnum.PENDING.getCode()) // 只更新支付中状态的记录
            .set(DeductionPaymentRecord::getStatus, status);

        int updateCount = getBaseMapper().update(null, updateWrapper);
        String statusDesc = DeductionPaymentStatusEnum.getDescByCode(status);
        log.info("更新抵扣金支付记录状态 - 支付单号: {}, 从支付中更新为: {}({}), 影响行数: {}",
            paymentNo, status, statusDesc, updateCount);

        if (updateCount == 0) {
            log.warn("未找到支付中状态的抵扣金支付记录 - 支付单号: {}", paymentNo);
        }

        return updateCount > 0;
    }

    @Override
    public String getDeductionPaymentStatus(String paymentNo) {
        LambdaQueryWrapper<DeductionPaymentRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeductionPaymentRecord::getPaymentNo, paymentNo)
            .orderByDesc(DeductionPaymentRecord::getCreateTime)
            .last("LIMIT 1");

        DeductionPaymentRecord record = getOne(queryWrapper);
        if (record != null) {
            return record.getStatus();
        }

        log.warn("未找到抵扣金支付记录 - 支付单号: {}", paymentNo);
        return null;
    }
}
