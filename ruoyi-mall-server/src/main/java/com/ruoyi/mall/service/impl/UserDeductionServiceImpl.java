package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.vo.UserDeductionReqVO;
import com.ruoyi.mall.domain.vo.UserDeductionRespVO;
import com.ruoyi.mall.domain.vo.UserDeductionVO;
import com.ruoyi.mall.mapper.DeductionPaymentRecordMapper;
import com.ruoyi.mall.service.IDeductionPaymentRecordService;
import com.ruoyi.mall.service.ITzUserService;
import com.ruoyi.mall.service.IUserDeductionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户平台抵扣金Service实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDeductionServiceImpl implements IUserDeductionService {

    private final ITzUserService tzUserService;
    private final IDeductionPaymentRecordService deductionPaymentRecordService;
    private final DeductionPaymentRecordMapper deductionPaymentRecordMapper;

    @Override
    public TableDataInfo<UserDeductionVO> queryUserDeductionList(UserDeductionVO userDeduction, PageQuery pageQuery) {
        // 查询用户信息
        LambdaQueryWrapper<TzUser> userWrapper = buildUserQueryWrapper(userDeduction);
        Page<TzUser> page = tzUserService.page(pageQuery.build(), userWrapper);

        List<UserDeductionVO> userDeductionList = new ArrayList<>();
        if (!page.getRecords().isEmpty()) {
            // 获取用户ID列表
            List<Long> userIds = page.getRecords().stream()
                .map(TzUser::getUserId)
                .collect(Collectors.toList());

            // 查询用户消费记录
            Map<Long, BigDecimal> userUsedMap = getUserDeductionUsed(userIds, 1);

            // 查询用户最近消费时间
            Map<Long, Date> userLastUsedTimeMap = getUserLastUsedTime(userIds);

            // 组装数据
            for (TzUser user : page.getRecords()) {
                UserDeductionVO vo = new UserDeductionVO();
                vo.setUserId(user.getUserId());
                vo.setUsername(user.getUsername());
                vo.setNickname(user.getNickname());
                vo.setPhone(user.getPhone());
                vo.setSex(user.getSex());
                vo.setStatus(user.getStatus());
                vo.setCreateTime(user.getCreateTime());

                // 设置平台抵扣金信息
                BigDecimal deductionTotal = user.getDeductionMoney() != null ?
                    user.getDeductionMoney() : BigDecimal.ZERO;
                vo.setDeductionTotal(deductionTotal);

                // 设置已使用平台抵扣金
                BigDecimal deductionUsed = userUsedMap.getOrDefault(user.getUserId(), BigDecimal.ZERO);
                vo.setDeductionUsed(deductionUsed);

                // 设置剩余平台抵扣金
                vo.setDeductionBalance(deductionTotal.subtract(deductionUsed));

                // 设置最近使用时间
                vo.setLastUsedTime(userLastUsedTimeMap.get(user.getUserId()));

                userDeductionList.add(vo);
            }
        }

        TableDataInfo<UserDeductionVO> rspData = new TableDataInfo<>();
        rspData.setCode(200);
        rspData.setMsg("查询成功");
        rspData.setRows(userDeductionList);
        rspData.setTotal(page.getTotal());
        return rspData;
    }

    @Override
    public TableDataInfo<UserDeductionRespVO> pageUserDeductionDetail(UserDeductionReqVO userDeduction, PageQuery pageQuery) {
        return TableDataInfo.build(deductionPaymentRecordMapper.selectUserDeductionDetailInfoPage(userDeduction, 1, pageQuery.build()));
    }

    @Override
    public List<UserDeductionRespVO> listUserDeductionDetail(UserDeductionReqVO userDeduction) {
        return deductionPaymentRecordMapper.selectUserDeductionDetailInfoList(userDeduction, 1);
    }


    @Override
    public List<UserDeductionVO> queryUserDeductionList(UserDeductionVO userDeduction) {
        // 查询用户信息
        LambdaQueryWrapper<TzUser> userWrapper = buildUserQueryWrapper(userDeduction);
        List<TzUser> userList = tzUserService.list(userWrapper);

        List<UserDeductionVO> userDeductionList = new ArrayList<>();
        if (userList.size() > 0) {
            // 获取用户ID列表
            List<Long> userIds = userList.stream()
                .map(TzUser::getUserId)
                .collect(Collectors.toList());

            // 查询用户消费记录
            Map<Long, BigDecimal> userUsedMap = getUserDeductionUsed(userIds, 1);

            // 查询用户最近消费时间
            Map<Long, Date> userLastUsedTimeMap = getUserLastUsedTime(userIds);

            // 组装数据
            for (TzUser user : userList) {
                UserDeductionVO vo = new UserDeductionVO();
                vo.setUserId(user.getUserId());
                vo.setUsername(user.getUsername());
                vo.setNickname(user.getNickname());
                vo.setPhone(user.getPhone());
                vo.setSex(user.getSex());
                vo.setStatus(user.getStatus());
                vo.setCreateTime(user.getCreateTime());

                // 设置平台抵扣金信息
                BigDecimal deductionTotal = user.getDeductionMoney() != null ?
                    user.getDeductionMoney() : BigDecimal.ZERO;
                vo.setDeductionTotal(deductionTotal);

                // 设置已使用平台抵扣金
                BigDecimal deductionUsed = userUsedMap.getOrDefault(user.getUserId(), BigDecimal.ZERO);
                vo.setDeductionUsed(deductionUsed);

                // 设置剩余平台抵扣金
                vo.setDeductionBalance(deductionTotal.subtract(deductionUsed));

                // 设置最近使用时间
                vo.setLastUsedTime(userLastUsedTimeMap.get(user.getUserId()));

                userDeductionList.add(vo);
            }
        }

        return userDeductionList;
    }

    @Override
    public UserDeductionVO getUserDeductionInfo(Long userId) {
        // 查询用户信息
        TzUser user = tzUserService.getById(userId);
        if (user == null) {
            return null;
        }

        UserDeductionVO vo = new UserDeductionVO();
        vo.setUserId(user.getUserId());
        vo.setUsername(user.getUsername());
        vo.setNickname(user.getNickname());
        vo.setPhone(user.getPhone());
        vo.setSex(user.getSex());
        vo.setStatus(user.getStatus());
        vo.setCreateTime(user.getCreateTime());

        // 设置平台抵扣金信息
        BigDecimal deductionTotal = user.getDeductionMoney() != null ?
            user.getDeductionMoney() : BigDecimal.ZERO;
        vo.setDeductionTotal(deductionTotal);

        // 查询用户消费记录
        List<Long> userIds = new ArrayList<>();
        userIds.add(userId);
        Map<Long, BigDecimal> userUsedMap = getUserDeductionUsed(userIds, 1);

        // 设置已使用平台抵扣金
        BigDecimal deductionUsed = userUsedMap.getOrDefault(userId, BigDecimal.ZERO);
        vo.setDeductionUsed(deductionUsed);

        // 设置剩余平台抵扣金
        vo.setDeductionBalance(deductionTotal.subtract(deductionUsed));

        // 查询用户最近消费时间
        Map<Long, Date> userLastUsedTimeMap = getUserLastUsedTime(userIds);
        vo.setLastUsedTime(userLastUsedTimeMap.get(userId));

        return vo;
    }

    /**
     * 构建用户查询条件
     */
    private LambdaQueryWrapper<TzUser> buildUserQueryWrapper(UserDeductionVO userDeduction) {
        LambdaQueryWrapper<TzUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(userDeduction.getUserId() != null, TzUser::getUserId, userDeduction.getUserId());
        lqw.like(StringUtils.isNotBlank(userDeduction.getUsername()), TzUser::getUsername, userDeduction.getUsername());
        lqw.like(StringUtils.isNotBlank(userDeduction.getPhone()), TzUser::getPhone, userDeduction.getPhone());
        lqw.orderByDesc(TzUser::getCreateTime);
        return lqw;
    }

    /**
     * 获取用户已使用平台抵扣金
     */
    private Map<Long, BigDecimal> getUserDeductionUsed(List<Long> userIds, Integer payType) {
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<Long, BigDecimal> resultMap = new HashMap<>();
        List<Map<String, Object>> resultList = deductionPaymentRecordMapper.selectUserDeductionUsed(userIds, payType);

        if (resultList != null && !resultList.isEmpty()) {
            for (Map<String, Object> map : resultList) {
                if (map.containsKey("key") && map.containsKey("value")) {
                    Long userId = Long.valueOf(map.get("key").toString());
                    BigDecimal usedAmount = new BigDecimal(map.get("value").toString());
                    resultMap.put(userId, usedAmount);
                }
            }
        }

        return resultMap;
    }


    /**
     * 获取用户最近使用平台抵扣金时间
     */
    private Map<Long, Date> getUserLastUsedTime(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<Long, Date> resultMap = new HashMap<>();
        List<Map<String, Object>> resultList = deductionPaymentRecordMapper.selectUserLastUsedTime(userIds);

        if (resultList != null && !resultList.isEmpty()) {
            for (Map<String, Object> map : resultList) {
                if (map.containsKey("key") && map.containsKey("value")) {
                    Long userId = Long.valueOf(map.get("key").toString());
                    Object timeValue = map.get("value");

                    // 处理不同类型的时间值
                    if (timeValue != null) {
                        Date lastUsedTime = null;
                        if (timeValue instanceof Date) {
                            lastUsedTime = (Date) timeValue;
                        } else if (timeValue instanceof java.time.LocalDateTime) {
                            java.time.LocalDateTime localDateTime = (java.time.LocalDateTime) timeValue;
                            lastUsedTime = java.util.Date.from(localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
                        } else {
                            try {
                                // 尝试解析字符串格式的时间
                                lastUsedTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(timeValue.toString());
                            } catch (Exception e) {
                                log.error("Failed to parse date value: {}", timeValue, e);
                            }
                        }

                        if (lastUsedTime != null) {
                            resultMap.put(userId, lastUsedTime);
                        }
                    }
                }
            }
        }

        return resultMap;
    }
}
