package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.TzUserStatus;
import com.ruoyi.mall.domain.entity.ConsignmentProduct;
import com.ruoyi.mall.domain.entity.Product;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.mapper.ShopMapper;
import com.ruoyi.mall.service.IConsignmentProductService;
import com.ruoyi.mall.service.IConsignmentService;
import com.ruoyi.mall.service.IProductService;
import com.ruoyi.mall.service.ITzUserService;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ConsignmentServiceImpl extends ServiceImpl<ShopMapper, Shop> implements IConsignmentService {

    private final ShopMapper shopMapper;
    private final SysUserMapper sysUserMapper;
    private final ITzUserService tzUserService;
    private final IConsignmentProductService consignmentProductService;
    private final IProductService productService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer checkConsignmentConfig() {

        log.info("开始检查代销用户代销时间是否过期任务");

        List<Shop> shops = shopMapper.selectList(new LambdaQueryWrapper<Shop>()
            .select(Shop::getId)
            .eq(Shop::getStatus, "0")
            .eq(Shop::getType, "0")
            .eq(Shop::getConsignmentPermission, "1")
            .lt(Shop::getConsignmentExpireTime, getToday())
            .isNotNull(Shop::getConsignmentExpireTime)
        );

        if (shops.isEmpty()) {
            log.info("没有代销用户代销时间过期");
            return 0;
        }

        int count = 0;
        // 遍历每个商家
        for (Shop shop : shops) {
            // 根据商家的UserId查询SysUser
            SysUser sysUser = sysUserMapper.selectById(shop.getUserId());
            if (sysUser == null) {
                log.warn("系统用户不存在，shopId: {}, userId: {}", shop.getId(), shop.getUserId());
                continue;
            }

            // 获取商城用户ID
            Long tzUserId = sysUser.getTzUserId();
            if (tzUserId == null) {
                log.warn("商城用户ID为空，shopId: {}, sysUserId: {}", shop.getId(), sysUser.getUserId());
                continue;
            }

            // 查询商城用户
            TzUser tzUser = tzUserService.getOne(new LambdaQueryWrapper<TzUser>()
                .eq(TzUser::getUserId, tzUserId)
                .eq(TzUser::getDelFlag, "0"));

            if (tzUser == null) {
                log.warn("商城用户不存在，shopId: {}, sysUserId: {}, tzUserId: {}",
                    shop.getId(), sysUser.getUserId(), tzUserId);
                continue;
            }

            LambdaUpdateWrapper<TzUser> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                .set(TzUser::getStatus, TzUserStatus.FAILURE.getCode())
                .eq(TzUser::getUserId, tzUser.getUserId());

            // 设置失效
            if (tzUser.getStatus().equals(TzUserStatus.OK.getCode())) {
                if (tzUserService.update(updateWrapper)) {

                    log.info("代销用户代销时间过期，设置失效状态：shopId: {}, sysUserId: {}, tzUserId: {}",
                        shop.getId(), sysUser.getUserId(), tzUserId);
                    count++;

                    //取消正在代销的商品
                    LambdaUpdateWrapper<ConsignmentProduct> updateWrapper1 = new LambdaUpdateWrapper<>();
                    updateWrapper1
                        .set(ConsignmentProduct::getStatus, "1")
                        .eq(ConsignmentProduct::getConsignmentShop, shop.getId());

                    if (consignmentProductService.update(updateWrapper1)) {
                        log.info("取消成功代销用户正在代销的产品，代销用户 shopId: {}, sysUserId: {}, tzUserId: {}",
                            shop.getId(), sysUser.getUserId(), tzUserId);
                    }


                } else {
                    log.info("代销用户代销时间过期，设置失效状态失败：shopId: {}, sysUserId: {}, tzUserId: {}",
                        shop.getId(), sysUser.getUserId(), tzUserId);
                }
            } else {
                log.info("代销用户状态不是正常状态,不设为失效：shopId: {}, sysUserId: {}, tzUserId: {}", shop.getId(), sysUser.getUserId(), tzUserId);
            }
        }

        log.info("代销用户代销时间过期检查完成，共检查代销用户：{}个，设置失效状态：{}个", shops.size(), count);

        return count;
    }

    @Override
    public void checkShopConsignmentConfig() {

        log.info("开始检查店铺代销时间是否过期任务");

        List<Shop> shops = list(
            new LambdaQueryWrapper<Shop>()
                .select(Shop::getId)
                .eq(Shop::getType, "1")
                .lt(Shop::getJurisdictionExpireTime, getToday())
                .isNotNull(Shop::getJurisdictionExpireTime));

        if (shops.isEmpty()) {
            log.info("没有店铺代销功能到期");
        }

        int count = 0;
        for (Shop shop : shops) {
            // 根据商家的UserId查询SysUser
            SysUser sysUser = sysUserMapper.selectById(shop.getUserId());
            if (sysUser == null) {
                log.warn("系统用户不存在，shopId: {}, userId: {}", shop.getId(), shop.getUserId());
                continue;
            }

            // 获取商城用户ID
            Long tzUserId = sysUser.getTzUserId();
            if (tzUserId == null) {
                log.warn("商城用户ID为空，shopId: {}, sysUserId: {}", shop.getId(), sysUser.getUserId());
                continue;
            }

            // 查询商城用户
            TzUser tzUser = tzUserService.getOne(new LambdaQueryWrapper<TzUser>()
                .eq(TzUser::getUserId, tzUserId)
                .eq(TzUser::getDelFlag, "0"));

            if (tzUser == null) {
                log.warn("商城用户不存在，shopId: {}, sysUserId: {}, tzUserId: {}",
                    shop.getId(), sysUser.getUserId(), tzUserId);
                continue;
            }

            //取消正在代销的商品
            LambdaUpdateWrapper<Product> updateWrapper1 = new LambdaUpdateWrapper<>();
            updateWrapper1
                .set(Product::getStatus, "0")
                .eq(Product::getShopId, shop.getId())
                .eq(Product::getIsDaixiao, "1");

            //取消代销用户代销的产品
            LambdaUpdateWrapper<ConsignmentProduct> updateWrapper2 = new LambdaUpdateWrapper<>();
            updateWrapper2
                .set(ConsignmentProduct::getStatus, "1")
                .eq(ConsignmentProduct::getShopId, shop.getId());

            if (productService.update(updateWrapper1) && consignmentProductService.update(updateWrapper2)) {
                log.info("取消成功商家正在代销的产品，商家 shopId: {}, sysUserId: {}, tzUserId: {}",
                    shop.getId(), sysUser.getUserId(), tzUserId);

            } else {
                log.info("取消成功商家正在代销的产品失败，商家 shopId: {}, sysUserId: {}, tzUserId: {}",
                    shop.getId(), sysUser.getUserId(), tzUserId);
            }
        }
    }


    private Date getToday() {
        // 获取当前日期（去除时间部分）
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date today = calendar.getTime();

        return today;
    }
}
