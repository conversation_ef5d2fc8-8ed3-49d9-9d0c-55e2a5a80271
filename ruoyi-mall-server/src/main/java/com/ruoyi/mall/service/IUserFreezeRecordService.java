package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.UserFreezeRecord;

import java.math.BigDecimal;
import java.util.Map;

/**
 *
 */
public interface IUserFreezeRecordService extends IService<UserFreezeRecord> {

    /**
     * 根据电话号码与类型查询用户信息
     *
     * @param phone 店铺电话
     * @return 店铺信息
     */
    Map<String, Object> getUserByPhone(String phone, Integer type);

    /**
     * 获取扣除记录
     *
     * @return 冻结记录列表
     */
    TableDataInfo<UserFreezeRecord> getFreezeRecords(PageQuery pageQuery, String userName, String phone);

    /**
     * 扣除金额记录
     *
     * @param userId 用户ID
     * @param amount 金额
     * @param reason 原因
     * @param type   类型
     * @return 结果
     */
    Integer freezeAmount(Long userId, BigDecimal amount, String reason, Integer type);

}
