package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.ShopAddress;
import com.ruoyi.mall.mapper.ShopAddressMapper;
import com.ruoyi.mall.service.IShopAddressService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class ShopAddressServiceImpl extends ServiceImpl<ShopAddressMapper, ShopAddress> implements IShopAddressService {

    @Autowired
    ShopAddressMapper shopAddressMapper;

    @Override
    public Integer insertShopAddress(ShopAddress shopAddress) {
        return shopAddressMapper.insert(shopAddress);
    }

    @Override
    public ShopAddress selectShopAddressById(Long id) {
        return shopAddressMapper.selectById(id);
    }

    @Override
    public List<ShopAddress> selectShopAddressList(ShopAddress shopAddress, PageQuery pageQuery) {

        LambdaQueryWrapper<ShopAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .eq(StringUtils.isNotEmpty(shopAddress.getShopId().toString()), ShopAddress::getShopId, shopAddress.getShopId())
            .orderByDesc(ShopAddress::getCreateTime);

        return shopAddressMapper.selectList(new Page<>(pageQuery.getPageNum(), pageQuery.getPageNum()), wrapper);
    }

    @Override
    public Integer updateShopAddress(ShopAddress shopAddress) {

        LambdaUpdateWrapper<ShopAddress> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .set(ShopAddress::getSendName, shopAddress.getSendName())
            .set(ShopAddress::getSendPhone, shopAddress.getSendPhone())
            .set(ShopAddress::getProvince, shopAddress.getProvince())
            .set(ShopAddress::getCity, shopAddress.getCity())
            .set(ShopAddress::getDistrict, shopAddress.getDistrict())
            .set(ShopAddress::getTown, shopAddress.getTown())
            .set(ShopAddress::getIsDefault, shopAddress.getIsDefault())
            .eq(ShopAddress::getId, shopAddress.getId());

        return shopAddressMapper.update(updateWrapper);
    }

    @Override
    public Integer deleteShopAddressById(Long id) {

        LambdaUpdateWrapper<ShopAddress> wrapper = new LambdaUpdateWrapper<>();
        wrapper
            .set(ShopAddress::getDelFlag, "1")
            .eq(ShopAddress::getId, id);

        return shopAddressMapper.update(wrapper);
    }

    @Override
    public Integer deleteShopAddressByIds(Long[] ids) {

        int count = 0;
        for (Long id : ids) {
            LambdaUpdateWrapper<ShopAddress> wrapper = new LambdaUpdateWrapper<>();
            wrapper
                .set(ShopAddress::getDelFlag, "1")
                .eq(ShopAddress::getId, id);

            count = shopAddressMapper.update(wrapper);
        }
        return count;
    }
}
