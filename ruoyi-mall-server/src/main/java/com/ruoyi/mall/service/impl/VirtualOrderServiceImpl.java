package com.ruoyi.mall.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.OrderVirtualStatusEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.OrderNoGenerator;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.bo.VirtualOrderBo;
import com.ruoyi.mall.domain.entity.Product;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.domain.vo.VirtualOrderVo;
import com.ruoyi.mall.enums.VirtualTypeEnum;
import com.ruoyi.mall.factory.PaymentSuccessFactory;
import com.ruoyi.mall.mapper.VirtualOrderMapper;
import com.ruoyi.mall.service.IProductService;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.mall.service.IVirtualOrderService;
import com.ruoyi.mall.strategy.VirtualPaymentSuccess.ConsignmentLevelPaymentSuccessStrategyResolver;
import com.ruoyi.mall.strategy.VirtualPaymentSuccess.JurisdictionPaymentSuccessStrategyResolver;
import com.ruoyi.mall.strategy.VirtualPaymentSuccess.PaymentSuccessStrategy;
import com.ruoyi.mall.strategy.VirtualProduct.VirtualFactory;
import com.ruoyi.mall.strategy.VirtualProduct.VirtualStrategy;
import com.ruoyi.mall.utils.ServiceFeeCalculator;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.enums.PayStatusEnum;
import com.ruoyi.pay.service.IPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 虚拟订单服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VirtualOrderServiceImpl extends ServiceImpl<VirtualOrderMapper, VirtualOrder> implements IVirtualOrderService {

    private final IPayService payService;
    private final IProductService productService;
    private final VirtualFactory virtualFactory;
    private final PaymentSuccessFactory paymentSuccessFactory;
    private final JurisdictionPaymentSuccessStrategyResolver jurisdictionPaymentSuccessStrategyResolver;
    private final ConsignmentLevelPaymentSuccessStrategyResolver consignmentLevelPaymentSuccessStrategyResolver;
    private final IShopService shopService;

    @Override
    public TableDataInfo<VirtualOrder> getLoginVirtualOrder(VirtualOrder virtualOrder, PageQuery pageQuery) {
        // 添加搜索条件
        LambdaQueryWrapper<VirtualOrder> virtualOrderLambdaQueryWrapper = new LambdaQueryWrapper<VirtualOrder>()
            .eq(ObjUtil.isNotEmpty(virtualOrder.getUserId()), VirtualOrder::getUserId, virtualOrder.getUserId())
            .eq(StringUtils.isNotBlank(virtualOrder.getStatus()), VirtualOrder::getStatus, virtualOrder.getStatus())
            .like(StringUtils.isNotBlank(virtualOrder.getOrderNo()), VirtualOrder::getOrderNo, virtualOrder.getOrderNo())
            .like(StringUtils.isNotBlank(virtualOrder.getPhone()), VirtualOrder::getPhone, virtualOrder.getPhone())
            .like(StringUtils.isNotBlank(virtualOrder.getUserName()), VirtualOrder::getUserName, virtualOrder.getUserName())
            .like(StringUtils.isNotBlank(virtualOrder.getProductName()), VirtualOrder::getProductName, virtualOrder.getProductName())
            .like(ObjUtil.isNotEmpty(virtualOrder.getProductType()), VirtualOrder::getProductType, virtualOrder.getProductType())
            .like(StringUtils.isNotBlank(virtualOrder.getUserName()), VirtualOrder::getUserName, virtualOrder.getUserName())
            .like(StringUtils.isNotBlank(virtualOrder.getPayType()), VirtualOrder::getPayType, virtualOrder.getPayType())

            .gt(StringUtils.isNotBlank(virtualOrder.getStartTime()), VirtualOrder::getCreateTime, DateUtils.parseDate(virtualOrder.getStartTime()))
            .lt(StringUtils.isNotBlank(virtualOrder.getEndTime()), VirtualOrder::getCreateTime, DateUtils.parseDate(virtualOrder.getEndTime()))

            .orderByDesc(VirtualOrder::getCreateTime);
        Page<VirtualOrder> page = page(pageQuery.build(), virtualOrderLambdaQueryWrapper);
        return TableDataInfo.build(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createVirtualOrder(VirtualOrderBo virtualOrderBo, VirtualTypeEnum virtualTypeEnum) {
        // 1. 创建订单实体
        VirtualOrder virtualOrder = new VirtualOrder();
        BeanUtils.copyProperties(virtualOrderBo, virtualOrder);
        // 商品数量
        virtualOrder.setQuantity(virtualOrderBo.getQuantity() != null ? virtualOrderBo.getQuantity() : 1);
        // 商品有效期暂时都是一个月
        virtualOrder.setValidDays(virtualOrderBo.getValidDays() != null ? virtualOrderBo.getValidDays() : 1);
        virtualOrder.setProductName(virtualTypeEnum.productName());
        virtualOrder.setProductType(virtualTypeEnum.type());
        virtualOrder.setRemark(virtualTypeEnum.remark());

        // 2. 计算手续费（千分之六）
        BigDecimal originalAmount = virtualOrderBo.getAmount();
        BigDecimal serviceFee = ServiceFeeCalculator.calculateVirtualOrderServiceFee(originalAmount);
        BigDecimal totalAmount = ServiceFeeCalculator.calculateTotalAmountWithServiceFee(originalAmount);

        // 设置手续费和最终金额
        virtualOrder.setServiceFee(serviceFee);
        virtualOrder.setAmount(totalAmount);

        // 保留orderId字段，用于关联相关的业务ID
        if (virtualOrderBo.getOrderId() != null) {
            virtualOrder.setOrderId(virtualOrderBo.getOrderId());
        }

        // 2. 生成订单编号
        String orderNo = OrderNoGenerator.generateOrderNo("V");
        virtualOrder.setOrderNo(orderNo);

        // 3. 设置用户信息
        virtualOrder.setUserId(virtualOrderBo.getUserId());
        // 直接设置用户名，避免依赖SecurityUtils
        if (StringUtils.isNotEmpty(virtualOrderBo.getUserName())) {
            virtualOrder.setUserName(virtualOrderBo.getUserName());
        } else {
            virtualOrder.setUserName("用户" + virtualOrderBo.getUserId());
        }

        // 4. 设置订单状态
        virtualOrder.setStatus(OrderVirtualStatusEnum.PENDING_PAYMENT.getCode()); // 待支付

        // 5. 设置删除标志
        virtualOrder.setDelFlag("0");

        // 7. 保存订单
        save(virtualOrder);

        return virtualOrder.getOrderNo();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> createPayment(Long id, String orderNo, Long userId, BigDecimal amount, String payType) {
        // 获取虚拟订单信息
        VirtualOrderVo order = getVirtualOrderDetail(orderNo);
        // 修改订单为支付中
        LambdaUpdateWrapper<VirtualOrder> objectLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        objectLambdaUpdateWrapper
            .set(VirtualOrder::getStatus, OrderVirtualStatusEnum.DURING_PAYMENT.getCode())
            .eq(VirtualOrder::getId, id);
        update(objectLambdaUpdateWrapper);

        // 创建支付
        return payService.createPayment(order.getId(), order.getOrderNo(), userId, order.getAmount(), payType);
    }

    @Override
    public VirtualOrderVo getVirtualOrderDetail(String orderNo) {
        VirtualOrder order = getOne(new LambdaQueryWrapper<VirtualOrder>().eq(VirtualOrder::getOrderNo, orderNo).eq(VirtualOrder::getDelFlag, 0));
        if (order == null) {
            return null;
        }

        VirtualOrderVo vo = new VirtualOrderVo();
        BeanUtils.copyProperties(order, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelVirtualOrder(String orderNo, Long userId) {
        // 1. 查找订单
        VirtualOrder order = getOne(new LambdaQueryWrapper<VirtualOrder>().eq(VirtualOrder::getOrderNo, orderNo));

        if (order == null) {
            log.error("订单不存在: {}", orderNo);
            throw new ServiceException("订单不存在");
        }

        // 2. 检查订单所属
        if (!order.getUserId().equals(userId)) {
            log.error("订单不属于当前用户: {}", orderNo);
            throw new ServiceException("无权操作此订单");
        }

        // 3. 检查订单状态
        if (!OrderVirtualStatusEnum.PENDING_PAYMENT.getCode().equals(order.getStatus())) {
            log.error("订单状态错误: {}", orderNo);
            throw new ServiceException("只能取消待支付的订单");
        }

        // 4. 更新订单状态
        order.setStatus(OrderVirtualStatusEnum.PENDING_RECEIVE.getCode()); // 已取消

        return updateById(order);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVirtualOrder(String orderNo, Long userId) {
        // 1. 查找订单
        VirtualOrder order = getOne(new LambdaQueryWrapper<VirtualOrder>().eq(VirtualOrder::getOrderNo, orderNo));

        if (order == null) {
            log.error("订单不存在: {}", orderNo);
            throw new ServiceException("订单不存在");
        }

        // 2. 检查订单所属
        if (!order.getUserId().equals(userId)) {
            log.error("订单不属于当前用户: {}", orderNo);
            throw new ServiceException("无权操作此订单");
        }

        // 3. 检查订单状态
        if (!OrderVirtualStatusEnum.PENDING_RECEIVE.getCode().equals(order.getStatus()) && !OrderVirtualStatusEnum.PENDING_SHIPMENT.getCode().equals(order.getStatus())) {
            log.error("订单状态错误: {}", orderNo);
            throw new ServiceException("只能删除已取消与已支付的订单");
        }
        return remove(new LambdaQueryWrapper<VirtualOrder>().eq(VirtualOrder::getOrderNo, orderNo));
    }

    @Override
    public List<VirtualOrder> getUserVirtualOrderList(Long userId, PageQuery pageQuery) {

        LambdaQueryWrapper<VirtualOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VirtualOrder::getUserId, userId)
            .orderByDesc(VirtualOrder::getCreateTime);

        return list(new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize()), lqw);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handlePaymentSuccess(String payType, Map<String, String> map) {
        // 1. 查询支付信息
        String paymentNo = map.get("out_trade_no");
        PayOrder payOrder = payService.getByPaymentNo(paymentNo);
        if (payOrder == null) {
            log.error("支付订单不存在: {}", paymentNo);
            return false;
        }
        // 2. 查找订单
        Long orderId = payOrder.getOrderId();
        VirtualOrder virtualOrder = getById(orderId);
        if (virtualOrder == null) {
            log.error("订单不存在: {}", orderId);
            throw new ServiceException("订单不存在");
        }

        // 3. 检查订单状态
        if (OrderVirtualStatusEnum.PENDING_SHIPMENT.getCode().equals(virtualOrder.getStatus())
            || OrderVirtualStatusEnum.PENDING_RECEIVE.getCode().equals(virtualOrder.getStatus())) {
            log.info("订单{}已处理，无需重复处理", orderId);
            return true;
        }

        if (!OrderVirtualStatusEnum.DURING_PAYMENT.getCode().equals(virtualOrder.getStatus())) {
            log.error("订单{}状态异常: {}", orderId, virtualOrder.getStatus());
            return false;
        }
        // 4. 处理支付回调
        boolean success = payService.handlePayCallback(payType, map);

        if (success) {
            // 5. 更新订单信息
            virtualOrder.setStatus(OrderVirtualStatusEnum.PENDING_SHIPMENT.getCode()); // 已支付
            virtualOrder.setPayTime(new Date());
            virtualOrder.setPayType(payOrder.getPayType());
            virtualOrder.setTransactionId(payOrder.getTransactionId());

            // 6. 计算有效期
            if (virtualOrder.getValidDays() != null && virtualOrder.getValidDays() > 0) {
                Date now = new Date();
                virtualOrder.setEffectiveTime(now);

                Calendar calendar = Calendar.getInstance();
                calendar.setTime(now);
                calendar.add(Calendar.DAY_OF_MONTH, virtualOrder.getValidDays());
                virtualOrder.setExpireTime(calendar.getTime());
            }

            // 7. 根据产品类型处理不同的支付成功逻辑
            processPaymentSuccessByProductType(virtualOrder);
        } else {
            log.error("支付订单处理失败: {}", paymentNo);
            // 更新订单信息为待支付状态
            virtualOrder.setStatus(OrderVirtualStatusEnum.PENDING_PAYMENT.getCode());
        }
        return updateById(virtualOrder);
    }

    /**
     * 根据产品类型处理不同的支付成功逻辑
     *
     * @param order 虚拟订单
     */
    private void processPaymentSuccessByProductType(VirtualOrder order) {
        Integer productType = order.getProductType();

        try {
            // 首先检查是否为权限类型1-3
            if (jurisdictionPaymentSuccessStrategyResolver.resolveAndHandle(order)) {
                log.info("权限类型[1-3]支付成功处理完成，订单ID: {}", order.getId());
                return;
            }

            // 然后检查是否为代销权限等级类型7-8
            if (consignmentLevelPaymentSuccessStrategyResolver.resolveAndHandle(order)) {
                log.info("代销权限等级类型[7-8]支付成功处理完成，订单ID: {}", order.getId());
                return;
            }

            // 然后尝试通过工厂获取对应的策略处理
            try {
                PaymentSuccessStrategy strategy = paymentSuccessFactory.getPaymentSuccessStrategy(productType);
                boolean result = strategy.handlePaymentSuccess(order);

                if (result) {
                    log.info("产品类型[{}]支付成功处理完成，订单ID: {}", productType, order.getId());
                } else {
                    log.warn("产品类型[{}]支付成功处理失败，订单ID: {}", productType, order.getId());
                }
            } catch (Exception e) {
                log.error("未找到产品类型[{}]对应的支付成功处理策略，订单ID: {}", productType, order.getId(), e);
            }
        } catch (Exception e) {
            log.error("处理支付成功逻辑异常，订单ID: {}, 产品类型: {}", order.getId(), productType, e);
        }
    }

    /**
     * 获取商家还能让自己商品代销的数量
     *
     * @return null 未开通权限  0 无数量  >0 可以代销的库存数量
     */
    @Override
    public Long getConsignmentCount(Long userId, Long shopId) {
        // 已经开通代销的数量
        long count = productService.count(
            new LambdaQueryWrapper<Product>()
                .eq(Product::getShopId, shopId)
                .eq(Product::getIsDaixiao, 1)
                .eq(Product::getDelFlag, 0)
        );
        // 获取商家权限
        Shop shop = shopService.getById(shopId);
        String jurisdiction = shop.getJurisdiction();
        Date jurisdictionExpireTime = shop.getJurisdictionExpireTime();
        if (jurisdiction == null) {
            return null;
        }
        // 判断权限时间是否过期
        if (jurisdictionExpireTime != null && jurisdictionExpireTime.getTime() < new Date().getTime()) {
            return null;
        }
        VirtualStrategy paymentStrategy = virtualFactory.getPaymentStrategy(Integer.valueOf(jurisdiction));
        Long count3 = paymentStrategy.getCount();
        return count3 - count;
    }

    @Override
    public void queryOrderStatus(String paymentNo) {
        PayOrder payOrder = payService.getByPaymentNo(paymentNo);
        String status = payService.queryPayStatus(payOrder);

        VirtualOrder order = getOne(
            new LambdaQueryWrapper<VirtualOrder>()
                .eq(VirtualOrder::getOrderNo, payOrder.getOrderNo())
        );
        if (PayStatusEnum.SUCCESS.getCode().equals(status)) {
            // 5. 更新订单信息
            order.setStatus(OrderVirtualStatusEnum.PENDING_SHIPMENT.getCode());
            order.setPayTime(new Date());
            order.setPayType(payOrder.getPayType());
            order.setTransactionId(payOrder.getTransactionId());

            // 6. 计算有效期
            if (order.getValidDays() != null && order.getValidDays() > 0) {
                Date now = new Date();
                order.setEffectiveTime(now);

                Calendar calendar = Calendar.getInstance();
                calendar.setTime(now);
                calendar.add(Calendar.DAY_OF_MONTH, order.getValidDays());
                order.setExpireTime(calendar.getTime());
            }

            // 7. 根据产品类型处理不同的支付成功逻辑
            processPaymentSuccessByProductType(order);
        } else if (PayStatusEnum.FAILED.getCode().equals(status)) {
            order.setStatus(OrderVirtualStatusEnum.PENDING_PAYMENT.getCode());
        }
        updateById(order);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> payWithShopDeduction(Long orderId, String orderNo, Long userId, BigDecimal amount) {
        try {
            // 1. 获取商家ID
            Long shopId = shopService.getShopIdByUserId(userId);
            if (shopId == null) {
                throw new ServiceException("商家信息不存在");
            }

            // 2. 检查商家是否存在
            if (!shopService.checkShopExists(shopId)) {
                throw new ServiceException("商家不存在");
            }

            // 3. 检查平台促销金余额是否足够
            BigDecimal promotionBalance = shopService.getShopPromotionBalance(shopId);
            if (promotionBalance == null || promotionBalance.compareTo(amount) < 0) {
                throw new ServiceException("商家平台促销金不足");
            }

            // 4. 在ruoyi-pay模块中创建支付记录
            Map<String, String> payResult = payService.createPayment(orderId, orderNo, userId, amount, PayOrder.PAY_TYPE_SHOP_DEDUCTION);

            // 5. 扣减平台促销金余额
            boolean result = shopService.deductShopPromotionBalance(shopId, amount);
            if (!result) {
                throw new ServiceException("扣减商家平台促销金失败");
            }

            // 6. 获取虚拟订单
            VirtualOrder order = getById(orderId);
            if (order == null) {
                throw new ServiceException("订单不存在");
            }

            // 7. 计算有效期
            Date now = new Date();
            order.setEffectiveTime(now);
            if (order.getValidDays() != null && order.getValidDays() > 0) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(now);
                calendar.add(Calendar.DAY_OF_MONTH, order.getValidDays());
                order.setExpireTime(calendar.getTime());
            }

            // 8. 修改订单状态
            order.setStatus(OrderVirtualStatusEnum.PENDING_SHIPMENT.getCode());
            order.setPayTime(now);
            order.setPayType(PayOrder.PAY_TYPE_SHOP_DEDUCTION);
            order.setTransactionId(payResult.get("transactionId"));
            updateById(order);

            // 9. 处理订单后续业务
            handleVirtualOrderPaymentSuccess(order);

            // 10. 返回支付成功信息
            return payResult;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("商家平台促销金支付失败", e);
            throw new ServiceException("支付处理失败，请稍后再试");
        }
    }

    /**
     * 处理虚拟订单支付成功后的业务逻辑
     *
     * @param order 虚拟订单
     */
    private void handleVirtualOrderPaymentSuccess(VirtualOrder order) {
        try {
            // 尝试处理
            processPaymentSuccessByProductType(order);
        } catch (Exception e) {
            log.error("处理虚拟订单支付成功后的业务逻辑失败", e);
            // 记录错误但不抛出异常，避免影响支付流程
        }
    }
}
