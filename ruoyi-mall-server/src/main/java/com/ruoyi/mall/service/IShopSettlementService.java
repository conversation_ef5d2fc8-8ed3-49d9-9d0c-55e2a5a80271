package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.SettlementRecordDTO;
import com.ruoyi.mall.domain.dto.SettlementStatsDTO;
import com.ruoyi.mall.domain.entity.ShopSettlement;
import com.ruoyi.mall.domain.vo.SettlementRecordVO;
import com.ruoyi.mall.domain.vo.SettlementStatsVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商家结算单服务接口
 */
public interface IShopSettlementService extends IService<ShopSettlement> {

    /**
     * 查询商家结算单列表
     *
     * @param shopSettlement 商家结算单
     * @param pageQuery      分页对象
     * @return 商家结算单分页数据
     */
    TableDataInfo<ShopSettlement> queryPageList(ShopSettlement shopSettlement, PageQuery pageQuery);

    /**
     * 获取商家结算单详细信息
     *
     * @param id 结算单ID
     * @return 商家结算单
     */
    ShopSettlement getById(Long id);

    /**
     * 获取商家结算单详细信息
     *
     * @param settlementNo 结算单号
     * @return 商家结算单
     */
    ShopSettlement getBySettlementNo(String settlementNo);

    /**
     * 商家申请结算
     *
     * @param shopId       商家ID
     * @param amount       申请结算金额
     * @param accountName  收款账户名
     * @param accountNo    收款账号
     * @param bankName     开户行
     * @param contactPhone 联系电话
     * @return 结算单
     */
    ShopSettlement applySettlement(Long shopId, BigDecimal amount, String accountName,
                                   String accountNo, String bankName, String bankBranchName, String contactPhone);

    /**
     * 审核结算单
     *
     * @param id          结算单ID
     * @param status      审核状态（1通过 2拒绝）
     * @param auditorId   审核人ID
     * @param auditorName 审核人名称
     * @param auditRemark 审核意见
     * @return 结果
     */
    boolean auditSettlement(Long id, String status, Long auditorId, String auditorName, String auditRemark);

    /**
     * 确认打款
     *
     * @param id        结算单ID
     * @param payerId   打款人ID
     * @param payerName 打款人名称
     * @param payProof  打款凭证
     * @param payRemark 打款备注
     * @return 结果
     */
    boolean confirmPayment(Long id, Long payerId, String payerName, String payProof, String payRemark);

    /**
     * 完成结算
     *
     * @param id 结算单ID
     * @return 结果
     */
    boolean completeSettlement(Long id);

    /**
     * 取消结算单
     *
     * @param id     结算单ID
     * @param remark 取消原因
     * @return 结果
     */
    boolean cancelSettlement(Long id, String remark);


    /**
     * 结算统计
     *
     * @param dto       请求dto
     * @param pageQuery 分页对象
     * @return TableDataInfo<SettlementStatsVO>
     * <AUTHOR>
     */
    TableDataInfo<SettlementStatsVO> pageSettlementStats(SettlementStatsDTO dto, PageQuery pageQuery);


    /**
     * 结算统计列表（不分页）
     *
     * @param dto 请求dto
     * @return List<SettlementStatsVO>
     * <AUTHOR>
     */
    List<SettlementStatsVO> listSettlementStats(SettlementStatsDTO dto);


    /**
     * 结算记录列表 （分页）
     *
     * @param dto
     * @param pageQuery
     * @return TableDataInfo<SettlementRecordVO>
     * <AUTHOR>
     */

    TableDataInfo<SettlementRecordVO> pageSettlementRecord(SettlementRecordDTO dto, PageQuery pageQuery);


    /**
     * 结算记录列表（不分页）
     *
     * @param dto
     * @return List<SettlementRecordVO>
     * <AUTHOR>
     */
    List<SettlementRecordVO> listSettlementRecord(SettlementRecordDTO dto);


}
