package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.mall.domain.entity.AuthorizationArea;
import com.ruoyi.mall.mapper.AuthorizationAreaMapper;
import com.ruoyi.mall.service.IAuthorizationAreaService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class AuthorizationAreaServiceImpl extends ServiceImpl<AuthorizationAreaMapper, AuthorizationArea> implements IAuthorizationAreaService {

    private final AuthorizationAreaMapper authorizationAreaMapper;

    @Override
    public Page<Map<String, Object>> queryAreaAuthorize(String type, String level, PageQuery pageQuery) {
        if ("B".equals(type)) {
            return authorizationAreaMapper.queryAreaAuthorizeByShop(type, level, pageQuery.build());
        } else {
            return authorizationAreaMapper.queryAreaAuthorizeByUser(type, level, pageQuery.build());
        }
    }

    @Override
    @Transactional
    public void receiveAuthorizationData(List<AuthorizationArea> dataList) {
        authorizationAreaMapper.delete(null);
        super.saveBatch(dataList);
    }
}
