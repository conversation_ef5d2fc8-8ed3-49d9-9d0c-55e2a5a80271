package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.mall.domain.entity.AuthorizationArea;

import java.util.List;
import java.util.Map;

/**
 * 地区授权
 */
public interface IAuthorizationAreaService extends IService<AuthorizationArea> {

    Page<Map<String, Object>> queryAreaAuthorize(String type, String level, PageQuery pageQuery);

    void receiveAuthorizationData(List<AuthorizationArea> dataList);

}
