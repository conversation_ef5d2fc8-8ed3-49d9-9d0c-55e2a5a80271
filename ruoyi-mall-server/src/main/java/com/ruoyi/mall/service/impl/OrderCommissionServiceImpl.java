package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.*;
import com.ruoyi.mall.mapper.OrderCommissionMapper;
import com.ruoyi.mall.service.*;
import com.ruoyi.pay.domain.PayOrder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;


@Service
@RequiredArgsConstructor
public class OrderCommissionServiceImpl extends MPJBaseServiceImpl<OrderCommissionMapper, OrderCommission> implements IOrderCommissionService {

    private final OrderCommissionMapper mapper;
    private final IShopWalletService shopWalletService;
    private final IOrderItemService orderItemService;
    private final IProductService productService;
    private final IOrderService orderService;
    private final IShopService shopService;

    @Override
    public Boolean insertOrderCommission(OrderCommission orderCommission) {
        if (orderCommission.getActualCommission().compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("赠送金不能为0");
        }

        // 获取商家钱包
        ShopWallet wallet = shopWalletService.getById(orderCommission.getShopId());
        if (wallet == null) {
            wallet = shopWalletService.createWallet(orderCommission.getShopId());
        }

        // 计算入账后余额
        BigDecimal beforeBalance = wallet.getBalance();
        BigDecimal afterBalance = beforeBalance.add(orderCommission.getActualCommission());

        // 更新钱包余额
        wallet.setBalance(afterBalance);
        wallet.setTotalIncome(wallet.getTotalIncome().add(orderCommission.getActualCommission()));
        boolean result = shopWalletService.updateById(wallet);

        if (!result) {
            mapper.insert(orderCommission);
        }
        return result;
    }

    @Override
    public Integer updateOrderCommission(OrderCommission orderCommission) {

        LambdaUpdateWrapper<OrderCommission> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .set(StringUtils.isNotEmpty(orderCommission.getStatus()), OrderCommission::getStatus, orderCommission.getStatus())
            .eq(OrderCommission::getId, orderCommission.getId());

        return mapper.update(updateWrapper);
    }

    @Override
    public Integer deleteOrderCommission(Long id) {

        LambdaUpdateWrapper<OrderCommission> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .set(OrderCommission::getDelFlag, "2")
            .eq(OrderCommission::getId, id);

        return mapper.update(updateWrapper);
    }

    @Override
    public OrderCommission getOrderCommissionById(Long id) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigDecimal getCommissionAmount(Long orderId, Long consignmentId) {

        //代销商家总共获取佣金
        BigDecimal commission = BigDecimal.ZERO;
        Order order = orderService.getById(orderId);
        if (order == null) {
            log.error("订单不存在");
            throw new ServiceException("订单不存在");
        }
        BigDecimal amount = BigDecimal.ZERO;
        //如果使用抵扣金支付,费用是商品总费用
        if (order.getPayType().equals(PayOrder.PAY_TYPE_DEDUCTION)) {
            amount = order.getDeductionAmount();
        } else {
            amount = order.getPayAmount();
        }

        //获取商品项ids,不同商品佣金不同
        List<OrderItem> items = orderItemService.getOrderItems(orderId);
        for (OrderItem orderItem : items) {
            Product product = productService.getById(orderItem.getProductId());

            //获取代销商品佣金结算方式
            if (product.getDaixiaoType().equals(1)) {//固定金额
                commission = BigDecimal.valueOf(product.getDaixiaoTypeValue()).multiply(BigDecimal.valueOf(orderItem.getQuantity()));
            } else if (product.getDaixiaoType().equals(2)) {//比例
                commission = amount.multiply(BigDecimal.valueOf(product.getDaixiaoTypeValue())).divide(BigDecimal.valueOf(100));
            }

            //记录代销商家获取的佣金
            OrderCommission orderCommission = new OrderCommission();
            orderCommission.setOrderId(orderId);
            orderCommission.setItemId(orderItem.getId());
            orderCommission.setShopId(consignmentId);
            orderCommission.setCommissionType(product.getDaixiaoType()); //佣金结算方式
            orderCommission.setOrderAmount(orderItem.getPayAmount()); //订单实际支付金额
            orderCommission.setActualCommission(commission);//结算佣金金额
            orderCommission.setCalcCommission(commission);
            orderCommission.setStatus("1");//已结算
            orderCommission.setDelFlag("0");//未删除

            if (save(orderCommission)) {
                LambdaUpdateWrapper<Shop> wrapper = new LambdaUpdateWrapper<>();
                wrapper
                    .set(Shop::getDeductionMoney, shopService.getById(order.getConsignmentId()).getDeductionMoney().add(commission))
                    .eq(Shop::getId, order.getConsignmentId());

                shopService.update(wrapper);
            }
        }

        return commission;
    }
}
