package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.bo.PlatformAgreementBo;
import com.ruoyi.mall.domain.entity.PlatformAgreement;

import java.util.Collection;
import java.util.List;

/**
 * 平台协议配置Service接口
 */
public interface IPlatformAgreementService extends IService<PlatformAgreement> {

    /**
     * 查询平台协议配置
     */
    PlatformAgreement queryById(Long id);

    /**
     * 查询平台协议配置列表
     */
    TableDataInfo<PlatformAgreement> queryPageList(PlatformAgreementBo bo, PageQuery pageQuery);

    /**
     * 查询平台协议配置列表
     */
    List<PlatformAgreement> queryList(PlatformAgreementBo bo);

    /**
     * 新增平台协议配置
     */
    Boolean insertByBo(PlatformAgreementBo bo);

    /**
     * 修改平台协议配置
     */
    Boolean updateByBo(PlatformAgreementBo bo);

    /**
     * 校验并批量删除平台协议配置信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
