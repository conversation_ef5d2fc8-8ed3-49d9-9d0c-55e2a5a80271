package com.ruoyi.mall.service;

import com.github.yulichang.base.MPJBaseService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.ShopAdvertisement;


public interface IShopAdvertisementService extends MPJBaseService<ShopAdvertisement> {

    /**
     * 新增商品广告
     *
     * @param shopAdvertisement
     * @return
     */
    Boolean addShopAdvertisement(ShopAdvertisement shopAdvertisement);

    /**
     * 删除商品广告
     *
     * @param id
     * @return
     */
    Integer deleteShopAdvertisement(Long id);

    /**
     * 批量删除广告ID集合
     *
     * @param ids id集合
     * @return
     */
    Integer deleteBatchShopAdvertisement(Long[] ids);

    /**
     * 根据ID查询商铺广告详情
     *
     * @param id
     * @return
     */
    ShopAdvertisement selectShopAdvertisementById(Long id);

    /**
     * 当前商铺分页查询广告
     *
     * @param shopAdvertisement 商铺广告对象
     * @param pageQuery         分页参数
     * @return
     */
    TableDataInfo<ShopAdvertisement> selectShopAdvertisementList(ShopAdvertisement shopAdvertisement, PageQuery pageQuery);

    /**
     * 修改商铺广告
     *
     * @param shopAdvertisement 商铺广告对象
     * @return
     */
    Integer updateShopAdvertisement(ShopAdvertisement shopAdvertisement);

    /**
     * 平台查询店铺广告列表
     *
     * @param shopAdvertisement 查询条件
     * @param pageQuery         分页参数
     * @return
     */
    TableDataInfo<ShopAdvertisement> selectAdvertisementList(ShopAdvertisement shopAdvertisement, PageQuery pageQuery);


    /**
     * app展示的店铺广告
     *
     * @param pageQuery 分页参数
     * @return
     */
    TableDataInfo<ShopAdvertisement> getAppShopAdvertisementList(Integer type, PageQuery pageQuery);


    /**
     * 店铺广告点击次数+1
     *
     * @param id
     * @return
     */
    Integer updateShopAdvertisementClickNumber(Long id);

    /**
     * 审核广告
     *
     * @param id     广告ID
     * @param status 审核状态：1-通过(待支付)，2-已生效，0-拒绝
     * @param remark 审核备注
     * @return 操作结果
     */
    Integer auditAdvertisement(Long id, String status, String remark);

    /**
     * 批量审核广告
     *
     * @param ids    广告ID集合
     * @param status 审核状态：1-通过(待支付)，2-已生效，0-拒绝
     * @param remark 审核备注
     * @return 操作结果
     */
    Integer batchAuditAdvertisement(Long[] ids, String status, String remark);

    /**
     * 获取随机的广告
     *
     * @return
     */
    ShopAdvertisement getAdvertisementRand();

}
