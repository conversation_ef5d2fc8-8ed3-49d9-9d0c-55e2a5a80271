package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.entity.UserComponentRecord;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户分量记录Service接口
 */
public interface IUserComponentRecordService extends IService<UserComponentRecord> {

    /**
     * 查询用户分量记录列表
     *
     * @param userComponentRecord 用户分量记录
     * @return 用户分量记录集合
     */
    List<UserComponentRecord> selectUserComponentRecordList(UserComponentRecord userComponentRecord);

    /**
     * 查询用户分量记录分页列表
     *
     * @param userComponentRecord 用户分量记录
     * @param pageQuery           分页参数
     * @return 用户分量记录分页集合
     */
    TableDataInfo<UserComponentRecord> selectUserComponentRecordPage(UserComponentRecord userComponentRecord, PageQuery pageQuery);

    /**
     * 添加用户分量记录
     *
     * @param userId         用户ID
     * @param componentValue 分量值
     * @param recordType     记录类型
     * @param businessId     业务ID
     * @param businessNo     业务编号
     * @param description    描述
     * @param operator       操作人
     * @return 结果
     */
    boolean addComponentRecord(Long userId, BigDecimal componentValue, Integer recordType,
                               Long businessId, String businessNo, String description, String operator);

    /**
     * 查询用户某段时间内获取的分量总和
     *
     * @param userId    用户ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 分量总和
     */
    BigDecimal sumUserComponent(Long userId, Date startDate, Date endDate);

    /**
     * 查询用户分量记录统计数据
     *
     * @param userId 用户ID
     * @return 统计数据
     */
    Map<String, Object> getUserComponentStatistics(Long userId);

    /**
     * 查询用户每日分量统计
     *
     * @param date      统计日期
     * @param username  用户名
     * @param phone     手机号
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    TableDataInfo<TzUser> getDailyStatistics(String date, String username, String phone, PageQuery pageQuery);
}
