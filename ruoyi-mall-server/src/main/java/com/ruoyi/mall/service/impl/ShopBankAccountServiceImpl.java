package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.dto.ShopBankAccountDTO;
import com.ruoyi.mall.domain.entity.ShopBankAccount;
import com.ruoyi.mall.mapper.ShopBankAccountMapper;
import com.ruoyi.mall.service.IShopBankAccountService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 商家银行账户Service业务层处理
 */
@RequiredArgsConstructor
@Service
public class ShopBankAccountServiceImpl extends ServiceImpl<ShopBankAccountMapper, ShopBankAccount> implements IShopBankAccountService {

    @Override
    public List<ShopBankAccount> selectShopBankAccountList(ShopBankAccount shopBankAccount) {
        LambdaQueryWrapper<ShopBankAccount> lqw = buildQueryWrapper(shopBankAccount);
        return list(lqw);
    }

    @Override
    public TableDataInfo<ShopBankAccount> selectShopBankAccountPage(ShopBankAccount shopBankAccount, PageQuery pageQuery) {
        LambdaQueryWrapper<ShopBankAccount> lqw = buildQueryWrapper(shopBankAccount);
        lqw.orderByDesc(ShopBankAccount::getIsDefault).orderByDesc(ShopBankAccount::getCreateTime);
        Page<ShopBankAccount> page = page(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addShopBankAccount(ShopBankAccountDTO dto, Long shopId) {
        // 校验银行账号是否已存在
        checkBankAccountNumberUnique(dto.getBankAccountNumber(), null, shopId);

        ShopBankAccount shopBankAccount = new ShopBankAccount();
        BeanUtils.copyProperties(dto, shopBankAccount);
        shopBankAccount.setShopId(shopId);
        shopBankAccount.setDelFlag("0");

        // 如果是默认账户，需要将其他账户设为非默认
        if ("1".equals(dto.getIsDefault())) {
            updateOtherAccountNotDefault(shopId);
        } else {
            // 如果是首个账户，自动设为默认
            long count = count(new LambdaQueryWrapper<ShopBankAccount>()
                .eq(ShopBankAccount::getShopId, shopId)
                .eq(ShopBankAccount::getDelFlag, "0"));
            if (count == 0) {
                shopBankAccount.setIsDefault("1");
            } else {
                shopBankAccount.setIsDefault(StringUtils.isBlank(dto.getIsDefault()) ? "0" : dto.getIsDefault());
            }
        }

        return save(shopBankAccount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateShopBankAccount(ShopBankAccountDTO dto, Long shopId) {
        // 校验账户是否存在
        ShopBankAccount existAccount = getById(dto.getId());
        if (existAccount == null || !shopId.equals(existAccount.getShopId())) {
            throw new ServiceException("银行账户不存在");
        }

        // 校验银行账号是否已存在(排除自身)
        checkBankAccountNumberUnique(dto.getBankAccountNumber(), dto.getId(), shopId);

        ShopBankAccount shopBankAccount = new ShopBankAccount();
        BeanUtils.copyProperties(dto, shopBankAccount);

        // 如果是默认账户，需要将其他账户设为非默认
        if ("1".equals(dto.getIsDefault()) && !"1".equals(existAccount.getIsDefault())) {
            updateOtherAccountNotDefault(shopId);
        }

        return updateById(shopBankAccount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteShopBankAccount(Long id, Long shopId) {
        // 校验账户是否存在
        ShopBankAccount account = getById(id);
        if (account == null || !shopId.equals(account.getShopId())) {
            throw new ServiceException("银行账户不存在");
        }

        // 如果是默认账户，且还有其他账户，则需要设置一个新的默认账户
        if ("1".equals(account.getIsDefault())) {
            List<ShopBankAccount> accounts = list(new LambdaQueryWrapper<ShopBankAccount>()
                .eq(ShopBankAccount::getShopId, shopId)
                .eq(ShopBankAccount::getDelFlag, "0")
                .ne(ShopBankAccount::getId, id)
                .orderByDesc(ShopBankAccount::getCreateTime)
                .last("LIMIT 1"));

            if (!accounts.isEmpty()) {
                ShopBankAccount newDefault = accounts.get(0);
                newDefault.setIsDefault("1");
                updateById(newDefault);
            }
        }

        // 逻辑删除
        return lambdaUpdate()
            .set(ShopBankAccount::getDelFlag, "2")
            .eq(ShopBankAccount::getId, id)
            .eq(ShopBankAccount::getShopId, shopId)
            .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDefaultAccount(Long id, Long shopId) {
        // 校验账户是否存在
        ShopBankAccount account = getById(id);
        if (account == null || !shopId.equals(account.getShopId()) || "2".equals(account.getDelFlag())) {
            throw new ServiceException("银行账户不存在");
        }

        // 如果已经是默认账户，则无需操作
        if ("1".equals(account.getIsDefault())) {
            return true;
        }

        // 将其他账户设为非默认
        updateOtherAccountNotDefault(shopId);

        // 设置当前账户为默认
        account.setIsDefault("1");
        return updateById(account);
    }

    /**
     * 将商家的其他银行账户设为非默认
     *
     * @param shopId 商家ID
     */
    private void updateOtherAccountNotDefault(Long shopId) {
        lambdaUpdate()
            .set(ShopBankAccount::getIsDefault, "0")
            .eq(ShopBankAccount::getShopId, shopId)
            .eq(ShopBankAccount::getIsDefault, "1")
            .update();
    }

    /**
     * 校验银行账号是否唯一
     *
     * @param bankAccountNumber 银行账号
     * @param id                账户ID
     * @param shopId            商家ID
     */
    private void checkBankAccountNumberUnique(String bankAccountNumber, Long id, Long shopId) {
        LambdaQueryWrapper<ShopBankAccount> lqw = new LambdaQueryWrapper<>();
        lqw.eq(ShopBankAccount::getBankAccountNumber, bankAccountNumber)
            .eq(ShopBankAccount::getShopId, shopId)
            .eq(ShopBankAccount::getDelFlag, "0");

        if (id != null) {
            lqw.ne(ShopBankAccount::getId, id);
        }

        if (count(lqw) > 0) {
            throw new ServiceException("该银行账号已被添加");
        }
    }

    private LambdaQueryWrapper<ShopBankAccount> buildQueryWrapper(ShopBankAccount shopBankAccount) {
        LambdaQueryWrapper<ShopBankAccount> lqw = new LambdaQueryWrapper<>();
        lqw.eq(shopBankAccount.getShopId() != null, ShopBankAccount::getShopId, shopBankAccount.getShopId())
            .like(StringUtils.isNotBlank(shopBankAccount.getBankAccountNumber()), ShopBankAccount::getBankAccountNumber, shopBankAccount.getBankAccountNumber())
            .like(StringUtils.isNotBlank(shopBankAccount.getBankAccountName()), ShopBankAccount::getBankAccountName, shopBankAccount.getBankAccountName())
            .like(StringUtils.isNotBlank(shopBankAccount.getBankBranchName()), ShopBankAccount::getBankBranchName, shopBankAccount.getBankBranchName())
            .like(StringUtils.isNotBlank(shopBankAccount.getAlipayAccount()), ShopBankAccount::getAlipayAccount, shopBankAccount.getAlipayAccount())
            .like(StringUtils.isNotBlank(shopBankAccount.getAlipayRealName()), ShopBankAccount::getAlipayRealName, shopBankAccount.getAlipayRealName())
            .like(StringUtils.isNotBlank(shopBankAccount.getWechatAccount()), ShopBankAccount::getWechatAccount, shopBankAccount.getWechatAccount())
            .eq(StringUtils.isNotBlank(shopBankAccount.getIsDefault()), ShopBankAccount::getIsDefault, shopBankAccount.getIsDefault())
            .eq(ShopBankAccount::getDelFlag, "0");
        return lqw;
    }
}
