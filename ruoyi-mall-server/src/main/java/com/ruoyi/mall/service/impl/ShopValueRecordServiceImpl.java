package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.dto.FaithStatisticsDTO;
import com.ruoyi.mall.domain.dto.QuantificationStatisticsDTO;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.ShopValueRecord;
import com.ruoyi.mall.mapper.ShopValueRecordMapper;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.mall.service.IShopValueRecordService;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商家量化值转换记录 Service 实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopValueRecordServiceImpl extends ServiceImpl<ShopValueRecordMapper, ShopValueRecord> implements IShopValueRecordService {

    private final IShopService shopService;
    private final ISysUserService sysUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopValueRecord convertQuantificationToFaith(Double quantificationAmount, Double rate) {
        // 1. 检查参数
        if (quantificationAmount == null || rate == null) {
            throw new ServiceException("参数不能为空");
        }
        if (quantificationAmount <= 0 || rate <= 0) {
            throw new ServiceException("转换数量和转换率必须大于0");
        }

        // 2. 查询信息
        SysUser sysUser = sysUserService.getById(1L);

        // 3. 检查量化值是否足够
        if (sysUser.getQuantificationValue() == null || sysUser.getQuantificationValue() < quantificationAmount) {
            throw new ServiceException("量化值不足");
        }

        // 4. 计算转换后的量化与损耗
        Double faithAmount = quantificationAmount * rate;
        Double lossAmount = quantificationAmount * (1 - rate);

        // 5. 更新商家信息
        Double oldQuantificationValue = sysUser.getQuantificationValue();
        Double oldUserFaith = sysUser.getUserFaith() != null ? sysUser.getUserFaith() : 0.0;
        sysUser.setQuantificationValue(oldQuantificationValue - quantificationAmount);
        sysUser.setUserFaith(oldUserFaith + faithAmount);

        if (!sysUserService.updateById(sysUser)) {
            throw new ServiceException("更新商家信息失败");
        }

        // 6. 创建实际获得的转换记录
        ShopValueRecord record = new ShopValueRecord();
        record.setOperationType(1); // 量化值转量化
        record.setBeforeQuantificationValue(oldQuantificationValue);
        record.setAfterQuantificationValue(sysUser.getQuantificationValue());
        record.setQuantificationValueAmount(quantificationAmount);
        record.setBeforeUserFaith(oldUserFaith);
        record.setAfterUserFaith(sysUser.getUserFaith());
        record.setUserFaithAmount(faithAmount);
        record.setConversionRate(rate);
        record.setRemark("量化值转换为量化");

        if (!save(record)) {
            throw new ServiceException("保存转换记录失败");
        }

        // 7. 创建损耗的转换记录
        ShopValueRecord lossRecord = new ShopValueRecord();
        lossRecord.setOperationType(3); // 量化值转量化损耗
        lossRecord.setBeforeQuantificationValue(oldQuantificationValue);
        lossRecord.setAfterQuantificationValue(sysUser.getQuantificationValue());
        lossRecord.setQuantificationValueAmount(lossAmount);
        lossRecord.setBeforeUserFaith(oldUserFaith);
        lossRecord.setAfterUserFaith(sysUser.getUserFaith());
        lossRecord.setUserFaithAmount(0.0); // 损耗记录实际获得的量化为0
        lossRecord.setConversionRate(1 - rate);
        lossRecord.setRemark("量化值转换为量化的损耗");

        if (!save(lossRecord)) {
            throw new ServiceException("保存损耗记录失败");
        }

        return record;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopValueRecord convertQuantificationToFaith(Long shopId, Double quantificationAmount, Double rate) {
        // 1. 检查参数
        if (shopId == null || quantificationAmount == null || rate == null) {
            throw new ServiceException("参数不能为空");
        }
        if (quantificationAmount <= 0 || rate <= 0) {
            throw new ServiceException("转换数量和转换率必须大于0");
        }

        // 2. 查询商家信息
        Shop shop = shopService.getById(shopId);
        if (shop == null) {
            throw new ServiceException("商家不存在");
        }

        // 3. 检查量化值是否足够
        if (shop.getQuantificationValue() == null || shop.getQuantificationValue() < quantificationAmount) {
            throw new ServiceException("量化值不足");
        }

        // 4. 计算转换后的量化与损耗
        Double faithAmount = quantificationAmount * rate;
        Double lossAmount = quantificationAmount * (1 - rate);

        // 5. 更新商家信息
        Double oldQuantificationValue = shop.getQuantificationValue();
        Double oldUserFaith = shop.getUserFaith() != null ? shop.getUserFaith() : 0.0;
        shop.setQuantificationValue(oldQuantificationValue - quantificationAmount);
        shop.setUserFaith(oldUserFaith + faithAmount);
        // 更新累计量化
        Double oldAltogetherUserFaith = shop.getAltogetherUserFaith() != null ? shop.getAltogetherUserFaith() : 0.0;
        shop.setAltogetherUserFaith(oldAltogetherUserFaith + faithAmount);

        if (!shopService.updateById(shop)) {
            throw new ServiceException("更新商家信息失败");
        }

        // 6. 创建实际获得的转换记录
        ShopValueRecord record = new ShopValueRecord();
        record.setShopId(shopId);
        record.setOperationType(1); // 量化值转量化
        record.setBeforeQuantificationValue(oldQuantificationValue);
        record.setAfterQuantificationValue(shop.getQuantificationValue());
        record.setQuantificationValueAmount(quantificationAmount);
        record.setBeforeUserFaith(oldUserFaith);
        record.setAfterUserFaith(shop.getUserFaith());
        record.setUserFaithAmount(faithAmount);
        record.setConversionRate(rate);
        record.setRemark("量化值转换为量化");

        if (!save(record)) {
            throw new ServiceException("保存转换记录失败");
        }

        // 7. 创建损耗的转换记录
        ShopValueRecord lossRecord = new ShopValueRecord();
        lossRecord.setShopId(shopId);
        lossRecord.setOperationType(3); // 量化值转量化损耗
        lossRecord.setBeforeQuantificationValue(oldQuantificationValue);
        lossRecord.setAfterQuantificationValue(shop.getQuantificationValue());
        lossRecord.setQuantificationValueAmount(lossAmount);
        lossRecord.setBeforeUserFaith(oldUserFaith);
        lossRecord.setAfterUserFaith(shop.getUserFaith());
        lossRecord.setUserFaithAmount(0.0); // 损耗记录实际获得的量化为0
        lossRecord.setConversionRate(1 - rate);
        lossRecord.setRemark("量化值转换为量化的损耗");

        if (!save(lossRecord)) {
            throw new ServiceException("保存损耗记录失败");
        }

        return record;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopValueRecord convertFaithToPromotionGold(Double faithAmount, Double rate) {
        // 1. 检查参数
        if (faithAmount == null || rate == null) {
            throw new ServiceException("参数不能为空");
        }
        if (faithAmount <= 0 || rate <= 0) {
            throw new ServiceException("转换数量和转换率必须大于0");
        }

        // 2. 查询商家信息
        SysUser sysUser = sysUserService.getById(1L);

        // 3. 检查量化是否足够
        if (sysUser.getUserFaith() == null || sysUser.getUserFaith() < faithAmount) {
            throw new ServiceException("量化不足");
        }

        // 4. 计算转换后的平台促销金和损耗
        double promotionGoldAmount = faithAmount * rate;
        Double lossAmount = faithAmount * (1 - rate);

        // 5. 更新商家信息
        Double oldUserFaith = sysUser.getUserFaith();
        BigDecimal oldPromotionGold = sysUser.getPlatformPromotionGold() != null ? sysUser.getPlatformPromotionGold() : BigDecimal.ZERO;
        sysUser.setUserFaith(oldUserFaith - faithAmount);
        sysUser.setPlatformPromotionGold(oldPromotionGold.add(BigDecimal.valueOf(promotionGoldAmount)));

        if (!sysUserService.updateById(sysUser)) {
            throw new ServiceException("更新商家信息失败");
        }

        // 6. 创建实际获得的转换记录
        ShopValueRecord record = new ShopValueRecord();
        record.setOperationType(2); // 量化转平台促销金
        record.setBeforeUserFaith(oldUserFaith);
        record.setAfterUserFaith(sysUser.getUserFaith());
        record.setUserFaithAmount(faithAmount);
        record.setBeforePromotionGold(oldPromotionGold);
        record.setAfterPromotionGold(sysUser.getPlatformPromotionGold());
        record.setPromotionGoldAmount(promotionGoldAmount);
        record.setConversionRate(rate);
        record.setRemark("量化转换为平台促销金");

        if (!save(record)) {
            throw new ServiceException("保存转换记录失败");
        }

        // 7. 创建损耗的转换记录
        ShopValueRecord lossRecord = new ShopValueRecord();
        lossRecord.setOperationType(4); // 量化转平台促销金损耗
        lossRecord.setBeforeUserFaith(oldUserFaith);
        lossRecord.setAfterUserFaith(sysUser.getUserFaith());
        lossRecord.setUserFaithAmount(lossAmount);
        lossRecord.setBeforePromotionGold(oldPromotionGold);
        lossRecord.setAfterPromotionGold(sysUser.getPlatformPromotionGold());
        lossRecord.setPromotionGoldAmount(0.0); // 损耗记录实际获得的促销金为0
        lossRecord.setConversionRate(1 - rate);
        lossRecord.setRemark("量化转换为平台促销金的损耗");

        if (!save(lossRecord)) {
            throw new ServiceException("保存损耗记录失败");
        }

        return record;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ShopValueRecord convertFaithToPromotionGold(Long shopId, Double faithAmount, Double rate) {
        // 1. 检查参数
        if (shopId == null || faithAmount == null || rate == null) {
            throw new ServiceException("参数不能为空");
        }
        if (faithAmount <= 0 || rate <= 0) {
            throw new ServiceException("转换数量和转换率必须大于0");
        }

        // 2. 查询商家信息
        Shop shop = shopService.getById(shopId);
        if (shop == null) {
            throw new ServiceException("商家不存在");
        }

        // 3. 检查量化是否足够
        if (shop.getUserFaith() == null || shop.getUserFaith() < faithAmount) {
            throw new ServiceException("量化不足");
        }

        // 4. 计算转换后的平台促销金和损耗
        double promotionGoldAmount = faithAmount * rate;
        Double lossAmount = faithAmount * (1 - rate);

        // 5. 更新商家信息
        Double oldUserFaith = shop.getUserFaith();
        BigDecimal oldPromotionGold = shop.getPlatformPromotionGold() != null ? shop.getPlatformPromotionGold() : BigDecimal.ZERO;
        shop.setUserFaith(oldUserFaith - faithAmount);
        shop.setPlatformPromotionGold(oldPromotionGold.add(BigDecimal.valueOf(promotionGoldAmount)));

        if (!shopService.updateById(shop)) {
            throw new ServiceException("更新商家信息失败");
        }

        // 6. 创建实际获得的转换记录
        ShopValueRecord record = new ShopValueRecord();
        record.setShopId(shopId);
        record.setOperationType(2); // 量化转平台促销金
        record.setBeforeUserFaith(oldUserFaith);
        record.setAfterUserFaith(shop.getUserFaith());
        record.setUserFaithAmount(faithAmount);
        record.setBeforePromotionGold(oldPromotionGold);
        record.setAfterPromotionGold(shop.getPlatformPromotionGold());
        record.setPromotionGoldAmount(promotionGoldAmount);
        record.setConversionRate(rate);
        record.setRemark("量化转换为平台促销金");

        if (!save(record)) {
            throw new ServiceException("保存转换记录失败");
        }

        // 7. 创建损耗的转换记录
        ShopValueRecord lossRecord = new ShopValueRecord();
        lossRecord.setShopId(shopId);
        lossRecord.setOperationType(4); // 量化转平台促销金损耗
        lossRecord.setBeforeUserFaith(oldUserFaith);
        lossRecord.setAfterUserFaith(shop.getUserFaith());
        lossRecord.setUserFaithAmount(lossAmount);
        lossRecord.setBeforePromotionGold(oldPromotionGold);
        lossRecord.setAfterPromotionGold(shop.getPlatformPromotionGold());
        lossRecord.setPromotionGoldAmount(0.0); // 损耗记录实际获得的促销金为0
        lossRecord.setConversionRate(1 - rate);
        lossRecord.setRemark("量化转换为平台促销金的损耗");

        if (!save(lossRecord)) {
            throw new ServiceException("保存损耗记录失败");
        }

        return record;
    }

    @Override
    public TableDataInfo<QuantificationStatisticsDTO> statisticsQuantificationByTimePage(String date, String phone, Integer operationType, PageQuery pageQuery) {
        // 设置默认日期为当天
        if (StringUtils.isEmpty(date)) {
            LocalDate today = LocalDate.now();
            date = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        // 构建查询开始和结束时间
        String beginTime = date + " 00:00:00";
        String endTime = date + " 23:59:59";

        // 先查询所有商家
        LambdaQueryWrapper<Shop> shopQuery = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(phone)) {
            shopQuery.like(Shop::getPhone, phone);
        }
        Page<Shop> page = shopService.page(pageQuery.build(), shopQuery);

        // 如果没有找到商家，返回空结果
        if (page.getTotal() == 0L) {
            return TableDataInfo.build(new ArrayList<>());
        }
        // 获取店铺列表
        List<Shop> shops = page.getRecords();

        // 获取所有商家ID
        List<Long> shopIds = shops.stream().map(Shop::getId).collect(Collectors.toList());

        // 构建查询条件
        LambdaQueryWrapper<ShopValueRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(operationType != null, ShopValueRecord::getOperationType, operationType)
            .ge(ShopValueRecord::getCreateTime, beginTime)
            .le(ShopValueRecord::getCreateTime, endTime)
            .in(ShopValueRecord::getShopId, shopIds)
            .orderByDesc(ShopValueRecord::getCreateTime);

        // 查询所有符合条件的记录
        List<ShopValueRecord> records = list(lqw);

        // 按店铺分组
        Map<Long, List<ShopValueRecord>> shopRecords = records.stream()
            .collect(Collectors.groupingBy(ShopValueRecord::getShopId));

        // 生成结果列表，确保每个商家都有记录
        List<QuantificationStatisticsDTO> result = new ArrayList<>();

        for (Shop shop : shops) {
            Long shopId = shop.getId();

            // 计算当天转化总量
            double dayConvertAmount = 0.0;
            List<ShopValueRecord> shopDayRecords = shopRecords.get(shopId);
            if (shopDayRecords != null && !shopDayRecords.isEmpty()) {
                dayConvertAmount = shopDayRecords.stream()
                    .mapToDouble(
                        item -> Optional.ofNullable(item.getQuantificationValueAmount()).orElse(0.0)
                    )
                    .sum();
            }

            // 计算该店铺所有转化总量
            LambdaQueryWrapper<ShopValueRecord> totalQuery = new LambdaQueryWrapper<>();
            totalQuery.eq(ShopValueRecord::getShopId, shopId)
                .eq(ShopValueRecord::getOperationType, operationType);

            QuantificationStatisticsDTO item = new QuantificationStatisticsDTO();
            item.setDate(date);
            item.setPhone(shop.getPhone());
            item.setShopName(shop.getName());
            item.setLegalPerson(shop.getLegalPerson());
            item.setDayConvertAmount(dayConvertAmount);

            result.add(item);
        }

        TableDataInfo<QuantificationStatisticsDTO> tableDataInfo = new TableDataInfo<>();
        tableDataInfo.setRows(result);
        tableDataInfo.setTotal(page.getTotal());
        tableDataInfo.setCode(200);
        tableDataInfo.setMsg("查询成功");
        return tableDataInfo;
    }

    @Override
    public List<QuantificationStatisticsDTO> exportQuantificationStatistics(String date, String phone, Integer operationType) {
        // 设置默认日期为当天
        if (StringUtils.isEmpty(date)) {
            LocalDate today = LocalDate.now();
            date = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        // 构建查询开始和结束时间
        String beginTime = date + " 00:00:00";
        String endTime = date + " 23:59:59";

        // 查询所有商家
        LambdaQueryWrapper<Shop> shopQuery = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(phone)) {
            shopQuery.like(Shop::getPhone, phone);
        }
        List<Shop> shops = shopService.list(shopQuery);

        // 如果没有找到商家，返回空结果
        if (shops == null || shops.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取所有商家ID
        List<Long> shopIds = shops.stream().map(Shop::getId).collect(Collectors.toList());

        // 构建查询条件
        LambdaQueryWrapper<ShopValueRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(operationType != null, ShopValueRecord::getOperationType, operationType)
            .ge(ShopValueRecord::getCreateTime, beginTime)
            .le(ShopValueRecord::getCreateTime, endTime)
            .in(ShopValueRecord::getShopId, shopIds)
            .orderByDesc(ShopValueRecord::getCreateTime);

        // 查询所有符合条件的记录
        List<ShopValueRecord> records = list(lqw);

        // 按店铺分组
        Map<Long, List<ShopValueRecord>> shopRecords = records.stream()
            .collect(Collectors.groupingBy(ShopValueRecord::getShopId));

        // 生成结果列表，确保每个商家都有记录
        List<QuantificationStatisticsDTO> result = new ArrayList<>();

        for (Shop shop : shops) {
            Long shopId = shop.getId();

            // 计算当天转化总量
            double dayConvertAmount = 0.0;
            List<ShopValueRecord> shopDayRecords = shopRecords.get(shopId);
            if (shopDayRecords != null && !shopDayRecords.isEmpty()) {
                dayConvertAmount = shopDayRecords.stream()
                    .mapToDouble(item -> Optional.ofNullable(item.getQuantificationValueAmount()).orElse(0.0))
                    .sum();
            }


            QuantificationStatisticsDTO item = new QuantificationStatisticsDTO();
            item.setDate(date);
            item.setPhone(shop.getPhone());
            item.setShopName(shop.getName());
            item.setLegalPerson(shop.getLegalPerson());
            item.setDayConvertAmount(dayConvertAmount);

            result.add(item);
        }

        return result;
    }

    @Override
    public List<FaithStatisticsDTO> exportFaithStatistics(String date, String phone, Integer operationType) {
        // 设置默认日期为当天
        if (StringUtils.isEmpty(date)) {
            LocalDate today = LocalDate.now();
            date = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        // 构建查询开始和结束时间
        String beginTime = date + " 00:00:00";
        String endTime = date + " 23:59:59";

        // 查询所有商家
        LambdaQueryWrapper<Shop> shopQuery = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(phone)) {
            shopQuery.like(Shop::getPhone, phone);
        }
        List<Shop> shops = shopService.list(shopQuery);

        // 如果没有找到商家，返回空结果
        if (shops == null || shops.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取所有商家ID
        List<Long> shopIds = shops.stream().map(Shop::getId).collect(Collectors.toList());

        // 构建查询条件
        LambdaQueryWrapper<ShopValueRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(operationType != null, ShopValueRecord::getOperationType, operationType)
            .ge(ShopValueRecord::getCreateTime, beginTime)
            .le(ShopValueRecord::getCreateTime, endTime)
            .in(ShopValueRecord::getShopId, shopIds)
            .orderByDesc(ShopValueRecord::getCreateTime);

        // 查询所有符合条件的记录
        List<ShopValueRecord> records = list(lqw);

        // 按店铺分组
        Map<Long, List<ShopValueRecord>> shopRecords = records.stream()
            .collect(Collectors.groupingBy(ShopValueRecord::getShopId));

        // 优化：批量查询所有店铺的总转化量
        Map<Long, Double> shopTotalAmounts = new HashMap<>();
        // 使用SQL查询每个商家的总转化量，避免多次查询
        List<Map<String, Object>> totalAmountsList = baseMapper.selectMaps(
            new QueryWrapper<ShopValueRecord>()
                .select("shop_id, SUM(user_faith_amount) as total_amount")
                .eq("operation_type", operationType)
                .in("shop_id", shopIds)
                .groupBy("shop_id")
        );

        // 将查询结果转换为Map
        for (Map<String, Object> map : totalAmountsList) {
            Long shopId = ((Number) map.get("shop_id")).longValue();
            Double totalAmount = ((Number) map.get("total_amount")).doubleValue();
            shopTotalAmounts.put(shopId, totalAmount);
        }

        // 生成结果列表，确保每个商家都有记录
        List<FaithStatisticsDTO> result = new ArrayList<>();

        for (Shop shop : shops) {
            Long shopId = shop.getId();

            // 计算当天转化总量
            double dayFaithAmount = 0.0;
            List<ShopValueRecord> shopDayRecords = shopRecords.get(shopId);
            if (shopDayRecords != null && !shopDayRecords.isEmpty()) {
                dayFaithAmount = shopDayRecords.stream()
                    .mapToDouble(ShopValueRecord::getUserFaithAmount)
                    .sum();
            }

            FaithStatisticsDTO item = new FaithStatisticsDTO();
            item.setDate(date);
            item.setPhone(shop.getPhone());
            item.setShopName(shop.getName());
            item.setLegalPerson(shop.getLegalPerson());
            item.setDayFaithAmount(dayFaithAmount);
            result.add(item);
        }

        return result;
    }

    @Override
    public TableDataInfo<FaithStatisticsDTO> statisticsFaithByTimePage(String date, String phone, Integer operationType, PageQuery pageQuery) {
        // 设置默认日期为当天
        if (StringUtils.isEmpty(date)) {
            LocalDate today = LocalDate.now();
            date = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        // 构建查询开始和结束时间
        String beginTime = date + " 00:00:00";
        String endTime = date + " 23:59:59";

        // 先查询所有商家
        LambdaQueryWrapper<Shop> shopQuery = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(phone)) {
            shopQuery.like(Shop::getPhone, phone);
        }
        Page<Shop> page = shopService.page(pageQuery.build(), shopQuery);

        // 如果没有找到商家，返回空结果
        if (page.getTotal() == 0L) {
            return TableDataInfo.build(new ArrayList<>());
        }
        // 获取店铺列表
        List<Shop> shops = page.getRecords();

        // 获取所有商家ID
        List<Long> shopIds = shops.stream().map(Shop::getId).collect(Collectors.toList());

        // 构建查询条件
        LambdaQueryWrapper<ShopValueRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(operationType != null, ShopValueRecord::getOperationType, operationType)
            .ge(ShopValueRecord::getCreateTime, beginTime)
            .le(ShopValueRecord::getCreateTime, endTime)
            .in(ShopValueRecord::getShopId, shopIds)
            .orderByDesc(ShopValueRecord::getCreateTime);

        // 查询所有符合条件的记录
        List<ShopValueRecord> records = list(lqw);

        // 按店铺分组
        Map<Long, List<ShopValueRecord>> shopRecords = records.stream()
            .collect(Collectors.groupingBy(ShopValueRecord::getShopId));

        // 生成结果列表，确保每个商家都有记录
        List<FaithStatisticsDTO> result = new ArrayList<>();

        for (Shop shop : shops) {
            Long shopId = shop.getId();

            // 计算当天转化总量
            double dayFaithAmount = 0.0;
            List<ShopValueRecord> shopDayRecords = shopRecords.get(shopId);
            if (shopDayRecords != null && !shopDayRecords.isEmpty()) {
                dayFaithAmount = shopDayRecords.stream()
                    .mapToDouble(
                        item -> Optional.ofNullable(item.getUserFaithAmount()).orElse(0.0))
                    .sum();
            }

            FaithStatisticsDTO item = new FaithStatisticsDTO();
            item.setDate(date);
            item.setPhone(shop.getPhone());
            item.setShopName(shop.getName());
            item.setLegalPerson(shop.getLegalPerson());
            item.setDayFaithAmount(dayFaithAmount);

            result.add(item);
        }

        TableDataInfo<FaithStatisticsDTO> tableDataInfo = new TableDataInfo<>();
        tableDataInfo.setRows(result);
        tableDataInfo.setTotal(page.getTotal());
        tableDataInfo.setCode(200);
        tableDataInfo.setMsg("查询成功");
        return tableDataInfo;
    }

    @Override
    public QuantificationStatisticsDTO adminQuantification(String date, String phone, Integer operationType) {

        // 获取admin的账号信息
        SysUser sysUser = sysUserService.getById(1L);

        // 设置默认日期为当天
        if (StringUtils.isEmpty(date)) {
            LocalDate today = LocalDate.now();
            date = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        // 构建查询开始和结束时间
        String beginTime = date + " 00:00:00";
        String endTime = date + " 23:59:59";

        // 构建查询条件
        LambdaQueryWrapper<ShopValueRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(operationType != null, ShopValueRecord::getOperationType, operationType)
            .ge(ShopValueRecord::getCreateTime, beginTime)
            .le(ShopValueRecord::getCreateTime, endTime)
            .isNull(ShopValueRecord::getShopId)
            .orderByDesc(ShopValueRecord::getCreateTime);

        // 查询所有符合条件的记录
        List<ShopValueRecord> shopDayRecords = list(lqw);

        // 计算当天转化总量
        double dayConvertAmount = 0.0;
        if (shopDayRecords != null && !shopDayRecords.isEmpty()) {
            dayConvertAmount = shopDayRecords.stream()
                .mapToDouble(item -> Optional.ofNullable(item.getQuantificationValueAmount()).orElse(0.0))
                .sum();
        }

        QuantificationStatisticsDTO item = new QuantificationStatisticsDTO();
        item.setDate(date);
        item.setPhone(sysUser.getPhonenumber());
        item.setShopName(sysUser.getUserName());
        item.setDayConvertAmount(dayConvertAmount);

        return item;
    }

    @Override
    public FaithStatisticsDTO adminFaith(String date, String phone, Integer operationType) {
        // 获取admin的账号信息
        SysUser sysUser = sysUserService.getById(1L);
        // 设置默认日期为当天
        if (StringUtils.isEmpty(date)) {
            LocalDate today = LocalDate.now();
            date = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }

        // 构建查询开始和结束时间
        String beginTime = date + " 00:00:00";
        String endTime = date + " 23:59:59";

        // 构建查询条件
        LambdaQueryWrapper<ShopValueRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(operationType != null, ShopValueRecord::getOperationType, operationType)
            .ge(ShopValueRecord::getCreateTime, beginTime)
            .le(ShopValueRecord::getCreateTime, endTime)
            .isNull(ShopValueRecord::getShopId)
            .orderByDesc(ShopValueRecord::getCreateTime);

        // 查询所有符合条件的记录
        List<ShopValueRecord> shopDayRecords = list(lqw);

        // 计算当天转化总量
        double dayFaithAmount = 0.0;
        if (shopDayRecords != null && !shopDayRecords.isEmpty()) {
            dayFaithAmount = shopDayRecords.stream()
                .mapToDouble(ShopValueRecord::getUserFaithAmount)
                .sum();
        }

        FaithStatisticsDTO item = new FaithStatisticsDTO();
        item.setDate(date);
        item.setPhone(sysUser.getPhonenumber());
        item.setShopName(sysUser.getUserName());
        item.setDayFaithAmount(dayFaithAmount);

        return item;
    }

    @Override
    public Map<String, Object> getDateQuantificationStats(String date) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 如果没有传入日期，使用当天日期
            if (StringUtils.isEmpty(date)) {
                date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }

            // 解析日期
            LocalDate queryDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDateTime startTime = queryDate.atStartOfDay();
            LocalDateTime endTime = queryDate.atTime(23, 59, 59);

            // 构建查询条件：查询指定日期的量化值进化记录
            LambdaQueryWrapper<ShopValueRecord> lqw = new LambdaQueryWrapper<>();
            lqw.eq(ShopValueRecord::getOperationType, 3) // 1：量化值转量化
                .isNotNull(ShopValueRecord::getShopId)
                .between(ShopValueRecord::getCreateTime, startTime, endTime)
                .eq(ShopValueRecord::getDelFlag, "0");

            // 查询所有符合条件的记录
            List<ShopValueRecord> records = list(lqw);

            // 计算总进化量（量化值转换的总量）
            double totalAmount = 0.0;
            if (records != null && !records.isEmpty()) {
                totalAmount = records.stream()
                    .mapToDouble(record -> record.getQuantificationValueAmount() != null ? record.getQuantificationValueAmount() : 0.0)
                    .sum();
            }

            result.put("totalAmount", totalAmount);
            log.info("获取日期 {} 的量化值进化统计成功，总进化量: {}", date, totalAmount);

        } catch (Exception e) {
            log.error("获取日期量化值进化统计失败，日期: {}", date, e);
            result.put("totalAmount", 0.0);
        }

        return result;
    }

    @Override
    public Map<String, Object> getDateFaithStats(String date) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 如果没有传入日期，使用当天日期
            if (StringUtils.isEmpty(date)) {
                date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }

            // 解析日期
            LocalDate queryDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDateTime startTime = queryDate.atStartOfDay();
            LocalDateTime endTime = queryDate.atTime(23, 59, 59);

            // 构建查询条件：查询指定日期的量化值进化记录
            LambdaQueryWrapper<ShopValueRecord> lqw = new LambdaQueryWrapper<>();
            lqw.eq(ShopValueRecord::getOperationType, 4) // 1：量化值转量化1：量化值转量化
                .isNotNull(ShopValueRecord::getShopId)
                .between(ShopValueRecord::getCreateTime, startTime, endTime)
                .eq(ShopValueRecord::getDelFlag, "0");

            // 查询所有符合条件的记录
            List<ShopValueRecord> records = list(lqw);

            // 计算总进化量（量化值转换的总量）
            double totalAmount = 0.0;
            if (records != null && !records.isEmpty()) {
                totalAmount = records.stream()
                    .mapToDouble(record -> record.getUserFaithAmount() != null ? record.getUserFaithAmount() : 0.0)
                    .sum();
            }

            result.put("totalAmount", totalAmount);
            log.info("获取日期 {} 的量化进化统计成功，总进化量: {}", date, totalAmount);

        } catch (Exception e) {
            log.error("获取日期量化值进化统计失败，日期: {}", date, e);
            result.put("totalAmount", 0.0);
        }

        return result;
    }
}
