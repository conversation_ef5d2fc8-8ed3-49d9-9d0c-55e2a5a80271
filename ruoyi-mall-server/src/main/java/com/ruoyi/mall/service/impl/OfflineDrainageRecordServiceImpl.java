package com.ruoyi.mall.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.TzUserStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.OfflineDrainageRecord;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.mapper.OfflineDrainageRecordMapper;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.mall.service.IConfigSettingService;
import com.ruoyi.mall.service.IOfflineDrainageRecordService;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.mall.utils.ServiceFeeCalculator;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 线下充值技术引流次数记录Service实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OfflineDrainageRecordServiceImpl extends ServiceImpl<OfflineDrainageRecordMapper, OfflineDrainageRecord> implements IOfflineDrainageRecordService {

    private final IShopService shopService;
    private final IConfigSettingService configSettingService;
    private final ISysUserService iSysUserService;
    private final TzUserMapper tzUserMapper;

    @Override
    public TableDataInfo<OfflineDrainageRecord> selectOfflineDrainageRecordPage(OfflineDrainageRecord record, PageQuery pageQuery) {
        LambdaQueryWrapper<OfflineDrainageRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(record.getShopId() != null, OfflineDrainageRecord::getShopId, record.getShopId());
        lqw.like(StringUtils.isNotBlank(record.getShopPhone()), OfflineDrainageRecord::getShopPhone, record.getShopPhone());
        lqw.like(StringUtils.isNotBlank(record.getShopName()), OfflineDrainageRecord::getShopName, record.getShopName());
        lqw.eq(record.getOperatorId() != null, OfflineDrainageRecord::getOperatorId, record.getOperatorId());
        lqw.like(StringUtils.isNotBlank(record.getOperatorName()), OfflineDrainageRecord::getOperatorName, record.getOperatorName());
        lqw.eq(StringUtils.isNotBlank(record.getStatus()), OfflineDrainageRecord::getStatus, record.getStatus());

        lqw.gt(DateUtils.parseDate(record.getBeginCreateTime()) != null, OfflineDrainageRecord::getCreateTime, DateUtils.parseDate(record.getBeginCreateTime()));
        lqw.le(DateUtils.parseDate(record.getEndCreateTime()) != null, OfflineDrainageRecord::getCreateTime, DateUtils.parseDate(record.getEndCreateTime()));

        lqw.orderByDesc(OfflineDrainageRecord::getCreateTime);

        Page<OfflineDrainageRecord> page = page(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    public Long createOfflineDrainageRecord(Long shopId, String shopPhone, String shopName, Integer count,
                                            Integer beforeCount, Integer afterCount,
                                            Long operatorId, String operatorName, String remark) {
        // 获取当时的技术引流费用
        JSONObject configValue = configSettingService.getConfigValue(ConfigSettingConstants.config.get(5));
        Double citationValue = configValue.getDouble("citationValue");


        OfflineDrainageRecord record = new OfflineDrainageRecord();
        record.setShopId(shopId);
        record.setShopPhone(shopPhone);
        record.setShopName(shopName);
        record.setCount(count);
        record.setCitationValue(citationValue);
        record.setCitationValueMoney(BigDecimal.valueOf(citationValue * count));
        record.setServiceFee(ServiceFeeCalculator.calculateVirtualOrderServiceFee(BigDecimal.valueOf(citationValue * count)));
        record.setBeforeCount(beforeCount);
        record.setAfterCount(afterCount);
        record.setOperatorId(operatorId);
        record.setOperatorName(operatorName);
        record.setRemark(remark);
        record.setStatus("0"); // 默认成功

        if (save(record)) {
            return record.getId();
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rechargeOfflineDrainage(Long shopId, Integer count, Long operatorId, String operatorName, String remark) {
        // 1. 参数校验
        if (shopId == null || count == null || count <= 0) {
            throw new ServiceException("参数不正确");
        }

        // 2. 获取商家信息
        Shop shop = shopService.getById(shopId);
        if (shop == null) {
            throw new ServiceException("商家不存在");
        }

        // 3. 获取当前技术引流次数
        Integer beforeCount = shop.getDrainage() != null ? shop.getDrainage() : 0;

        // 4. 增加技术引流次数
        boolean result = shopService.increaseDrainageCount(shopId, count);
        if (!result) {
            throw new ServiceException("增加技术引流次数失败");
        }

        // 5. 计算充值后的技术引流次数
        Integer afterCount = beforeCount + count;

        // 6. 记录充值操作
        createOfflineDrainageRecord(shopId, shop.getPhone(), shop.getBusinessName(), count, beforeCount, afterCount, operatorId, operatorName, remark);

        // 7. 如果用户的关系链状态是失效改为正常
        SysUser sysUser = iSysUserService.getById(shop.getUserId());
        Long tzUserId = sysUser.getTzUserId();
        if (tzUserId != null) {
            TzUser tzUser = tzUserMapper.selectOne(
                new LambdaQueryWrapper<TzUser>()
                    .eq(TzUser::getUserId, tzUserId)
                    .eq(TzUser::getDelFlag, "0")
            );

            if (tzUser == null) {
                log.error("技术引流支付成功，但商城用户不存在，tzUserId: {}", tzUserId);
            } else if (TzUserStatus.FAILURE.getCode().equals(tzUser.getStatus())) {
                // 如果用户是失效状态，则更新为正常状态
                tzUser.setStatus(TzUserStatus.OK.getCode());
                tzUserMapper.updateById(tzUser);
                log.info("技术引流支付成功，商家用户状态从失效改为正常，tzUserId: {}", tzUserId);
            }
        } else {
            log.error("技术引流支付成功，但系统用户没有关联商城用户ID，sysUserId: {}", sysUser.getUserId());
        }

        return true;
    }
}
