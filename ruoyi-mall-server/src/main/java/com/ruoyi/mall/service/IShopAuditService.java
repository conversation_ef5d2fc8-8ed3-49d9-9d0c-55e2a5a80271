package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.ApplyShopDTO;
import com.ruoyi.mall.domain.dto.ShopAuditDTO;
import com.ruoyi.mall.domain.entity.ShopAudit;

/**
 * 商家审核记录Service接口
 *
 * <AUTHOR>
 */
public interface IShopAuditService extends IService<ShopAudit> {

    /**
     * 查询商家审核记录列表
     *
     * @param shopAudit 商家审核记录
     * @param pageQuery 分页参数
     * @return 商家审核记录分页数据
     */
    TableDataInfo<ShopAudit> selectShopAuditPage(ShopAudit shopAudit, PageQuery pageQuery);

    TableDataInfo<ShopAudit> selectShopAuditRecordPage(ShopAudit shopAudit, PageQuery pageQuery);

    /**
     * 创建商家审核申请
     *
     * @param applyShopDTO 申请信息
     * @param userId       用户ID
     * @return 结果
     */
    boolean createShopAudit(ApplyShopDTO applyShopDTO, Long userId);

    /**
     * 审核商家申请
     *
     * @param shopAuditDTO 审核信息
     * @return 结果
     */
    boolean auditShop(ShopAuditDTO shopAuditDTO);

}
