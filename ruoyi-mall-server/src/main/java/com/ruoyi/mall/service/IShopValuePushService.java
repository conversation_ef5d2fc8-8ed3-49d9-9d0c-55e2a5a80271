package com.ruoyi.mall.service;

import com.ruoyi.mall.domain.bo.ReceiveQuantizedUserFaithBo;

import java.util.List;
import java.util.Map;

/**
 * 外部系统推送店铺量化值与量化服务接口
 */
public interface IShopValuePushService {

    /**
     * 批量处理外部系统推送的店铺量化值与量化
     *
     * @param shopValuePushDTOList 外部推送的值DTO列表
     * @return 处理结果，key为手机号，value为处理是否成功
     */
    Map<String, Boolean> handleBatchShopValuePush(List<ReceiveQuantizedUserFaithBo> shopValuePushDTOList);

    /**
     * 处理单个外部系统推送的店铺量化值与量化（批量处理内部调用）
     *
     * @param shopValuePushDTO 外部推送的值DTO
     * @return 处理是否成功
     */
    boolean handleShopValuePush(ReceiveQuantizedUserFaithBo shopValuePushDTO);
}
