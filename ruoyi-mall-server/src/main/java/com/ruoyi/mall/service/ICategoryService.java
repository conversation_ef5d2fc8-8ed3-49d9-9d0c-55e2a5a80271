package com.ruoyi.mall.service;

import com.github.yulichang.base.MPJBaseService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.Category;

import java.util.List;

/**
 * 商品分类Service接口
 *
 * <AUTHOR>
 */
public interface ICategoryService extends MPJBaseService<Category> {

    /**
     * 查询商品分类列表
     *
     * @param category 商品分类信息
     * @return 商品分类集合
     */
    List<Category> selectCategoryList(Category category);

    /**
     * 查询商品分类分页列表
     *
     * @param category  商品分类信息
     * @param pageQuery 分页参数
     * @return 商品分类分页数据
     */
    TableDataInfo<Category> selectCategoryPage(Category category, PageQuery pageQuery);

    /**
     * 查询商品详细信息
     *
     * @param id 商品ID
     * @return 商品信息
     */
    Category selectCategoryById(Long id);

    /**
     * 查询商品分类树结构
     *
     * @param category 商品分类信息
     * @return 商品分类树结构
     */
    List<Category> selectCategoryTree(Category category);

    /**
     * 新增商品分类
     *
     * @param category 商品分类信息
     * @return 结果
     */
    boolean insertCategory(Category category);

    /**
     * 修改商品分类
     *
     * @param category 商品分类信息
     * @return 结果
     */
    boolean updateCategory(Category category);

    /**
     * 批量删除商品分类
     *
     * @param ids 需要删除的商品分类ID
     * @return 结果
     */
    void deleteCategoryByIds(Long[] ids);

    /**
     * 删除商品分类信息
     *
     * @param id 商品分类ID
     * @return 结果
     */
    boolean deleteCategoryById(Long id);

    /**
     * 根据当前分类ID查询所有子分类ID包括自己
     *
     * @param categoryId
     * @return
     */
    List<Long> getCategoryIdByCategoryId(Long categoryId);
}
