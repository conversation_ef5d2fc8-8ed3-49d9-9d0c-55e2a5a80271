package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.enums.OrderVirtualStatusEnum;
import com.ruoyi.common.enums.TzUserStatus;
import com.ruoyi.mall.domain.entity.OfflineDrainageRecord;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.enums.VirtualTypeEnum;
import com.ruoyi.mall.service.*;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 商家技术引流服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopDrainageServiceImpl implements IShopDrainageService {

    private final IShopService shopService;
    private final IVirtualOrderService virtualOrderService;
    private final ITzUserService tzUserService;
    private final SysUserMapper sysUserMapper;
    private final IOfflineDrainageRecordService offlineDrainageRecordService;

    /**
     * 查询商家七天内是否有开通技术引流
     *
     * @param shopId 商家ID
     * @return 是否在七天内开通了技术引流
     */
    @Override
    public Boolean hasShopDrainageWithinSevenDays(Long shopId) {
        // 获取商家信息
        Shop shop = shopService.getById(shopId);
        if (shop == null || shop.getUserId() == null) {
            return false;
        }
        // 计算七天前的时间
        Date sevenDaysAgo = new Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000);

        long count = virtualOrderService.count(
            new LambdaQueryWrapper<VirtualOrder>()
                .eq(VirtualOrder::getUserId, shop.getUserId())
                .eq(VirtualOrder::getProductType, VirtualTypeEnum.JISHU_DRAINAGE.type())
                .eq(VirtualOrder::getStatus, OrderVirtualStatusEnum.PENDING_SHIPMENT.getCode()) // 已支付状态
                .eq(VirtualOrder::getDelFlag, "0")
                .ge(VirtualOrder::getPayTime, sevenDaysAgo)
        );
        long count1 = offlineDrainageRecordService.count(
            new LambdaQueryWrapper<OfflineDrainageRecord>()
                .select(OfflineDrainageRecord::getCount)
                .eq(OfflineDrainageRecord::getShopId, shop.getId())
                .eq(OfflineDrainageRecord::getStatus, "0")
                .gt(OfflineDrainageRecord::getCreateTime, sevenDaysAgo)
        );

        // 查询该商家在最近七天内是否有支付成功的技术引流订单
        return count > 0 || count1 > 0;
    }

    /**
     * 检查并设置商家技术引流状态
     * 检查商家七天内是否有开通技术引流，没有开通过的设置为失效状态
     *
     * @return 处理的商家数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer checkAndSetShopDrainageStatus() {
        log.info("开始执行商家技术引流状态检查");

        // 查询所有正常状态的商家
        List<Shop> shops = shopService.list(new LambdaQueryWrapper<Shop>()
            .eq(Shop::getDelFlag, "0")
            .eq(Shop::getStatus, "0"));

        if (shops.isEmpty()) {
            log.info("没有需要检查的商家");
            return 0;
        }

        int failureCount = 0;

        // 遍历每个商家，检查他们是否在七天内开通了技术引流
        for (Shop shop : shops) {
            // 根据商家的UserId查询SysUser
            SysUser sysUser = sysUserMapper.selectById(shop.getUserId());
            if (sysUser == null) {
                log.warn("系统用户不存在，shopId: {}, userId: {}", shop.getId(), shop.getUserId());
                continue;
            }

            // 获取商城用户ID
            Long tzUserId = sysUser.getTzUserId();
            if (tzUserId == null) {
                log.warn("商城用户ID为空，shopId: {}, sysUserId: {}", shop.getId(), sysUser.getUserId());
                continue;
            }

            // 查询商城用户
            TzUser tzUser = tzUserService.getOne(new LambdaQueryWrapper<TzUser>()
                .eq(TzUser::getUserId, tzUserId)
                .eq(TzUser::getDelFlag, "0"));

            if (tzUser == null) {
                log.warn("商城用户不存在，shopId: {}, sysUserId: {}, tzUserId: {}",
                    shop.getId(), sysUser.getUserId(), tzUserId);
                continue;
            }

            // 只检查正常状态的用户
            if (TzUserStatus.OK.getCode().equals(tzUser.getStatus())) {
                // 查询该商家所有的技术引流次数
                long count = virtualOrderService.list(
                    new LambdaQueryWrapper<VirtualOrder>()
                        .select(VirtualOrder::getQuantity)
                        .eq(VirtualOrder::getUserId, shop.getUserId())
                        .eq(VirtualOrder::getProductType, VirtualTypeEnum.JISHU_DRAINAGE.type())
                        .eq(VirtualOrder::getStatus, OrderVirtualStatusEnum.PENDING_SHIPMENT.getCode()) // 已支付状态
                        .eq(VirtualOrder::getDelFlag, "0")
                        .select(VirtualOrder::getQuantity)
                ).stream().mapToInt(VirtualOrder::getQuantity).sum();

                double sum = offlineDrainageRecordService.list(
                    new LambdaQueryWrapper<OfflineDrainageRecord>()
                        .select(OfflineDrainageRecord::getCount)
                        .eq(OfflineDrainageRecord::getShopId, shop.getId())
                        .eq(OfflineDrainageRecord::getStatus, "0")
                ).stream().mapToInt(OfflineDrainageRecord::getCount).sum();


                // 技术引流次数小于1000次，不进行设置失效状态
                if (count + sum < 1000) {
                    continue;
                }


                // 查询该商家在最近七天内是否有支付成功的技术引流订单
                boolean hasDrainageWithinSevenDays = hasShopDrainageWithinSevenDays(shop.getId());

                // 如果商家七天内没有开通技术引流，则设置为失效状态
                if (!hasDrainageWithinSevenDays) {
                    tzUser.setStatus(TzUserStatus.FAILURE.getCode());
                    tzUserService.updateById(tzUser);
                    failureCount++;
                    log.info("商家七天内未开通技术引流，设置为失效状态：shopId: {}, sysUserId: {}, tzUserId: {}",
                        shop.getId(), sysUser.getUserId(), tzUserId);
                }
            }
        }

        log.info("商家技术引流状态检查完成，共检查商家：{}个，设置失效状态：{}个", shops.size(), failureCount);
        return failureCount;
    }
}
