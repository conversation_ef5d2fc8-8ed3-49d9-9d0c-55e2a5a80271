package com.ruoyi.mall.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.ShopFreezeRecord;
import com.ruoyi.mall.domain.entity.ShopWallet;
import com.ruoyi.mall.mapper.ShopFreezeRecordMapper;
import com.ruoyi.mall.mapper.ShopMapper;
import com.ruoyi.mall.service.IShopPaymentService;
import com.ruoyi.mall.service.IShopWalletService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 店铺支付服务实现
 */
@Service
@RequiredArgsConstructor
public class ShopPaymentServiceImpl extends ServiceImpl<ShopFreezeRecordMapper, ShopFreezeRecord> implements IShopPaymentService {

    private final ShopMapper shopMapper;

    private final ShopFreezeRecordMapper shopFreezeRecordMapper;

    private final IShopWalletService shopWalletService;

    @Override
    public Map<String, Object> getShopByPhone(String phone) {
        Shop shop = shopMapper.selectShopByPhone(phone);
        if (shop == null) {
            throw new ServiceException("未找到该店铺信息");
        }

        // 获取店铺钱包信息
        ShopWallet wallet = shopWalletService.getByShopId(shop.getId());
        BigDecimal balance = BigDecimal.ZERO;
        if (wallet != null) {
            balance = wallet.getBalance();
        }

        Map<String, Object> result = new HashMap<>();
        result.put("shopId", shop.getId());
        result.put("shopName", shop.getBusinessName());
        result.put("phone", shop.getPhone());
        result.put("balance", balance); // 使用钱包余额而不是代销抵扣金
        return result;
    }

    @Override
    public TableDataInfo<ShopFreezeRecord> getShopFreezeRecords(PageQuery pageQuery, Long shopId, String shopName, String shopPhone, String status) {
        Page<ShopFreezeRecord> shopFreezeRecordPage = shopFreezeRecordMapper.selectPage(
            pageQuery.build(),
            new LambdaQueryWrapper<ShopFreezeRecord>()
                .eq(ObjUtil.isNotNull(shopId), ShopFreezeRecord::getShopId, shopId)
                .like(StrUtil.isNotEmpty(shopName), ShopFreezeRecord::getShopName, shopName)
                .like(StrUtil.isNotEmpty(shopPhone), ShopFreezeRecord::getShopPhone, shopPhone)
                .eq(StrUtil.isNotEmpty(status), ShopFreezeRecord::getStatus, status)
                .orderByDesc(ShopFreezeRecord::getCreateTime)
        );
        return TableDataInfo.build(shopFreezeRecordPage);
    }

    @Override
    @Transactional
    public Integer freezeShopAmount(Long shopId, BigDecimal amount, String reason) {
        Shop shop = shopMapper.selectById(shopId);
        if (shop == null) {
            throw new ServiceException("未找到该店铺信息");
        }

        // 获取店铺钱包信息
        ShopWallet wallet = shopWalletService.getByShopId(shopId);
        if (wallet == null) {
            throw new ServiceException("店铺钱包不存在");
        }

        // 检查钱包余额是否足够
        BigDecimal availableAmount = wallet.getBalance() != null ? wallet.getBalance() : BigDecimal.ZERO;
        if (availableAmount.compareTo(amount) < 0) {
            throw new ServiceException("钱包余额不足，当前余额：" + availableAmount + "元");
        }

        // 实际冻结钱包中的金额
        BigDecimal newBalance = availableAmount.subtract(amount);
        BigDecimal currentFrozenAmount = wallet.getFrozenAmount() != null ? wallet.getFrozenAmount() : BigDecimal.ZERO;
        BigDecimal newFrozenAmount = currentFrozenAmount.add(amount);

        wallet.setBalance(newBalance);
        wallet.setFrozenAmount(newFrozenAmount);

        // 更新钱包
        boolean walletUpdated = shopWalletService.updateById(wallet);
        if (!walletUpdated) {
            throw new ServiceException("更新钱包失败");
        }

        // 创建冻结记录
        ShopFreezeRecord freezeRecord = new ShopFreezeRecord();
        freezeRecord.setShopId(shopId);
        freezeRecord.setShopName(shop.getBusinessName()); // 使用商家名称
        freezeRecord.setShopPhone(shop.getPhone());
        freezeRecord.setAmount(amount);
        freezeRecord.setReason(reason);
        freezeRecord.setStatus("frozen");
        // 冻结时间使用BaseEntity的createTime字段

        return shopFreezeRecordMapper.insert(freezeRecord);
    }

    @Override
    @Transactional
    public Integer unfreezeShopAmount(Long freezeId) {
        ShopFreezeRecord freezeRecord = shopFreezeRecordMapper.selectById(freezeId);
        if (freezeRecord == null) {
            throw new ServiceException("未找到冻结记录");
        }

        if ("unfrozen".equals(freezeRecord.getStatus())) {
            throw new ServiceException("该记录已解冻");
        }

        Shop shop = shopMapper.selectById(freezeRecord.getShopId());
        if (shop == null) {
            throw new ServiceException("未找到该店铺信息");
        }

        // 获取钱包信息
        ShopWallet wallet = shopWalletService.getByShopId(freezeRecord.getShopId());
        if (wallet == null) {
            throw new ServiceException("店铺钱包不存在");
        }

        // 检查冻结金额是否足够
        BigDecimal freezeAmount = freezeRecord.getAmount();
        BigDecimal currentFrozenAmount = wallet.getFrozenAmount() != null ? wallet.getFrozenAmount() : BigDecimal.ZERO;
        if (currentFrozenAmount.compareTo(freezeAmount) < 0) {
            throw new ServiceException("冻结金额不足，无法解冻");
        }

        // 实际解冻钱包中的金额
        BigDecimal currentBalance = wallet.getBalance() != null ? wallet.getBalance() : BigDecimal.ZERO;
        BigDecimal newBalance = currentBalance.add(freezeAmount);
        BigDecimal newFrozenAmount = currentFrozenAmount.subtract(freezeAmount);

        wallet.setBalance(newBalance);
        wallet.setFrozenAmount(newFrozenAmount);

        // 更新钱包
        boolean walletUpdated = shopWalletService.updateById(wallet);
        if (!walletUpdated) {
            throw new ServiceException("更新钱包失败");
        }

        // 更新冻结记录状态
        freezeRecord.setStatus("unfrozen");
        freezeRecord.setUnfreezeBy(LoginHelper.getUsername());
        freezeRecord.setUnfreezeTime(DateUtils.getNowDate());

        return shopFreezeRecordMapper.updateById(freezeRecord);
    }
}
