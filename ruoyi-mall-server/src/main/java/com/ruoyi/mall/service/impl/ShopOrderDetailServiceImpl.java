package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.query.ShopOrderDetailDTO;
import com.ruoyi.mall.domain.vo.ShopOrderDetailVO;
import com.ruoyi.mall.mapper.ShopOrderDetailMapper;
import com.ruoyi.mall.service.IShopOrderDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商家订单服务接口实现类
 */

@Service
public class ShopOrderDetailServiceImpl implements IShopOrderDetailService {

    @Autowired
    private ShopOrderDetailMapper shopOrderDetailMapper;


    @Override
    public TableDataInfo<ShopOrderDetailVO> getMallShopOrderDetails(ShopOrderDetailDTO dto, PageQuery pageQuery) {

        IPage<ShopOrderDetailVO> shopOrderDetailVOIPage = shopOrderDetailMapper.selectMallShopOrderDetails(dto, pageQuery.build());
        return TableDataInfo.build(shopOrderDetailVOIPage);
    }

    @Override
    public List<ShopOrderDetailVO> exportFundDetails(ShopOrderDetailDTO dto) {
        return shopOrderDetailMapper.selectMallShopOrderDetailsForExport(dto);
    }


}
