package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.TzUserAddress;
import com.ruoyi.mall.mapper.TzUserAddressMapper;
import com.ruoyi.mall.service.ITzUserAddressService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户收货地址Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class TzUserAddressServiceImpl extends ServiceImpl<TzUserAddressMapper, TzUserAddress> implements ITzUserAddressService {

    /**
     * 查询用户收货地址列表
     *
     * @param tzUserAddress 用户收货地址
     * @return 用户收货地址
     */
    @Override
    public List<TzUserAddress> selectTzUserAddressList(TzUserAddress tzUserAddress) {
        LambdaQueryWrapper<TzUserAddress> lqw = new LambdaQueryWrapper<>();
        lqw.eq(tzUserAddress.getUserId() != null, TzUserAddress::getUserId, tzUserAddress.getUserId());
        lqw.like(StringUtils.isNotBlank(tzUserAddress.getReceiver()), TzUserAddress::getReceiver, tzUserAddress.getReceiver());
        lqw.like(StringUtils.isNotBlank(tzUserAddress.getPhone()), TzUserAddress::getPhone, tzUserAddress.getPhone());
        lqw.eq(tzUserAddress.getIsDefault() != null, TzUserAddress::getIsDefault, tzUserAddress.getIsDefault());
        return list(lqw);
    }

    /**
     * 分页查询用户收货地址列表
     *
     * @param tzUserAddress 用户收货地址
     * @param pageQuery     分页参数
     * @return 用户收货地址分页数据
     */
    @Override
    public TableDataInfo<TzUserAddress> selectTzUserAddressPage(TzUserAddress tzUserAddress, PageQuery pageQuery) {
        LambdaQueryWrapper<TzUserAddress> lqw = new LambdaQueryWrapper<>();
        lqw.eq(tzUserAddress.getUserId() != null, TzUserAddress::getUserId, tzUserAddress.getUserId());
        lqw.like(StringUtils.isNotBlank(tzUserAddress.getReceiver()), TzUserAddress::getReceiver, tzUserAddress.getReceiver());
        lqw.like(StringUtils.isNotBlank(tzUserAddress.getPhone()), TzUserAddress::getPhone, tzUserAddress.getPhone());
        lqw.eq(tzUserAddress.getIsDefault() != null, TzUserAddress::getIsDefault, tzUserAddress.getIsDefault());
        return TableDataInfo.build(page(pageQuery.build(), lqw));
    }

    /**
     * 设置默认收货地址
     *
     * @param addressId 地址ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int setDefaultAddress(Long addressId) {
        // 获取当前地址信息
        TzUserAddress address = getById(addressId);
        if (address == null) {
            return 0;
        }

        // 将该用户的所有地址设置为非默认
        LambdaQueryWrapper<TzUserAddress> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TzUserAddress::getUserId, address.getUserId());
        lqw.eq(TzUserAddress::getIsDefault, 1);
        List<TzUserAddress> defaultAddresses = list(lqw);
        for (TzUserAddress defaultAddress : defaultAddresses) {
            defaultAddress.setIsDefault(0);
            updateById(defaultAddress);
        }

        // 设置当前地址为默认
        address.setIsDefault(1);
        return updateById(address) ? 1 : 0;
    }

    @Override
    public TzUserAddress getUserDefaultAddress(Long userId) {
        LambdaQueryWrapper<TzUserAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .eq(TzUserAddress::getUserId, userId)
            .eq(TzUserAddress::getIsDefault, 1);

        return getOne(wrapper);
    }
}
