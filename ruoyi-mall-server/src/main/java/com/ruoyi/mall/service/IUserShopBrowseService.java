package com.ruoyi.mall.service;

import com.github.yulichang.base.MPJBaseService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.UserShopBrowse;

public interface IUserShopBrowseService extends MPJBaseService<UserShopBrowse> {

    /**
     * 新增用户浏览店铺
     */
    Integer saveUserShopBrowse(UserShopBrowse userShopBrowse);

    /**
     * 根据用户id和商铺id查询用户是否店铺粉丝
     *
     * @param userId 用户id
     * @param shopId 店铺id
     */
    Integer findUserShopBrowse(Long userId, Long shopId);

    /**
     * p判断用户今日是否点击过该店铺
     *
     * @param userId 用户id
     * @param shopId 商铺id
     * @return
     */
    Integer todayUserShopBrowseIsClick(Long userId, Long shopId);

    /**
     * 根据商铺ID查询粉丝
     *
     * @param shopId    商铺ID
     * @param pageQuery 分页参数
     * @return
     */
    TableDataInfo<UserShopBrowse> getShopUserBrowseList(Long shopId, PageQuery pageQuery);

}
