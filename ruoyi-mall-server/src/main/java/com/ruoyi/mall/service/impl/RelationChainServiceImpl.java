package com.ruoyi.mall.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.RelationChainQueryParam;
import com.ruoyi.mall.domain.vo.ConfigSettingVo;
import com.ruoyi.mall.domain.vo.RelationChainVO;
import com.ruoyi.mall.domain.vo.UserStatusJurisdictionVO;
import com.ruoyi.mall.mapper.RelationChainMapper;
import com.ruoyi.mall.service.IRelationChainService;
import com.ruoyi.mall.strategy.ReceptionA.ReceptionAFactory;
import com.ruoyi.mall.strategy.ReceptionA.ReceptionAStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.ruoyi.common.helper.LoginHelper.getUserId;

/**
 * 关系链服务实现
 */
@Service
@RequiredArgsConstructor
public class RelationChainServiceImpl implements IRelationChainService {

    private final RelationChainMapper relationChainMapper;
    private final ReceptionAFactory receptionAFactory;

    /**
     * 获取关系链列表
     *
     * @param queryParam 查询参数
     * @return 关系链列表
     */
    @Override
    public TableDataInfo<RelationChainVO> getRelationChainList(PageQuery pageQuery, RelationChainQueryParam queryParam) {
        ReceptionAStrategy receptionStrategy = receptionAFactory.getReceptionStrategy(1);
        ConfigSettingVo configSettingVo = receptionStrategy.getValue();
        JSONObject configValue = configSettingVo.getConfigValue();
        int dailyThreshold = configValue.getInt("dailyThreshold");
        int dailyExtraQuantityUnit = configValue.getInt("dailyExtraQuantityUnit");
        Double dailyThresholdReward = configValue.getDouble("dailyThresholdReward");

        // 查询关系链列表
        IPage<RelationChainVO> list = relationChainMapper.selectRelationChainList(pageQuery.build(), queryParam, dailyThreshold, dailyThreshold + dailyExtraQuantityUnit, dailyThresholdReward);

        return TableDataInfo.build(list);
    }

    /**
     * 修改用户状态
     *
     * @param userId 用户ID（手机号）
     * @param status 状态
     * @return 结果
     */
    @Override
    @Transactional
    public int changeUserStatus(Long userId, Integer status) {
        // 查询用户是否为商家
        Boolean isMerchant = relationChainMapper.checkUserIsMerchant(userId);

        int rows = 0;

        // 如果是商家，需要修改sys_user表的状态
        if (Boolean.TRUE.equals(isMerchant)) {
            rows = relationChainMapper.updateSysUserStatus(userId, status);
        }

        // 修改用户表的状态
        rows += relationChainMapper.updateUserStatus(userId, status);

        return rows;
    }


    /**
     * 获取用户状态和权限
     *
     * @param
     * @return UserStatusJurisdictionVO
     * <AUTHOR>
     */
    @Override
    public UserStatusJurisdictionVO getStatusAndJurisdiction() {
        Long userId = getUserId();
        return relationChainMapper.selectStatusAndJurisdiction(userId);
    }
}
