package com.ruoyi.mall.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.entity.UserComponentRecord;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.mall.mapper.UserComponentRecordMapper;
import com.ruoyi.mall.service.IUserComponentRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户分量记录Service业务层处理
 */
@RequiredArgsConstructor
@Service
public class UserComponentRecordServiceImpl extends ServiceImpl<UserComponentRecordMapper, UserComponentRecord> implements IUserComponentRecordService {

    private final UserComponentRecordMapper userComponentRecordMapper;

    private final TzUserMapper tzUserMapper;

    @Override
    public List<UserComponentRecord> selectUserComponentRecordList(UserComponentRecord userComponentRecord) {
        LambdaQueryWrapper<UserComponentRecord> lqw = buildQueryWrapper(userComponentRecord);
        return list(lqw);
    }

    @Override
    public TableDataInfo<UserComponentRecord> selectUserComponentRecordPage(UserComponentRecord userComponentRecord, PageQuery pageQuery) {
        LambdaQueryWrapper<UserComponentRecord> lqw = buildQueryWrapper(userComponentRecord);
        lqw.orderByDesc(UserComponentRecord::getCreateTime);
        Page<UserComponentRecord> page = page(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    public boolean addComponentRecord(Long userId, BigDecimal componentValue, Integer recordType,
                                      Long businessId, String businessNo, String description, String operator) {
        UserComponentRecord record = new UserComponentRecord();
        record.setUserId(userId);
        record.setComponentValue(componentValue);
        record.setRecordType(recordType);
        record.setBusinessId(businessId);
        record.setBusinessNo(businessNo);
        record.setDescription(description);
        record.setOperator(operator);
        record.setDelFlag("0");
        return save(record);
    }

    @Override
    public BigDecimal sumUserComponent(Long userId, Date startDate, Date endDate) {
        LambdaQueryWrapper<UserComponentRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(UserComponentRecord::getUserId, userId)
            .ge(startDate != null, UserComponentRecord::getCreateTime, startDate)
            .le(endDate != null, UserComponentRecord::getCreateTime, endDate)
            .eq(UserComponentRecord::getDelFlag, "0");

        List<UserComponentRecord> records = list(lqw);

        BigDecimal sum = BigDecimal.ZERO;
        for (UserComponentRecord record : records) {
            sum = sum.add(record.getComponentValue());
        }

        return sum;
    }

    @Override
    public Map<String, Object> getUserComponentStatistics(Long userId) {
        Map<String, Object> result = new HashMap<>();

        // 计算今日获得的分量
        Calendar today = Calendar.getInstance();
        today.set(Calendar.HOUR_OF_DAY, 0);
        today.set(Calendar.MINUTE, 0);
        today.set(Calendar.SECOND, 0);
        today.set(Calendar.MILLISECOND, 0);
        Date todayStart = today.getTime();

        Calendar tomorrow = Calendar.getInstance();
        tomorrow.add(Calendar.DAY_OF_MONTH, 1);
        tomorrow.set(Calendar.HOUR_OF_DAY, 0);
        tomorrow.set(Calendar.MINUTE, 0);
        tomorrow.set(Calendar.SECOND, 0);
        tomorrow.set(Calendar.MILLISECOND, 0);
        Date tomorrowStart = tomorrow.getTime();

        BigDecimal todayComponent = sumUserComponent(userId, todayStart, tomorrowStart);
        result.put("todayComponent", todayComponent);

        // 计算本周获得的分量
        Calendar thisWeek = Calendar.getInstance();
        thisWeek.set(Calendar.DAY_OF_WEEK, thisWeek.getFirstDayOfWeek());
        thisWeek.set(Calendar.HOUR_OF_DAY, 0);
        thisWeek.set(Calendar.MINUTE, 0);
        thisWeek.set(Calendar.SECOND, 0);
        thisWeek.set(Calendar.MILLISECOND, 0);
        Date weekStart = thisWeek.getTime();

        BigDecimal weekComponent = sumUserComponent(userId, weekStart, null);
        result.put("weekComponent", weekComponent);

        // 计算本月获得的分量
        Calendar thisMonth = Calendar.getInstance();
        thisMonth.set(Calendar.DAY_OF_MONTH, 1);
        thisMonth.set(Calendar.HOUR_OF_DAY, 0);
        thisMonth.set(Calendar.MINUTE, 0);
        thisMonth.set(Calendar.SECOND, 0);
        thisMonth.set(Calendar.MILLISECOND, 0);
        Date monthStart = thisMonth.getTime();

        BigDecimal monthComponent = sumUserComponent(userId, monthStart, null);
        result.put("monthComponent", monthComponent);

        // 计算总分量
        BigDecimal totalComponent = sumUserComponent(userId, null, null);
        result.put("totalComponent", totalComponent);

        return result;
    }


    /**
     * 查询用户每日分量统计
     */
    @Override
    public TableDataInfo<TzUser> getDailyStatistics(String date, String username, String phone, PageQuery pageQuery) {
        LocalDate localDate = LocalDate.parse(date);
        LocalDateTime startTime = localDate.atStartOfDay();
        LocalDateTime endTime = localDate.atTime(LocalTime.MAX);
        // 查询符合条件的用户
        Page<TzUser> page = tzUserMapper.selectPage(pageQuery.build(),
            new LambdaQueryWrapper<TzUser>()
                .eq(TzUser::getUserType, "C")
                .like(StrUtil.isNotBlank(username), TzUser::getUsername, username)
                .like(StrUtil.isNotBlank(phone), TzUser::getPhone, phone)
        );
        if (page.getTotal() == 0) {
            return TableDataInfo.build(page);
        }
        // 查询记录
        List<UserComponentRecord> records = userComponentRecordMapper.selectList(
            new LambdaQueryWrapper<UserComponentRecord>()
                .in(UserComponentRecord::getUserId, page.getRecords().stream().map(TzUser::getUserId).collect(Collectors.toList()))
                .ge(UserComponentRecord::getCreateTime, startTime)
                .le(UserComponentRecord::getCreateTime, endTime)
        );
        page.getRecords().forEach(user -> {
            user.setDayAmount(
                records.stream()
                    .filter(record -> record.getUserId().equals(user.getUserId()))
                    .map(UserComponentRecord::getComponentValue)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
            );
        });


        return TableDataInfo.build(page);
    }

    private LambdaQueryWrapper<UserComponentRecord> buildQueryWrapper(UserComponentRecord userComponentRecord) {
        LambdaQueryWrapper<UserComponentRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(userComponentRecord.getUserId() != null, UserComponentRecord::getUserId, userComponentRecord.getUserId())
            .eq(userComponentRecord.getRecordType() != null, UserComponentRecord::getRecordType, userComponentRecord.getRecordType())
            .eq(userComponentRecord.getBusinessId() != null, UserComponentRecord::getBusinessId, userComponentRecord.getBusinessId())
            .like(StringUtils.isNotBlank(userComponentRecord.getBusinessNo()), UserComponentRecord::getBusinessNo, userComponentRecord.getBusinessNo())
            .like(StringUtils.isNotBlank(userComponentRecord.getDescription()), UserComponentRecord::getDescription, userComponentRecord.getDescription())
            .like(StringUtils.isNotBlank(userComponentRecord.getOperator()), UserComponentRecord::getOperator, userComponentRecord.getOperator())
            .ge(userComponentRecord.getParams().get("beginCreateTime") != null, UserComponentRecord::getCreateTime, userComponentRecord.getParams().get("beginCreateTime"))
            .le(userComponentRecord.getParams().get("endCreateTime") != null, UserComponentRecord::getCreateTime, userComponentRecord.getParams().get("endCreateTime"))
            .eq(UserComponentRecord::getDelFlag, "0");
        return lqw;
    }
}
