package com.ruoyi.mall.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.ProductEvaluate;
import com.ruoyi.mall.domain.vo.EvaluateVo;
import com.ruoyi.mall.mapper.ProductEvaluateMapper;
import com.ruoyi.mall.service.IProductEvaluateService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@RequiredArgsConstructor
public class ProductEvaluateServiceImpl extends ServiceImpl<ProductEvaluateMapper, ProductEvaluate> implements IProductEvaluateService {

    @Autowired
    private ProductEvaluateMapper productEvaluateMapper;


    @Override
    public TableDataInfo<EvaluateVo> selProductEvaluate(Long productId, Integer type, PageQuery pageQuery) {
        return TableDataInfo.build(productEvaluateMapper.selProductEvaluate(productId, type, pageQuery.build()));
    }


    @Override
    public Integer ExamineEvaluate(Long id, String status) {
        return productEvaluateMapper.ExamineEvaluate(id, status);
    }

    @Override
    public Integer saveProductEvaluate(ProductEvaluate productEvaluate) {

        LambdaQueryWrapper<ProductEvaluate> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .eq(ProductEvaluate::getUserId, productEvaluate.getUserId())
            .eq(ProductEvaluate::getOrderId, productEvaluate.getOrderId())
            .eq(ProductEvaluate::getItemId, productEvaluate.getItemId());

        if (productEvaluateMapper.selectOne(wrapper) != null) {
            throw new ServiceException("当前订单已评价");
        }

        return productEvaluateMapper.insert(productEvaluate);
    }

    @Override
    public ProductEvaluate getProductEvaluateById(Long id) {
        return productEvaluateMapper.selectById(id);
    }

    @Override
    public Integer deleteProductEvaluateIds(Long[] ids) {
        return productEvaluateMapper.deleteProductEvaluateByIds(ids);
    }

    @Override
    public Integer deleteProductEvaluateId(Long id) {
        return productEvaluateMapper.deleteById(id);
    }

    @Override
    public ProductEvaluate getProductEvaluateByItemId(Long itemId) {

        LambdaQueryWrapper<ProductEvaluate> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .eq(ProductEvaluate::getItemId, itemId);

        ProductEvaluate productEvaluate = productEvaluateMapper.selectOne(wrapper);
        if (productEvaluate == null) {
            throw new ServiceException("没有当前商品项评价");
        }
        if (productEvaluate.getStatus().equals("2")) {
            throw new ServiceException("这条商品评价已被隐藏");
        }
        return productEvaluate;
    }

    @Override
    public Integer replyEvaluate(Long id, String reply) {
        ProductEvaluate productEvaluate = productEvaluateMapper.selectById(id);
        if (productEvaluate == null) {
            return 0;
        }
        productEvaluate.setReply(reply);
        productEvaluate.setReplyTime(new Date());
        return productEvaluateMapper.updateById(productEvaluate);
    }

    @Override
    public Integer editProductEvaluate(ProductEvaluate productEvaluate) {
        ProductEvaluate evaluate = productEvaluateMapper.selectById(productEvaluate.getId());
        if (evaluate.getStatus().equals("2")) {
            throw new ServiceException("评价已被隐藏不能进行修改");
        }

        LambdaUpdateWrapper<ProductEvaluate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .set(ProductEvaluate::getImg, productEvaluate.getImg())
            .set(ProductEvaluate::getVideo, productEvaluate.getVideo())
            .set(StringUtils.isNotBlank(productEvaluate.getContent()), ProductEvaluate::getContent, productEvaluate.getContent())
            .set(ProductEvaluate::getLevel, productEvaluate.getLevel())
            .set(StringUtils.isNotBlank(productEvaluate.getIsShow()), ProductEvaluate::getIsShow, productEvaluate.getIsShow())
            .set(ProductEvaluate::getStatus, "0")
            .eq(ProductEvaluate::getId, productEvaluate.getId());
        return productEvaluateMapper.update(updateWrapper);
    }
}
