package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.ShopBankAccountDTO;
import com.ruoyi.mall.domain.entity.ShopBankAccount;

import java.util.List;

/**
 * 商家银行卡账户Service接口
 */
public interface IShopBankAccountService extends IService<ShopBankAccount> {

    /**
     * 查询商家银行卡账户列表
     *
     * @param shopBankAccount 商家银行卡账户
     * @return 商家银行卡账户集合
     */
    List<ShopBankAccount> selectShopBankAccountList(ShopBankAccount shopBankAccount);

    /**
     * 查询商家银行卡账户分页列表
     *
     * @param shopBankAccount 商家银行卡账户
     * @param pageQuery       分页参数
     * @return 商家银行卡账户分页集合
     */
    TableDataInfo<ShopBankAccount> selectShopBankAccountPage(ShopBankAccount shopBankAccount, PageQuery pageQuery);

    /**
     * 新增商家银行卡账户
     *
     * @param dto    商家银行卡账户DTO
     * @param shopId 商家ID
     * @return 结果
     */
    boolean addShopBankAccount(ShopBankAccountDTO dto, Long shopId);

    /**
     * 修改商家银行卡账户
     *
     * @param dto    商家银行卡账户DTO
     * @param shopId 商家ID
     * @return 结果
     */
    boolean updateShopBankAccount(ShopBankAccountDTO dto, Long shopId);

    /**
     * 删除商家银行卡账户
     *
     * @param id     账户ID
     * @param shopId 商家ID
     * @return 结果
     */
    boolean deleteShopBankAccount(Long id, Long shopId);

    /**
     * 设置默认银行卡账户
     *
     * @param id     账户ID
     * @param shopId 商家ID
     * @return 结果
     */
    boolean setDefaultAccount(Long id, Long shopId);
}
