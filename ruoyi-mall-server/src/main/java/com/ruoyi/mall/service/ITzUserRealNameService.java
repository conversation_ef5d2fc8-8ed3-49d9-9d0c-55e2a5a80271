package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.UserRealNameAuditDTO;
import com.ruoyi.mall.domain.entity.TzUserRealName;
import com.ruoyi.mall.domain.vo.UserRealNameVO;

public interface ITzUserRealNameService extends IService<TzUserRealName> {

    /**
     * 获取用户实名审核列表
     *
     * @param tzUserRealName
     * @return
     */
    TableDataInfo<TzUserRealName> getUserRealNameList(TzUserRealName tzUserRealName, PageQuery pageQuery);

    /**
     * 修改用户实名
     *
     * @param tzUserRealName
     * @return
     */
    Boolean editUserRealName(TzUserRealName tzUserRealName);

    /**
     * 删除用户实名
     *
     * @param id
     * @return
     */
    Integer deleteUserRealName(Long id);

    /**
     * 新增用户实名
     *
     * @param tzUserRealName
     * @return
     */
    Boolean addUserRealName(TzUserRealName tzUserRealName);

    /**
     * 根据用户id查询用户实名
     *
     * @param userId
     * @return
     */
    TzUserRealName getUserRealNameById(Long userId);

    /**
     * 管理端获取用户实名审核列表
     *
     * @param tzUserRealName 查询条件
     * @param pageQuery      分页参数
     * @return 实名审核列表
     */
    TableDataInfo<UserRealNameVO> getAdminUserRealNameList(TzUserRealName tzUserRealName, PageQuery pageQuery);

    /**
     * 管理端审核用户实名
     *
     * @param auditDTO 审核参数
     */
    void adminAuditUserRealName(UserRealNameAuditDTO auditDTO);

    /**
     * 根据ID获取用户实名详情
     *
     * @param id 实名认证ID
     * @return 实名详情
     */
    UserRealNameVO getUserRealNameDetail(Long id);

}
