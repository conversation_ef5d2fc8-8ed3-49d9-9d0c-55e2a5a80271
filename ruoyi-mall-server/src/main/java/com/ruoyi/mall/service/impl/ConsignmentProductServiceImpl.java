package com.ruoyi.mall.service.impl;


import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.constant.ConfigSettingConstants;
import com.ruoyi.common.constant.ConsignmentProductLimitConstants;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.service.UserService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.mall.domain.entity.*;
import com.ruoyi.mall.mapper.ConsignmentProductMapper;
import com.ruoyi.mall.service.*;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.ruoyi.common.helper.LoginHelper.getUserId;


@Slf4j
@Service
@RequiredArgsConstructor
public class ConsignmentProductServiceImpl extends MPJBaseServiceImpl<ConsignmentProductMapper, ConsignmentProduct> implements IConsignmentProductService {


    private final IShopService shopService;
    private final IProductService productService;
    private final ITZUserFocusService userFocusService;
    private final ConsignmentProductMapper consignmentProductMapper;
    private final IConfigSettingService configSettingService;
    private final ISysUserService sysUserService;
    private final UserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addConsignmentProduct(Long userId, Long productId) {
        SysUser sysUser = sysUserService.getById(userId);
        //判断商家/产品是否存在
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .select(Product::getId, Product::getShopId, Product::getJumpUrl)
            .eq(Product::getIsDaixiao, 1) //是代销产品
            .eq(Product::getAuditStatus, "1") //审核通过
            .eq(Product::getStatus, "1")  //上架
            .eq(Product::getId, productId);//产品ID

        Product product = productService.getOne(wrapper);

        if (product == null) {
            log.error("商品不存在/该商品不是代销商品");
            throw new ServiceException("商品不存在/该商品不是代销商品");
        }

        //查询用户是否是代销用户
        LambdaQueryWrapper<Shop> shopWrapper = new LambdaQueryWrapper<>();
        shopWrapper
            .select(Shop::getId, Shop::getConsignmentLevel, Shop::getConsignmentLevelExpireTime)
            .eq(Shop::getUserId, userId)
            .eq(Shop::getDelFlag, "0") //未删除
            .eq(Shop::getAuthStatus, "1") //已认证
            .eq(Shop::getStatus, "0") //正常
            .eq(Shop::getConsignmentPermission, "1")//开通代销权限
            .ge(Shop::getConsignmentExpireTime, new SimpleDateFormat("YYYY-MM-dd").format(new Date()));//代销权限时间没过

        Shop shop = shopService.getOne(shopWrapper);

        if (shop == null) {
            throw new ServiceException("当前用户不是代销用户/没开通代销权限/权限时间过期");
        }

        // 检查代销等级权限是否过期
        checkAndUpdateConsignmentLevel(shop);

        // 检查代销权限和商品数量限制
        checkConsignmentPermissionAndLimit(shop);

        //判断是否是该店铺粉丝
        LambdaQueryWrapper<TZUserFocus> userFocusWrapper = new LambdaQueryWrapper<>();
        userFocusWrapper
            .eq(TZUserFocus::getUserId, sysUser.getTzUserId())
            .eq(TZUserFocus::getShopId, product.getShopId())
            .eq(TZUserFocus::getStatus, "0");
        if (userFocusService.getOne(userFocusWrapper) == null) {
            throw new ServiceException("当前用户不是该店铺粉丝");
        }

        LambdaQueryWrapper<ConsignmentProduct> consignmentProductLambdaQueryWrapper = new LambdaQueryWrapper<>();
        consignmentProductLambdaQueryWrapper
            .eq(ConsignmentProduct::getProductId, product.getId())
            .eq(ConsignmentProduct::getStatus, "0")
            .eq(ConsignmentProduct::getConsignmentShop, shop.getId());
        Long count = consignmentProductMapper.selectCount(consignmentProductLambdaQueryWrapper);
        if (count > 0) {
            throw new ServiceException("该商品已代销");
        }
        // 查询以前是否代销过
        ConsignmentProduct dbConsignmentProduct = consignmentProductMapper.selectOne(
            new LambdaQueryWrapper<ConsignmentProduct>()
                .select(ConsignmentProduct::getId, ConsignmentProduct::getStatus)
                .eq(ConsignmentProduct::getConsignmentShop, shop.getId())
                .eq(ConsignmentProduct::getProductId, productId)
                .eq(ConsignmentProduct::getStatus, "1")
        );
        if (dbConsignmentProduct != null) {
            dbConsignmentProduct.setStatus("0");
            return consignmentProductMapper.updateById(dbConsignmentProduct);
        }
        ConsignmentProduct consignmentProduct = new ConsignmentProduct();
        consignmentProduct.setProductId(productId);
        consignmentProduct.setShopId(product.getShopId());
        consignmentProduct.setConsignmentShop(shop.getId());
        consignmentProduct.setProductUrl(product.getJumpUrl());
        consignmentProduct.setCreateTime(new Date());
        consignmentProduct.setStatus("0");
        consignmentProduct.setDelFlag("0");

        return consignmentProductMapper.insert(consignmentProduct);
    }

    @Override
    public Integer deleteConsignmentProduct(Long id) {

        LambdaUpdateWrapper<ConsignmentProduct> wrapper = new LambdaUpdateWrapper<>();
        wrapper
            .set(ConsignmentProduct::getStatus, "1")
            .set(ConsignmentProduct::getDelFlag, "2")
            .eq(ConsignmentProduct::getId, id);

        return consignmentProductMapper.update(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addConsignmentProductByUrl(Long userId, String url, String remark) {

        //判断商家/产品是否存在
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .select(Product::getId, Product::getShopId)
            .eq(Product::getIsDaixiao, "1") //是代销产品
            .eq(Product::getAuditStatus, "1") //审核通过
            .eq(Product::getStatus, "1")  //上架
            .eq(Product::getJumpUrl, url);//跳转链接

        Product product = productService.getOne(wrapper);

        if (product == null) {
            throw new ServiceException("商品不存在/该商品不是代销商品");
        }

        //查询用户是否是代销用户
        LambdaQueryWrapper<Shop> shopWrapper = new LambdaQueryWrapper<>();
        shopWrapper
            .select(Shop::getId)
            .eq(Shop::getUserId, userId)
            .eq(Shop::getDelFlag, "0") //未删除
            .eq(Shop::getAuthStatus, "1") //已认证
            .eq(Shop::getStatus, "0") //正常
            .eq(Shop::getType, "0") //代表是代销商家
            .eq(Shop::getConsignmentPermission, "1")//开通代销权限
            .ge(Shop::getConsignmentExpireTime, new SimpleDateFormat("YYYY-MM-dd").format(new Date()));//代销权限时间没过

        Shop shop = shopService.getOne(shopWrapper);

        if (shop == null) {
            throw new ServiceException("当前用户不是代销用户/没开通代销权限/权限时间过期");
        }

        //判断是否是该店铺粉丝
        LambdaQueryWrapper<UserShopBrowse> userShopBrowseWrapper = new LambdaQueryWrapper<>();
        userShopBrowseWrapper
            .eq(UserShopBrowse::getUserId, userId)
            .eq(UserShopBrowse::getShopId, product.getShopId());

        if (shopService.getOne(shopWrapper) == null) {
            throw new ServiceException("申请用户不是该店铺粉丝");
        }

        LambdaQueryWrapper<ConsignmentProduct> consignmentProductLambdaQueryWrapper = new LambdaQueryWrapper<>();
        consignmentProductLambdaQueryWrapper
            .eq(ConsignmentProduct::getProductId, product.getId())
            .eq(ConsignmentProduct::getConsignmentShop, shop.getId());
        if (consignmentProductMapper.selectOne(consignmentProductLambdaQueryWrapper) != null) {
            throw new ServiceException("该商品已添加");
        }

        ConsignmentProduct consignmentProduct = new ConsignmentProduct();
        consignmentProduct.setCreateTime(new Date());
        consignmentProduct.setConsignmentShop(shop.getId());
        consignmentProduct.setProductUrl(url);
        consignmentProduct.setShopId(product.getShopId());
        consignmentProduct.setProductId(product.getId());
        consignmentProduct.setStatus("0");
        consignmentProduct.setDelFlag("0");

        return consignmentProductMapper.insert(consignmentProduct);
    }

    @Override
    public TableDataInfo<ConsignmentProduct> selectConsignmentProductList(Long userId, PageQuery pageQuery) {

        MPJLambdaWrapper<ConsignmentProduct> wrapper = new MPJLambdaWrapper<>();
        wrapper
            .selectAll(ConsignmentProduct.class)
            .selectAs(Product::getName, ConsignmentProduct::getProductName)
            .selectAs(Product::getStatus, ConsignmentProduct::getProductStatus)
            .leftJoin(Product.class, Product::getId, ConsignmentProduct::getProductId)
            .eq(ConsignmentProduct::getConsignmentShop, shopService.getShopIdByUserId(getUserId()))
            .orderByDesc(ConsignmentProduct::getCreateTime);

        return TableDataInfo.build(selectJoinListPage(pageQuery.build(), ConsignmentProduct.class, wrapper));
    }

    @Override
    public Integer cancelConsignmentProduct(Long shopId, Long productId) {

        LambdaUpdateWrapper<ConsignmentProduct> wrapper = new LambdaUpdateWrapper<>();
        wrapper
            .set(ConsignmentProduct::getStatus, "1")
            .eq(ConsignmentProduct::getConsignmentShop, shopId)
            .eq(ConsignmentProduct::getProductId, productId);
        return consignmentProductMapper.update(wrapper);
    }

    /**
     * 检查并更新代销等级权限
     */
    private void checkAndUpdateConsignmentLevel(Shop shop) {
        String consignmentLevel = shop.getConsignmentLevel();
        Date levelExpireTime = shop.getConsignmentLevelExpireTime();

        // 如果有代销等级且已过期，则降级到基础代销
        if (consignmentLevel != null && !"0".equals(consignmentLevel) &&
            levelExpireTime != null && levelExpireTime.before(new Date())) {

            log.info("代销等级权限已过期，从等级{}降级到基础代销", consignmentLevel);
            shop.setConsignmentLevel("0");
            shop.setConsignmentLevelExpireTime(null);
            shopService.updateById(shop);
        }
    }

    /**
     * 检查代销权限和商品数量限制
     */
    private void checkConsignmentPermissionAndLimit(Shop targetShop) {
        // 获取代销用户当前已代销的商品总数量（所有店铺的代销商品）
        Long currentConsignmentCount = count(
            new LambdaQueryWrapper<ConsignmentProduct>()
                .eq(ConsignmentProduct::getConsignmentShop, targetShop.getId())
                .eq(ConsignmentProduct::getStatus, "0")
                .eq(ConsignmentProduct::getDelFlag, "0")
        );

        // 获取代销权限允许的最大商品数量
        Long maxConsignmentCount = getConsignmentProductLimit(targetShop.getConsignmentLevel());

        if (maxConsignmentCount == null) {
            throw new ServiceException("未开通代销权限或权限已过期");
        }

        if (currentConsignmentCount >= maxConsignmentCount) {
            throw new ServiceException(String.format("代销商品数量已达上限(%d/%d)，请升级代销权限或取消部分商品代销",
                currentConsignmentCount, maxConsignmentCount));
        }

        log.info("代销权限检查通过，当前代销商品数量: {}/{}", currentConsignmentCount, maxConsignmentCount);
    }

    /**
     * 获取代销商品数量限制
     */
    private Long getConsignmentProductLimit(String consignmentLevel) {
        if (consignmentLevel == null) {
            consignmentLevel = "0";
        }

        try {
            switch (consignmentLevel) {
                case "1":
                    // 从配置中获取代销权限一的商品数量限制
                    JSONObject config1 = configSettingService.getConfigValue(ConfigSettingConstants.config.get(11));
                    if (config1 != null && config1.containsKey("maxProducts")) {
                        return config1.getLong("maxProducts");
                    }
                    return ConsignmentProductLimitConstants.ConsignmentProductLimitOne; // 默认值

                case "2":
                    // 从配置中获取代销权限二的商品数量限制
                    JSONObject config2 = configSettingService.getConfigValue(ConfigSettingConstants.config.get(12));
                    if (config2 != null && config2.containsKey("maxProducts")) {
                        return config2.getLong("maxProducts");
                    }
                    return ConsignmentProductLimitConstants.ConsignmentProductLimitTwO; // 默认值

                default:
                    return ConsignmentProductLimitConstants.ConsignmentProductLimitZero; // 基础代销固定3个
            }
        } catch (Exception e) {
            log.error("获取代销权限配置失败，使用默认值。权限等级: {}, 错误: {}", consignmentLevel, e.getMessage());
            // 如果配置获取失败，返回默认值
            switch (consignmentLevel) {
                case "1":
                    return 10L;
                case "2":
                    return 50L;
                default:
                    return 3L;
            }
        }
    }


}
