package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.DeductionRecord;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.mapper.DeductionRecordMapper;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.mall.service.IDeductionRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 抵扣金赠送记录Service实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeductionRecordServiceImpl extends ServiceImpl<DeductionRecordMapper, DeductionRecord> implements IDeductionRecordService {

    private final TzUserMapper tzUserMapper;

    @Override
    public TableDataInfo<DeductionRecord> queryPageList(DeductionRecord deductionRecord, PageQuery pageQuery) {
        LambdaQueryWrapper<DeductionRecord> lqw = buildQueryWrapper(deductionRecord);
        Page<DeductionRecord> page = page(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    private LambdaQueryWrapper<DeductionRecord> buildQueryWrapper(DeductionRecord deductionRecord) {
        LambdaQueryWrapper<DeductionRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(deductionRecord.getShopId() != null, DeductionRecord::getShopId, deductionRecord.getShopId());
        lqw.eq(deductionRecord.getUserId() != null, DeductionRecord::getUserId, deductionRecord.getUserId());
        lqw.like(StringUtils.isNotBlank(deductionRecord.getPhone()), DeductionRecord::getPhone, deductionRecord.getPhone());
        lqw.eq(StringUtils.isNotBlank(deductionRecord.getStatus()), DeductionRecord::getStatus, deductionRecord.getStatus());
        lqw.orderByDesc(DeductionRecord::getCreateTime);
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean giveDeductionMoney(Long shopId, String phone, BigDecimal amount, String reason) {
        // 1. 验证参数
        if (shopId == null) {
            throw new ServiceException("商家ID不能为空");
        }
        if (StringUtils.isEmpty(phone)) {
            throw new ServiceException("用户手机号不能为空");
        }
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("赠送金必须大于0");
        }

        // 2. 查询用户是否存在
        TzUser user = tzUserMapper.selectOne(
            Wrappers.<TzUser>lambdaQuery().eq(TzUser::getPhone, phone)
        );
        if (user == null) {
            throw new ServiceException("用户不存在，请检查手机号");
        }

        // 3. 创建赠送记录
        DeductionRecord record = new DeductionRecord();
        record.setShopId(shopId);
        record.setUserId(user.getUserId());
        record.setPhone(phone);
        record.setAmount(amount);
        record.setReason(reason);
        record.setStatus("0"); // 成功
        record.setDelFlag("0");

        // 4. 更新用户抵扣金余额
        BigDecimal currentDeduction = user.getDeductionMoney() != null ? user.getDeductionMoney() : BigDecimal.ZERO;
        user.setDeductionMoney(currentDeduction.add(amount));

        boolean updateResult = tzUserMapper.updateById(user) > 0;
        boolean saveResult = save(record);

        return updateResult && saveResult;
    }
}
