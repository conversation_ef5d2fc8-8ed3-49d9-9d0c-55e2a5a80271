package com.ruoyi.mall.service;

import com.github.yulichang.base.MPJBaseService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.ConsignmentProduct;

public interface IConsignmentProductService extends MPJBaseService<ConsignmentProduct> {

    /**
     * 添加代销商家代销商品
     *
     * @param productId 产品ID
     * @return
     */
    Integer addConsignmentProduct(Long userId, Long productId);


    /**
     * 删除代销商家代销商品
     *
     * @param id 代销商品ID
     * @return
     */
    Integer deleteConsignmentProduct(Long id);

    /**
     * 代销商家复制链接代销产品
     *
     * @param url    代销商品URL
     * @param remark 备注
     * @return
     */
    Integer addConsignmentProductByUrl(Long userId, String url, String remark);

    /**
     * 查询代销商家代销的商品列表
     *
     * @param userId    代销商家ID
     * @param pageQuery 分页参数
     * @return
     */
    TableDataInfo<ConsignmentProduct> selectConsignmentProductList(Long userId, PageQuery pageQuery);

    /**
     * 代销用户取消正在代销的产品
     *
     * @param id 代销ID
     * @return
     */
    Integer cancelConsignmentProduct(Long shopId, Long productId);


}
