package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.Delivery;

import java.util.List;

/**
 * 购物车Service接口
 */
public interface IDeliveryService extends IService<Delivery> {

    TableDataInfo<Delivery> selectDeliveryPage(Delivery delivery, PageQuery pageQuery);

    List<Delivery> selectDeliveryAll(Delivery delivery);
}
