package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.MessageNotice;
import com.ruoyi.mall.mapper.MessageNoticeMapper;
import com.ruoyi.mall.service.IMessageNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;


@Service
@RequiredArgsConstructor
public class MessageNoticeServiceImpl extends ServiceImpl<MessageNoticeMapper, MessageNotice> implements IMessageNoticeService {

    @Autowired
    public final MessageNoticeMapper mapper;

    @Override
    public int AddMessageNotice(MessageNotice messageNotice) {
        messageNotice.setIsRead("0");//默认为未读
        messageNotice.setDelFLag("0");
        return mapper.insert(messageNotice);
    }

    @Override
    public boolean deleteMessageNoticeByIds(Long[] ids) {
        return mapper.deleteBatchIds(Arrays.asList(ids)) > 0;
    }

    @Override
    public TableDataInfo<MessageNotice> getMessageNoticeList(MessageNotice messageNotice, PageQuery pageQuery) {

        LambdaQueryWrapper<MessageNotice> wrapper = new LambdaQueryWrapper<>();
        wrapper
            .eq(MessageNotice::getUserId, messageNotice.getUserId())
            .eq(StringUtils.isNotBlank(messageNotice.getMessageType()), MessageNotice::getMessageType, messageNotice.getMessageType())  //消息不同类型
            .eq(StringUtils.isNotBlank(messageNotice.getIsRead()), MessageNotice::getIsRead, messageNotice.getIsRead())  //是否已读
            .eq(MessageNotice::getDelFLag, "0")
            .orderByDesc(MessageNotice::getCreateTime);

        return TableDataInfo.build(mapper.selectPage(pageQuery.build(), wrapper));
    }

    @Override
    public MessageNotice getMessageNoticeById(Long id) {
        return mapper.selectById(id);
    }

    @Override
    public Integer editMessageNoticeIsRead(Long id) {

        LambdaUpdateWrapper<MessageNotice> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper
            .set(MessageNotice::getIsRead, "1")
            .eq(MessageNotice::getId, id);

        return mapper.update(updateWrapper);
    }
}
