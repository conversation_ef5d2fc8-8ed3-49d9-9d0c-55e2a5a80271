package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.OrderRefund;

/**
 * 订单退货
 */
public interface IOrderRefundService extends IService<OrderRefund> {

    /**
     * 申请退款
     *
     * @param newOrderRefund 退款申请信息
     * @return 操作结果
     */
    Boolean applyRefund(OrderRefund newOrderRefund);

    /**
     * 取消退款申请
     *
     * @param refundId 退款ID
     * @return 操作结果
     */
    Boolean cancelRefund(Long refundId);

    /**
     * 商家处理退款申请(同意)
     *
     * @param refundId 退款ID
     * @return 操作结果
     */
    Boolean processRefund(Long refundId);

    /**
     * 商家拒绝退款申请
     *
     * @param refundId      退款ID
     * @param rejectMessage 拒绝原因
     * @return 操作结果
     */
    Boolean rejectRefund(Long refundId, String rejectMessage);

    /**
     * 分页查询退款列表
     *
     * @param orderRefund 查询条件
     * @param pageQuery   分页条件
     * @return 退款列表
     */
    TableDataInfo<OrderRefund> selectRefundPage(OrderRefund orderRefund, PageQuery pageQuery);

    /**
     * 获取退款详情
     *
     * @param refundId   退款ID
     * @param isShopMode 是否为商家模式（商家模式会加载更多信息）
     * @return 退款详情
     */
    OrderRefund getRefundDetail(Long refundId, boolean isShopMode);
}
