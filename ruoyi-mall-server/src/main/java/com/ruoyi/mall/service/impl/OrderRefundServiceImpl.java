package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.OrderStatusEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.Order;
import com.ruoyi.mall.domain.entity.OrderRefund;
import com.ruoyi.mall.enums.RefundStatusEnum;
import com.ruoyi.mall.enums.ReturnMoneyStsEnum;
import com.ruoyi.mall.mapper.OrderMapper;
import com.ruoyi.mall.mapper.OrderRefundMapper;
import com.ruoyi.mall.service.IOrderRefundService;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.mall.service.IShopWalletService;
import com.ruoyi.pay.config.PayConfig;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.enums.PayStatusEnum;
import com.ruoyi.pay.service.IPayService;
import com.ruoyi.pay.strategy.PayStrategy;
import com.ruoyi.pay.strategy.PayStrategyFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 订单退货
 */
@RequiredArgsConstructor
@Service
public class OrderRefundServiceImpl extends ServiceImpl<OrderRefundMapper, OrderRefund> implements IOrderRefundService {

    private final OrderMapper orderMapper;
    private final IPayService payService;
    private final IShopWalletService shopWalletService;
    private final PayStrategyFactory payStrategyFactory;
    private final IShopService shopService;
    private final PayConfig payConfig;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyRefund(OrderRefund newOrderRefund) {
        // 获取订单信息
        Order order = orderMapper.selectById(newOrderRefund.getOrderId());
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        // 如果未指定退款金额，则使用订单实付金额
        if (newOrderRefund.getRefundAmount() == null) {
            newOrderRefund.setRefundAmount(order.getPayAmount());
        } else if (newOrderRefund.getRefundAmount().compareTo(order.getPayAmount()) > 0) {
            throw new ServiceException("退款金额不能大于订单实付金额");
        }

        boolean save = save(newOrderRefund);
        Order updateOrder = new Order();
        updateOrder.setId(newOrderRefund.getOrderId());
        updateOrder.setRefundStatus(RefundStatusEnum.APPLY.value());
        orderMapper.updateById(updateOrder);
        return save;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelRefund(Long refundId) {
        // 获取退款记录
        OrderRefund orderRefund = getById(refundId);
        if (orderRefund == null) {
            throw new ServiceException("退款记录不存在");
        }

        // 更新退款状态为客户撤回
        orderRefund.setReturnMoneySts(ReturnMoneyStsEnum.CANCEL.value());
        orderRefund.setCancelTime(new Date());
        boolean updated = updateById(orderRefund);

        // 更新订单的退款状态
        if (updated) {
            Order order = new Order();
            order.setId(orderRefund.getOrderId());
            order.setRefundStatus(RefundStatusEnum.DISAGREE.value()); // 取消退款相当于退款失败
            orderMapper.updateById(order);
        }

        return updated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processRefund(Long refundId) {
        // 获取退款记录
        OrderRefund orderRefund = getById(refundId);
        if (orderRefund == null) {
            throw new ServiceException("退款记录不存在");
        }

        // 验证退款状态
        if (!ReturnMoneyStsEnum.APPLY.value().equals(orderRefund.getReturnMoneySts())) {
            throw new ServiceException("当前退款状态不允许处理");
        }

        // 获取订单信息
        Order order = orderMapper.selectById(orderRefund.getOrderId());
        if (order == null) {
            throw new ServiceException("订单信息不存在");
        }

        // 验证退款金额
        if (orderRefund.getRefundAmount() == null) {
            orderRefund.setRefundAmount(order.getPayAmount());
        } else if (orderRefund.getRefundAmount().compareTo(order.getPayAmount()) > 0) {
            throw new ServiceException("退款金额不能大于订单实付金额");
        }

        // 获取支付记录
        PayOrder payOrder = payService.getByOrderNo(order.getOrderNo());
        if (payOrder == null) {
            throw new ServiceException("支付记录不存在");
        }

        try {
            // 执行退款操作
            String refundAmountStr;
            String refundReasonStr = orderRefund.getBuyerReason();

            // 商家承担手续费 - 退全额
            refundAmountStr = orderRefund.getRefundAmount().toString();

            PayStrategy payStrategy = payStrategyFactory.getPaymentStrategy(payOrder.getPayType());
            boolean refundResult = payStrategy.RefundPayment(
                payOrder.getPaymentNo(),
                refundAmountStr, // 根据承担方使用不同的金额
                refundReasonStr
            );

            // 修改支付记录为退款成功
            LambdaUpdateWrapper<PayOrder> payOrderLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            payOrderLambdaUpdateWrapper.set(PayOrder::getPayStatus, PayStatusEnum.REFUND.getCode()).eq(PayOrder::getOrderId, order.getId());
            payService.update(payOrderLambdaUpdateWrapper);

            // 根据承担方处理手续费
            boolean refundResult1;
            // 商家承担 - 从商家钱包扣除退款金额和手续费
            refundResult1 = shopWalletService.processRefund(
                orderRefund.getShopId(),
                order.getOrderNo(),
                orderRefund.getRefundAmount(),
                "用户申请退款，原因：" + orderRefund.getBuyerReason()
            );

            // 更新退款状态
            if (refundResult && refundResult1) {
                // 退款成功
                orderRefund.setReturnMoneySts(ReturnMoneyStsEnum.SUCCESS.value());
                boolean updated = updateById(orderRefund);

                // 更新订单退款状态
                if (updated) {
                    order.setStatus(OrderStatusEnum.REFUNDED.getCode());
                    order.setRefundStatus(RefundStatusEnum.SUCCEED.value());
                    orderMapper.updateById(order);
                }
                return updated;
            } else {
                // 退款失败
                throw new ServiceException("退款处理失败");
            }
        } catch (Exception e) {
            // 退款失败，记录状态
            orderRefund.setReturnMoneySts(ReturnMoneyStsEnum.FAIL.value());
            updateById(orderRefund);

            order.setRefundStatus(RefundStatusEnum.DISAGREE.value());
            orderMapper.updateById(order);

            throw new ServiceException("退款处理失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean rejectRefund(Long refundId, String rejectMessage) {
        // 获取退款记录
        OrderRefund orderRefund = getById(refundId);
        if (orderRefund == null) {
            throw new ServiceException("退款记录不存在");
        }

        // 验证退款状态
        if (!ReturnMoneyStsEnum.APPLY.value().equals(orderRefund.getReturnMoneySts())) {
            throw new ServiceException("当前退款状态不允许拒绝");
        }

        // 更新退款状态为商家拒绝
        orderRefund.setReturnMoneySts(ReturnMoneyStsEnum.REJECT.value());
        orderRefund.setRejectMessage(rejectMessage);
        orderRefund.setRejectTime(new Date());
        boolean updated = updateById(orderRefund);

        // 更新订单的退款状态
        if (updated) {
            Order order = new Order();
            order.setId(orderRefund.getOrderId());
            order.setRefundStatus(RefundStatusEnum.DISAGREE.value());
            orderMapper.updateById(order);
        }

        return updated;
    }

    @Override
    public TableDataInfo<OrderRefund> selectRefundPage(OrderRefund orderRefund, PageQuery pageQuery) {
        MPJLambdaWrapper<OrderRefund> queryWrapper = JoinWrappers.lambda(OrderRefund.class)
            .leftJoin(Order.class, Order::getId, OrderRefund::getOrderId)
            .selectAll(OrderRefund.class)
            .select(Order::getOrderNo)
            // 退款的时候，额外显示订单数据
            //.selectAs(Order::getStatus,"`order.status`");


            // 根据退款编号查询
            .like(StringUtils.isNotEmpty(orderRefund.getRefundSn()),
                OrderRefund::getRefundSn, orderRefund.getRefundSn())
            // 根据店铺ID查询
            .eq(orderRefund.getShopId() != null,
                OrderRefund::getShopId, orderRefund.getShopId())
            // 电话号码查询
            .like(orderRefund.getBuyerMobile() != null,
                OrderRefund::getBuyerMobile, orderRefund.getBuyerMobile())
            // 退货理由查询
            .like(orderRefund.getBuyerReason() != null,
                OrderRefund::getBuyerReason, orderRefund.getBuyerReason())
            // 根据订单编号查询
            .like(orderRefund.getOrderNo() != null,
                Order::getOrderNo, orderRefund.getOrderNo())
            // 根据用户ID查询
            .eq(orderRefund.getUserId() != null,
                OrderRefund::getUserId, orderRefund.getUserId())
            // 根据订单ID查询
            .eq(orderRefund.getOrderId() != null,
                OrderRefund::getOrderId, orderRefund.getOrderId())
            // 根据退款状态查询
            .eq(orderRefund.getReturnMoneySts() != null,
                OrderRefund::getReturnMoneySts, orderRefund.getReturnMoneySts())
            // 按创建时间降序排序
            .orderByDesc(OrderRefund::getCreateTime);

        Page<OrderRefund> page = page(pageQuery.build(), queryWrapper);

        return TableDataInfo.build(page);
    }

    @Override
    public OrderRefund getRefundDetail(Long refundId, boolean isShopMode) {
        OrderRefund refund = getById(refundId);
        if (refund != null && isShopMode) {
            Order order = orderMapper.selectById(refund.getOrderId());
            refund.setOrder(order);
        }
        return refund;
    }
}
