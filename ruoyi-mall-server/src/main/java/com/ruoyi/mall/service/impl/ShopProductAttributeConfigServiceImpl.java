package com.ruoyi.mall.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.Product;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.ShopProductAttributeConfig;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.mapper.ShopProductAttributeConfigMapper;
import com.ruoyi.mall.service.IProductService;
import com.ruoyi.mall.service.IShopProductAttributeConfigService;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.mall.service.ITzUserService;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ShopProductAttributeConfigServiceImpl extends ServiceImpl<ShopProductAttributeConfigMapper, ShopProductAttributeConfig> implements IShopProductAttributeConfigService {


    private final ITzUserService tzUserService;
    private final SysUserMapper sysUserMapper;
    private final IShopService shopService;
    private final IProductService productService;

    @Override
    public Integer insertShopProductAttributeConfig(ShopProductAttributeConfig shopProductAttributeConfig) {

        Shop shop=shopService.getById(shopProductAttributeConfig.getShopId());
        if(shop==null){
            throw new ServiceException("商铺不存在");
        }else if(!shop.getType().equals("1")){
            throw  new ServiceException("当前账号不是商家");
        }

        SysUser sysUser = sysUserMapper.selectById(shopProductAttributeConfig.getShopId());
        if(sysUser==null){
            throw  new ServiceException("系统用户不存在");
        }
        TzUser tzUser = tzUserService.getById(sysUser.getTzUserId());
        if(tzUser==null){
            throw  new ServiceException("用户不存在");
        }else if(!tzUser.getUserType().equals("B")){
            throw new ServiceException("当前用户不是商家用户,没有该权限功能");
        }

        return baseMapper.insert(shopProductAttributeConfig);
    }

    @Override
    public TableDataInfo<ShopProductAttributeConfig> selectShopProductAttributeConfigList(ShopProductAttributeConfig shopProductAttributeConfig, PageQuery pageQuery) {

        LambdaQueryWrapper<ShopProductAttributeConfig> lqw = new LambdaQueryWrapper<>();
        lqw
            .eq(StringUtils.isNotEmpty(shopProductAttributeConfig.getProductId().toString()),ShopProductAttributeConfig::getProductId, shopProductAttributeConfig.getProductId())
            .eq(StringUtils.isNotEmpty(shopProductAttributeConfig.getShopId().toString()),ShopProductAttributeConfig::getShopId, shopProductAttributeConfig.getShopId())
            .eq(StringUtils.isNotEmpty(shopProductAttributeConfig.getType()),ShopProductAttributeConfig::getType, shopProductAttributeConfig.getType())
            .eq(StringUtils.isNotEmpty(shopProductAttributeConfig.getStatus()),ShopProductAttributeConfig::getStatus, shopProductAttributeConfig.getStatus())
            .like(StringUtils.isNotEmpty(shopProductAttributeConfig.getSearchValue()),ShopProductAttributeConfig::getSearchValue, shopProductAttributeConfig.getSearchValue())
            .orderByDesc(ShopProductAttributeConfig::getCreateTime);

        return TableDataInfo.build(page(pageQuery.build(), lqw));
    }

    @Override
    public Integer updateShopProductAttributeConfig(Long productId, Long shopId, String status) {

        LambdaQueryWrapper<ShopProductAttributeConfig> wrapper=new LambdaQueryWrapper<>();
        wrapper
            .eq(ShopProductAttributeConfig::getProductId, productId)
            .eq(ShopProductAttributeConfig::getShopId, shopId);

        ShopProductAttributeConfig shopProductAttributeConfig = getOne(wrapper);

        if(shopProductAttributeConfig==null){
            throw new ServiceException("商品属性配置不存在");
        }

        LambdaQueryWrapper<Product> wrapper1=new LambdaQueryWrapper<>();
        wrapper1.eq(Product::getId, productId);

        Product product = productService.getOne(wrapper1);
        if(product==null){
            throw new ServiceException("商品不存在");
        }

        LambdaUpdateWrapper<ShopProductAttributeConfig> lqw = new LambdaUpdateWrapper<>();
        lqw
            .eq(ShopProductAttributeConfig::getProductId, productId)
            .eq(ShopProductAttributeConfig::getShopId, shopId)
            .set(ShopProductAttributeConfig::getStatus, status);

        if(update(lqw)){
            if(status.equals("0")){//未支付
                throw new ServiceException("请支付");
            }else if(status.equals("1")){//支付成功
                //0:推荐，1：新品，2：热卖)
               LambdaUpdateWrapper<Product> lqw1 = new LambdaUpdateWrapper<>();
               String type=shopProductAttributeConfig.getType();
               if(type.equals("1")){
                   lqw1.set(Product::getIsRecommend, "1");
               }else if(type.equals("2")){
                   lqw1.set(Product::getIsNew, "1");
               }else if(type.equals("3")){
                   lqw1.set(Product::getIsHot, "1");
               }

               lqw1.eq(Product::getId, productId);
               if(productService.update(lqw1)){
                  return 1;
               }

            }else if(status.equals("2")){//支付失败
                throw  new ServiceException("支付失败");
            }
        }

        return 0;
    }


}
