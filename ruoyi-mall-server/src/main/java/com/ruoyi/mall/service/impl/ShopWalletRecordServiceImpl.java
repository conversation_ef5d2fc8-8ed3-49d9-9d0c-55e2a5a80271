package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.OrderNoGenerator;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.ShopWalletRecord;
import com.ruoyi.mall.domain.query.QueryShopWalletRecordDTO;
import com.ruoyi.mall.enums.TradeMethodEnum;
import com.ruoyi.mall.enums.TradeTypeEnum;
import com.ruoyi.mall.mapper.ShopWalletRecordMapper;
import com.ruoyi.mall.service.IShopWalletRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商家钱包交易记录服务实现类
 */
@RequiredArgsConstructor
@Service
public class ShopWalletRecordServiceImpl implements IShopWalletRecordService {

    private final ShopWalletRecordMapper shopWalletRecordMapper;

    /**
     * 查询商家钱包交易记录列表
     *
     * @param shopWalletRecord 商家钱包交易记录
     * @param pageQuery        分页对象
     * @return 商家钱包交易记录分页数据
     */
    @Override
    public TableDataInfo<ShopWalletRecord> queryPageList(ShopWalletRecord shopWalletRecord, PageQuery pageQuery) {
        LambdaQueryWrapper<ShopWalletRecord> lqw = buildQueryWrapper(shopWalletRecord);
        Page<ShopWalletRecord> page = shopWalletRecordMapper.selectPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<ShopWalletRecord> buildQueryWrapper(ShopWalletRecord shopWalletRecord) {
        LambdaQueryWrapper<ShopWalletRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(shopWalletRecord.getWalletId() != null, ShopWalletRecord::getWalletId, shopWalletRecord.getWalletId());
        lqw.eq(shopWalletRecord.getShopId() != null, ShopWalletRecord::getShopId, shopWalletRecord.getShopId());
        lqw.eq(StringUtils.isNotBlank(shopWalletRecord.getTradeNo()), ShopWalletRecord::getTradeNo, shopWalletRecord.getTradeNo());
        lqw.eq(StringUtils.isNotBlank(shopWalletRecord.getOrderNo()), ShopWalletRecord::getOrderNo, shopWalletRecord.getOrderNo());
        lqw.eq(shopWalletRecord.getTradeType() != null, ShopWalletRecord::getTradeType, shopWalletRecord.getTradeType());
        lqw.eq(shopWalletRecord.getTradeMethod() != null, ShopWalletRecord::getTradeMethod, shopWalletRecord.getTradeMethod());
        lqw.eq(StringUtils.isNotBlank(shopWalletRecord.getStatus()), ShopWalletRecord::getStatus, shopWalletRecord.getStatus());
        lqw.orderByDesc(ShopWalletRecord::getCreateTime);
        return lqw;
    }

    /**
     * 获取商家钱包交易记录详细信息
     *
     * @param id 记录ID
     * @return 商家钱包交易记录
     */
    @Override
    public ShopWalletRecord getById(Long id) {
        return shopWalletRecordMapper.selectById(id);
    }

    /**
     * 新增商家钱包交易记录
     *
     * @param shopWalletRecord 商家钱包交易记录
     * @return 结果
     */
    @Override
    public boolean save(ShopWalletRecord shopWalletRecord) {
        if (StringUtils.isEmpty(shopWalletRecord.getTradeNo())) {
            // 生成交易单号
            shopWalletRecord.setTradeNo(generateTradeNo());
        }
        if (StringUtils.isEmpty(shopWalletRecord.getStatus())) {
            shopWalletRecord.setStatus("0"); // 默认成功状态
        }
        return shopWalletRecordMapper.insert(shopWalletRecord) > 0;
    }

    /**
     * 删除商家钱包交易记录
     *
     * @param id 记录ID
     * @return 结果
     */
    @Override
    public boolean removeById(Long id) {
        return shopWalletRecordMapper.deleteById(id) > 0;
    }

    /**
     * 创建交易记录
     *
     * @param walletId      钱包ID
     * @param shopId        商家ID
     * @param amount        交易金额
     * @param tradeType     交易类型
     * @param tradeMethod   交易方式
     * @param beforeBalance 交易前余额
     * @param afterBalance  交易后余额
     * @param orderNo       关联订单号
     * @param remark        备注
     * @return 结果
     */
    @Override
    public boolean createRecord(Long walletId, Long shopId, BigDecimal amount, TradeTypeEnum tradeType,
                                TradeMethodEnum tradeMethod, BigDecimal beforeBalance, BigDecimal afterBalance,
                                String orderNo, String remark) {
        ShopWalletRecord record = new ShopWalletRecord();
        record.setWalletId(walletId);
        record.setShopId(shopId);
        record.setTradeNo(generateTradeNo());
        record.setOrderNo(orderNo);
        record.setAmount(amount);
        record.setBeforeBalance(beforeBalance);
        record.setAfterBalance(afterBalance);
        record.setTradeType(tradeType.getValue());
        record.setTradeMethod(tradeMethod.getValue());
        record.setRemark(remark);
        record.setStatus("0"); // 默认成功状态

        return save(record);
    }

    @Override
    public List<ShopWalletRecord> getShopWalletRecordList(QueryShopWalletRecordDTO query) {

        //获取年/月以及总交易数量
        List<ShopWalletRecord> shopWalletRecordList = getShopWalletRecordTime(query.getShopId(), query.getPageNUm(), query.getPageSize());

        if (!shopWalletRecordList.isEmpty()) {
            for (ShopWalletRecord s : shopWalletRecordList) {
                LambdaQueryWrapper<ShopWalletRecord> wrapper = new LambdaQueryWrapper<>();
                wrapper
                    .eq(ShopWalletRecord::getShopId, query.getShopId())
                    .last(" DATE_FORMAT(create)time,'%Y-%m')= " + s.getMonth())
                    .orderByDesc(ShopWalletRecord::getCreateTime);
                s.setShopWalletRecord(shopWalletRecordMapper.selectList(wrapper));
            }
            return shopWalletRecordList;
        }
        return null;
    }

    /**
     * 生成交易单号
     */
    private String generateTradeNo() {
        return OrderNoGenerator.generateOrderNo("TW");
    }

    /**
     * 根据商家ID获取交易年月，总计交易数量
     *
     * @param shopId 商家ID
     */
    private List<ShopWalletRecord> getShopWalletRecordTime(Long shopId, Integer pageNum, Integer pageSize) {

        MPJLambdaWrapper<ShopWalletRecord> wrapper = new MPJLambdaWrapper<>();
        wrapper.select("DATE_FORMAT(create_time, '%Y-%m') as month, SUM(amount) as totalAmount")
            .eq(ShopWalletRecord::getShopId, shopId)
            .groupBy("month") // 按月份分组统计
            .last(" LIMIT " + (pageNum - 1) * pageSize + " ," + pageSize);

        return shopWalletRecordMapper.selectList(wrapper);
    }
}
