package com.ruoyi.mall.service;

import com.github.yulichang.base.MPJBaseService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.Product;
import com.ruoyi.mall.domain.query.QueryConsignmentProductDTO;
import com.ruoyi.mall.domain.query.QueryProductByCategoryIdDTO;
import com.ruoyi.mall.domain.query.QueryProductByShopQueryDTO;
import com.ruoyi.mall.domain.query.QueryShopByTypeGetProductDTO;
import com.ruoyi.mall.domain.vo.DaiXiaoInfoVo;
import com.ruoyi.mall.domain.vo.ProductInfoVo;
import com.ruoyi.mall.domain.vo.ProductVo;

import java.util.List;

/**
 * 商品Service接口
 *
 * <AUTHOR>
 */
public interface IProductService extends MPJBaseService<Product> {

    /**
     * 查询商品列表
     *
     * @param product 商品信息
     * @return 商品集合
     */
    List<Product> selectProductList(Product product);

    /**
     * 查询商品分页列表
     *
     * @param product   商品信息
     * @param pageQuery 分页参数
     * @return 商品分页数据
     */
    TableDataInfo<Product> selectProductPage(Product product, PageQuery pageQuery);

    /**
     * 查询商品详细信息
     *
     * @param id 商品ID
     * @return 商品信息
     */
    Product selectProductById(Long id);

    /**
     * 新增商品
     *
     * @param product 商品信息
     * @return 结果
     */
    boolean insertProduct(Product product);

    /**
     * 修改商品
     *
     * @param product 商品信息
     * @return 结果
     */
    boolean updateProduct(Product product);

    /**
     * 批量删除商品
     *
     * @param ids 需要删除的商品ID
     * @return 结果
     */
    boolean deleteProductByIds(Long[] ids);

    /**
     * 删除商品信息
     *
     * @param id 商品ID
     * @return 结果
     */
    boolean deleteProductById(Long id);

    /**
     * 分页查询商品列表
     *
     * @param product   查询条件
     * @param pageQuery 分页条件
     * @return 商品分页列表
     */
    TableDataInfo<Product> queryPageList(Product product, PageQuery pageQuery);

    /**
     * 获取商品详情
     *
     * @param productId 商品ID
     * @return 商品详情
     */
    Product getProductDetail(Long productId);

    /**
     * 保存商品及其SKU信息
     *
     * @param product 商品信息
     * @return 是否成功
     */
    Long saveProductWithSkus(Product product);

    /**
     * 更新商品及其SKU信息
     *
     * @param product 商品信息
     * @return 是否成功
     */
    boolean updateProductWithSkus(Product product);

    /**
     * 检查商品名称是否唯一
     *
     * @param product 商品信息
     * @return true: 不存在重名, false: 存在重名
     */
    boolean checkProductNameUnique(Product product);

    /**
     * 批量更新商品上下架状态
     *
     * @param productIds 商品ID列表
     * @param status     上架状态: 0->下架, 1->上架
     * @return 是否成功
     */
    boolean batchUpdatePublishStatus(List<Long> productIds, String status);

    /**
     * 更新商品库存
     *
     * @param productId 商品ID
     * @param count     变动数量 (正数增加库存，负数减少库存)
     * @return 是否成功
     */
    boolean updateStock(Long productId, int count);

    /**
     * 添加销量
     *
     * @param productId 商品ID
     * @param count     销量增加数量
     * @return 是否成功
     */
    boolean addSales(Long productId, int count);

    /**
     * 根据不同类型查询产品；列表
     *
     * @param type   1：新品推荐，2：猜你喜欢，3：销量排行
     * @param userId 用户id
     */
    TableDataInfo<ProductVo> getProductByType(Integer type, Long userId, PageQuery pageQuery);

    /**
     * 获取代销商品列表
     *
     * @param pageQuery 分页参数
     * @return
     * @ userId 用户ID
     */
    TableDataInfo<DaiXiaoInfoVo> getDaiXiaoProductList(Long userId, QueryConsignmentProductDTO query, PageQuery pageQuery);

    /**
     * 获取店铺下的代销产品列表
     *
     * @param shopId    店铺ID
     * @param pageQuery 分页参数
     * @return
     */
    TableDataInfo<DaiXiaoInfoVo> getShopDaiXiaoProductList(Long shopId, QueryConsignmentProductDTO queryConsignmentProductDTO, PageQuery pageQuery);

    /**
     * 设置代销商品设置跳转url
     *
     * @param productId 代销商品ID
     * @return
     */
    int updateDaiXiaoProductUrl(Long productId);

    /**
     * 增加商品库存
     *
     * @param productId 商品ID
     * @param quantity  增加数量
     * @return 是否成功
     */
    boolean increaseStock(Long productId, Integer quantity);


    /**
     * 减少商品库存
     *
     * @param productId 商品ID
     * @param quantity  减少数量
     * @return 是否成功
     */
    boolean decreaseStock(Long productId, Integer quantity);

    /**
     * 随机查询代销产品列表
     *
     * @return
     */
    TableDataInfo<ProductVo> getRandomConsignmentProductPage(PageQuery pageQuery);

    /**
     * 分页随机查询产品列表
     *
     * @return
     */
    TableDataInfo<ProductVo> getRandomProductPage(PageQuery pageQuery);

    /**
     * 商家取消代销产品
     *
     * @param productId 产品ID
     * @return
     */
    void cancelConsignmentProduct(Long productId);

    /**
     * 根据与产品ID获取产品详情（app）
     *
     * @param productId 产品ID
     * @param shopId    代销产品传代销ID，反之为商铺ID
     * @return
     */
    ProductInfoVo getAppProductInfo(Long productId, Long shopId);

    /**
     * 根据商铺不同类型获取列表
     *
     * @param query 查询条件
     * @return
     */
    TableDataInfo<ProductVo> getProductByType(QueryShopByTypeGetProductDTO query, PageQuery pageQuery);


    /**
     * 在店铺里面根据查询条件获取商品列表
     *
     * @param query 查询参数
     * @return
     */
    TableDataInfo<ProductVo> getProductByShopQuery(QueryProductByShopQueryDTO query, PageQuery pageQuery);

    /**
     * 首页根据与分类ID获取产品列表
     *
     * @return
     */
    TableDataInfo<ProductVo> getHomeProductByCategoryId(Long categoryId, String query, PageQuery pageQuery);

    /**
     * 根据关键字获取商品列表
     *
     * @return
     */
    TableDataInfo<ProductVo> getProductByKeyword(String keyword, PageQuery pageQuery);

    /**
     * 根据用户当前购买成功的商品进行推荐
     *
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<ProductVo> getProductByUserBuyProduct(Integer pageNum, Integer pageSize, Long productId);


    /**
     * 在店铺内根据商品分类ID获取商品列表
     *
     * @return
     */
    TableDataInfo<ProductVo> getShopProductByCategoryId(QueryProductByCategoryIdDTO queryProductByCategoryIdDTO, PageQuery pageQuery);
}
