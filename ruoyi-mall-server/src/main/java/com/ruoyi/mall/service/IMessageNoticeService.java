package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.MessageNotice;

public interface IMessageNoticeService extends IService<MessageNotice> {

    /**
     * 新增消息通知
     *
     * @param messageNotice 消息通知对象
     * @return
     */
    int AddMessageNotice(MessageNotice messageNotice);

    /**
     * 批量删除消息ids
     *
     * @param ids 消息ID集合
     * @return
     */
    boolean deleteMessageNoticeByIds(Long[] ids);

    /**
     * 获取当前用户的消息通知列表
     *
     * @param messageNotice 消息通知对象
     * @param pageQuery     分页对象
     * @return
     */
    TableDataInfo<MessageNotice> getMessageNoticeList(MessageNotice messageNotice, PageQuery pageQuery);

    /**
     * 根据ID获取消息详情
     *
     * @param id
     * @return
     */
    MessageNotice getMessageNoticeById(Long id);

    /**
     * 标记消息已读
     *
     * @param id 消息ID
     * @return
     */
    Integer editMessageNoticeIsRead(Long id);

}
