package com.ruoyi.mall.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.ShopAdvertisement;
import com.ruoyi.mall.domain.entity.VirtualOrder;
import com.ruoyi.mall.mapper.ShopAdvertisementMapper;
import com.ruoyi.mall.service.IShopAdvertisementService;
import com.ruoyi.mall.service.IVirtualOrderService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.ruoyi.common.helper.LoginHelper.getLoginUser;


@Service
@RequiredArgsConstructor
public class ShopAdvertisementServiceImpl extends MPJBaseServiceImpl<ShopAdvertisementMapper, ShopAdvertisement> implements IShopAdvertisementService {

    private final ShopAdvertisementMapper shopAdvertisementMapper;
    private final IVirtualOrderService virtualOrderService;

    @Override
    public Boolean addShopAdvertisement(ShopAdvertisement shopAdvertisement) {
        validateAdvertisement(shopAdvertisement);

        shopAdvertisement.setShopId(getLoginUser().getShopId());
        shopAdvertisement.setStatus("0");
        return save(shopAdvertisement);
    }

    @Override
    @Transactional
    public Integer deleteShopAdvertisement(Long id) {
        ShopAdvertisement advertisement = getById(id);
        if (advertisement == null) {
            throw new ServiceException("广告不存在");
        }

        if (!"0".equals(advertisement.getStatus()) && !"1".equals(advertisement.getStatus())) {
            throw new ServiceException("只能删除待支付状态的广告");
        }
        int flag = shopAdvertisementMapper.deleteById(id);
        // 删除广告，如果广告已经抽奖了支付订单，一起删除
        if (flag > 0) {
            virtualOrderService.remove(
                new LambdaQueryWrapper<VirtualOrder>().eq(VirtualOrder::getOrderId, advertisement.getId())
            );
        }
        return flag;
    }

    @Override
    public Integer deleteBatchShopAdvertisement(Long[] ids) {
        int count = 0;
        for (Long id : ids) {
            ShopAdvertisement advertisement = getById(id);
            if (advertisement == null) {
                continue;
            }

            if (!"0".equals(advertisement.getStatus())) {
                continue;
            }
            count += shopAdvertisementMapper.deleteById(id);
        }
        return count;
    }

    @Override
    public ShopAdvertisement selectShopAdvertisementById(Long id) {
        return getById(id);
    }

    @Override
    public TableDataInfo<ShopAdvertisement> selectShopAdvertisementList(ShopAdvertisement shopAdvertisement, PageQuery pageQuery) {

        LambdaQueryWrapper<ShopAdvertisement> wrapper = new LambdaQueryWrapper<>();
        wrapper
//            .ge(StringUtils.isNotEmpty(shopAdvertisement.getStartTime().toString()),ShopAdvertisement::getStartTime,shopAdvertisement.getStartTime())
//            .le(StringUtils.isNotEmpty(shopAdvertisement.getEndTime().toString()),ShopAdvertisement::getEndTime,shopAdvertisement.getEndTime())
            .like(StringUtils.isNotEmpty(shopAdvertisement.getAdName()), ShopAdvertisement::getAdName, shopAdvertisement.getAdName())
            .eq(StringUtils.isNotEmpty(shopAdvertisement.getStatus()), ShopAdvertisement::getStatus, shopAdvertisement.getStatus())
            .eq(ObjUtil.isNotNull(shopAdvertisement.getShopId()), ShopAdvertisement::getShopId, shopAdvertisement.getShopId())
            .orderByDesc(ShopAdvertisement::getCreateTime);
        return TableDataInfo.build(shopAdvertisementMapper.selectList(pageQuery.build(), wrapper));
    }

    @Override
    public Integer updateShopAdvertisement(ShopAdvertisement shopAdvertisement) {
        ShopAdvertisement existingAd = getById(shopAdvertisement.getId());
        if (existingAd == null) {
            throw new ServiceException("广告不存在");
        }

        if ("2".equals(existingAd.getStatus())) {
            throw new ServiceException("已生效的不能修改");
        }

        validateAdvertisement(shopAdvertisement);
        // 修改之后需要重新审核
        shopAdvertisement.setStatus("0");
        return shopAdvertisementMapper.updateById(shopAdvertisement);
    }

    @Override
    public TableDataInfo<ShopAdvertisement> selectAdvertisementList(ShopAdvertisement shopAdvertisement, PageQuery pageQuery) {
        LambdaQueryWrapper<ShopAdvertisement> wrapper = new LambdaQueryWrapper<>();
        wrapper
//            .ge(StringUtils.isNotEmpty(shopAdvertisement.getStartTime().toString()),ShopAdvertisement::getStartTime,shopAdvertisement.getStartTime())
//            .le(StringUtils.isNotEmpty(shopAdvertisement.getEndTime().toString()),ShopAdvertisement::getEndTime,shopAdvertisement.getEndTime())
            .eq(ObjUtil.isNotNull(shopAdvertisement.getShopId()), ShopAdvertisement::getShopId, shopAdvertisement.getShopId())
            .like(StringUtils.isNotEmpty(shopAdvertisement.getAdName()), ShopAdvertisement::getAdName, shopAdvertisement.getAdName())
            .eq(StringUtils.isNotEmpty(shopAdvertisement.getStatus()), ShopAdvertisement::getStatus, shopAdvertisement.getStatus());

        return TableDataInfo.build(shopAdvertisementMapper.selectList(pageQuery.build(), wrapper));
    }

    @Override
    public TableDataInfo<ShopAdvertisement> getAppShopAdvertisementList(Integer type, PageQuery pageQuery) {

        if (!(type == 0 || type == 1 || type == 2)) {
            throw new ServiceException("参数类型错误");
        }
        LambdaQueryWrapper<ShopAdvertisement> wrapper = new LambdaQueryWrapper<>();
        String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        wrapper
            .eq(ShopAdvertisement::getStatus, "2")
            .eq(ShopAdvertisement::getType, type)
            .and(q ->
                q.le(ShopAdvertisement::getStartTime, today)  // 开始时间不晚于今天
                    .or()
                    .ge(ShopAdvertisement::getEndTime, today)    // 或结束时间不早于今天
            )
            .last("ORDER BY RAND()");

        return TableDataInfo.build(shopAdvertisementMapper.selectList(pageQuery.build(), wrapper));
    }

    @SneakyThrows
    @Override
    public Integer updateShopAdvertisementClickNumber(Long id) {

        ShopAdvertisement shopAdvertisement = getById(id);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = sdf.format(new Date());
        Date day = sdf.parse(dateString);
        if (day.compareTo(shopAdvertisement.getStartTime()) < 0 || day.compareTo(shopAdvertisement.getEndTime()) < 0) {
            throw new ServiceException("广告不在投放时间内");
        }

        LambdaUpdateWrapper<ShopAdvertisement> wrapper = new LambdaUpdateWrapper<>();
        wrapper
            .set(ShopAdvertisement::getClickNumber, shopAdvertisement.getClickNumber() + 1)
            .eq(ShopAdvertisement::getId, id);

        return shopAdvertisementMapper.update(wrapper);
    }

    @Override
    public Integer auditAdvertisement(Long id, String status, String remark) {
        ShopAdvertisement existingAd = getById(id);
        if (existingAd == null) {
            throw new ServiceException("广告不存在");
        }

        // 只能审核待审核状态的广告
        if (!"0".equals(existingAd.getStatus())) {
            throw new ServiceException("只能审核待审核状态的广告");
        }

        // 更新广告状态
        LambdaUpdateWrapper<ShopAdvertisement> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ShopAdvertisement::getId, id)
            .set(ShopAdvertisement::getStatus, status);

        // 不再设置有效期，只处理状态变更和备注
        // 审核通过为1(待支付)，拒绝为3
        if ("3".equals(status)) {
            // 设置拒绝理由
            if (StringUtils.isEmpty(remark)) {
                throw new ServiceException("拒绝审核时必须填写备注理由");
            }
            wrapper.set(ShopAdvertisement::getUpdateBy, remark);
        } else if ("1".equals(status)) {
            // 审核通过，但不设置有效期，有效期在支付成功后设置
            if (StringUtils.isNotEmpty(remark)) {
                wrapper.set(ShopAdvertisement::getUpdateBy, remark);
            }
        } else {
            throw new ServiceException("无效的审核状态，只能是1(通过)或3(拒绝)");
        }

        return shopAdvertisementMapper.update(wrapper);
    }

    @Override
    public Integer batchAuditAdvertisement(Long[] ids, String status, String remark) {
        int count = 0;
        for (Long id : ids) {
            try {
                count += auditAdvertisement(id, status, remark);
            } catch (Exception e) {
                log.error("审核广告失败, id: " + id + ", 原因: " + e.getMessage());
                // 继续处理下一个
            }
        }
        return count;
    }

    @Override
    public ShopAdvertisement getAdvertisementRand() {

        LambdaQueryWrapper<ShopAdvertisement> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .eq(ShopAdvertisement::getDelFlag, "0")
            .eq(ShopAdvertisement::getStatus, "1")
            .le(ShopAdvertisement::getStartTime, new SimpleDateFormat("yyyy-MM-dd").format(new Date()))
            .ge(ShopAdvertisement::getEndTime, new SimpleDateFormat("yyyy-MM-dd").format(new Date()))
            .last("ORDER BY RAND() limit 1 ");

        return getOne(queryWrapper);
    }

    private void validateAdvertisement(ShopAdvertisement advertisement) {
        if (StringUtils.isEmpty(advertisement.getAdName())) {
            throw new ServiceException("广告名称不能为空");
        }

        if (advertisement.getAdName().length() > 50) {
            throw new ServiceException("广告名称长度不能超过50个字符");
        }

        if (StringUtils.isEmpty(advertisement.getAdType())) {
            throw new ServiceException("广告类型不能为空");
        }

        if (!("1".equals(advertisement.getAdType()) || "2".equals(advertisement.getAdType()))) {
            throw new ServiceException("广告类型不正确，只能是图片(1)或视频(2)");
        }

        if (StringUtils.isEmpty(advertisement.getAdUrl())) {
            throw new ServiceException("广告展示内容不能为空");
        }

        if ("2".equals(advertisement.getAdType())) {
            String adUrl = advertisement.getAdUrl().toLowerCase();
            if (!(adUrl.endsWith(".mp4") || adUrl.endsWith(".avi") || adUrl.endsWith(".mov") || adUrl.endsWith(".wmv"))) {
                throw new ServiceException("视频格式不支持，请上传mp4、avi、mov或wmv格式的视频");
            }
        }

        if ("1".equals(advertisement.getAdType())) {
            String adUrl = advertisement.getAdUrl().toLowerCase();
            if (!(adUrl.endsWith(".jpg") || adUrl.endsWith(".jpeg") || adUrl.endsWith(".png") || adUrl.endsWith(".gif"))) {
                throw new ServiceException("图片格式不支持，请上传jpg、jpeg、png或gif格式的图片");
            }
        }
    }
}
