package com.ruoyi.mall.service;

/**
 * 商家技术引流服务接口
 */
public interface IShopDrainageService {

    /**
     * 查询商家七天内是否有开通技术引流
     *
     * @param shopId 商家ID
     * @return 是否在七天内开通了技术引流
     */
    Boolean hasShopDrainageWithinSevenDays(Long shopId);

    /**
     * 检查并设置商家技术引流状态
     * 检查商家七天内是否有开通技术引流，没有开通过的设置为失效状态
     *
     * @return 处理的商家数量
     */
    Integer checkAndSetShopDrainageStatus();
}
