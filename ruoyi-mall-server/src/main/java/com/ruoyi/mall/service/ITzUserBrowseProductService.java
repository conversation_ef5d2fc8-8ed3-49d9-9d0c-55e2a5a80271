package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.mall.domain.entity.TzUserBrowseProduct;

import java.util.List;
import java.util.Map;

public interface ITzUserBrowseProductService extends IService<TzUserBrowseProduct> {


    /**
     * 新增用户浏览产品,修改次数
     *
     * @param productId 产品ID
     * @return
     */
    Integer addUserBrowseProduct(Long productId);


    /**
     * 获取给人产品浏览历史
     *
     * @param pageQuery
     * @return
     */
    List<Map<String, Object>> getUserBrowseProductList(PageQuery pageQuery);
}
