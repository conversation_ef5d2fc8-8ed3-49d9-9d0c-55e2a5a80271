package com.ruoyi.mall.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.OrderStatusEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.OrderNoGenerator;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.dto.*;
import com.ruoyi.mall.domain.entity.*;
import com.ruoyi.mall.domain.query.QueryOrderTypeDTO;
import com.ruoyi.mall.domain.vo.CartInfoVo;
import com.ruoyi.mall.domain.vo.CartVo;
import com.ruoyi.mall.domain.vo.OrderExportVo;
import com.ruoyi.mall.domain.vo.OrderVo;
import com.ruoyi.mall.enums.EvaluateSatusEnum;
import com.ruoyi.mall.enums.OrderTypeEnum;
import com.ruoyi.mall.enums.PayTypeEnum;
import com.ruoyi.mall.mapper.OrderItemMapper;
import com.ruoyi.mall.mapper.OrderMapper;
import com.ruoyi.mall.service.*;
import com.ruoyi.pay.domain.PayOrder;
import com.ruoyi.pay.enums.DeductionPaymentStatusEnum;
import com.ruoyi.pay.enums.PayStatusEnum;
import com.ruoyi.pay.service.IDeductionPaymentRecordService;
import com.ruoyi.pay.service.IPayService;
import com.ruoyi.pay.service.IUserBalanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.helper.LoginHelper.getUserId;

/**
 * 订单Service业务层处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderServiceImpl extends MPJBaseServiceImpl<OrderMapper, Order> implements IOrderService {

    private final OrderItemMapper orderItemMapper;
    private final IOrderItemService orderItemService;
    private final ICartService cartService;
    private final IDeliveryService deliveryService;
    private final IPayService payService;
    private final IShopService shopService;
    private final IProductService productService;
    private final ISkuService skuService;
    private final ITzUserService userService;
    private final IUserBalanceService userBalanceService;
    private final IDeductionPaymentRecordService deductionPaymentRecordService;

    @Override
    public List<Order> selectOrderList(Order order) {
        MPJLambdaWrapper<Order> lqw = new MPJLambdaWrapper<>();
        lqw.leftJoin(TzUser.class, TzUser::getUserId, Order::getUserId)
            .selectAll(Order.class)
            .selectAs(TzUser::getPhone, Order::getUserPhone);

        lqw.eq(order.getUserId() != null, Order::getUserId, order.getUserId())
            .eq(order.getShopId() != null, Order::getShopId, order.getShopId())
            .like(StrUtil.isNotBlank(order.getOrderNo()), Order::getOrderNo, order.getOrderNo())
            .like(StrUtil.isNotBlank(order.getOrderName()), Order::getOrderName, order.getOrderName())
            .like(StrUtil.isNotBlank(order.getReceiverName()), Order::getReceiverName, order.getReceiverName())
            .like(StrUtil.isNotBlank(order.getReceiverPhone()), Order::getReceiverPhone, order.getReceiverPhone())
            .like(StrUtil.isNotBlank(order.getDeliveryNumber()), Order::getDeliveryNumber, order.getDeliveryNumber())
            .eq(StringUtils.isNotBlank(order.getStatus()), Order::getStatus, order.getStatus())
            .eq(StringUtils.isNotBlank(order.getPayType()), Order::getPayType, order.getPayType())
            .ge(order.getParams() != null && order.getParams().get("startTime") != null, Order::getCreateTime, order.getParams().get("startTime"))
            .le(order.getParams() != null && order.getParams().get("endTime") != null, Order::getCreateTime, order.getParams().get("endTime"))
            .orderByDesc(Order::getCreateTime);
        return list(lqw);
    }

    @Override
    public TableDataInfo<Order> selectOrderPage(Order order, PageQuery pageQuery) {
        MPJLambdaWrapper<Order> lqw = new MPJLambdaWrapper<>();
        lqw.leftJoin(TzUser.class, TzUser::getUserId, Order::getUserId)
            .selectAll(Order.class)
            .selectAs(TzUser::getPhone, Order::getUserPhone);

        lqw.eq(order.getUserId() != null, Order::getUserId, order.getUserId())
            .eq(order.getShopId() != null, Order::getShopId, order.getShopId())
            .eq(order.getConsignmentId() != null, Order::getConsignmentId, order.getConsignmentId())
            .like(StrUtil.isNotBlank(order.getOrderNo()), Order::getOrderNo, order.getOrderNo())
            .like(StrUtil.isNotBlank(order.getOrderName()), Order::getOrderName, order.getOrderName())
            .like(StrUtil.isNotBlank(order.getReceiverName()), Order::getReceiverName, order.getReceiverName())
            .like(StrUtil.isNotBlank(order.getReceiverPhone()), Order::getReceiverPhone, order.getReceiverPhone())
            .like(StrUtil.isNotBlank(order.getDeliveryNumber()), Order::getDeliveryNumber, order.getDeliveryNumber())
            .eq(StringUtils.isNotBlank(order.getStatus()), Order::getStatus, order.getStatus())
            .eq(StringUtils.isNotBlank(order.getPayType()), Order::getPayType, order.getPayType())
            .ge(order.getParams() != null && order.getParams().get("startTime") != null, Order::getCreateTime, order.getParams().get("startTime"))
            .le(order.getParams() != null && order.getParams().get("endTime") != null, Order::getCreateTime, order.getParams().get("endTime"))
            .orderByDesc(Order::getCreateTime);
        Page<Order> page = page(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    public Order selectOrderByOrderNo(String orderNo) {
        // 1. 先查询订单基本信息
        LambdaQueryWrapper<Order> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(Order::getOrderNo, orderNo)
            .eq(Order::getDelFlag, 0);
        Order order = getOne(orderWrapper);

        if (order == null) {
            return null;
        }

        // 2. 查询用户信息并设置到订单中
        if (order.getUserId() != null) {
            TzUser user = userService.getById(order.getUserId());
            if (user != null) {
                order.setUserPhone(user.getPhone());
                order.setUserNickName(user.getNickname());
            }
        }

        // 3. 查询商家信息并设置到订单中
        if (order.getShopId() != null) {
            Shop shop = shopService.getById(order.getShopId());
            if (shop != null) {
                order.setShopName(shop.getName());
                order.setShopLogo(shop.getLogo());
                order.setShopPhone(shop.getPhone());
            }
        }

        // 4. 查询订单商品项
        LambdaQueryWrapper<OrderItem> itemWrapper = new LambdaQueryWrapper<>();
        itemWrapper.eq(OrderItem::getOrderId, order.getId())
            .eq(OrderItem::getDelFlag, 0);
        List<OrderItem> orderItems = orderItemService.list(itemWrapper);

        // 5. 为每个订单商品项补充商品详细信息
        if (orderItems != null && !orderItems.isEmpty()) {
            for (OrderItem orderItem : orderItems) {
                // 查询商品信息
                Product product = productService.getById(orderItem.getProductId());
                if (product != null) {
                    // 如果订单商品项的商品名称为空，使用商品表中的名称
                    if (orderItem.getProductName() == null || orderItem.getProductName().isEmpty()) {
                        orderItem.setProductName(product.getName());
                    }
                    // 如果订单商品项没有图片，使用商品第一张图片
                    if (orderItem.getProductImage() == null || orderItem.getProductImage().isEmpty()) {
                        String images = product.getImages();
                        if (images != null && !images.isEmpty()) {
                            // 取第一张图片
                            String firstImage = images.split(",")[0];
                            orderItem.setProductImage(firstImage);
                        }
                    }
                }

                // 查询SKU信息
                if (orderItem.getSkuId() != null) {
                    Sku sku = skuService.getById(orderItem.getSkuId());
                    if (sku != null) {
                        // 如果订单商品项的规格为空，使用SKU表中的规格
                        if (orderItem.getSkuSpec() == null || orderItem.getSkuSpec().isEmpty()) {
                            orderItem.setSkuSpec(sku.getValue());
                        }
                        // 如果订单商品项没有图片，优先使用SKU图片
                        if (orderItem.getProductImage() == null || orderItem.getProductImage().isEmpty()) {
                            orderItem.setProductImage(sku.getImage());
                        }
                    }
                }
            }
            order.setOrderItems(orderItems);
        }

        return order;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createOrderByCartIds(OrderCreateDTO orderCreateDTO) {

        if (getUserId() == null) {
            throw new ServiceException("请先登录");
        }

        //获取购物车下单的店铺以及店下商品
        List<CartInfoVo> cartInfoVoList = cartService.getOrderPreview(orderCreateDTO.getCartIds()).getCartInfoList();
        if (cartInfoVoList.isEmpty()) {
            throw new ServiceException("请选择你要购买的商品");
        }

        List<String> orderNos = new ArrayList<>();
        boolean isFirstOrder = true;
        //总手续费
        BigDecimal totalFeeAmount = BigDecimal.ZERO;

        //为每个店铺/代销商家创建订单
        for (CartInfoVo cartInfoVo : cartInfoVoList) {
            Long shopId = cartInfoVo.getShopId();
            Long consignmentId = null;
            if (cartInfoVo.getType().equals("0")) {//代销
                consignmentId = cartInfoVo.getShopId();
                shopId = productService.getById(cartInfoVo.getCart().get(0).getProductId()).getShopId();
            }

            // 创建订单
            Order order = new Order();
            order.setOrderNo(generateOrderNo());
            order.setUserId(getUserId());
            order.setShopId(shopId);
            order.setConsignmentId(consignmentId);
            order.setRemark(orderCreateDTO.getRemark());//备注
            // 待付款
            order.setStatus(OrderStatusEnum.PENDING_PAYMENT.getCode());
            // 待支付
            order.setPayStatus(PayStatusEnum.PENDING.getCode());
            order.setDelFlag("0");

            // 设置订单组信息
            order.setOrderGroupNo(generateOrderGroupNo());
            if (isFirstOrder) {
                order.setIsMainOrder(1);
                order.setOrderGroupCount(cartInfoVoList.size());
                isFirstOrder = false;
            } else {
                order.setIsMainOrder(0);
            }

            //设置收货信息
            order.setReceiverType(orderCreateDTO.getReceiverType());//货物提货方式
            order.setReceiverName(orderCreateDTO.getReceiverName());
            order.setReceiverPhone(orderCreateDTO.getReceiverPhone());
            order.setReceiverCity(orderCreateDTO.getReceiverCity());
            order.setReceiverProvince(orderCreateDTO.getReceiverProvince());
            order.setReceiverDistrict(orderCreateDTO.getReceiverDistrict());
            order.setReceiverAddress(orderCreateDTO.getReceiverAddress());
            order.setReceiverAddressAll(orderCreateDTO.getReceiverProvince() + orderCreateDTO.getReceiverCity() + orderCreateDTO.getReceiverDistrict() + orderCreateDTO.getReceiverAddress());

            //创建订单商品
            List<OrderItem> orderItems = new ArrayList<>();
            //商品总金额
            BigDecimal totalAmount = BigDecimal.ZERO;
//            支付总金额
//            BigDecimal totalPayAmount = BigDecimal.ZERO;
            //商品数量
            long totalQuantity = 0;

            List<String> orderNameList = new ArrayList<>();
            for (CartVo cart : cartInfoVo.getCart()) {

                //商品总价
                BigDecimal productPrice = cart.getPrice().multiply(new BigDecimal(cart.getQuantity())).setScale(2, RoundingMode.HALF_UP);
                //实际支付金额 商品价格+手续费-优惠金额
//                BigDecimal payAmount = productPrice;

                OrderItem orderItem = new OrderItem();
                orderItem.setProductId(cart.getProductId());
                orderItem.setSkuId(cart.getSkuId());
                orderItem.setProductName(cart.getProductName());
                orderItem.setProductImage(cart.getProductImage());
                orderItem.setSkuId(cart.getSkuId());
                orderItem.setSkuSpec(cart.getSkuName() + ";" + cart.getSkuValue());
                orderItem.setProductPrice(cart.getPrice());
                orderItem.setQuantity(cart.getQuantity());
                orderItem.setDiscountAmount(BigDecimal.ZERO);//优惠金额
                orderItem.setFeeBearer(cart.getFeeBearer());
                orderItem.setTotalAmount(productPrice);
//                orderItem.setPayAmount(payAmount);
                orderItem.setRemark(orderCreateDTO.getRemark());
                orderItem.setCommentStatus("0");
                orderItem.setRefundStatus("0");
                orderItem.setDelFlag("0");

                orderNameList.add(orderItem.getProductName());
                orderItems.add(orderItem);
                totalAmount = totalAmount.add(productPrice);
//                totalPayAmount = totalPayAmount.add(payAmount);
                totalQuantity = totalQuantity + cart.getQuantity();

                // 在保存订单之前处理库存扣减
                handleStockDeduction(orderItem);
            }

            // 设置订单金额
            order.setOrderName(StringUtils.join(orderNameList, ","));
            order.setTotalAmount(totalAmount);
            order.setTotalQuantity(totalQuantity);
            order.setFreightAmount(BigDecimal.ZERO);
            order.setDiscountAmount(BigDecimal.ZERO);
//            order.setPayAmount(totalPayAmount);
            order.setFeeAmount(totalFeeAmount);//总手续费=所有商品手续费总和
            order.setDeductionAmount(BigDecimal.ZERO);//使用抵扣手续费
            order.setFreightAmount(BigDecimal.ZERO);//运费险
            order.setPayType(PayTypeEnum.ALI_PAY.getNumber());//支付类型

            // 保存订单
            boolean save = save(order);
            if (!save) {
                throw new ServiceException("创建订单失败");
            }
            for (OrderItem orderItem : orderItems) {
                orderItem.setOrderId(order.getId());
                orderItem.setOrderNo(order.getOrderNo());
                orderItem.setUserId(order.getUserId());
                orderItem.setShopId(order.getShopId());
            }
            orderItemMapper.insertBatch(orderItems);

            orderNos.add(order.getOrderNo());
        }

        // 5. 清空购物车
        cartService.deleteCartByIds(orderCreateDTO.getCartIds().stream().toArray(Long[]::new));

        // 6. 返回订单号列表（用逗号分隔）
        return String.join(",", orderNos);
    }

    @Override
    public String createOrderByProductId(CreateOrderByProductIdDTO createOrderByProductIdDTO) {

        if (getUserId() == null) {
            throw new ServiceException("请先登录");
        }

        if (StringUtils.isEmpty(createOrderByProductIdDTO.getProductId().toString())) {
            throw new ServiceException("请选择你要购买商品");
        }

        Product product = productService.getById(createOrderByProductIdDTO.getProductId());
        if (product == null) {
            throw new ServiceException("商品不存在");
        } else if (product.getStatus().equals("0")) {
            throw new ServiceException("商品已下架");
        }

        Sku sku = skuService.getById(createOrderByProductIdDTO.getSkuId());
        if (sku == null) {
            throw new ServiceException("商品规格不存在");
        } else if (sku.getStock() < createOrderByProductIdDTO.getQuantity()) {
            throw new ServiceException("商品规格库存不足");
        }

        //判断当前购买商品从店铺购买还是代销商家购买
        Long shopId = createOrderByProductIdDTO.getShopId();
        Long consignmentId = null;
        if (shopService.getById(shopId).getType().equals("0")) {//代销
            consignmentId = createOrderByProductIdDTO.getShopId();
            shopId = productService.getById(createOrderByProductIdDTO.getProductId()).getShopId();
        }

        // 创建订单
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setUserId(getUserId());
        order.setShopId(shopId);
        order.setConsignmentId(consignmentId);
        order.setRemark(createOrderByProductIdDTO.getRemark());//备注
        // 待付款
        order.setStatus(OrderStatusEnum.PENDING_PAYMENT.getCode());
        // 待支付
        order.setPayStatus(PayStatusEnum.PENDING.getCode());
        order.setIsMainOrder(0);//不是主订单
        order.setDelFlag("0");//删除标志

        //设置收货信息
        order.setReceiverType(createOrderByProductIdDTO.getReceiverType());//货物提货方式
        order.setReceiverName(createOrderByProductIdDTO.getReceiverName());
        order.setReceiverPhone(createOrderByProductIdDTO.getReceiverPhone());
        order.setReceiverCity(createOrderByProductIdDTO.getReceiverCity());
        order.setReceiverProvince(createOrderByProductIdDTO.getReceiverProvince());
        order.setReceiverDistrict(createOrderByProductIdDTO.getReceiverDistrict());
        order.setReceiverAddress(createOrderByProductIdDTO.getReceiverAddress());
        order.setReceiverAddressAll(createOrderByProductIdDTO.getReceiverProvince() + createOrderByProductIdDTO.getReceiverCity() + createOrderByProductIdDTO.getReceiverDistrict() + createOrderByProductIdDTO.getReceiverAddress());

        //创建订单商品
        List<OrderItem> orderItems = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.valueOf(sku.getPrice()).multiply(BigDecimal.valueOf(createOrderByProductIdDTO.getQuantity()))
            .setScale(2, RoundingMode.HALF_UP);//商品总价

        OrderItem orderItem = new OrderItem();
        orderItem.setProductId(createOrderByProductIdDTO.getProductId());
        orderItem.setSkuId(createOrderByProductIdDTO.getSkuId());
        orderItem.setProductName(product.getName());
        orderItem.setProductImage(sku.getImage());
        orderItem.setSkuId(createOrderByProductIdDTO.getSkuId());
        orderItem.setSkuSpec(sku.getName() + ";" + sku.getValue());
        orderItem.setProductPrice(BigDecimal.valueOf(sku.getPrice()).setScale(2));
        orderItem.setQuantity(createOrderByProductIdDTO.getQuantity());
        orderItem.setDiscountAmount(BigDecimal.ZERO);//优惠金额
        orderItem.setTotalAmount(totalAmount);
        orderItem.setPayAmount(totalAmount);
        orderItem.setFeeBearer(product.getFeeBearer());//手续费承担方
        orderItem.setRemark(createOrderByProductIdDTO.getRemark());
        orderItem.setCommentStatus("0");
        orderItem.setRefundStatus("0");
        orderItem.setDelFlag("0");

        orderItems.add(orderItem);

        // 在保存订单之前处理库存扣减
        handleStockDeduction(orderItem);

        order.setOrderName(product.getName());
        order.setTotalQuantity(Long.valueOf(createOrderByProductIdDTO.getQuantity()));//订单商品数量
        order.setPayType(createOrderByProductIdDTO.getPayType());//支付类型
        order.setTotalAmount(totalAmount);//商品总价
        order.setPayAmount(totalAmount);//实际支付金额
        order.setFreightAmount(BigDecimal.ZERO);//运费险
        order.setDiscountAmount(BigDecimal.ZERO);//优惠金额

        // 保存订单
        save(order);
        for (OrderItem orderItem1 : orderItems) {
            orderItem1.setOrderId(order.getId());
            orderItem1.setOrderNo(order.getOrderNo());
            orderItem1.setUserId(order.getUserId());
            orderItem1.setShopId(order.getShopId());
        }
        orderItemMapper.insertBatch(orderItems);

        return order.getOrderNo();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelOrder(String orderNo) {
        // 1. 查询订单
        Order order = getOrderByOrderNo(orderNo);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        // 2. 检查订单状态
        if (!OrderStatusEnum.PENDING_PAYMENT.getCode().equals(order.getStatus())) {
            throw new ServiceException("订单只要待支付才能进行取消订单");
        }

        // 3. 恢复商品库存
        List<OrderItem> orderItems = orderItemService.getOrderItems(order.getId());
        for (OrderItem orderItem : orderItems) {
            // 恢复SKU库存
            if (orderItem.getSkuId() != null) {
                skuService.increaseStock(orderItem.getSkuId(), orderItem.getQuantity());
            }
            // 恢复商品总库存
            productService.increaseStock(orderItem.getProductId(), orderItem.getQuantity());
        }

        // 4. 处理抵扣金退回（如果是抵扣金组合支付）
        if (PayOrder.PAY_TYPE_DEDUCTION.equals(order.getPayType()) &&
            order.getDeductionAmount() != null &&
            order.getDeductionAmount().compareTo(BigDecimal.ZERO) > 0) {

            try {
                boolean refundResult = userBalanceService.increaseUserBalance(order.getUserId(), order.getDeductionAmount());
                if (refundResult) {
                    log.info("取消订单退回抵扣金成功 - 订单号: {}, 退回金额: {}元", orderNo, order.getDeductionAmount());
                } else {
                    log.error("取消订单退回抵扣金失败 - 订单号: {}, 金额: {}元", orderNo, order.getDeductionAmount());
                }
            } catch (Exception e) {
                log.error("取消订单退回抵扣金异常 - 订单号: {}, 金额: {}元", orderNo, order.getDeductionAmount(), e);
            }
        }

        // 5. 取消支付单
        PayOrder payOrder = payService.getByOrderNo(orderNo);
        if (payOrder != null) {
            payService.cancelPayment(payOrder.getPaymentNo());
        }

        // 6. 更新订单状态
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getOrderNo, orderNo)
            .set(Order::getStatus, OrderStatusEnum.CANCELLED.getCode())
            .set(Order::getCancelTime, new Date())
            .set(Order::getCancelReason, "用户取消订单");

        return update(updateWrapper);
    }

    @Override
    public Boolean deleteOrderById(String orderNo) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getOrderNo, orderNo)
            .set(Order::getDelFlag, "2");

        LambdaUpdateWrapper<OrderItem> itemLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        itemLambdaUpdateWrapper.set(OrderItem::getDelFlag, "2").eq(OrderItem::getOrderNo, orderNo);
        return update(updateWrapper) && orderItemService.update(itemLambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteOrderByIds(String[] orderNos) {

        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Order::getOrderNo, Arrays.asList(orderNos));

        List<Order> orders = list(queryWrapper);

        // 检查是否存在未完成的订单
        boolean hasInvalidStatus = orders.stream()
            .anyMatch(order -> order.getStatus().equals(OrderStatusEnum.PENDING_PAYMENT.getCode()) ||
                order.getStatus().equals(OrderStatusEnum.DURING_PAYMENT.getCode()) ||
                order.getStatus().equals(OrderStatusEnum.PENDING_RECEIVE.getCode()) ||
                order.getStatus().equals(OrderStatusEnum.REFUNDED.getCode()));

        if (hasInvalidStatus || orders.isEmpty()) {
            throw new ServiceException("存在状态为0的订单，不能删除");
        }

        LambdaUpdateWrapper<Order> orderLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        orderLambdaUpdateWrapper.set(Order::getDelFlag, "2").in(Order::getOrderNo, Arrays.asList(orderNos));

        LambdaUpdateWrapper<OrderItem> itemLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        itemLambdaUpdateWrapper.set(OrderItem::getDelFlag, "2").in(OrderItem::getOrderNo, Arrays.asList(orderNos));

        return update(orderLambdaUpdateWrapper) && orderItemService.update(itemLambdaUpdateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmReceive(String orderNo) {
        Order order = getOrderByOrderNo(orderNo);
        if (order == null || !OrderStatusEnum.PENDING_RECEIVE.getCode().equals(order.getStatus())) {
            return false;
        }
        order.setStatus(OrderStatusEnum.COMPLETED.getCode()); // 已完成
        order.setReceiveTime(new Date());
        boolean result = updateById(order);
        return result;
    }

    /**
     * 处理支付回调
     *
     * @param payType 支付类型
     * @param map     回调参数
     * @return 处理结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handlePayCallback(String payType, Map<String, String> map) {
        boolean success = payService.handlePayCallback(payType, map);
        String paymentNo = map.get("out_trade_no");
        PayOrder payOrder = payService.getByPaymentNo(paymentNo);
        if (success) {
            if (payOrder != null && payOrder.getOrderId() != null) {
                // 处理组合支付的订单金额更新
                LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(Order::getId, payOrder.getOrderId())
                    // 支付状态设置为已支付
                    .set(Order::getPayStatus, PayStatusEnum.SUCCESS.getCode())
                    // 订单状态为待发货
                    .set(Order::getStatus, OrderStatusEnum.PENDING_SHIPMENT.getCode())
                    .set(Order::getPayTime, new Date());

                update(updateWrapper);
                // 处理库存扣减
                handlePaymentStockDeduction(payOrder.getOrderId());

                // 如果是抵扣金组合支付，更新抵扣金支付记录状态为成功
                if (PayOrder.PAY_TYPE_DEDUCTION.equals(payType)) {
                    try {
                        // 先检查当前状态，避免重复更新
                        String currentStatus = deductionPaymentRecordService.getDeductionPaymentStatus(paymentNo);
                        if (DeductionPaymentStatusEnum.PENDING.getCode().equals(currentStatus)) {
                            boolean updateRecordResult = deductionPaymentRecordService.updateDeductionPaymentStatus(
                                paymentNo, DeductionPaymentStatusEnum.SUCCESS.getCode());
                            if (updateRecordResult) {
                                log.info("更新抵扣金支付记录状态为成功 - 支付单号: {}", paymentNo);
                            } else {
                                log.error("更新抵扣金支付记录状态为成功失败 - 支付单号: {}", paymentNo);
                            }
                        } else {
                            log.info("抵扣金支付记录状态无需更新 - 支付单号: {}, 当前状态: {}({})",
                                paymentNo, currentStatus, DeductionPaymentStatusEnum.getDescByCode(currentStatus));
                        }
                    } catch (Exception recordException) {
                        log.error("更新抵扣金支付记录状态异常 - 支付单号: {}", paymentNo, recordException);
                    }
                }

                log.info("订单支付成功 - 订单ID: {}, 支付方式: {}, 支付单号: {}",
                    payOrder.getOrderId(), payType, paymentNo);
            }
        } else {
            // 支付失败处理
            if (payOrder != null && payOrder.getOrderId() != null) {
                // 处理抵扣金退回（如果是抵扣金组合支付）
                handleDeductionRefundOnPaymentFailure(payOrder, payType);

                // 更新订单支付状态为失败
                LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(Order::getId, payOrder.getOrderId())
                    // 设置为支付失败
                    .set(Order::getPayStatus, PayStatusEnum.FAILED.getCode())
                    .set(Order::getStatus, OrderStatusEnum.PENDING_PAYMENT.getCode());
                update(updateWrapper);

                log.warn("订单支付失败 - 订单ID: {}, 支付方式: {}, 支付单号: {}",
                    payOrder.getOrderId(), payType, paymentNo);
            }
        }
        return success;
    }

    /**
     * 订单发货
     */
    @Override
    public Boolean shipmentOder(ShipmentOderDto shipmentOderDto) {
        LambdaUpdateWrapper<Order> qw = new LambdaUpdateWrapper<Order>();
        if (ObjUtil.equals("1", shipmentOderDto.getReceiverType())) {
            Delivery delivery = deliveryService.getById(shipmentOderDto.getDeliveryId());
            if (null == delivery) {
                throw new ServiceException("未知的快递公司");
            }
            qw.set(Order::getDeliveryId, delivery.getId())
                .set(Order::getDeliveryName, delivery.getDeliveryName())
                .set(Order::getDeliveryNumber, shipmentOderDto.getDeliveryNumber());
        }

        qw.set(Order::getDeliveryTime, new Date())
            .set(Order::getStatus, OrderStatusEnum.PENDING_RECEIVE.getCode())
            .eq(Order::getOrderNo, shipmentOderDto.getOrderNo());
        return update(qw);
    }

    @Override
    public Order getOrderByOrderNo(String orderNo) {
        LambdaQueryWrapper<Order> qw = new LambdaQueryWrapper<Order>()
            .eq(Order::getOrderNo, orderNo)
            .eq(Order::getDelFlag, 0);
        return getOne(qw);
    }

    @Override
    public OrderPreviewDTO previewOrder(List<Long> cartIds) {
        if (cartIds == null || cartIds.isEmpty()) {
            throw new RuntimeException("请选择要购买的商品");
        }

        return cartService.getOrderPreview(cartIds);
    }

    /**
     * 根据用户选中的购物车商品生成订单预览
     *
     * @return 订单预览信息
     */
    @Override
    public OrderPreviewDTO previewSelectedCarts() {
        // 获取购物车选中商品
        Cart queryCart = new Cart();
        queryCart.setUserId(LoginHelper.getUserId());
        queryCart.setSelected("1");
        List<Cart> cartList = cartService.selectCartList(queryCart);

        if (cartList.isEmpty()) {
            throw new RuntimeException("请选择要购买的商品");
        }

        return null;
    }




//    /**
//     * 生成订单预览信息
//     *
//     * @param cartList 购物车商品列表
//     * @return 订单预览信息
//     */
//    private OrderPreviewDTO generateOrderPreview(List<Cart> cartList) {
//        Long userId = LoginHelper.getUserId();
//
//        // 按店铺分组购物车商品
//        Map<Long, List<Cart>> shopCartMap = cartList.stream()
//            .collect(Collectors.groupingBy(cart -> cart.getShop().getId()));
//
//        // 获取所有店铺信息
//        List<Long> shopIds = new ArrayList<>(shopCartMap.keySet());
//        List<Shop> shopList = shopService.listByIds(shopIds);
//
//        // 计算商品总数量和总金额
//        Integer totalQuantity = 0;
//        BigDecimal totalAmount = BigDecimal.ZERO;
//        BigDecimal totalFreightAmount = BigDecimal.ZERO;
//        BigDecimal totalDiscountAmount = BigDecimal.ZERO;
//
//        for (Cart cart : cartList) {
//            // 设置商品和SKU信息
//            if (cart.getProduct() == null || cart.getSku() == null) {
//                Cart fullCart = cartService.selectCartById(cart.getId());
//                cart.setProduct(fullCart.getProduct());
//                cart.setSku(fullCart.getSku());
//                cart.setShop(fullCart.getShop());
//                cart.setPrice(fullCart.getPrice());
//                cart.setTotalAmount(fullCart.getTotalAmount());
//            }
//
//            totalQuantity += cart.getQuantity();
//            if (cart.getTotalAmount() != null) {
//                totalAmount = totalAmount.add(cart.getTotalAmount());
//            }
//        }
//
//        // 创建订单预览对象
//        OrderPreviewDTO preview = new OrderPreviewDTO();
//        preview.setShopCartMap(shopCartMap);
//        preview.setShopList(shopList);
//        preview.setTotalQuantity(totalQuantity);
//        preview.setTotalAmount(totalAmount);
//
//        // 计算运费（这里简化处理，实际可能需要根据地址和商品重量等计算）
//        // 每个店铺单独计算运费
////        for (Map.Entry<Long, List<Cart>> entry : shopCartMap.entrySet()) {
////            // 这里可以根据店铺的运费规则计算运费
////            BigDecimal shopFreight = BigDecimal.ZERO;
////            totalFreightAmount = totalFreightAmount.add(shopFreight);
////        }
//        preview.setTotalFreightAmount(totalFreightAmount);
//
//        // 计算优惠金额（这里简化处理，实际可能需要根据优惠券等计算）
//        preview.setTotalDiscountAmount(totalDiscountAmount);
//
//        // 计算实际支付金额
//        preview.setTotalPayAmount(totalAmount.add(totalFreightAmount).subtract(totalDiscountAmount));
//
//        // 获取用户默认收货地址
//        TzUserAddress one = userAddressService.getOne(new LambdaQueryWrapper<TzUserAddress>().eq(TzUserAddress::getUserId, userId).eq(TzUserAddress::getIsDefault, 1));
//        if (one != null) {
//            preview.setReceiverName(one.getReceiver());
//            preview.setReceiverPhone(one.getPhone());
//            preview.setReceiverProvince(one.getProvince());
//            preview.setReceiverCity(one.getCity());
//            preview.setReceiverDistrict(one.getDistrict());
//            preview.setReceiverAddress(one.getDetailAddress());
//        }
//
//        return preview;
//    }

    /**
     * 生成订单编号
     */
    private String generateOrderNo() {
        // 雪花算法生成订单编号
        return OrderNoGenerator.generateOrderNo();
    }

    /**
     * 生成订单组编号
     */
    private String generateOrderGroupNo() {
        return OrderNoGenerator.generateOrderNo("G");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> createPayment(Order pramOrder, String orderNo, Long userId, String payType) {
        if (PayOrder.PAY_TYPE_DEDUCTION.equals(payType)) {
            return handleDeductionCombinedPayment(pramOrder, orderNo, userId);
        } else {
            return handleNormalPayment(pramOrder, orderNo, userId, payType);
        }
    }

    /**
     * 处理抵扣金组合支付
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> handleDeductionCombinedPayment(Order pramOrder, String orderNo, Long userId) {
        // 1. 计算抵扣金金额（商品总金额 - 优惠 - 运费）
        BigDecimal deductionAmount = pramOrder.getTotalAmount()
                .subtract(pramOrder.getDiscountAmount())
                .subtract(pramOrder.getFreightAmount());

        // 2. 检查抵扣金余额
        TzUser user = userService.getById(userId);
        if (user.getDeductionMoney().compareTo(deductionAmount) < 0) {
            throw new ServiceException("平台兑换券余额不足，当前余额：" + user.getDeductionMoney() + "元，需要：" + deductionAmount + "元");
        }

        // 3. 计算手续费（根据商品的手续费承担方配置）
//        BigDecimal serviceFee = calculateServiceFeeForDeduction(pramOrder.getId());
        // 手续费固定0.11
        BigDecimal serviceFee = new BigDecimal("0.11");

        try {
            // 4. 扣减抵扣金
            boolean deductResult = userBalanceService.deductUserBalance(userId, deductionAmount);
            if (!deductResult) {
                throw new ServiceException("扣减平台兑换券失败");
            }

            // 5. 更新订单状态和金额
            updateOrderForDeductionPayment(pramOrder, deductionAmount, serviceFee);

            // 6. 处理库存扣减
            handlePaymentStockDeduction(pramOrder.getId());

            // 7. 创建抵扣金组合支付订单
            Map<String, String> result = payService.createDeductionPayment(
                    pramOrder.getId(), orderNo, userId, deductionAmount, serviceFee);

            log.info("创建抵扣金组合支付成功 - 订单号: {}, 抵扣金: {}元, 手续费: {}元",
                    orderNo, deductionAmount, serviceFee);

            return result;

        } catch (Exception e) {
            // 回滚抵扣金
            try {
                userBalanceService.increaseUserBalance(userId, deductionAmount);
                log.info("抵扣金组合支付失败，已回滚抵扣金: {}元", deductionAmount);
            } catch (Exception rollbackException) {
                log.error("回滚抵扣金失败", rollbackException);
            }
            throw new ServiceException("创建抵扣金组合支付失败：" + e.getMessage());
        }
    }

    /**
     * 处理普通支付
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> handleNormalPayment(Order pramOrder, String orderNo, Long userId, String payType) {
        // 修改订单为支付中
        Order order = new Order();
        order.setId(pramOrder.getId());
        order.setPayType(payType);
        order.setStatus(OrderStatusEnum.DURING_PAYMENT.getCode());
        order.setPayStatus(PayStatusEnum.DURING.getCode());
        order.setDeductionAmount(BigDecimal.ZERO);
        order.setPayAmount(pramOrder.getTotalAmount().subtract(pramOrder.getDiscountAmount()).subtract(pramOrder.getFreightAmount()));

        updateById(order);
        handlePaymentStockDeduction(pramOrder.getId());

        // 计算手续费
        BigDecimal userFeeAmount = calculateServiceFeeForNormal(pramOrder.getId());

        // 更新订单的总手续费
        Order updateOrder = new Order();
        updateOrder.setId(pramOrder.getId());
        updateOrder.setFeeAmount(userFeeAmount);
        updateOrder.setPayAmount(order.getPayAmount().add(userFeeAmount));
        updateById(updateOrder);

        return payService.createPayment(pramOrder.getId(), orderNo, userId, order.getPayAmount(), userFeeAmount, payType);
    }

    /**
     * 计算抵扣金支付的手续费
     */
    private BigDecimal calculateServiceFeeForDeduction(Long orderId) {
        List<OrderItem> orderItems = orderItemService.lambdaQuery()
            .eq(OrderItem::getOrderId, orderId)
            .list();

        BigDecimal totalServiceFee = BigDecimal.ZERO;
        BigDecimal feeRate = new BigDecimal("0.006"); // 千分之六

        for (OrderItem orderItem : orderItems) {
            Product product = productService.getById(orderItem.getProductId());
            if (product != null && "1".equals(product.getFeeBearer())) {
                // 用户承担手续费
                BigDecimal itemFee = orderItem.getTotalAmount()
                    .multiply(feeRate)
                    .setScale(2, RoundingMode.HALF_UP);
                totalServiceFee = totalServiceFee.add(itemFee);
            }
        }

        // 确保手续费至少0.01元（避免0元支付）
        if (totalServiceFee.compareTo(new BigDecimal("0.01")) < 0) {
            totalServiceFee = new BigDecimal("0.01");
        }

        return totalServiceFee;
    }

    /**
     * 计算普通支付的手续费
     */
    private BigDecimal calculateServiceFeeForNormal(Long orderId) {
        List<OrderItem> orderItems = orderItemService.lambdaQuery()
            .eq(OrderItem::getOrderId, orderId)
            .list();

        BigDecimal userFeeAmount = BigDecimal.ZERO;
        BigDecimal feeRate = new BigDecimal("0.006"); // 千分之六

        for (OrderItem orderItem : orderItems) {
            Product product = productService.getById(orderItem.getProductId());
            if (product != null) {
                // 计算该订单项的手续费
                BigDecimal itemFeeAmount = orderItem.getTotalAmount().multiply(feeRate).setScale(2, RoundingMode.HALF_UP);

                // 更新订单项的手续费信息
                OrderItem updateItem = new OrderItem();
                updateItem.setId(orderItem.getId());
                updateItem.setFeeAmount(itemFeeAmount);

                if ("1".equals(product.getFeeBearer())) {
                    // 用户承担手续费
                    updateItem.setFeeBearer("1");
                    userFeeAmount = userFeeAmount.add(itemFeeAmount);
                    updateItem.setPayAmount(orderItem.getTotalAmount().add(itemFeeAmount));
                } else {
                    // 店铺承担手续费
                    updateItem.setFeeBearer("0");
                    updateItem.setPayAmount(orderItem.getTotalAmount());
                }

                orderItemService.updateById(updateItem);
            }
        }

        return userFeeAmount;
    }

    /**
     * 更新订单信息（抵扣金支付）
     */
    private void updateOrderForDeductionPayment(Order pramOrder, BigDecimal deductionAmount, BigDecimal serviceFee) {
        Order order = new Order();
        order.setId(pramOrder.getId());
        order.setPayType(PayOrder.PAY_TYPE_DEDUCTION);
        order.setStatus(OrderStatusEnum.DURING_PAYMENT.getCode());
        order.setPayStatus(PayStatusEnum.DURING.getCode());
        order.setDeductionAmount(deductionAmount); // 记录抵扣金使用金额
        order.setPayAmount(serviceFee); // 记录需要支付的手续费
        order.setFeeAmount(serviceFee); // 记录手续费金额

        updateById(order);
    }

    /**
     * 处理支付失败时的抵扣金退回
     */
    private void handleDeductionRefundOnPaymentFailure(PayOrder payOrder, String payType) {
        // 只处理抵扣金组合支付的情况
        if (!PayOrder.PAY_TYPE_DEDUCTION.equals(payType)) {
            return;
        }

        // 检查是否有原始抵扣金金额记录
        if (payOrder.getOriginalAmount() == null || payOrder.getOriginalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("支付失败但无抵扣金记录 - 支付单号: {}", payOrder.getPaymentNo());
            return;
        }

        try {
            // 获取订单信息
            Order order = getById(payOrder.getOrderId());
            if (order == null) {
                log.error("支付失败退回抵扣金时未找到订单 - 订单ID: {}", payOrder.getOrderId());
                return;
            }

            // 退回抵扣金
            BigDecimal refundAmount = payOrder.getOriginalAmount();
            boolean refundResult = userBalanceService.increaseUserBalance(order.getUserId(), refundAmount);

            if (refundResult) {
                log.info("支付失败退回抵扣金成功 - 用户ID: {}, 订单号: {}, 退回金额: {}元",
                    order.getUserId(), order.getOrderNo(), refundAmount);

                // 清空订单中的抵扣金记录
                LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(Order::getId, payOrder.getOrderId())
                    .set(Order::getDeductionAmount, BigDecimal.ZERO);
                update(updateWrapper);

                // 更新抵扣金支付记录状态为失败
                try {
                    // 先检查当前状态，避免重复更新
                    String currentStatus = deductionPaymentRecordService.getDeductionPaymentStatus(payOrder.getPaymentNo());
                    if (DeductionPaymentStatusEnum.PENDING.getCode().equals(currentStatus)) {
                        boolean updateRecordResult = deductionPaymentRecordService.updateDeductionPaymentStatus(
                            payOrder.getPaymentNo(), DeductionPaymentStatusEnum.FAILED.getCode());
                        if (updateRecordResult) {
                            log.info("更新抵扣金支付记录状态为失败成功 - 支付单号: {}", payOrder.getPaymentNo());
                        } else {
                            log.error("更新抵扣金支付记录状态为失败失败 - 支付单号: {}", payOrder.getPaymentNo());
                        }
                    } else {
                        log.info("抵扣金支付记录状态无需更新 - 支付单号: {}, 当前状态: {}({})",
                            payOrder.getPaymentNo(), currentStatus, DeductionPaymentStatusEnum.getDescByCode(currentStatus));
                    }
                } catch (Exception recordException) {
                    log.error("更新抵扣金支付记录状态异常 - 支付单号: {}", payOrder.getPaymentNo(), recordException);
                }

            } else {
                log.error("支付失败退回抵扣金失败 - 用户ID: {}, 订单号: {}, 金额: {}元",
                        order.getUserId(), order.getOrderNo(), refundAmount);
            }

        } catch (Exception e) {
            log.error("支付失败退回抵扣金异常 - 支付单号: {}, 金额: {}元",
                    payOrder.getPaymentNo(), payOrder.getOriginalAmount(), e);
        }
    }

    /**
     * 处理库存扣减
     *
     * @param orderItem 订单商品
     */
    private void handleStockDeduction(OrderItem orderItem) {
        Product product = productService.getById(orderItem.getProductId());
        if (product == null) {
            throw new RuntimeException("商品不存在：" + orderItem.getProductId());
        }

        // 根据减库存方式处理
        if (product.getStockType() != null && product.getStockType() == 1) {
            // 下单减库存 - 商品总库存
            if (!productService.updateStock(orderItem.getProductId(), -orderItem.getQuantity())) {
                throw new RuntimeException("商品库存不足：" + product.getName());
            }

            // 下单减库存 - SKU库存
            if (orderItem.getSkuId() != null) {
                if (!skuService.updateStock(orderItem.getSkuId(), -orderItem.getQuantity())) {
                    throw new RuntimeException("商品规格库存不足：" + product.getName());
                }
            }
        }
    }

    /**
     * 处理支付时的库存扣减
     *
     * @param orderId 订单Id
     */
    private void handlePaymentStockDeduction(Long orderId) {
        // 查询订单商品
        LambdaQueryWrapper<OrderItem> lqw = new LambdaQueryWrapper<>();
        lqw.eq(OrderItem::getOrderId, orderId);
        lqw.eq(OrderItem::getDelFlag, "0");
        List<OrderItem> orderItems = orderItemMapper.selectList(lqw);

        for (OrderItem orderItem : orderItems) {
            Product product = productService.getById(orderItem.getProductId());
            if (product == null) {
                throw new RuntimeException("商品不存在：" + orderItem.getProductId());
            }

            // 付款减库存
            if (product.getStockType() != null && product.getStockType() == 2) {
                // 扣减商品总库存
                if (!productService.updateStock(orderItem.getProductId(), -orderItem.getQuantity())) {
                    throw new RuntimeException("商品库存不足：" + product.getName());
                }

                // 扣减SKU库存
                if (orderItem.getSkuId() != null) {
                    if (!skuService.updateStock(orderItem.getSkuId(), -orderItem.getQuantity())) {
                        throw new RuntimeException("商品规格库存不足：" + product.getName());
                    }
                }
            }
        }
    }

    @Override
    public String queryPayStatus(String paymentNo) {
        PayOrder payOrder = payService.getByPaymentNo(paymentNo);
        if (payOrder == null) {
            throw new RuntimeException("支付订单不存在");
        }
        String status = payService.queryPayStatus(payOrder);
        LambdaUpdateWrapper<Order> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper
            .eq(Order::getOrderNo, payOrder.getOrderNo())
            .set(Order::getPayStatus, status);

        // 支付成功变成待发货。失败就待支付
        if (PayStatusEnum.SUCCESS.getCode().equals(status)) {
            lambdaUpdateWrapper.set(Order::getPayType, payOrder.getPayType());
            lambdaUpdateWrapper.set(Order::getPayTime, new Date());
            lambdaUpdateWrapper.set(Order::getStatus, OrderStatusEnum.PENDING_SHIPMENT.getCode());

            // 如果是抵扣金组合支付，需要更新订单金额和抵扣金支付记录状态
            if (PayOrder.PAY_TYPE_DEDUCTION.equals(payOrder.getPayType()) && payOrder.getOriginalAmount() != null) {
                // 更新订单的实际支付金额（商品金额 + 手续费）
                BigDecimal totalAmount = payOrder.getOriginalAmount().add(payOrder.getFeeAmount());
                lambdaUpdateWrapper.set(Order::getPayAmount, totalAmount);

                // 更新抵扣金支付记录状态为成功
                try {
                    String currentStatus = deductionPaymentRecordService.getDeductionPaymentStatus(paymentNo);
                    if (DeductionPaymentStatusEnum.PENDING.getCode().equals(currentStatus)) {
                        boolean updateRecordResult = deductionPaymentRecordService.updateDeductionPaymentStatus(
                                paymentNo, DeductionPaymentStatusEnum.SUCCESS.getCode());
                        if (updateRecordResult) {
                            log.info("定时任务更新抵扣金支付记录状态为成功 - 支付单号: {}", paymentNo);
                        }
                    }
                } catch (Exception e) {
                    log.error("定时任务更新抵扣金支付记录状态异常 - 支付单号: {}", paymentNo, e);
                }

                log.info("定时任务处理抵扣金组合支付成功 - 订单号: {}, 抵扣金: {}元, 手续费: {}元, 总金额: {}元",
                        payOrder.getOrderNo(), payOrder.getOriginalAmount(), payOrder.getFeeAmount(), totalAmount);
            }

        } else if (PayStatusEnum.FAILED.getCode().equals(status)) {
            lambdaUpdateWrapper.set(Order::getStatus, OrderStatusEnum.PENDING_PAYMENT.getCode());

            // 如果是抵扣金组合支付失败，需要退回抵扣金
            if (PayOrder.PAY_TYPE_DEDUCTION.equals(payOrder.getPayType()) && payOrder.getOriginalAmount() != null) {
                handleDeductionRefundOnPaymentFailure(payOrder, payOrder.getPayType());
                log.info("定时任务处理抵扣金组合支付失败，已退回抵扣金 - 订单号: {}, 退回金额: {}元",
                        payOrder.getOrderNo(), payOrder.getOriginalAmount());
            }
        } else if (PayStatusEnum.DURING.getCode().equals(status)) {
            lambdaUpdateWrapper.set(Order::getStatus, OrderStatusEnum.DURING_PAYMENT.getCode());
        }

        update(lambdaUpdateWrapper);
        return status;
    }

    @Override
    public String queryOrderPayStatus(String orderNo) {
        return "";
    }

    @Override
    public Boolean updateOrderAddress(UpdateOrderAddressDTO updateOrderAddressDTO) {
        // 查询订单是否存在
        Order order = getOrderByOrderNo(updateOrderAddressDTO.getOrderNo());
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        // 只有待发货状态的订单才能修改收货地址
        if (!OrderStatusEnum.PENDING_SHIPMENT.getCode().equals(order.getStatus())) {
            throw new ServiceException("只有待发货状态的订单才能修改收货地址");
        }

        // 更新收货地址信息
        Order updateOrder = new Order();
        updateOrder.setId(order.getId());
        updateOrder.setReceiverName(updateOrderAddressDTO.getReceiverName());
        updateOrder.setReceiverPhone(updateOrderAddressDTO.getReceiverPhone());
        updateOrder.setReceiverProvince(updateOrderAddressDTO.getReceiverProvince());
        updateOrder.setReceiverCity(updateOrderAddressDTO.getReceiverCity());
        updateOrder.setReceiverDistrict(updateOrderAddressDTO.getReceiverDistrict());
        updateOrder.setReceiverAddress(updateOrderAddressDTO.getReceiverAddress());

        // 组装完整地址
        String fullAddress = updateOrderAddressDTO.getReceiverProvince() +
            updateOrderAddressDTO.getReceiverCity() +
            updateOrderAddressDTO.getReceiverDistrict() +
            updateOrderAddressDTO.getReceiverAddress();
        updateOrder.setReceiverAddressAll(fullAddress);

        return updateById(updateOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOrderPrice(UpdateOrderPriceDTO updateOrderPriceDTO) {
        // 查询订单是否存在
        Order order = getOrderByOrderNo(updateOrderPriceDTO.getOrderNo());
        if (order == null) {
            throw new ServiceException("订单不存在");
        }

        // 只有待支付状态的订单才能修改价格
        if (!OrderStatusEnum.PENDING_PAYMENT.getCode().equals(order.getStatus())) {
            throw new ServiceException("只有待支付状态的订单才能修改价格");
        }

        // 验证价格合理性
        if (updateOrderPriceDTO.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("订单总金额必须大于0");
        }

        // 构建更新对象
        Order updateOrder = new Order();
        updateOrder.setId(order.getId());
        updateOrder.setTotalAmount(updateOrderPriceDTO.getTotalAmount());

        // 设置修改原因
        if (StringUtils.isNotEmpty(updateOrderPriceDTO.getRemark())) {
            updateOrder.setRemark(updateOrderPriceDTO.getRemark());
        }

        log.info("修改订单价格 - 订单号: {}, 原总金额: {}元, 新总金额: {}元",
            updateOrderPriceDTO.getOrderNo(),
            order.getTotalAmount(),
            updateOrderPriceDTO.getTotalAmount()
        );

        return updateById(updateOrder);
    }


    @Override
    public TableDataInfo<OrderVo> getOrderListByType(Long userId, QueryOrderTypeDTO queryOrderTypeDTO,PageQuery pageQuery) {

        MPJLambdaWrapper<Order> wrapper = new MPJLambdaWrapper<>();
        wrapper
            .selectAll(Order.class)
            .selectAs(Order::getId, OrderVo::getOrderId)
            .selectAs(Shop::getName, OrderVo::getShopName)
            .selectAs(Shop::getLogo, OrderVo::getShopLogo)
            .selectCollection(OrderItem.class, OrderVo::getItems)
            .leftJoin(OrderItem.class, OrderItem::getOrderId, Order::getId)
            .selectAs(Order::getStatus, OrderVo::getOrderStatus);

        //判断是用户还是商家还是代销
        LambdaQueryWrapper<TzUser> userWrapper = new LambdaQueryWrapper<>();
        userWrapper
            .select(TzUser::getUserType)
            .eq(TzUser::getUserId, userId);
        String userType = userService.getOne(userWrapper).getUserType();

        if (ObjUtil.equals(userType, "B")) {
            wrapper
                .leftJoin(Shop.class, Shop::getId, Order::getShopId)
                .leftJoin(SysUser.class, SysUser::getUserId, Shop::getUserId)
                .leftJoin(TzUser.class, TzUser::getUserId, SysUser::getTzUserId);
        } else if (ObjUtil.equals(userType, "CB")) {
            wrapper
                .leftJoin(Shop.class, Shop::getId, Order::getConsignmentId)
                .leftJoin(SysUser.class, SysUser::getUserId, Shop::getUserId)
                .leftJoin(TzUser.class, TzUser::getUserId, SysUser::getTzUserId);
        } else if (ObjUtil.equals(userType, "C")) {
            wrapper
                .leftJoin(Shop.class, Shop::getId, Order::getShopId)
                .leftJoin(TzUser.class,TzUser::getUserId,Order::getUserId);
        }
        wrapper.eq(TzUser::getUserId, userId);


        //根据不同类型返回结果
        if (Objects.equals(queryOrderTypeDTO.getType(), OrderTypeEnum.ALL.value())) {//全部

        } else if (Objects.equals(queryOrderTypeDTO.getType(), OrderTypeEnum.PENDING.value())) {//待支付
            wrapper.eq(Order::getStatus, OrderStatusEnum.PENDING_PAYMENT.getCode())
                .or()
                .eq(Order::getStatus, OrderStatusEnum.DURING_PAYMENT.getCode());
        } else if (Objects.equals(queryOrderTypeDTO.getType(), OrderTypeEnum.PAYING.value())) {//待发货
            wrapper.eq(Order::getStatus, OrderStatusEnum.PENDING_SHIPMENT.getCode());
        } else if (Objects.equals(queryOrderTypeDTO.getType(), OrderTypeEnum.DELIVERING.value())) {//待收货
            wrapper.eq(Order::getStatus, OrderStatusEnum.PENDING_RECEIVE.getCode());
        } else if (Objects.equals(queryOrderTypeDTO.getType(), OrderTypeEnum.COMPLETED.value())) {//已完成
            wrapper.eq(Order::getStatus, OrderStatusEnum.COMPLETED.getCode());
        } else if (Objects.equals(queryOrderTypeDTO.getType(), OrderTypeEnum.RECEIVED.value())) {//待评价
            wrapper.eq(Order::getStatus, OrderStatusEnum.COMPLETED.getCode())
                .eq(OrderItem::getCommentStatus, EvaluateSatusEnum.ITEM_NOT_REVIEWED.getCode());
        } else if (Objects.equals(queryOrderTypeDTO.getType(), OrderTypeEnum.REFUND.value())) {//售后
            wrapper.eq(Order::getStatus, OrderStatusEnum.REFUNDED.getCode());
        } else if (Objects.equals(queryOrderTypeDTO.getType(), OrderTypeEnum.CANCELED.value())) {//取消
            wrapper.eq(Order::getStatus, OrderStatusEnum.CANCELLED.getCode());
        } else {
            throw new ServiceException("参数订单类型不对");
        }

        //模糊查询
        wrapper
            .like(StringUtils.isNotBlank(queryOrderTypeDTO.getQuery()), OrderItem::getProductName, queryOrderTypeDTO.getQuery())
            .or()
            .like(StringUtils.isNotBlank(queryOrderTypeDTO.getQuery()), Order::getOrderNo, queryOrderTypeDTO.getQuery())
            .or()
            .like(StringUtils.isNotBlank(queryOrderTypeDTO.getQuery()), Order::getReceiverName, queryOrderTypeDTO.getQuery())
            .or()
            .like(StringUtils.isNotBlank(queryOrderTypeDTO.getQuery()), Shop::getName, queryOrderTypeDTO.getQuery());

        //时间查询
        wrapper
            .ge(StringUtils.isNotBlank(queryOrderTypeDTO.getStartTime()), Order::getCreateTime, queryOrderTypeDTO.getStartTime())
            .le(StringUtils.isNotBlank(queryOrderTypeDTO.getEndTime()), Order::getCreateTime, queryOrderTypeDTO.getEndTime());

        //按时间排序
        wrapper.orderByDesc(Order::getCreateTime);

        //分页查询
        Page<OrderVo> page = baseMapper.selectJoinPage(pageQuery.build(), OrderVo.class, wrapper);

        return TableDataInfo.build(page);
    }

    @Override
    public OrderVo getOrderInfo(Long orderId) {

        MPJLambdaWrapper<Order> wrapper = new MPJLambdaWrapper<>();
        wrapper
            .selectAll(Order.class)
            .selectAs(Order::getReceiverName, OrderVo::getReceiveName)
            .selectAs(Order::getReceiverPhone, OrderVo::getReceivePhone)
            .selectAs(Order::getReceiverProvince, OrderVo::getReceiverProvince)
            .selectAs(Order::getReceiverCity, OrderVo::getReceiverCity)
            .selectAs(Order::getReceiverDistrict, OrderVo::getReceiverDistrict)
            .selectAs(Order::getReceiverAddressAll, OrderVo::getReceiverAddressAll)
            .selectAs(Order::getReceiverAddress, OrderVo::getReceiverAddress)
            .selectAs(Order::getPayAmount, OrderVo::getPayAmount)
            .selectAs(Order::getFeeAmount, OrderVo::getFeeAmount)
            .selectAs(Order::getFreightAmount, OrderVo::getFreightAmount)
            .selectAs(Order::getDeductionAmount, OrderVo::getDeductionAmount)
            .selectAs(Order::getDiscountAmount, OrderVo::getDiscountAmount)
            .selectAs(Order::getId, OrderVo::getOrderId)
            .selectAs(Shop::getName, OrderVo::getShopName)
            .selectAs(Shop::getLogo, OrderVo::getShopLogo)
            .selectCollection(OrderItem.class, OrderVo::getItems)
            .leftJoin(OrderItem.class, OrderItem::getOrderId, Order::getId)
            .selectAs(Order::getStatus, OrderVo::getOrderStatus)
            .leftJoin(Shop.class, Shop::getId, Order::getShopId)
            .eq(Order::getId, orderId);

        return selectJoinOne(OrderVo.class, wrapper);
    }


    @Override
    public List<OrderExportVo> selectOrderExportList(Order order) {
        // 查询订单列表
        List<Order> orderList = selectOrderList(order);
        // 转换为导出对象
        return orderList.stream().map(this::convertToExportVo).collect(Collectors.toList());
    }

    /**
     * 将Order对象转换为OrderExportVo对象
     *
     * @param order 订单对象
     * @return 订单导出对象
     */
    private OrderExportVo convertToExportVo(Order order) {
        OrderExportVo exportVo = new OrderExportVo();
        exportVo.setUserPhone(order.getUserPhone());
        exportVo.setOrderNo(order.getOrderNo());
        exportVo.setStatus(order.getStatus());
        exportVo.setRefundStatus(order.getRefundStatus() != null ? order.getRefundStatus().toString() : null);
        exportVo.setOrderName(order.getOrderName());
        exportVo.setTotalAmount(order.getTotalAmount());
        exportVo.setTotalQuantity(order.getTotalQuantity());
        exportVo.setDeductionAmount(order.getDeductionAmount());
        exportVo.setPayType(order.getPayType());
        exportVo.setPayAmount(order.getPayAmount());
        exportVo.setReceiverName(order.getReceiverName());
        exportVo.setReceiverPhone(order.getReceiverPhone());
        exportVo.setReceiverAddressAll(order.getReceiverAddressAll());
        exportVo.setCreateTime(order.getCreateTime());
        return exportVo;
    }
}
