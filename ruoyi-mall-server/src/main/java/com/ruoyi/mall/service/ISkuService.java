package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.Sku;

import java.util.List;

/**
 * 规格Service接口
 *
 * <AUTHOR>
 */
public interface ISkuService extends IService<Sku> {
    /**
     * 查询商品规格列表
     *
     * @param sku 商品规格
     * @return 商品规格集合
     */
    List<Sku> selectSkuList(Sku sku);

    /**
     * 查询商品规格分页列表
     *
     * @param sku       商品规格
     * @param pageQuery 分页参数
     * @return 商品规格分页数据
     */
    TableDataInfo<Sku> selectSkuPage(Sku sku, PageQuery pageQuery);

    /**
     * 查询商品规格详细信息
     *
     * @param id 商品规格主键
     * @return 商品规格
     */
    Sku selectSkuById(Long id);

    /**
     * 新增商品规格
     *
     * @param sku 商品规格
     * @return 结果
     */
    boolean insertSku(Sku sku);

    /**
     * 修改商品规格
     *
     * @param sku 商品规格
     * @return 结果
     */
    boolean updateSku(Sku sku);

    /**
     * 批量删除商品规格
     *
     * @param ids 需要删除的商品规格主键
     * @return 结果
     */
    boolean deleteSkuByIds(Long[] ids);

    /**
     * 删除商品规格信息
     *
     * @param id 商品规格主键
     * @return 结果
     */
    boolean deleteSkuById(Long id);

    /**
     * 更新商品规格库存
     *
     * @param skuId 规格ID
     * @param count 库存变化量（正数增加，负数减少）
     * @return 更新是否成功
     */
    boolean updateStock(Long skuId, int count);

    /**
     * 增加SKU库存
     *
     * @param skuId    SKU ID
     * @param quantity 增加数量
     * @return 是否成功
     */
    boolean increaseStock(Long skuId, Integer quantity);

    /**
     * 减少SKU库存
     *
     * @param skuId    SKU ID
     * @param quantity 减少数量
     * @return 是否成功
     */
    boolean decreaseStock(Long skuId, Integer quantity);
}
