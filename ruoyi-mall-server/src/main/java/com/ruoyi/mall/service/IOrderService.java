package com.ruoyi.mall.service;

import com.github.yulichang.base.MPJBaseService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.*;
import com.ruoyi.mall.domain.entity.Order;
import com.ruoyi.mall.domain.query.QueryOrderTypeDTO;
import com.ruoyi.mall.domain.vo.OrderExportVo;
import com.ruoyi.mall.domain.vo.OrderVo;

import java.util.List;
import java.util.Map;

/**
 * 订单Service接口
 */
public interface IOrderService extends MPJBaseService<Order> {
    /**
     * 查询订单列表
     */
    List<Order> selectOrderList(Order order);

    /**
     * 查询订单分页列表
     */
    TableDataInfo<Order> selectOrderPage(Order order, PageQuery pageQuery);

    /**
     * 查询订单详细信息
     */
    Order selectOrderByOrderNo(String orderNo);

    /**
     * 根基购物车id创建订单
     *
     * @param orderCreateDTO
     * @return
     */
    String createOrderByCartIds(OrderCreateDTO orderCreateDTO);

    /**
     * 立即购买商品
     *
     * @param createOrderByProductIdDTO
     * @return
     */
    String createOrderByProductId(CreateOrderByProductIdDTO createOrderByProductIdDTO);

    /**
     * 取消订单
     */
    Boolean cancelOrder(String id);

    /**
     * 删除订单
     */
    Boolean deleteOrderById(String orderNo);

    /**
     * 批量删除订单
     */
    Boolean deleteOrderByIds(String[] orderNos);

    /**
     * 确认收货
     */
    Boolean confirmReceive(String orderNo);

    /**
     * 处理支付回调
     *
     * @param payType 支付类型
     * @param map     回调参数
     * @return 处理结果
     */
    Boolean handlePayCallback(String payType, Map<String, String> map);

    Boolean shipmentOder(ShipmentOderDto shipmentOderDto);

    /**
     * 预览订单
     *
     * @param cartIds 购物车商品ID列表
     * @return 订单预览信息
     */
    OrderPreviewDTO previewOrder(List<Long> cartIds);

    /**
     * 预览已选中的购物车商品
     *
     * @return 订单预览信息
     */
    OrderPreviewDTO previewSelectedCarts();

    Order getOrderByOrderNo(String orderNo);


    Map<String, String> createPayment(Order order, String orderNo, Long userId, String payType);

    String queryPayStatus(String paymentNo);

    String queryOrderPayStatus(String orderNo);

    /**
     * 修改订单收货地址
     *
     * @param updateOrderAddressDTO 修改地址参数
     * @return 是否修改成功
     */
    Boolean updateOrderAddress(UpdateOrderAddressDTO updateOrderAddressDTO);

    /**
     * 修改订单价格
     *
     * @param updateOrderPriceDTO 修改价格参数
     * @return 是否修改成功
     */
    Boolean updateOrderPrice(UpdateOrderPriceDTO updateOrderPriceDTO);

    /**
     * 获取订单导出数据
     *
     * @param order 查询条件
     * @return 订单导出数据列表
     */
    List<OrderExportVo> selectOrderExportList(Order order);

    /**
     * 根据不同类型查询订单商品项列表
     *
     * @param queryOrderTypeDTO 查询餐宿
     * @return
     * @@param useId 用户ID
     */
    TableDataInfo<OrderVo> getOrderListByType(Long userId, QueryOrderTypeDTO queryOrderTypeDTO, PageQuery pageQuery);

    /**
     * 获取订单详情
     *
     * @param orderId 订单ID
     * @return
     */
    OrderVo getOrderInfo(Long orderId);
}
