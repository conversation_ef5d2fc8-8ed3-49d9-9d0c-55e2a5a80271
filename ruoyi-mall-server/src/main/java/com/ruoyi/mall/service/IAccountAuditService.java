package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.AccountAuditDTO;
import com.ruoyi.mall.domain.entity.UserAccountAudit;

/**
 * 账户审核服务接口
 *
 * <AUTHOR>
 */
public interface IAccountAuditService extends IService<UserAccountAudit> {


    /**
     * 获取用户账户审核列表
     *
     * @param userAccountAudit
     * @param pageQuery
     * @return
     */
    TableDataInfo<UserAccountAudit> selectAccountRecordPage(UserAccountAudit userAccountAudit, PageQuery pageQuery);


    /**
     * 获取用户账户审核列表
     *
     * @param userAccountAudit
     * @param pageQuery
     * @return
     */
    TableDataInfo<UserAccountAudit> selectAccountAuditPage(UserAccountAudit userAccountAudit, PageQuery pageQuery);


    /**
     * 审核账户注销申请
     *
     * @param accountAuditDTO 审核信息
     * @return 结果
     */
    boolean auditAccount(AccountAuditDTO accountAuditDTO);


}
