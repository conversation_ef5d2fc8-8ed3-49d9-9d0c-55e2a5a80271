package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.OfflineDrainageRecord;

/**
 * 线下充值技术引流次数记录Service接口
 */
public interface IOfflineDrainageRecordService extends IService<OfflineDrainageRecord> {

    /**
     * 查询线下充值技术引流次数记录列表
     *
     * @param record    查询条件
     * @param pageQuery 分页参数
     * @return 分页记录列表
     */
    TableDataInfo<OfflineDrainageRecord> selectOfflineDrainageRecordPage(OfflineDrainageRecord record, PageQuery pageQuery);

    /**
     * 创建线下充值技术引流次数记录
     *
     * @param shopId       商家ID
     * @param shopName     商家名称
     * @param count        充值数量
     * @param beforeCount  操作前技术引流次数
     * @param afterCount   操作后技术引流次数
     * @param operatorId   操作人ID
     * @param operatorName 操作人名称
     * @param remark       备注
     * @return 记录ID
     */
    Long createOfflineDrainageRecord(Long shopId, String shopPhone, String shopName, Integer count,
                                     Integer beforeCount, Integer afterCount,
                                     Long operatorId, String operatorName, String remark);

    /**
     * 线下充值技术引流次数
     *
     * @param shopId       商家ID
     * @param count        充值数量
     * @param operatorId   操作人ID
     * @param operatorName 操作人名称
     * @param remark       备注
     * @return 是否成功
     */
    boolean rechargeOfflineDrainage(Long shopId, Integer count, Long operatorId, String operatorName, String remark);
}
