package com.ruoyi.mall.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.bo.ConfigSettingBo;
import com.ruoyi.mall.domain.entity.ConfigSetting;
import com.ruoyi.mall.domain.vo.ConfigSettingVo;
import com.ruoyi.mall.mapper.ConfigSettingMapper;
import com.ruoyi.mall.service.IConfigSettingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 定制化配置服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConfigSettingServiceImpl extends ServiceImpl<ConfigSettingMapper, ConfigSetting> implements IConfigSettingService {

    @Override
    public JSONObject getConfigValue(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }

        LambdaQueryWrapper<ConfigSetting> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConfigSetting::getConfigKey, key)
            .eq(ConfigSetting::getDelFlag, "0");

        ConfigSetting config = getOne(wrapper);
        return config != null ? new JSONObject(config.getConfigValue()) : null;
    }

    @Override
    public ConfigSettingVo getConfigByKey(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }

        LambdaQueryWrapper<ConfigSetting> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ConfigSetting::getConfigKey, key)
            .eq(ConfigSetting::getDelFlag, "0");

        ConfigSetting config = getOne(wrapper);
        if (config == null) {
            return null;
        }

        ConfigSettingVo vo = new ConfigSettingVo();
        BeanUtils.copyProperties(config, vo);
        vo.setConfigValue(new JSONObject(config.getConfigValue()));
        return vo;
    }

    @Override
    public Boolean saveConfig(ConfigSettingBo configBo) {
        ConfigSetting config = new ConfigSetting();
        BeanUtils.copyProperties(configBo, config);
        config.setDelFlag("0");
        config.setConfigValue(configBo.getConfigValue().toString());
        return save(config);
    }

    @Override
    public Boolean updateConfig(ConfigSettingBo configBo) {

        LambdaUpdateWrapper<ConfigSetting> qw = new LambdaUpdateWrapper<>();
        qw
            .set(ConfigSetting::getConfigValue, configBo.getConfigValue().toString())
            .eq(ConfigSetting::getConfigKey, configBo.getConfigKey());

        return update(qw);
    }
}
