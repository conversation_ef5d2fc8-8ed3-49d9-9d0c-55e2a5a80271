package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.DeductionRecord;

import java.math.BigDecimal;

/**
 * 抵扣金赠送记录Service接口
 */
public interface IDeductionRecordService extends IService<DeductionRecord> {

    /**
     * 查询抵扣金赠送记录列表
     *
     * @param deductionRecord 抵扣金赠送记录
     * @param pageQuery       分页参数
     * @return 抵扣金赠送记录分页列表
     */
    TableDataInfo<DeductionRecord> queryPageList(DeductionRecord deductionRecord, PageQuery pageQuery);

    /**
     * 商家赠送抵扣金给用户
     *
     * @param shopId 商家ID
     * @param phone  用户手机号
     * @param amount 赠送金
     * @param reason 赠送原因
     * @return 是否赠送成功
     */
    boolean giveDeductionMoney(Long shopId, String phone, BigDecimal amount, String reason);
}
