package com.ruoyi.mall.service;

import com.ruoyi.mall.domain.entity.Area;

import java.util.List;

/**
 * 地区服务接口
 */
public interface IAreaService {

    /**
     * 获取所有省份列表
     *
     * @return 省份列表
     */
    List<Area> getProvinces();

    /**
     * 获取指定省份下的城市列表
     *
     * @param provinceCode 省份编码
     * @return 城市列表
     */
    List<Area> getCitiesByProvince(Long provinceCode);

    /**
     * 获取指定城市下的区县列表
     *
     * @param cityCode 城市编码
     * @return 区县列表
     */
    List<Area> getDistrictsByCity(Long cityCode);

    /**
     * 获取指定区县下的街道列表
     *
     * @param districtCode 区县编码
     * @return 街道列表
     */
    List<Area> getTownsByDistrict(Long districtCode);

    /**
     * 根据编码获取地区信息
     *
     * @param code 地区编码
     * @return 地区信息
     */
    Area getAreaByCode(Long code);

    /**
     * 获取完整的地区名称（省市区）
     *
     * @param provinceCode 省份编码
     * @param cityCode     城市编码
     * @param districtCode 区县编码
     * @return 完整地区名称，如"广东省深圳市南山区"
     */
    String getFullAreaName(Long provinceCode, Long cityCode, Long districtCode);

    /**
     * 根据父ID查询下级地区列表
     *
     * @param parentId 父级ID
     * @return 下级地区列表
     */
    List<Area> getAreasByParentId(Long parentId);
}
