package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.entity.*;
import com.ruoyi.mall.domain.query.QueryConsignmentProductDTO;
import com.ruoyi.mall.domain.query.QueryProductByCategoryIdDTO;
import com.ruoyi.mall.domain.query.QueryProductByShopQueryDTO;
import com.ruoyi.mall.domain.query.QueryShopByTypeGetProductDTO;
import com.ruoyi.mall.domain.vo.DaiXiaoInfoVo;
import com.ruoyi.mall.domain.vo.EvaluateVo;
import com.ruoyi.mall.domain.vo.ProductInfoVo;
import com.ruoyi.mall.domain.vo.ProductVo;
import com.ruoyi.mall.mapper.ConsignmentProductMapper;
import com.ruoyi.mall.mapper.ProductMapper;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.mall.service.*;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品Service实现
 */
@RequiredArgsConstructor
@Service
public class ProductServiceImpl extends MPJBaseServiceImpl<ProductMapper, Product> implements IProductService {

    private final ISkuService skuService;
    private final ICategoryService categoryService;
    private final ProductMapper productMapper;
    private final ITZUserFocusService tzUserFocusService;
    private final IShopService shopService;
    private final IProductEvaluateService productEvaluateService;
    private final ConsignmentProductMapper consignmentProductMapper;
    private final TzUserMapper tzUserMapper;

    @Override
    public TableDataInfo<Product> queryPageList(Product product, PageQuery pageQuery) {
        // 使用 MPJLambdaWrapper 实现联表查询
        MPJLambdaWrapper<Product> wrapper = JoinWrappers.lambda(Product.class)
            .selectAll(Product.class)
            .selectAs(Category::getName, Product::getCategoryName)
            .selectAs(Shop::getBusinessName, Product::getMerchantName)
            // 关联分类表
            .leftJoin(Category.class, Category::getId, Product::getCategoryId)
            .leftJoin(Shop.class, Shop::getId, Product::getShopId)

            // 商品名称模糊查询
            .like(StringUtils.isNotEmpty(product.getName()), Product::getName, product.getName())
            // 商品名称模糊查询
            .like(StringUtils.isNotEmpty(product.getMerchantName()), Shop::getBusinessName, product.getMerchantName())
            // 商品分类查询
            .eq(product.getCategoryId() != null, Product::getCategoryId, product.getCategoryId())
            // 店铺查询
            .eq(product.getShopId() != null, Product::getShopId, product.getShopId())
            // 上架状态查询
            .eq(StringUtils.isNotEmpty(product.getStatus()), Product::getStatus, product.getStatus())
            // 审核状态查询
            .eq(StringUtils.isNotEmpty(product.getAuditStatus()), Product::getAuditStatus, product.getAuditStatus())
            // 推荐状态查询
            .eq(StringUtils.isNotEmpty(product.getIsRecommend()), Product::getIsRecommend, product.getIsRecommend())
            // 新品状态查询
            .eq(StringUtils.isNotEmpty(product.getIsNew()), Product::getIsNew, product.getIsNew())
            // 只查询未删除的数据
            .eq(Product::getDelFlag, "0")
            // 价格区间查询
            .ge(product.getMinPrice() != null, Product::getPrice, product.getMinPrice())
            .le(product.getMaxPrice() != null, Product::getPrice, product.getMaxPrice())
            // 按排序和ID排序
            .orderByDesc(Product::getSort).orderByDesc(Product::getId);
        // 执行联表分页查询
        IPage<Product> productIPage = selectJoinListPage(pageQuery.build(), Product.class, wrapper);
        return TableDataInfo.build(productIPage);
    }

    @Override
    public Product getProductDetail(Long productId) {
        // 获取商品基本信息
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getId, productId)
            .eq(Product::getDelFlag, "0");
        Product product = getOne(queryWrapper);

        if (product == null) {
            return null;
        }

        // 设置分类名称
        if (product.getCategoryId() != null) {
            Category category = categoryService.getById(product.getCategoryId());
            if (category != null) {
                product.setCategoryName(category.getName());
            }
        }

        // 获取SKU列表
        List<Sku> skuList = skuService.lambdaQuery()
            .eq(Sku::getProductId, productId)
            .orderByAsc(Sku::getSort)  // 按排序字段排序
            .list();
        product.setSkuList(skuList);

        // 调试日志
        System.out.println("商品ID: " + productId + ", SKU数量: " + (skuList != null ? skuList.size() : 0));

        return product;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveProductWithSkus(Product product) {
        Long shopId = product.getShopId();
        Long categoryId = product.getCategoryId();

        // 获取店铺信息
        Shop shop = baseMapper.selectShopById(shopId);
        if (shop == null) {
            throw new ServiceException("店铺不存在");
        }

        // 检查店铺是否已有分类
        if (shop.getShopCategoryId() != null) {
            // 店铺已有分类，必须使用该分类
            if (!shop.getShopCategoryId().equals(categoryId)) {
                throw new ServiceException("该店铺只能使用: " + shop.getShopCategoryName() + "分类，请修改商品分类");
            }
        } else {
            // 店铺还没有分类，将第一次创建商品时的分类设为店铺分类
            Category category = categoryService.getById(categoryId);
            if (category == null) {
                throw new ServiceException("分类不存在");
            }

            // 更新店铺的分类信息
            Shop updateShop = new Shop();
            updateShop.setId(shopId);
            updateShop.setShopCategoryId(categoryId);
            updateShop.setShopCategoryName(category.getName());
            baseMapper.updateShop(updateShop);

            // 更新商品的分类名称
            product.setCategoryName(category.getName());
        }

        // 保存商品基本信息
        boolean success = save(product);
        if (!success) {
            throw new ServiceException("保存商品失败");
        }

        // 保存SKU信息
        if (product.getSkuList() != null && !product.getSkuList().isEmpty()) {
            for (Sku sku : product.getSkuList()) {
                sku.setProductId(product.getId());
            }
            skuService.saveBatch(product.getSkuList());
        }

        return product.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProductWithSkus(Product product) {
        Long shopId = product.getShopId();
        Long categoryId = product.getCategoryId();

        // 获取店铺信息
        Shop shop = baseMapper.selectShopById(shopId);
        if (shop == null) {
            throw new ServiceException("店铺不存在");
        }

        // 检查店铺分类是否与商品分类一致
        if (shop.getShopCategoryId() != null && !shop.getShopCategoryId().equals(categoryId)) {
            throw new ServiceException("该店铺只能使用分类: " + shop.getShopCategoryName() + "，请修改商品分类");
        }

        // 获取分类信息
        Category category = categoryService.getById(categoryId);
        if (category != null) {
            product.setCategoryName(category.getName());
        }

        // 更新商品基本信息
        boolean success = updateById(product);

        // 更新SKU信息，先删除原有SKU，再插入新的SKU
        if (success && product.getSkuList() != null) {
            // 删除原有SKU
            skuService.lambdaUpdate().eq(Sku::getProductId, product.getId()).remove();

            // 保存新的SKU
            if (!product.getSkuList().isEmpty()) {
                for (Sku sku : product.getSkuList()) {
                    sku.setId(null);
                    sku.setProductId(product.getId());
                }
                success = skuService.saveBatch(product.getSkuList());
            }
        }

        return success;
    }

    @Override
    public boolean checkProductNameUnique(Product product) {
        Long productId = product.getId() == null ? -1L : product.getId();
        Long shopId = product.getShopId();

        // 查询名称相同的记录
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Product::getName, product.getName())
            .eq(Product::getShopId, shopId)
            .eq(Product::getDelFlag, "0");

        Product existingProduct = getOne(queryWrapper);

        // 如果存在同名商品，且不是当前编辑的商品，则返回false（不唯一）
        return existingProduct == null || existingProduct.getId().equals(productId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProductByIds(Long[] productIds) {
        // 逻辑删除商品
        LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(Product::getId, Arrays.asList(productIds))
            .set(Product::getDelFlag, "2");
        boolean success = update(updateWrapper);

        // 逻辑删除关联的SKU
        if (success) {
            for (Long productId : productIds) {
                skuService.lambdaUpdate()
                    .eq(Sku::getProductId, productId)
                    .remove();
            }
        }

        return success;
    }

    @Override
    public boolean batchUpdatePublishStatus(List<Long> productIds, String status) {
        // 批量更新上架状态
        LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(Product::getId, productIds)
            .set(Product::getStatus, status);

        return update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStock(Long productId, int count) {
        Product product = getById(productId);
        if (product == null) {
            throw new ServiceException("商品不存在");
        }

        // 如果是减库存操作，需要检查库存是否足够
        if (product.getStock() < Math.abs(count)) {
            throw new ServiceException("商品库存不足");
        }

        // 更新库存
        LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Product::getId, productId)
            .setSql("stock = stock + " + count);

        boolean success = update(updateWrapper);

        // 如果库存为0，自动下架商品
        if (success && product.getStock() + count <= 0) {
            Product updateProduct = new Product();
            updateProduct.setId(productId);
            updateProduct.setStatus("0"); // 下架状态
            updateById(updateProduct);
        }

        return success;
    }

    @Override
    public boolean addSales(Long productId, int count) {
        if (count <= 0) {
            return false;
        }

        // 增加销量
        LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Product::getId, productId)
            .setSql("sales = sales + " + count);

        return update(updateWrapper);
    }

    @Override
    public TableDataInfo<ProductVo> getProductByType(Integer type, Long userId, PageQuery pageQuery) {
        // 参数校验
        if (type == null) {
            throw new ServiceException("参数类型不能为空");
        }
        // 提取分页参数构建，避免重复调用
        Page page = pageQuery.build();
        Page<ProductVo> list;

        if (type == 1) {
            list = productMapper.selectProductByBrowse(userId, page);
        } else if (type == 2) {
            list = productMapper.UserBrowseLikeProduct(userId, page);
        } else if (type == 3) {
            list = productMapper.selectProductBySales(page);
        } else {
            throw new ServiceException("传的参数类型不对");
        }

        return TableDataInfo.build(list);
    }


    @Override
    public TableDataInfo<DaiXiaoInfoVo> getDaiXiaoProductList(Long userId, QueryConsignmentProductDTO query, PageQuery pageQuery) {

      //获取代销用户数据
        LambdaQueryWrapper<Shop> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Shop::getUserId, userId);
        Shop shop = shopService.getOne(wrapper);
        if(shop==null){
            throw new ServiceException("用户不存在");
        }

        List<Long> shopIds =new ArrayList<>();
        //获取同一身份证号码用户的店铺
        LambdaQueryWrapper<TzUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TzUser::getIdCard, shop.getLegalPersonIdCard());
        TzUser tzUser = tzUserMapper.selectOne(lqw);
        if(tzUser!=null){

            LambdaQueryWrapper<TZUserFocus> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                .eq(TZUserFocus::getUserId, tzUser.getUserId())
                .eq(TZUserFocus::getStatus, "0");

            //获取是哪些店铺粉丝的店铺id
            shopIds = tzUserFocusService.list(queryWrapper)
                .stream()
                .map(TZUserFocus::getShopId)
                .collect(Collectors.toList());
        }

        if (shopIds.isEmpty()) {
            return TableDataInfo.build();
        }

        // 使用优化的 SQL 查询方案
        return getConsignmentProductsWithOptimizedSQL(pageQuery, query, shopIds);
    }

    @Override
    public TableDataInfo<DaiXiaoInfoVo> getShopDaiXiaoProductList(Long shopId, QueryConsignmentProductDTO query, PageQuery pageQuery) {

        // 1. 构建查询条件
        MPJLambdaWrapper<Product> wrapper = new MPJLambdaWrapper<>(Product.class)  // 明确指定泛型类型
            .select("t.id productId,t.daixiao_type daixiaoType,t.daixiao_type_value daixiaoTypeValue,t.shop_id,t.name,t.cover,t.status,t1.phone,t.price price")
            .leftJoin(Shop.class, Shop::getId, Product::getShopId)
            .eq(Product::getIsDaixiao, 1)  //是代销产品
            .eq(Product::getAuditStatus, "1") //审核通过
            .eq(Product::getStatus, "1") //上架
            .like(StringUtils.isNotEmpty(query.getProductName()), Product::getName, query.getProductName())
            .eq(Product::getShopId, shopId)
            .groupBy(Product::getId)
            .orderByDesc(Product::getUrlCreateTime);

        TableDataInfo<DaiXiaoInfoVo> tableDataInfo = TableDataInfo.build(productMapper.selectJoinPage(pageQuery.build(), DaiXiaoInfoVo.class, wrapper));

        // 批量设置代销商品数量
        setConsignmentProductCounts(tableDataInfo.getRows());

        return tableDataInfo;
    }

    /**
     * 批量设置代销商品数量 - 优化版本，避免循环查询
     *
     * @param daiXiaoInfoVos 代销信息列表
     */
    private void setConsignmentProductCounts(List<DaiXiaoInfoVo> daiXiaoInfoVos) {
        if (daiXiaoInfoVos == null || daiXiaoInfoVos.isEmpty()) {
            return;
        }

        try {
            // 1. 获取所有商品ID，去重并过滤空值
            List<Long> productIds = daiXiaoInfoVos.stream()
                .map(DaiXiaoInfoVo::getProductId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

            if (productIds.isEmpty()) {
                // 如果没有有效的商品ID，设置所有数量为0
                daiXiaoInfoVos.forEach(vo -> vo.setNumber(0));
                return;
            }

            // 2. 批量查询代销商品数量
            Map<Long, Integer> consignmentCountMap = getConsignmentProductCountMap(productIds);

            // 3. 设置每个商品的代销数量
            for (DaiXiaoInfoVo vo : daiXiaoInfoVos) {
                if (vo.getProductId() != null) {
                    Integer count = consignmentCountMap.getOrDefault(vo.getProductId(), 0);
                    vo.setNumber(count);
                } else {
                    vo.setNumber(0);
                }
            }

        } catch (Exception e) {
            log.error("批量设置代销商品数量失败", e);
            // 发生异常时，设置所有数量为0，保证程序正常运行
            daiXiaoInfoVos.forEach(vo -> vo.setNumber(0));
        }
    }

    /**
     * 批量查询代销商品数量映射
     *
     * @param productIds 商品ID列表
     * @return 商品ID -> 代销数量的映射
     */
    private Map<Long, Integer> getConsignmentProductCountMap(List<Long> productIds) {
        // 分批处理，避免IN查询参数过多
        final int BATCH_SIZE = 1000;

        if (productIds.size() <= BATCH_SIZE) {
            return getConsignmentCountForBatch(productIds);
        }

        // 分批查询并合并结果
        Map<Long, Integer> resultMap = new HashMap<>();
        for (int i = 0; i < productIds.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, productIds.size());
            List<Long> batchIds = productIds.subList(i, endIndex);

            Map<Long, Integer> batchResult = getConsignmentCountForBatch(batchIds);
            resultMap.putAll(batchResult);
        }

        return resultMap;
    }

    /**
     * 查询单批商品的代销数量
     *
     * @param productIds 商品ID列表
     * @return 商品ID -> 代销数量的映射
     */
    private Map<Long, Integer> getConsignmentCountForBatch(List<Long> productIds) {
        try {
            // 批量查询代销商品数量，按商品ID分组
            List<Map<String, Object>> consignmentCountMaps = consignmentProductMapper.selectCountGroupByProductId(productIds);

            // 将查询结果转换为 Map<商品ID, 代销数量>
            return consignmentCountMaps.stream()
                .collect(Collectors.toMap(
                    map -> {
                        Object productIdObj = map.get("productId");
                        return productIdObj != null ? Long.parseLong(productIdObj.toString()) : 0L;
                    },
                    map -> {
                        Object countObj = map.get("count");
                        return countObj != null ? Integer.parseInt(countObj.toString()) : 0;
                    },
                    (existing, replacement) -> existing, // 处理重复key的情况
                    HashMap::new
                ));

        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询代销商品数量失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public int updateDaiXiaoProductUrl(Long productId) {

        String url = "http://" + productId;

        LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .set(Product::getJumpUrl, url)
            .set(Product::getUrlCreateTime, new Date())
            .eq(Product::getId, productId)
            .eq(Product::getDelFlag, "0");

        return productMapper.update(updateWrapper);
    }


    @Override
    public List<Product> selectProductList(Product product) {
        LambdaQueryWrapper<Product> queryWrapper = buildQueryWrapper(product);
        return list(queryWrapper);
    }

    @Override
    public TableDataInfo<Product> selectProductPage(Product product, PageQuery pageQuery) {
        LambdaQueryWrapper<Product> queryWrapper = buildQueryWrapper(product);
        Page<Product> page = page(pageQuery.build(), queryWrapper);
        return TableDataInfo.build(page);
    }

    @Override
    public Product selectProductById(Long id) {
        return getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertProduct(Product product) {
        return save(product);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProduct(Product product) {
        return updateById(product);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProductById(Long id) {
        // 逻辑删除商品
        LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Product::getId, id)
            .set(Product::getDelFlag, "2");
        boolean success = update(updateWrapper);

        // 逻辑删除关联的SKU
        if (success) {
            skuService.lambdaUpdate()
                .eq(Sku::getProductId, id)
                .remove();
        }

        return success;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<Product> buildQueryWrapper(Product product) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        queryWrapper.like(StringUtils.isNotEmpty(product.getName()),
            Product::getName, product.getName());

        queryWrapper.eq(product.getCategoryId() != null,
            Product::getCategoryId, product.getCategoryId());

        queryWrapper.eq(product.getShopId() != null,
            Product::getShopId, product.getShopId());

        queryWrapper.eq(StringUtils.isNotEmpty(product.getStatus()),
            Product::getStatus, product.getStatus());

        queryWrapper.eq(StringUtils.isNotEmpty(product.getIsRecommend()),
            Product::getIsRecommend, product.getIsRecommend());

        queryWrapper.eq(StringUtils.isNotEmpty(product.getIsNew()),
            Product::getIsNew, product.getIsNew());

        queryWrapper.eq(StringUtils.isNotEmpty(product.getIsHot()),
            Product::getIsHot, product.getIsHot());

        queryWrapper.lt(product.getMaxPrice() != null,
            Product::getPrice, product.getMaxPrice());

        queryWrapper.gt(product.getMinPrice() != null,
            Product::getPrice, product.getMinPrice());

        // 按排序和ID排序
        queryWrapper.orderByDesc(Product::getSort).orderByDesc(Product::getId);

        return queryWrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean increaseStock(Long productId, Integer quantity) {
        if (productId == null || quantity == null || quantity <= 0) {
            return false;
        }

        LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Product::getId, productId)
            .setSql("stock = stock + " + quantity);

        return update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean decreaseStock(Long productId, Integer quantity) {
        if (productId == null || quantity == null || quantity <= 0) {
            return false;
        }

        LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Product::getId, productId)
            .ge(Product::getStock, quantity)  // 确保库存足够
            .setSql("stock = stock - " + quantity);

        return update(updateWrapper);
    }

    @Override
    public TableDataInfo<ProductVo> getRandomConsignmentProductPage(PageQuery pageQuery) {
        return TableDataInfo.build(productMapper.getRandomConsignmentProductPage(pageQuery.build()));
    }

    @Override
    public TableDataInfo<ProductVo> getRandomProductPage(PageQuery pageQuery) {
        return TableDataInfo.build(productMapper.getRandomProductPage(pageQuery.build()));
    }

    @Override
    @Transactional
    public void cancelConsignmentProduct(Long productId) {
        LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
            .set(Product::getIsDaixiao, 0)
            .eq(Product::getId, productId);

        if (update(updateWrapper)) {
            LambdaUpdateWrapper<ConsignmentProduct> wrapper = new LambdaUpdateWrapper<>();
            wrapper
                .set(ConsignmentProduct::getStatus, "1")
                .eq(ConsignmentProduct::getProductId, productId);

            consignmentProductMapper.update(wrapper);
        }
    }

    @Override
    public ProductInfoVo getAppProductInfo(Long productId, Long shopId) {

        ProductInfoVo productInfoVo = new ProductInfoVo();
        //判断商家/代销商家是否存在
        LambdaQueryWrapper<Shop> shopLambdaQueryWrapper = new LambdaQueryWrapper<>();
        shopLambdaQueryWrapper
            .select(Shop::getId, Shop::getName, Shop::getBusinessName, Shop::getLogo, Shop::getFans, Shop::getStatus)
            .eq(Shop::getId, shopId);
        Shop shop = shopService.getById(shopId);
        //当店铺不存在或者停用
        if (shop == null || shop.getStatus().equals("1")) {
            throw new ServiceException("店铺不存在/停用");
        }

        Product product = getById(productId);
        //当商品不存在或者未审核通过
        if (product == null) {
            throw new ServiceException("商品不存在");
        }

        //获取商品消息和商家地址
        MPJLambdaWrapper<Product> wrapper = new MPJLambdaWrapper<>();
        wrapper
            .select(Product::getShopId, Product::getName, Product::getVideo, Product::getImages, Product::getPrice, Product::getStatus,
                Product::getDescription, Product::getContent, Product::getStock, Product::getSales, Product::getCategoryId, Product::getFeeBearer)
            .select("CONCAT_WS('', IFNULL(province, ''), IFNULL(city, ''))  as address")
            .selectAs(Shop::getProvince, ProductInfoVo::getProvince)
            .selectAs(Shop::getCity, ProductInfoVo::getCity)
            .selectAs(Product::getId, ProductInfoVo::getProductId)
            .leftJoin(Shop.class, Shop::getId, Product::getShopId)
            .eq(Shop::getStatus, "0")
            .eq(Shop::getAuthStatus, "1")
            .eq(Product::getId, productId)
            .eq(Product::getAuditStatus, "1");

        productInfoVo = selectJoinOne(ProductInfoVo.class, wrapper);
        if (productInfoVo != null && StringUtils.isNotEmpty(productInfoVo.getImages())) {
            List<String> imagesList = Arrays.asList(productInfoVo.getImages().split(","));
            productInfoVo.setImagesList(imagesList);
        }

        if (productInfoVo != null) {

            //存储商家信息
            productInfoVo.setShop(shop);

            productInfoVo.setProductId(productId);
            LambdaQueryWrapper<Sku> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper
                .eq(Sku::getProductId, productId)
                .eq(Sku::getStatus, "0");
            // sku
            List<Sku> skuList = skuService.list(queryWrapper);
            productInfoVo.setSkus(skuList);


            PageQuery query = new PageQuery();
            query.setPageNum(1);
            query.setPageSize(3);
            //商品评价
            TableDataInfo<EvaluateVo> productEvaluateTableDataInfo = productEvaluateService.selProductEvaluate(productId, 1, query);
            productInfoVo.setProductEvaluates(productEvaluateTableDataInfo.getRows());
            //评价总条数
            productInfoVo.setEvaluateCount((int) productEvaluateTableDataInfo.getTotal());
            //获取买家秀
            productInfoVo.setCustomerShows(productEvaluateService.selProductEvaluate(productId, 5, query).getRows());
        }
        return productInfoVo;
    }

    @Override
    public TableDataInfo<ProductVo> getProductByType(QueryShopByTypeGetProductDTO query, PageQuery pageQuery) {

        Shop shop = shopService.getById(query.getShopId());
        Page<ProductVo> productList;
        MPJLambdaWrapper<Product> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper.select(Product::getId, Product::getShopId, Product::getName, Product::getCover, Product::getPrice, Product::getSales);

        if (shop == null || shop.getStatus().equals("1")) {
            throw new ServiceException("店铺不存在/停用");
        } else if (shop.getType().equals("1")) {//商铺
            queryWrapper
                .selectAs(Shop::getId, ProductVo::getShopId)
                .leftJoin(Shop.class, Shop::getId, Product::getShopId)
                .eq(Product::getShopId, query.getShopId());

            if (query.getType().equals(1)) {//推荐
                queryWrapper.eq(Product::getIsRecommend, "1");
            } else if (query.getType().equals(2)) {//热卖
                queryWrapper.eq(Product::getIsHot, "1");
            } else if (query.getType().equals(3)) {//上新
                queryWrapper.eq(Product::getIsNew, "1");
            }

        } else if (shop.getType().equals("0")) { //代销商家
            queryWrapper
                .selectAs(ConsignmentProduct::getConsignmentShop, ProductVo::getShopId)
                .leftJoin(Shop.class, Shop::getId, Product::getShopId)
                .leftJoin(ConsignmentProduct.class, ConsignmentProduct::getProductId, Product::getId)
                .eq(ConsignmentProduct::getConsignmentShop, query.getShopId())
                .eq(ConsignmentProduct::getStatus, "0");//正常

        }
        queryWrapper
            .eq(Product::getStatus, "1")  //上架
            .eq(Product::getAuditStatus, "1") //审核通过
            .eq(Shop::getStatus, "0")//正常
            .orderByDesc(Product::getSales);

        productList = selectJoinListPage(pageQuery.build(), ProductVo.class, queryWrapper);

        return TableDataInfo.build(productList);
    }

    @Override
    public TableDataInfo<ProductVo> getProductByShopQuery(QueryProductByShopQueryDTO query, PageQuery pageQuery) {
        Shop shop = shopService.getById(query.getShopId());
        MPJLambdaWrapper<Product> queryWrapper = new MPJLambdaWrapper<>();

        //判断当前是商家还是代销
        if (shop == null || shop.getStatus().equals("1")) {
            throw new ServiceException("商家不存在/停用");
        } else if (shop.getType().equals("1")) {//商铺
            queryWrapper
                .select(Product::getId, Product::getShopId, Product::getName, Product::getCover, Product::getPrice, Product::getSales)
                .leftJoin(Shop.class, Shop::getId, Product::getShopId)
                .eq(Product::getShopId, query.getShopId());

        } else if (shop.getType().equals("0")) { //代销商家
            queryWrapper
                .select(Product::getId, Product::getName, Product::getCover, Product::getPrice, Product::getSales)
                .selectAs(ConsignmentProduct::getConsignmentShop, ProductVo::getShopId)
                .leftJoin(Shop.class, Shop::getId, Product::getShopId)
                .leftJoin(ConsignmentProduct.class, ConsignmentProduct::getProductId, Product::getId)
                .eq(ConsignmentProduct::getConsignmentShop, query.getShopId())
                .eq(ConsignmentProduct::getStatus, "0");//正常
        }

        queryWrapper
            .eq(Product::getStatus, "1")  //上架
            .eq(Product::getAuditStatus, "1") //审核通过
            .like(Product::getName, query.getQuery())
            .or()
            .like(Product::getCategoryName, query.getQuery())
            .or()
            .like(Product::getDescription, query.getQuery());//模糊查询

        return TableDataInfo.build(selectJoinListPage(pageQuery.build(), ProductVo.class, queryWrapper));
    }

    @Override
    public TableDataInfo<ProductVo> getShopProductByCategoryId(QueryProductByCategoryIdDTO query, PageQuery pageQuery) {
        MPJLambdaWrapper<Product> queryWrapper = new MPJLambdaWrapper<>();
        queryWrapper
            .select(Product::getId, Product::getShopId, Product::getName, Product::getCover, Product::getPrice, Product::getSales)
            .leftJoin(Shop.class, Shop::getId, Product::getShopId)
            .eq(Product::getShopId, query.getShopId())
            .eq(Product::getCategoryId, query.getCategoryId())
            .eq(Product::getAuditStatus, "1")
            .eq(Product::getStatus, "1")
            .eq(Shop::getStatus, "0")
            .orderByDesc(Product::getPrice);

        return TableDataInfo.build(productMapper.selectJoinPage(pageQuery.build(), ProductVo.class, queryWrapper));
    }

    @Override
    public TableDataInfo<ProductVo> getHomeProductByCategoryId(Long categoryId, String query, PageQuery pageQuery) {
        //获取当前分类ID所有下级ID包括自己ID
        List<Long> allChiIdId = categoryService.getCategoryIdByCategoryId(categoryId);
        if (allChiIdId.isEmpty()) {
            throw new ServiceException("没有分类");
        }
        return TableDataInfo.build(productMapper.getProductByCategoryIds(allChiIdId, query, pageQuery.build()));
    }

    @Override
    public TableDataInfo<ProductVo> getProductByKeyword(String keyword, PageQuery pageQuery) {
        return TableDataInfo.build(productMapper.getProductByKeyword(pageQuery.build(), keyword));
    }

    @Override
    public List<ProductVo> getProductByUserBuyProduct(Integer pageNum, Integer pageSize, Long productId) {

        MPJLambdaWrapper<Product> wrapper = new MPJLambdaWrapper<>();
        wrapper
            .select(Product::getCategoryId, Product::getName)
            .selectAs(Category::getName, Product::getCategoryName)
            .leftJoin(Category.class, Category::getId, Product::getCategoryId)
            .eq(Product::getId, productId);

        return Collections.emptyList();
    }

    /**
     * 使用优化的 SQL 查询代销商品
     *
     * @param pageQuery 分页参数
     * @param query     查询条件
     * @param shopIds   店铺ID列表
     * @return 代销商品信息
     */
    private TableDataInfo<DaiXiaoInfoVo> getConsignmentProductsWithOptimizedSQL(PageQuery pageQuery,
                                                                                QueryConsignmentProductDTO query,
                                                                                List<Long> shopIds) {
        try {
            // 参数校验
            if (shopIds == null || shopIds.isEmpty()) {
                return TableDataInfo.build(new Page<>());
            }

            // 执行优化的 SQL 查询
            Page<DaiXiaoInfoVo> page = productMapper.selectConsignmentProductsOptimized(
                pageQuery.build(),
                query,
                shopIds
            );
            return TableDataInfo.build(page);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询代销商品失败", e);
            return TableDataInfo.build(new Page<>());
        }
    }

}
