package com.ruoyi.mall.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.bo.ShopReturnAddressBo;
import com.ruoyi.mall.domain.entity.ShopReturnAddress;
import com.ruoyi.mall.mapper.ShopReturnAddressMapper;
import com.ruoyi.mall.service.IShopReturnAddressService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 商家退货地址Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class ShopReturnAddressServiceImpl implements IShopReturnAddressService {

    private final ShopReturnAddressMapper baseMapper;

    /**
     * 查询商家退货地址
     */
    @Override
    public ShopReturnAddress queryById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询商家退货地址列表
     */
    @Override
    public TableDataInfo<ShopReturnAddress> queryPageList(ShopReturnAddressBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ShopReturnAddress> lqw = buildQueryWrapper(bo);
        Page<ShopReturnAddress> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询商家退货地址列表
     */
    @Override
    public List<ShopReturnAddress> queryList(ShopReturnAddressBo bo) {
        LambdaQueryWrapper<ShopReturnAddress> lqw = buildQueryWrapper(bo);
        return baseMapper.selectList(lqw);
    }

    private LambdaQueryWrapper<ShopReturnAddress> buildQueryWrapper(ShopReturnAddressBo bo) {
        LambdaQueryWrapper<ShopReturnAddress> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getReceiverName()), ShopReturnAddress::getReceiverName, bo.getReceiverName());
        lqw.like(StringUtils.isNotBlank(bo.getReceiverPhone()), ShopReturnAddress::getReceiverPhone, bo.getReceiverPhone());
        lqw.eq(ObjectUtil.isNotNull(bo.getProvinceCode()), ShopReturnAddress::getProvinceCode, bo.getProvinceCode());
        lqw.like(StringUtils.isNotBlank(bo.getProvinceName()), ShopReturnAddress::getProvinceName, bo.getProvinceName());
        lqw.eq(ObjectUtil.isNotNull(bo.getCityCode()), ShopReturnAddress::getCityCode, bo.getCityCode());
        lqw.like(StringUtils.isNotBlank(bo.getCityName()), ShopReturnAddress::getCityName, bo.getCityName());
        lqw.eq(ObjectUtil.isNotNull(bo.getDistrictCode()), ShopReturnAddress::getDistrictCode, bo.getDistrictCode());
        lqw.like(StringUtils.isNotBlank(bo.getDistrictName()), ShopReturnAddress::getDistrictName, bo.getDistrictName());
        lqw.like(StringUtils.isNotBlank(bo.getDetailAddress()), ShopReturnAddress::getDetailAddress, bo.getDetailAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getIsDefault()), ShopReturnAddress::getIsDefault, bo.getIsDefault());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ShopReturnAddress::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getAddressRemark()), ShopReturnAddress::getAddressRemark, bo.getAddressRemark());
        return lqw;
    }

    /**
     * 新增商家退货地址
     */
    @Override
    public Boolean insertByBo(ShopReturnAddressBo bo) {
        ShopReturnAddress add = BeanUtil.toBean(bo, ShopReturnAddress.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改商家退货地址
     */
    @Override
    public Boolean updateByBo(ShopReturnAddressBo bo) {
        ShopReturnAddress update = BeanUtil.toBean(bo, ShopReturnAddress.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ShopReturnAddress entity) {
        // 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除商家退货地址
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean setDefault(Long id, Long shopId) {
        if (id == null) {
            throw new ServiceException("地址ID不能为空");
        }

        if (shopId == null) {
            throw new ServiceException("商家ID不能为空");
        }

        // 验证地址所属商家
        ShopReturnAddress address = baseMapper.selectById(id);
        if (address == null) {
            throw new ServiceException("退货地址不存在");
        }

        if (!address.getShopId().equals(shopId)) {
            throw new ServiceException("无权操作其他商家的退货地址");
        }

        // 1. 先将所有地址设为非默认
        resetDefaultAddress(shopId);

        // 2. 再将指定地址设为默认
        LambdaUpdateWrapper<ShopReturnAddress> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(ShopReturnAddress::getId, id);
        updateWrapper.set(ShopReturnAddress::getIsDefault, "1");

        return baseMapper.update(null, updateWrapper) > 0;

    }

    /**
     * 重置商家所有退货地址为非默认
     *
     * @param shopId 商家ID
     */
    private Integer resetDefaultAddress(Long shopId) {
        LambdaUpdateWrapper<ShopReturnAddress> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(ShopReturnAddress::getShopId, shopId);
        updateWrapper.set(ShopReturnAddress::getIsDefault, "0");
        return baseMapper.update(null, updateWrapper);
    }
}
