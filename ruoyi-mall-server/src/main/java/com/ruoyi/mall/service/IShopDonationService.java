package com.ruoyi.mall.service;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.vo.ShopDonationRecordVo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商家促销金赠送服务接口
 */
public interface IShopDonationService {

    /**
     * 商家赠送促销金给用户
     *
     * @param shopId   商家ID
     * @param phone    用户手机号
     * @param amount   赠送金
     * @param password 操作密码
     * @param remark   备注
     * @return 操作结果
     */
    boolean donatePromotionGold(Long shopId, String phone, BigDecimal amount, String password, String remark);

    /**
     * 代销赠送促销金给用户
     *
     * @param shopId   商家ID
     * @param phone    用户手机号
     * @param amount   赠送金
     * @param password 操作密码
     * @param remark   备注
     * @return 操作结果
     */
    boolean donatePromotionGoldByDistributor(Long shopId, String phone, BigDecimal amount, String password, String remark);

    /**
     * 获取商家赠送记录列表
     *
     * @param shopId       商家ID
     * @param phone        用户手机号
     * @param donationType 赠送类型
     * @param pageQuery    分页查询参数
     * @return 赠送记录分页列表
     */
    TableDataInfo<ShopDonationRecordVo> getDonationRecords(Date beginTime, Date endTime, Long shopId, String phone, Integer donationType, PageQuery pageQuery);

    /**
     * 获取商家赠送记录列表（用于导出）
     *
     * @param shopId       商家ID
     * @param phone        用户手机号
     * @param donationType 赠送类型
     * @return 赠送记录列表
     */
    List<ShopDonationRecordVo> getDonationRecordsForExport(Date beginTime, Date endTime, Long shopId, String phone, Integer donationType);

}
