package com.ruoyi.mall.service;

/**
 * 账户服务接口
 *
 * <AUTHOR>
 */
public interface IAccountService {


    /**
     * 注销账户
     *
     * @param dto
     * @return void
     * <AUTHOR>
     */

    /*    void cancelAccount(*//*AccountCancelDTO dto*//*);*/


    boolean canUserBeDeleted(Long userId);


    /**
     * 校验账户是否可以注销
     */
    boolean checkAccountCanBeDeleted(Long userId);


    /************************** 注销审核 ***********************************/
//    AccountStatusVO getAccountStatus(Long userId);
//    void saveAccountStatus(AccountStatusDTO dto);
    /**********************************************************************/

}
