package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.mall.domain.entity.OrderItem;

import java.util.List;

/**
 * 订单商品服务接口
 */
public interface IOrderItemService extends IService<OrderItem> {

    /**
     * 获取订单的商品列表
     *
     * @param orderId 订单ID
     * @return 订单商品列表
     */
    List<OrderItem> getOrderItems(Long orderId);

    /**
     * 获取评价商品列表
     *
     * @param type      评价类型
     * @param pageQuery
     * @return
     */
    List<OrderItem> toEvaluate(Long userId, int type, PageQuery pageQuery);


}
