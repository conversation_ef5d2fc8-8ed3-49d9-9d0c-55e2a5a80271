package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.dto.ApplyShopDTO;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.vo.ShopHomeInfoVo;
import com.ruoyi.mall.domain.vo.ShopVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商家信息Service接口
 *
 * <AUTHOR>
 */
public interface IShopService extends IService<Shop> {
    /**
     * 查询商家信息列表
     *
     * @param shop 商家信息
     * @return 商家信息集合
     */
    List<Shop> selectShopList(Shop shop);

    /**
     * 查询商家信息分页列表
     *
     * @param shop      商家信息
     * @param pageQuery 分页参数
     * @return 商家信息分页数据
     */
    TableDataInfo<Shop> selectShopPage(Shop shop, PageQuery pageQuery);

    /**
     * 新增商家信息
     *
     * @param shop 商家信息
     * @return 结果
     */
    boolean insertShop(Shop shop);

    /**
     * 修改商家信息
     *
     * @param shop 商家信息
     * @return 结果
     */
    boolean updateShop(Shop shop);

    /**
     * 修改商家状态
     *
     * @param shop 商家信息
     * @return 结果
     */
    boolean updateShopStatus(Shop shop);

    /**
     * 批量删除商家信息
     *
     * @param ids 需要删除的商家信息ID
     * @return 结果
     */
    boolean deleteShopByIds(Long[] ids);

    /**
     * 删除商家信息
     *
     * @param id 商家信息ID
     * @return 结果
     */
    boolean deleteShopById(Long id);

    /**
     * 申请成为商家
     *
     * @param applyShopDTO 申请信息
     * @return 结果
     */
    boolean applyShop(ApplyShopDTO applyShopDTO, LoginUser loginUser);


    /**
     * 查询首页店铺
     */
    TableDataInfo<ShopVo> selectUserShopList(Long userId, PageQuery pageQuery);

    /**
     * 根据用户ID查询商家ID
     *
     * @param userId 用户ID
     */
    Long getShopIdByUserId(Long userId);

    /**
     * 获取商家的引流次数
     *
     * @param shopId 商家id
     * @return
     */
    Integer getDrainageCount(Long shopId);

    /**
     * 获取店铺首页信息
     *
     * @param shopId 店铺ID
     * @return 店铺首页信息
     */
    ShopHomeInfoVo getShopHomeInfo(Integer shopType, Long shopId);


    /**
     * 根据关键字查询店铺列表
     *
     * @param keyword 关键字
     * @return
     */
    TableDataInfo<ShopVo> selectShopListByKeyWord(Long userId, String keyword, PageQuery pageQuery);

    /**
     * 获取商家平台促销金余额
     *
     * @param shopId 商家ID
     * @return 平台促销金余额
     */
    BigDecimal getShopPromotionBalance(Long shopId);

    /**
     * 扣减商家平台促销金
     *
     * @param shopId 商家ID
     * @param amount 扣减金额
     * @return 是否成功
     */
    boolean deductShopPromotionBalance(Long shopId, BigDecimal amount);

    /**
     * 增加商家平台促销金
     *
     * @param shopId 商家ID
     * @param amount 增加金额
     * @return 是否成功
     */
    boolean increaseShopPromotionBalance(Long shopId, BigDecimal amount);

    /**
     * 获取商家抵扣金余额
     *
     * @param shopId 商家ID
     * @return 抵扣金余额
     * @deprecated 请使用 getShopPromotionBalance 方法
     */
    @Deprecated
    BigDecimal getShopDeductionBalance(Long shopId);

    /**
     * 扣减商家抵扣金
     *
     * @param shopId 商家ID
     * @param amount 扣减金额
     * @return 是否成功
     * @deprecated 请使用 deductShopPromotionBalance 方法
     */
    @Deprecated
    boolean deductShopDeductionBalance(Long shopId, BigDecimal amount);

    /**
     * 增加商家抵扣金
     *
     * @param shopId 商家ID
     * @param amount 增加金额
     * @return 是否成功
     * @deprecated 请使用 increaseShopPromotionBalance 方法
     */
    @Deprecated
    boolean increaseShopDeductionBalance(Long shopId, BigDecimal amount);

    /**
     * 检查商家是否存在
     *
     * @param shopId 商家ID
     * @return 是否存在
     */
    boolean checkShopExists(Long shopId);

    /**
     * 增加商家量化值
     *
     * @param shopId 商家ID
     * @param amount 增加数量
     * @return 是否成功
     */
    boolean increaseQuantificationValue(Long shopId, Double amount);

    /**
     * 减少商家量化值
     *
     * @param shopId 商家ID
     * @param amount 减少数量
     * @return 是否成功
     */
    boolean decreaseQuantificationValue(Long shopId, Double amount);

    /**
     * 增加商家量化
     *
     * @param shopId 商家ID
     * @param amount 增加数量
     * @return 是否成功
     */
    boolean increaseUserFaith(Long shopId, Double amount);

    /**
     * 减少商家量化
     *
     * @param shopId 商家ID
     * @param amount 减少数量
     * @return 是否成功
     */
    boolean decreaseUserFaith(Long shopId, Double amount);

    /**
     * 增加商家平台促销金
     *
     * @param shopId 商家ID
     * @param amount 增加数量
     * @return 是否成功
     */
    boolean increasePlatformPromotionGold(Long shopId, Double amount);

    /**
     * 减少商家平台促销金
     *
     * @param shopId 商家ID
     * @param amount 减少数量
     * @return 是否成功
     */
    boolean decreasePlatformPromotionGold(Long shopId, Double amount);

    /**
     * 增加商家技术引流次数
     *
     * @param shopId 商家ID
     * @param count  增加次数
     * @return 是否成功
     */
    boolean increaseDrainageCount(Long shopId, Integer count);

    void editStatus(Long shopId, String status);

    /**
     * 获取代销商抵扣金余额
     *
     * @param shopId 代销商ID
     * @return 抵扣金余额
     */
    BigDecimal getDistributorDeductionBalance(Long shopId);

    /**
     * 扣减代销商抵扣金
     *
     * @param shopId 代销商ID
     * @param amount 扣减金额
     * @return 是否成功
     */
    boolean deductDistributorDeductionBalance(Long shopId, BigDecimal amount);

    /**
     * 增加代销商抵扣金
     *
     * @param shopId 代销商ID
     * @param amount 增加金额
     * @return 是否成功
     */
    boolean increaseDistributorDeductionBalance(Long shopId, BigDecimal amount);

    String sendForgotPasswordCode(String phone);

    void resetPassword(String phone, String code, String password, String uuid);

}
