package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.mapper.ShopMapper;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.mall.service.IAccountDeletionService;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 账户审核状态后注销 业务接口实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
//@RequiredArgsConstructor
public class AccountDeletionServiceImpl implements IAccountDeletionService {


    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private TzUserMapper tzUserMapper;

    @Resource
    private ShopMapper shopMapper;


    /**
     * 注销账户具体业务操作
     */

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public R<Void> deleteAuditedAccount() {

        log.info("【deleteAuditedAccount】开始执行注销逻辑");

        //  注销账户逻辑处理
        Long userId = LoginHelper.getUserId();
        SysUser sysUser = sysUserMapper.selectById(userId);

        Long tzUserId = sysUser.getTzUserId();

        TzUser tzUser;


        // 4.1 商家 B
        if (/*"1".equals(userType) && */"1".equals(sysUser.getIsShop())) {
            // 4.1 处理B 要删除 tz_user、mall_shop和sys_user
            // 4.1.1 删除用户表tz_user
            LambdaQueryWrapper<TzUser> tzUserQW = new LambdaQueryWrapper<>();
            tzUserQW.eq(TzUser::getUserId, tzUserId)
            /*.eq(TzUser::getUserType, "B")*/;
            tzUser = tzUserMapper.selectOne(tzUserQW);
            if (tzUser != null) {
                int updateTzUser = tzUserMapper.updateTzUserDelFagByUserId(tzUserId, "2");
//                log.error("===============tz_user===========>>>>>>>>>>>>>>>>{}", updateTzUser);
                System.out.println("===============tz_user===========>>>>>>>>>>>>>>>>" + updateTzUser);
            }
            // 4.1.2 删除商家信息
            LambdaQueryWrapper<Shop> shopQW = new LambdaQueryWrapper<>();
            shopQW.eq(Shop::getUserId, userId);
            Shop shop = shopMapper.selectOne(shopQW);
            if (shop != null) {
                int updateShop = shopMapper.updateShopDelFagByUserId(userId, "2");
//                log.error("=============mall_shop=============>>>>>>>>>>>>>>>>{}", updateShop);
                System.out.println("=============mall_shop=============>>>>>>>>>>>>>>>>" + updateShop);
            }
            // 4.1.3 删除用户表sys_user
            LambdaQueryWrapper<SysUser> sysUserQW = new LambdaQueryWrapper<>();
            sysUserQW.eq(SysUser::getUserId, userId)
                /* .eq(SysUser::getUserType, "1")*/
                .eq(SysUser::getIsShop, "1");
            sysUser = sysUserMapper.selectOne(sysUserQW);
            if (sysUser != null) {
                int updateSysUser = sysUserMapper.updateSysUserDelFagByUserId(userId, "2");
//                log.error("=============sys_user=============>>>>>>>>>>>>>>>>{}", updateSysUser);
                System.out.println("=============sys_user=============>>>>>>>>>>>>>>>>" + updateSysUser);
            }

            return R.ok("账号注销成功");
        }

        // 4.2 代销商 CB
        if (/*"2".equals(userType) &&*/ "1".equals(sysUser.getIsConsignment())) {
            // 4.1 处理B 要删除 tz_user、mall_shop和sys_user
            // 4.1.1 删除用户表tz_user
            LambdaQueryWrapper<TzUser> tzUserQW = new LambdaQueryWrapper<>();
            tzUserQW.eq(TzUser::getUserId, tzUserId)
            /*.eq(TzUser::getUserType, "CB")*/;
            tzUser = tzUserMapper.selectOne(tzUserQW);
            if (tzUser != null) {
                int updateTzUser = tzUserMapper.updateTzUserDelFagByUserId(tzUserId, "2");
//                log.error("===============tz_user===========>>>>>>>>>>>>>>>>{}", updateTzUser);
                System.out.println("===============tz_user===========>>>>>>>>>>>>>>>>" + updateTzUser);
            }
            // 4.1.2 删除商家信息
            LambdaQueryWrapper<Shop> shopQW = new LambdaQueryWrapper<>();
            shopQW.eq(Shop::getUserId, userId);
            Shop shop = shopMapper.selectOne(shopQW);
            if (shop != null) {
                int updateShop = shopMapper.updateShopDelFagByUserId(userId, "2");
//                log.error("=============mall_shop=============>>>>>>>>>>>>>>>>{}", updateShop);
                System.out.println("=============mall_shop=============>>>>>>>>>>>>>>>>" + updateShop);
            }
            // 4.1.3 删除用户表sys_user
            LambdaQueryWrapper<SysUser> sysUserQW = new LambdaQueryWrapper<>();
            sysUserQW.eq(SysUser::getUserId, userId)
                /*.eq(SysUser::getUserType, "2")*/
                .eq(SysUser::getIsConsignment, "1");
            sysUser = sysUserMapper.selectOne(sysUserQW);
            if (sysUser != null) {
                int updateSysUser = sysUserMapper.updateSysUserDelFagByUserId(userId, "2");
//                log.error("=============sys_user=============>>>>>>>>>>>>>>>>{}", updateSysUser);
                System.out.println("=============sys_user=============>>>>>>>>>>>>>>>>" + updateSysUser);
            }

            return R.ok("账号注销成功");
        }


        return R.fail("账号注销失败");


    }
}
