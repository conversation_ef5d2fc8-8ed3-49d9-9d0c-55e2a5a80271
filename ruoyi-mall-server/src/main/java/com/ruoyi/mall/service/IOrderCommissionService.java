package com.ruoyi.mall.service;

import com.github.yulichang.base.MPJBaseService;
import com.ruoyi.mall.domain.entity.OrderCommission;

import java.math.BigDecimal;

public interface IOrderCommissionService extends MPJBaseService<OrderCommission> {


    /**
     * 新增结算佣金订单
     *
     * @param orderCommission
     * @return
     */
    Boolean insertOrderCommission(OrderCommission orderCommission);

    /**
     * 修改佣金结算订单
     *
     * @param orderCommission
     * @return
     */
    Integer updateOrderCommission(OrderCommission orderCommission);


    /**
     * 删除用户结算订单
     *
     * @param id
     * @return
     */
    Integer deleteOrderCommission(Long id);

    /**
     * 获取佣金结算详情
     *
     * @param id 订单结算ID
     * @return
     */
    OrderCommission getOrderCommissionById(Long id);


    /**
     * 根据订单给代销商家结算佣金
     *
     * @param orderId       订单ID
     * @param consignmentId 代销商家ID
     * @return
     */
    BigDecimal getCommissionAmount(Long orderId, Long consignmentId);
}
