package com.ruoyi.mall.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PromotionLossCalculator;
import com.ruoyi.mall.domain.entity.ShopDonationRecord;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.vo.ShopDonationRecordVo;
import com.ruoyi.mall.mapper.ShopDonationRecordMapper;
import com.ruoyi.mall.service.IShopDonationService;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.mall.service.ITzUserService;
import com.ruoyi.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商家促销金赠送服务实现类
 */
@Service
@RequiredArgsConstructor
public class ShopDonationServiceImpl implements IShopDonationService {

    private final ShopDonationRecordMapper shopDonationRecordMapper;
    private final ISysUserService sysUserService;
    private final ITzUserService userService;
    private final IShopService shopService;

    /**
     * 商家赠送促销金给用户
     *
     * @param shopId   商家ID
     * @param phone    用户手机号
     * @param amount   赠送金
     * @param password 操作密码
     * @param remark   备注
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean donatePromotionGold(Long shopId, String phone, BigDecimal amount, String password, String remark) {
        // 1. 验证操作密码
        if (!validatePassword(LoginHelper.getUserId(), password)) {
            throw new ServiceException("操作密码错误");
        }

        // 2. 查找用户 - 从TzUser表查询
        LambdaQueryWrapper<TzUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TzUser::getPhone, phone);
        TzUser user = userService.getOne(lqw);
        if (user == null) {
            throw new ServiceException("用户不存在，请检查手机号是否正确");
        }

        // 3. 检查商家促销金余额
        BigDecimal balance = shopService.getShopPromotionBalance(shopId);
        if (balance == null || balance.compareTo(amount) < 0) {
            throw new ServiceException("促销金余额不足");
        }

        // 4. 计算千六损耗
        PromotionLossCalculator.LossCalculationResult lossResult = PromotionLossCalculator.calculateLossDetail(amount);

        // 5. 创建赠送记录
        ShopDonationRecord record = new ShopDonationRecord();
        record.setShopId(shopId);
        record.setUserId(user.getUserId());
        record.setReceiverPhone(phone);
        record.setAmount(amount);
        record.setLossAmount(lossResult.getLossAmount());
        record.setActualAmount(lossResult.getActualAmount());
        record.setStatus("1"); // 成功
        record.setDonationType("1"); // 商家赠送
        record.setDonationTime(new Date());
        record.setRemark(remark);
        record.setDelFlag("0");

        // 6. 扣减商家促销金
        boolean deductResult = shopService.deductShopPromotionBalance(shopId, amount);
        if (!deductResult) {
            throw new ServiceException("扣减商家促销金失败");
        }

        // 7. 增加用户平台补贴金（实际到账金额）
        boolean addResult = increaseUserDeductionBalance(user.getUserId(), lossResult.getActualAmount());
        if (!addResult) {
            throw new ServiceException("增加用户平台补贴金失败");
        }

        // 7. 保存赠送记录
        int rows = shopDonationRecordMapper.insert(record);
        return rows > 0;
    }

    /**
     * 代销赠送促销金给用户
     *
     * @param shopId   商家ID
     * @param phone    用户手机号
     * @param amount   赠送金
     * @param password 操作密码
     * @param remark   备注
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean donatePromotionGoldByDistributor(Long shopId, String phone, BigDecimal amount, String password, String remark) {
        // 1. 验证操作密码
        if (!validatePassword(LoginHelper.getUserId(), password)) {
            throw new ServiceException("操作密码错误");
        }

        // 2. 查找用户 - 从TzUser表查询
        LambdaQueryWrapper<TzUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TzUser::getPhone, phone);
        TzUser user = userService.getOne(lqw);
        if (user == null) {
            throw new ServiceException("用户不存在，请检查手机号是否正确");
        }

        // 3. 检查代销商抵扣金余额
        BigDecimal balance = shopService.getDistributorDeductionBalance(shopId);
        if (balance == null || balance.compareTo(amount) < 0) {
            throw new ServiceException("代销商抵扣金余额不足");
        }

        // 4. 计算千六损耗
        PromotionLossCalculator.LossCalculationResult lossResult = PromotionLossCalculator.calculateLossDetail(amount);

        // 5. 创建赠送记录
        ShopDonationRecord record = new ShopDonationRecord();
        record.setShopId(shopId);
        record.setUserId(user.getUserId());
        record.setReceiverPhone(phone);
        record.setAmount(amount);
        record.setLossAmount(lossResult.getLossAmount());
        record.setActualAmount(lossResult.getActualAmount());
        record.setStatus("1"); // 成功
        record.setDonationType("2"); // 代销赠送
        record.setDonationTime(new Date());
        record.setRemark(remark);
        record.setCreateBy(LoginHelper.getUsername());
        record.setCreateTime(DateUtils.getNowDate());
        record.setUpdateBy(LoginHelper.getUsername());
        record.setUpdateTime(DateUtils.getNowDate());
        record.setDelFlag("0");

        // 5. 扣减代销商抵扣金
        boolean deductResult = shopService.deductDistributorDeductionBalance(shopId, amount);
        if (!deductResult) {
            throw new ServiceException("扣减代销商抵扣金失败");
        }

        // 6. 增加用户平台补贴金（实际到账金额）
        boolean addResult = increaseUserDeductionBalance(user.getUserId(), lossResult.getActualAmount());
        if (!addResult) {
            throw new ServiceException("增加用户平台补贴金失败");
        }

        // 7. 保存赠送记录
        int rows = shopDonationRecordMapper.insert(record);
        return rows > 0;
    }

    /**
     * 获取商家赠送记录列表
     *
     * @param shopId       商家ID
     * @param phone        用户手机号
     * @param donationType 赠送类型
     * @param pageQuery    分页查询参数
     * @return 赠送记录分页列表
     */
    @Override
    public TableDataInfo<ShopDonationRecordVo> getDonationRecords(Date beginTime, Date endTime, Long shopId, String phone, Integer donationType, PageQuery pageQuery) {
        Page<ShopDonationRecordVo> resultPage = shopDonationRecordMapper.selectDonationRecordPage(pageQuery.build(), beginTime, endTime, shopId, phone, donationType);
        return TableDataInfo.build(resultPage);
    }

    /**
     * 获取商家赠送记录列表（用于导出）
     *
     * @param shopId       商家ID
     * @param phone        用户手机号
     * @param donationType 赠送类型
     * @return 赠送记录列表
     */
    @Override
    public List<ShopDonationRecordVo> getDonationRecordsForExport(Date beginTime, Date endTime, Long shopId, String phone, Integer donationType) {
        Page<ShopDonationRecord> page = new Page<>(1, Integer.MAX_VALUE);
        Page<ShopDonationRecordVo> resultPage = shopDonationRecordMapper.selectDonationRecordPage(page, beginTime, endTime, shopId, phone, donationType);
        return resultPage.getRecords();
    }

    /**
     * 验证操作密码
     *
     * @param userId   用户ID
     * @param password 操作密码
     * @return 验证结果
     */
    private boolean validatePassword(Long userId, String password) {
        // 获取用户信息
        SysUser sysUser = sysUserService.getById(userId);
        if (sysUser == null) {
            return false;
        }

        // 验证操作密码是否正确
        return BCrypt.checkpw(password, sysUser.getOperationPassword());
    }

    /**
     * 增加用户平台补贴金
     *
     * @param userId 用户ID
     * @param amount 增加金额
     * @return 操作结果
     */
    private boolean increaseUserDeductionBalance(Long userId, BigDecimal amount) {
        if (userId == null || amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        TzUser user = userService.getById(userId);
        if (user == null) {
            return false;
        }

        // 获取当前平台补贴金余额
        BigDecimal currentBalance = user.getDeductionMoney();
        if (currentBalance == null) {
            currentBalance = BigDecimal.ZERO;
        }

        // 计算新的余额
        BigDecimal newBalance = currentBalance.add(amount);

        // 更新用户平台补贴金余额
        TzUser updateUser = new TzUser();
        updateUser.setUserId(userId);
        updateUser.setDeductionMoney(newBalance);

        return userService.updateById(updateUser);
    }
}
