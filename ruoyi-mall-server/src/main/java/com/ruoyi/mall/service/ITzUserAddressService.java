package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.TzUserAddress;

import java.util.List;

/**
 * 用户收货地址Service接口
 *
 * <AUTHOR>
 */
public interface ITzUserAddressService extends IService<TzUserAddress> {
    /**
     * 查询用户收货地址列表
     *
     * @param tzUserAddress 用户收货地址
     * @return 用户收货地址集合
     */
    List<TzUserAddress> selectTzUserAddressList(TzUserAddress tzUserAddress);

    /**
     * 分页查询用户收货地址列表
     *
     * @param tzUserAddress 用户收货地址
     * @param pageQuery     分页参数
     * @return 用户收货地址分页数据
     */
    TableDataInfo<TzUserAddress> selectTzUserAddressPage(TzUserAddress tzUserAddress, PageQuery pageQuery);

    /**
     * 设置默认收货地址
     *
     * @param addressId 地址ID
     * @return 结果
     */
    int setDefaultAddress(Long addressId);

    /**
     * 获取用户默认地址
     *
     * @param userId 用户ID
     * @return
     */
    TzUserAddress getUserDefaultAddress(Long userId);

}
