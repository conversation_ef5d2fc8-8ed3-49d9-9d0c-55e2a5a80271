package com.ruoyi.mall.service.impl;

import com.ruoyi.mall.domain.vo.UserDeductionDailyStatVO;
import com.ruoyi.mall.mapper.DeductionPaymentRecordMapper;
import com.ruoyi.mall.service.IUserDeductionDailyStatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户平台抵扣金每日统计Service实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDeductionDailyStatServiceImpl implements IUserDeductionDailyStatService {

    private final DeductionPaymentRecordMapper deductionPaymentRecordMapper;

    @Override
    public List<UserDeductionDailyStatVO> getYesterdayDeductionStat() {
        // 计算昨天的日期范围
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime startOfYesterday = yesterday.atStartOfDay();
        LocalDateTime endOfYesterday = yesterday.atTime(23, 59, 59);

        // 格式化昨天日期为字符串（YYYY-MM-DD）
        String yesterdayStr = yesterday.format(DateTimeFormatter.ISO_LOCAL_DATE);

        // 查询昨天的抵扣金使用统计（1表示平台用户抵扣金）
        List<Map<String, Object>> statList = deductionPaymentRecordMapper.selectUserDeductionDailyStat(
            startOfYesterday, endOfYesterday, 1);

        List<UserDeductionDailyStatVO> resultList = new ArrayList<>();

        // 转换为VO对象
        for (Map<String, Object> stat : statList) {
            UserDeductionDailyStatVO vo = new UserDeductionDailyStatVO();

            // 设置更新日期（昨天）
            vo.setUpdateDate(yesterdayStr);

            // 设置用户手机号
            vo.setPhone(String.valueOf(stat.get("phone")));

            // 设置昨日使用的抵扣金
            BigDecimal dailyUsed = (BigDecimal) stat.get("daily_used");
            vo.setWriteOffSubsidy(dailyUsed.toString());

            // 设置累计使用的抵扣金
            BigDecimal totalUsed = (BigDecimal) stat.get("total_used");
            vo.setWriteOffSubsidyTotal(totalUsed.toString());

            // 设置未使用的抵扣金（剩余）
            BigDecimal remainDeduction = (BigDecimal) stat.get("remain_deduction");
            vo.setUnWriteOffSubsidy(remainDeduction.toString());

            resultList.add(vo);
        }

        return resultList;
    }
}
