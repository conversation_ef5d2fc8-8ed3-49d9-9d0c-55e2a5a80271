package com.ruoyi.mall.service;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.vo.ShopPromotionReqVO;
import com.ruoyi.mall.domain.vo.ShopPromotionRespVO;
import com.ruoyi.mall.domain.vo.ShopPromotionVO;

import java.util.List;

/**
 * 商家平台促销金Service接口
 */
public interface IUserPromotionService {


    /**
     * 查询商家促销金列表
     */
    TableDataInfo<ShopPromotionVO> queryUserPromotionList(ShopPromotionVO shopPromotion, PageQuery pageQuery);


    /**
     * 查询每个商家平台促销金明细列表 (分页)
     */
    TableDataInfo<ShopPromotionRespVO> pageUserPromotionDetail(ShopPromotionReqVO shopPromotion, PageQuery pageQuery);


    /**
     * 查询每个商家平台促销金明细列表 （不分页）
     */
    List<ShopPromotionRespVO> listUserPromotionDetail(ShopPromotionReqVO shopPromotion);


}
