package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.bo.ReceiveQuantizedUserFaithBo;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.mapper.ShopMapper;
import com.ruoyi.mall.service.IShopService;
import com.ruoyi.mall.service.IShopValuePushService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 外部系统推送店铺量化值与量化服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopValuePushServiceImpl implements IShopValuePushService {

    private final ShopMapper shopMapper;
    private final IShopService shopService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Boolean> handleBatchShopValuePush(List<ReceiveQuantizedUserFaithBo> shopValuePushDTOList) {
        if (shopValuePushDTOList == null || shopValuePushDTOList.isEmpty()) {
            throw new ServiceException("批量推送数据不能为空");
        }

        Map<String, Boolean> resultMap = new HashMap<>(shopValuePushDTOList.size());

        // 处理每一条数据
        for (ReceiveQuantizedUserFaithBo dto : shopValuePushDTOList) {
            try {
                boolean success = handleShopValuePush(dto);
                resultMap.put(dto.getPhone(), success);
            } catch (Exception e) {
                log.error("处理店铺量化值和量化推送失败，手机号：{}，错误：{}", dto.getPhone(), e.getMessage(), e);
                resultMap.put(dto.getPhone(), false);
                // 捕获异常继续处理下一条，不影响整体处理
            }
        }

        log.info("批量处理外部推送的店铺量化值和量化完成，总数：{}，成功：{}，失败：{}",
            shopValuePushDTOList.size(),
            resultMap.values().stream().filter(Boolean::booleanValue).count(),
            resultMap.values().stream().filter(value -> !value).count());

        return resultMap;
    }

    @Override
    public boolean handleShopValuePush(ReceiveQuantizedUserFaithBo receiveQuantizedUserFaithBo) {
        // 1. 参数校验
        if (receiveQuantizedUserFaithBo == null || StringUtils.isEmpty(receiveQuantizedUserFaithBo.getPhone())
            || receiveQuantizedUserFaithBo.getQuantificationValue() == null) {
            throw new ServiceException("参数不完整，推送失败");
        }

        if (receiveQuantizedUserFaithBo.getQuantificationValue() < 0) {
            throw new ServiceException("量化值或量化不能为负数");
        }

        // 2. 根据手机号查询商家信息
        LambdaQueryWrapper<Shop> lqw = new LambdaQueryWrapper<>();
        lqw.eq(Shop::getPhone, receiveQuantizedUserFaithBo.getPhone())
            .eq(Shop::getDelFlag, "0"); // 未删除的商家

        Shop shop = shopMapper.selectOne(lqw);
        if (shop == null) {
            throw new ServiceException("未找到该手机号对应的商家");
        }

        // 3. 获取商家原有的量化值和量化
        Double oldQuantificationValue = shop.getQuantificationValue() != null ? shop.getQuantificationValue() : 0.0;
        Double oldAltogetherQuantificationValue = shop.getAltogetherQuantificationValue() != null ? shop.getAltogetherQuantificationValue() : 0.0;

        // 4. 设置新的量化值和量化
        shop.setQuantificationValue(oldQuantificationValue + receiveQuantizedUserFaithBo.getQuantificationValue());

        // 5. 更新累计量化值和累计量化
        shop.setAltogetherQuantificationValue(oldAltogetherQuantificationValue + receiveQuantizedUserFaithBo.getQuantificationValue());

        // 6. 更新商家信息
        boolean result = shopService.updateById(shop);
        if (result) {
            log.info("外部系统推送的量化值和量化已处理，商家ID：{}，手机号：{}，量化值：{}",
                shop.getId(), receiveQuantizedUserFaithBo.getPhone(), receiveQuantizedUserFaithBo.getQuantificationValue());
        } else {
            log.error("外部系统推送的量化值和量化处理失败，商家ID：{}，手机号：{}", shop.getId(), receiveQuantizedUserFaithBo.getPhone());
            throw new ServiceException("更新商家信息失败");
        }

        return result;
    }
}
