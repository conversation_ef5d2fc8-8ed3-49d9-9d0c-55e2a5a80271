package com.ruoyi.mall.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.dto.AccountAuditDTO;
import com.ruoyi.mall.domain.entity.UserAccountAudit;
import com.ruoyi.mall.mapper.AccountAuditMapper;
import com.ruoyi.mall.mapper.ShopMapper;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.mall.service.IAccountAuditService;
import com.ruoyi.mall.service.IAccountDeletionService;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;


/**
 * 账户服务接口实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AccountAuditServiceImpl extends ServiceImpl<AccountAuditMapper, UserAccountAudit> implements IAccountAuditService {


    private final AccountAuditMapper accountAuditMapper;
    private final SysUserMapper sysUserMapper;
    private final TzUserMapper tzUserMapper;
    private final ShopMapper shopMapper;
    @Resource
    IAccountDeletionService accountDeletionService;

    @Override
    public TableDataInfo<UserAccountAudit> selectAccountRecordPage(UserAccountAudit userAccountAudit, PageQuery pageQuery) {
        LambdaQueryWrapper<UserAccountAudit> lqw = new LambdaQueryWrapper<>();
        lqw.like(StringUtils.isNotBlank(userAccountAudit.getUsername()), UserAccountAudit::getUsername, userAccountAudit.getUsername());
        lqw.like(StringUtils.isNotBlank(userAccountAudit.getPhone()), UserAccountAudit::getPhone, userAccountAudit.getPhone());
//        lqw.eq(StringUtils.isNotBlank(userAccountAudit.getAuditStatus()), UserAccountAudit::getAuditStatus, userAccountAudit.getAuditStatus());
        lqw.eq(UserAccountAudit::getAuditStatus, "1");
        lqw.orderByDesc(UserAccountAudit::getCreateTime);
        Page<UserAccountAudit> page = page(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    public TableDataInfo<UserAccountAudit> selectAccountAuditPage(UserAccountAudit userAccountAudit, PageQuery pageQuery) {
        LambdaQueryWrapper<UserAccountAudit> lqw = new LambdaQueryWrapper<>();
        lqw.like(StringUtils.isNotBlank(userAccountAudit.getUsername()), UserAccountAudit::getUsername, userAccountAudit.getUsername());
        lqw.like(StringUtils.isNotBlank(userAccountAudit.getPhone()), UserAccountAudit::getPhone, userAccountAudit.getPhone());
        lqw.eq(StringUtils.isNotBlank(userAccountAudit.getAuditStatus()), UserAccountAudit::getAuditStatus, userAccountAudit.getAuditStatus());
//        lqw.in(UserAccountAudit::getAuditStatus, "0","2");
        lqw.ne(UserAccountAudit::getAuditStatus, "1");
        lqw.orderByDesc(UserAccountAudit::getCreateTime);
        Page<UserAccountAudit> page = page(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 审核账号注销申请
     *
     * @param accountAuditDTO 审核信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditAccount(AccountAuditDTO accountAuditDTO) {


        // 获取审核记录
        UserAccountAudit userAccountAudit = getById(accountAuditDTO.getId());

        if (userAccountAudit == null) {
            throw new ServiceException("审核记录不存在");
        }

        if (!"0".equals(userAccountAudit.getAuditStatus())) {
            throw new ServiceException("该申请已审核");
        }


        // 新增账注销
        String auditStatus = accountAuditDTO.getAuditStatus();
        if ("1".equals(auditStatus)) {
            try {
                log.info("-------------调用【deleteAuditedAccount】操作开始-----------------------");
                accountDeletionService.deleteAuditedAccount();
                log.info("-------------调用【deleteAuditedAccount】操作结束-----------------------");
            } catch (Exception e) {
                log.error("=============================>>>>>>>>>>>>>>>>删除账户失败，异常信息：", e);
            }
        }


        // 更新审核状态
        userAccountAudit.setAuditStatus(accountAuditDTO.getAuditStatus());
        userAccountAudit.setAuditRemark(accountAuditDTO.getAuditRemark());
        userAccountAudit.setAuditTime(new Date());

        boolean result = updateById(userAccountAudit);


        return result;

    }


}
