package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.helper.LoginHelper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.mall.domain.dto.UserRealNameAuditDTO;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.entity.TzUserRealName;
import com.ruoyi.mall.domain.vo.UserRealNameVO;
import com.ruoyi.mall.enums.RealNameStatusEnum;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.mall.mapper.TzUserRealNameMapper;
import com.ruoyi.mall.service.ITzUserRealNameService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
public class TzUserRealNameServiceImpl extends ServiceImpl<TzUserRealNameMapper, TzUserRealName> implements ITzUserRealNameService {

    private final TzUserRealNameMapper tzUserRealNameMapper;
    private final TzUserMapper userMapper;

    @Override
    public TableDataInfo<TzUserRealName> getUserRealNameList(TzUserRealName tzUserRealName, PageQuery pageQuery) {
        LambdaQueryWrapper<TzUserRealName> lqw = new LambdaQueryWrapper<>();
        lqw
            .like(TzUserRealName::getRealName, tzUserRealName.getRealName())
            .like(TzUserRealName::getIdCard, tzUserRealName.getIdCard())
            .like(TzUserRealName::getStatus, tzUserRealName.getStatus());

        Page<TzUserRealName> page = tzUserRealNameMapper.selectPage(pageQuery.build(), lqw);

        return TableDataInfo.build(page);
    }

    @Override
    public Boolean editUserRealName(TzUserRealName tzUserRealName) {

        LambdaUpdateWrapper<TzUserRealName> lqw = new LambdaUpdateWrapper<>();
        lqw.set(TzUserRealName::getRealName, tzUserRealName.getRealName())
            .set(TzUserRealName::getIdCard, tzUserRealName.getIdCard())
            .set(TzUserRealName::getIdCardBack, tzUserRealName.getIdCardBack())
            .set(TzUserRealName::getIdCardFront, tzUserRealName.getIdCardFront())
            .set(TzUserRealName::getIdCardHand, tzUserRealName.getIdCardHand())
            .set(TzUserRealName::getCardType, tzUserRealName.getCardType())
            .set(TzUserRealName::getProvinceCode, tzUserRealName.getProvinceCode())
            .set(TzUserRealName::getProvince, tzUserRealName.getProvince())
            .set(TzUserRealName::getCity, tzUserRealName.getCity())
            .set(TzUserRealName::getCityCode, tzUserRealName.getCityCode())
            .set(TzUserRealName::getDistrict, tzUserRealName.getDistrict())
            .set(TzUserRealName::getDistrictCode, tzUserRealName.getDistrictCode())
            .set(TzUserRealName::getTown, tzUserRealName.getTown())
            .set(TzUserRealName::getTownCode, tzUserRealName.getTownCode())
            .set(TzUserRealName::getAddress, tzUserRealName.getAddress())
            .set(TzUserRealName::getStatus, "0")
            .eq(TzUserRealName::getUserId, tzUserRealName.getUserId());

        return update(lqw);
    }

    @Override
    public Integer deleteUserRealName(Long id) {
        return tzUserRealNameMapper.deleteById(id);
    }

    @Override
    public Boolean addUserRealName(TzUserRealName tzUserRealName) {

        //判断身份证是否被实名过
        MPJLambdaWrapper<TzUserRealName> lqw = new MPJLambdaWrapper<>();
        lqw
            .leftJoin(TzUser.class, TzUser::getUserId, TzUserRealName::getUserId)
            .eq(TzUserRealName::getIdCard, tzUserRealName.getIdCard())
            .eq(TzUser::getUserType,"C");
        if (getOne(lqw) != null) {
            throw new ServiceException("该身份证已被实名");
        }
        tzUserRealName.setStatus("0");
        tzUserRealName.setDelFlag("0");
        return save(tzUserRealName);
    }

    @Override
    public TzUserRealName getUserRealNameById(Long userId) {

        LambdaQueryWrapper<TzUserRealName> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TzUserRealName::getUserId, userId)
            .eq(TzUserRealName::getDelFlag, "0");

        return getOne(lqw);
    }

    @Override
    public TableDataInfo<UserRealNameVO> getAdminUserRealNameList(TzUserRealName tzUserRealName, PageQuery pageQuery) {
        LambdaQueryWrapper<TzUserRealName> lqw = new LambdaQueryWrapper<>();

        // 构建查询条件
        if (StringUtils.isNotBlank(tzUserRealName.getRealName())) {
            lqw.like(TzUserRealName::getRealName, tzUserRealName.getRealName());
        }
        if (StringUtils.isNotBlank(tzUserRealName.getIdCard())) {
            lqw.like(TzUserRealName::getIdCard, tzUserRealName.getIdCard());
        }
        if (StringUtils.isNotBlank(tzUserRealName.getStatus())) {
            lqw.eq(TzUserRealName::getStatus, tzUserRealName.getStatus());
        }

        // 按创建时间倒序排列
        lqw.orderByDesc(TzUserRealName::getCreateTime);

        Page<TzUserRealName> page = tzUserRealNameMapper.selectPage(pageQuery.build(), lqw);

        // 转换为VO对象
        List<UserRealNameVO> voList = page.getRecords().stream().map(this::convertToVO).collect(Collectors.toList());

        TableDataInfo<UserRealNameVO> result = new TableDataInfo<>();
        result.setRows(voList);
        result.setTotal(page.getTotal());

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adminAuditUserRealName(UserRealNameAuditDTO auditDTO) {
        // 获取当前登录用户ID
        Long auditorId = LoginHelper.getUserId();
        String username = LoginHelper.getUsername();

        // 更新审核信息
        LambdaUpdateWrapper<TzUserRealName> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(TzUserRealName::getStatus, auditDTO.getStatus())
            .set(TzUserRealName::getReason, auditDTO.getReason())
            .set(TzUserRealName::getAuditorId, auditorId)
            .set(TzUserRealName::getAuditorName, username)
            .set(TzUserRealName::getAuditTime, new Date())
            .eq(TzUserRealName::getId, auditDTO.getId());

        boolean updateResult = update(updateWrapper);
        if (!updateResult) {
            throw new ServiceException("更新实名信息失败");
        }

        // 如果审核通过，更新用户表的实名状态
        if (RealNameStatusEnum.APPROVED.getCode().equals(auditDTO.getStatus())) {
            TzUserRealName userRealName = getById(auditDTO.getId());
            if (userRealName != null) {
                LambdaUpdateWrapper<TzUser> userUpdateWrapper = new LambdaUpdateWrapper<>();
                userUpdateWrapper
                    .set(TzUser::getIsRealName, "1")
                    .set(TzUser::getUsername, userRealName.getRealName())
                    .set(TzUser::getProvinceCode, userRealName.getProvinceCode())
                    .set(TzUser::getProvince, userRealName.getProvince())
                    .set(TzUser::getCityCode, userRealName.getCityCode())
                    .set(TzUser::getCity, userRealName.getCity())
                    .set(TzUser::getDistrictCode, userRealName.getDistrictCode())
                    .set(TzUser::getDistrict, userRealName.getDistrict())
                    .set(TzUser::getTownCode, userRealName.getTownCode())
                    .set(TzUser::getTown, userRealName.getTown())
                    .set(TzUser::getAddress, userRealName.getAddress())
                    .set(TzUser::getIdCard, userRealName.getIdCard())
                    .set(TzUser::getIdCardBack, userRealName.getIdCardBack())
                    .set(TzUser::getIdCardFront, userRealName.getIdCardFront())
                    .set(TzUser::getIdCardHand, userRealName.getIdCardHand())
                    .set(TzUser::getCardType, userRealName.getCardType())
                    .eq(TzUser::getUserId, userRealName.getUserId());

                int flag = userMapper.update(userUpdateWrapper);
                if (flag <= 0) {
                    throw new ServiceException("更新用户信息失败");
                }
            }
        }
    }

    @Override
    public UserRealNameVO getUserRealNameDetail(Long id) {
        TzUserRealName userRealName = getById(id);
        if (userRealName == null) {
            throw new ServiceException("实名认证记录不存在");
        }

        return convertToVO(userRealName);
    }

    /**
     * 转换为VO对象
     */
    private UserRealNameVO convertToVO(TzUserRealName userRealName) {
        UserRealNameVO vo = new UserRealNameVO();
        BeanUtils.copyProperties(userRealName, vo);

        // 设置状态描述
        RealNameStatusEnum statusEnum = RealNameStatusEnum.getByCode(userRealName.getStatus());
        if (statusEnum != null) {
            vo.setStatusText(statusEnum.getInfo());
        }

        // 查询用户信息
        if (userRealName.getUserId() != null) {
            TzUser user = userMapper.selectById(userRealName.getUserId());
            if (user != null) {
                vo.setUsername(user.getUsername());
                vo.setNickname(user.getNickname());
                vo.setPhone(user.getPhone());
            }
        }
        return vo;
    }
}
