package com.ruoyi.mall.service;

import com.github.yulichang.base.MPJBaseService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.entity.TZUserFocus;

import java.util.List;

public interface ITZUserFocusService extends MPJBaseService<TZUserFocus> {

    /**
     * 新增用户关注
     *
     * @param shopId 商家ID
     * @param status 关注状态
     * @return
     */
    Integer saveUserFocus(Long shopId, Long userId, String status);

    /**
     * 获取用户关注店铺
     *
     * @param userId    用户ID
     * @param pageQuery 分页参数
     * @return
     */
    List<TZUserFocus> getUserFocusList(Long userId, PageQuery pageQuery);

    /**
     * 获取用户关注店铺分页
     *
     * @return
     */
    TableDataInfo<TZUserFocus> getUserFocusPage(TZUserFocus focus, PageQuery pageQuery);

    /**
     * 代销商家取消用户关注
     *
     * @return 操作结果
     */
    boolean cancelUserFocus(Long focusId);

    /**
     * 获取用户是否关注
     *
     * @return
     */
    String getUserIsFocus(Long shopId, Long userId);
}
