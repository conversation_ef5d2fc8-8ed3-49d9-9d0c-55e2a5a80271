package com.ruoyi.mall.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.entity.UserFreezeRecord;
import com.ruoyi.mall.mapper.ShopMapper;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.mall.mapper.UserFreezeRecordMapper;
import com.ruoyi.mall.service.IUserFreezeRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 店铺支付服务实现
 */
@Service
@RequiredArgsConstructor
public class UserFreezeRecordServiceImpl extends ServiceImpl<UserFreezeRecordMapper, UserFreezeRecord> implements IUserFreezeRecordService {

    private final ShopMapper shopMapper;

    private final UserFreezeRecordMapper userFreezeRecordMapper;

    private final TzUserMapper tzUserMapper;

    @Override
    public Map<String, Object> getUserByPhone(String phone, Integer type) {
        if (type == 2) {
            Shop shop = shopMapper.selectOne(
                new LambdaQueryWrapper<Shop>().eq(Shop::getPhone, phone).eq(Shop::getStatus, 0)
            );
            if (shop == null) {
                throw new ServiceException("未找到用户信息");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("id", shop.getId());
            result.put("name", shop.getBusinessName());
            result.put("phone", shop.getPhone());
            result.put("balance", shop.getPlatformPromotionGold());
            return result;
        }
        TzUser tzUser = tzUserMapper.selectOne(
            new LambdaQueryWrapper<TzUser>().eq(TzUser::getPhone, phone).eq(TzUser::getStatus, 0)
        );
        if (tzUser == null) {
            throw new ServiceException("未找到用户信息");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("id", tzUser.getUserId());
        result.put("name", tzUser.getUsername());
        result.put("phone", tzUser.getPhone());
        result.put("balance", tzUser.getDeductionMoney());
        return result;
    }

    @Override
    public TableDataInfo<UserFreezeRecord> getFreezeRecords(PageQuery pageQuery, String name, String phone) {
        Page<UserFreezeRecord> shopFreezeRecordPage = userFreezeRecordMapper.selectPage(
            pageQuery.build(),
            new LambdaQueryWrapper<UserFreezeRecord>()
                .like(StrUtil.isNotEmpty(name), UserFreezeRecord::getUserName, name)
                .like(StrUtil.isNotEmpty(phone), UserFreezeRecord::getPhone, phone)
                .orderByDesc(UserFreezeRecord::getCreateTime)
        );
        return TableDataInfo.build(shopFreezeRecordPage);
    }

    @Override
    @Transactional
    public Integer freezeAmount(Long userId, BigDecimal amount, String reason, Integer type) {
        String userName;
        String phone;
        if (type == 2) {
            Shop shop = shopMapper.selectById(userId);
            if (shop == null) {
                throw new ServiceException("未找到用户信息");
            }
            userName = shop.getBusinessName();
            phone = shop.getPhone();
            BigDecimal subtract = shop.getPlatformPromotionGold().subtract(amount);
            if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("用户余额不足");
            }
            shop.setPlatformPromotionGold(subtract);
            shopMapper.updateById(shop);
        } else {
            TzUser tzUser = tzUserMapper.selectById(userId);
            if (tzUser == null) {
                throw new ServiceException("未找到用户信息");
            }
            userName = tzUser.getUsername();
            phone = tzUser.getPhone();
            BigDecimal subtract = tzUser.getDeductionMoney().subtract(amount);
            if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("用户余额不足");
            }
            tzUser.setDeductionMoney(subtract);
            tzUserMapper.updateById(tzUser);
        }

        // 创建扣款记录
        UserFreezeRecord freezeRecord = new UserFreezeRecord();
        freezeRecord.setUserId(userId);
        freezeRecord.setUserName(userName);
        freezeRecord.setPhone(phone);
        freezeRecord.setAmount(amount);
        freezeRecord.setReason(reason);

        return userFreezeRecordMapper.insert(freezeRecord);
    }
}
