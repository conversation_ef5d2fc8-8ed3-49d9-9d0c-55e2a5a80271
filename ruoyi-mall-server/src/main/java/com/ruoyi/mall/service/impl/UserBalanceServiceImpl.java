package com.ruoyi.mall.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.mapper.TzUserMapper;
import com.ruoyi.pay.service.IUserBalanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 用户余额服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserBalanceServiceImpl implements IUserBalanceService {

    private final TzUserMapper tzUserMapper;

    @Override
    public BigDecimal getUserDeductionBalance(Long userId) {
        TzUser user = tzUserMapper.selectById(userId);
        if (user == null) {
            return BigDecimal.ZERO;
        }
        return user.getDeductionMoney() != null ? user.getDeductionMoney() : BigDecimal.ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deductUserBalance(Long userId, BigDecimal amount) {
        TzUser user = tzUserMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        BigDecimal currentBalance = user.getDeductionMoney() != null ? user.getDeductionMoney() : BigDecimal.ZERO;
        if (currentBalance.compareTo(amount) < 0) {
            return false;
        }

        user.setDeductionMoney(currentBalance.subtract(amount));
        return tzUserMapper.updateById(user) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deductUserBalanceWithFee(Long userId, BigDecimal amount, BigDecimal feeAmount) {
        TzUser user = tzUserMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        BigDecimal currentBalance = user.getDeductionMoney() != null ? user.getDeductionMoney() : BigDecimal.ZERO;
        BigDecimal totalDeduction = amount.add(feeAmount);

        if (currentBalance.compareTo(totalDeduction) < 0) {
            return false;
        }

        user.setDeductionMoney(currentBalance.subtract(totalDeduction));
        return tzUserMapper.updateById(user) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean increaseUserBalance(Long userId, BigDecimal amount) {
        TzUser user = tzUserMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        BigDecimal currentBalance = user.getDeductionMoney() != null ? user.getDeductionMoney() : BigDecimal.ZERO;
        user.setDeductionMoney(currentBalance.add(amount));
        return tzUserMapper.updateById(user) > 0;
    }

    @Override
    public boolean checkUserExists(Long userId) {
        return tzUserMapper.exists(Wrappers.<TzUser>lambdaQuery().eq(TzUser::getUserId, userId));
    }
}
