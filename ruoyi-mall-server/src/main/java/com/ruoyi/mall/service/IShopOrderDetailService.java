package com.ruoyi.mall.service;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.query.ShopOrderDetailDTO;
import com.ruoyi.mall.domain.vo.ShopOrderDetailVO;

import java.util.List;

/**
 * 商品订单服务接口
 */
public interface IShopOrderDetailService {

    TableDataInfo<ShopOrderDetailVO> getMallShopOrderDetails(ShopOrderDetailDTO query, PageQuery pageQuery);

    List<ShopOrderDetailVO> exportFundDetails(ShopOrderDetailDTO dto);


}
