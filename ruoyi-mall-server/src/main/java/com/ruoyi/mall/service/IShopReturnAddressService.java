package com.ruoyi.mall.service;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.bo.ShopReturnAddressBo;
import com.ruoyi.mall.domain.entity.ShopReturnAddress;

import java.util.Collection;
import java.util.List;

/**
 * 商家退货地址Service接口
 */
public interface IShopReturnAddressService {

    /**
     * 查询商家退货地址
     */
    ShopReturnAddress queryById(Long id);

    /**
     * 查询商家退货地址列表
     */
    TableDataInfo<ShopReturnAddress> queryPageList(ShopReturnAddressBo bo, PageQuery pageQuery);

    /**
     * 查询商家退货地址列表
     */
    List<ShopReturnAddress> queryList(ShopReturnAddressBo bo);

    /**
     * 新增商家退货地址
     */
    Boolean insertByBo(ShopReturnAddressBo bo);

    /**
     * 修改商家退货地址
     */
    Boolean updateByBo(ShopReturnAddressBo bo);

    /**
     * 校验并批量删除商家退货地址信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean setDefault(Long id, Long shopId);

}
