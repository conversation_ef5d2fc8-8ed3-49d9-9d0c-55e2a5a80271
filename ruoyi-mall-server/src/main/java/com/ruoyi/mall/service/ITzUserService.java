package com.ruoyi.mall.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.mall.domain.bo.RelationChainBo;
import com.ruoyi.mall.domain.dto.*;
import com.ruoyi.mall.domain.entity.ShopAudit;
import com.ruoyi.mall.domain.entity.TzUser;
import com.ruoyi.mall.domain.vo.LoginVO;
import com.ruoyi.mall.domain.vo.TzUserInfo;
import com.ruoyi.sms.enums.SmsType;

import java.util.List;
import java.util.Map;

/**
 * 商城用户Service接口
 */
public interface ITzUserService extends IService<TzUser> {
    /**
     * 查询商城用户
     *
     * @param userId 商城用户主键
     * @return 商城用户
     */
    TzUser selectTzUserByUserId(Long userId);

    /**
     * 查询商城用户列表
     *
     * @param tzUser 商城用户
     * @return 商城用户集合
     */
    List<TzUser> selectTzUserList(TzUser tzUser);

    /**
     * 分页查询商城用户列表
     *
     * @param tzUser    商城用户
     * @param pageQuery 分页参数
     * @return 商城用户分页数据
     */
    TableDataInfo<TzUser> selectTzUserPage(TzUser tzUser, PageQuery pageQuery);

    /**
     * 新增商城用户
     *
     * @param tzUser 商城用户
     * @return 结果
     */
    int insertTzUser(TzUser tzUser);

    /**
     * 修改商城用户
     *
     * @param tzUser 商城用户
     * @return 结果
     */
    int updateTzUser(TzUser tzUser);

    /**
     * 批量删除商城用户
     *
     * @param userIds 需要删除的商城用户主键集合
     * @return 结果
     */
    int deleteTzUserByUserIds(Long[] userIds);

    /**
     * 删除商城用户信息
     *
     * @param userId 商城用户主键
     * @return 结果
     */
    int deleteTzUserByUserId(Long userId);

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    int resetUserPassword(TzUser user);

    /**
     * 发送登录验证码
     *
     * @param phone 手机号
     */
    String sendLoginCode(String phone);

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    LoginVO login(LoginDTO loginDTO);

    /**
     * 发送注册验证码
     *
     * @param phone 手机号
     */
    String sendRegisterCode(SmsType smsType, String phone);

    /**
     * 用户注册
     *
     * @param registerDTO 注册信息
     * @return 注册结果
     */
    LoginVO register(RegisterDTO registerDTO);

    /**
     * 生成一个邀请码
     */
    String getInvitationCode();

    /**
     * 获取关系链
     */
    List<RelationChainBo> getRelationChain(Integer dailyThreshold, Integer sum, Double dailyThresholdReward);


    /**
     * 统计用户推荐用户成为商家数量
     *
     * @param userId 用户id
     * @return
     */
    Map<String, Object> countMonthlyRecommendedShop(Long userId);

    /**
     * 获取当面登录用户信息
     *
     * @return
     */
    TzUserInfo getLonginUserInfo(Long userId);

    /**
     * 用户忘记密码重新设置密码
     *
     * @param forgetPasswordDTO 参数
     * @return
     */
    Integer forgetPassword(ForgetPasswordDTO forgetPasswordDTO);

    /**
     * 检查手机号是否已经注册
     *
     * @param phone 电话号码
     * @return
     */
    TzUser checkPhoneIsExist(String phone);

    /**
     * 接收单个平台兑换金
     *
     * @param exchangeFundDTO 兑换金请求
     * @return 处理结果
     */
    boolean receiveExchangeFund(ExchangeFundDTO exchangeFundDTO);

    /**
     * 批量接收平台兑换金
     *
     * @param batchDTO 批量兑换金请求
     * @return 处理结果
     */
    boolean batchReceiveExchangeFund(BatchExchangeFundDTO batchDTO);

    /**
     * 判断用户是否能申请成功商家
     *
     * @param userId 用户ID
     * @return
     */
    Long checkUserIsChangeShop(Long userId);

    /**
     * 获取用户商家审核信息
     *
     * @return
     */
    ShopAudit getShopAudit(Long userId);

    /**
     * 用户实名认证
     *
     * @param userRealNameDTO
     * @returnR
     */
    Integer UserRealName(UserRealNameDTO userRealNameDTO);

    /**
     * 验证是否是本人申请注销账号
     *
     * @return
     */
    boolean checkIsMy(ApplyLogOutDTO applyLogOutDTO);

    /**
     * 邀请用户注册
     *
     * @param inviteUserRegisterDTO
     * @return
     */
    boolean inviteUserRegister(InviteUserRegisterDTO inviteUserRegisterDTO);

    /**
     * 设置支付密码
     *
     * @param setPayPasswordDTO
     * @return
     */
    Integer setPayPassword(SetPayPasswordDTO setPayPasswordDTO);

    /**
     * 验证支付密码是否正确
     *
     * @param userId
     * @param payPassword
     * @return
     */
    Integer verifyIsMyShelf(Long userId, String payPassword);

    /**
     * 验证用户是否设置支付密码
     *
     * @param userId
     * @return
     */
    Integer checkIsSetPayPassword(Long userId);
}
