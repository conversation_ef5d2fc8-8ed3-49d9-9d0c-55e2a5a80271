package com.ruoyi.mall.domain.bo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 虚拟订单业务对象
 */
@Data
public class VirtualOrderBo {

    /**
     * 用户ID（由系统自动设置，前端无需传入）
     */
    private Long userId;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 用户名称（由系统自动设置，前端无需传入）
     */
    private String userName;

    /**
     * 商品数量 暂时都是一
     */
    private Integer quantity;

    /**
     * 商品类型
     */
    private String productType;

    /**
     * 相关的业务ID（商品类型为6时，为广告ID）
     */
    private Long orderId;

    /**
     * 订单类型（1：商家的，2：用户的）用户暂时只有开通代销
     * 默认为1
     */
    private String orderType;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 支付金额（包含手续费）
     */
    private BigDecimal amount;

    /**
     * 手续费金额（千分之六）
     */
    private BigDecimal serviceFee;

    /**
     * 有效期（天）都是30 ，广告为7天
     */
    private Integer validDays;

    /**
     * 订单备注
     */
    private String remark;
}
