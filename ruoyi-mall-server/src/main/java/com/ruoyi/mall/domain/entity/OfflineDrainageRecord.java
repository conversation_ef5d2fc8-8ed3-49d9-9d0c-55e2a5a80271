package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 线下充值技术引流次数记录表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_offline_drainage_record")
public class OfflineDrainageRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 联系电话
     */
    private String shopPhone;

    /**
     * 商家名称
     */
    private String shopName;

    /**
     * 充值数量
     */
    private Integer count;

    /**
     * 技术引流单次费用
     */
    private Double citationValue;

    /**
     * 操作技术引流费用
     */
    private BigDecimal citationValueMoney;

    /**
     * 手续费
     */
    private BigDecimal serviceFee;

    /**
     * 操作前技术引流次数
     */
    private Integer beforeCount;

    /**
     * 操作后技术引流次数
     */
    private Integer afterCount;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态（0成功 1失败）
     */
    private String status;

    /**
     * 开始创建时间
     */
    @TableField(exist = false)
    private String beginCreateTime;

    /**
     * 结束创建时间
     */
    @TableField(exist = false)
    private String endCreateTime;

}
