package com.ruoyi.mall.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ShopHomeInfoVo {

    /**
     * 今日交易数量（成功的订单）
     */
    private Long todayOrderCount;

    /**
     * 累计交易数量（成功的订单）
     */
    private Long totalOrderCount;

    /**
     * 今日订单数
     */
    private Long todayOrderNum;

    /**
     * 量化值
     */
    private Double quantificationValue;

    /**
     * 粉丝数
     */
    private Integer fans;

    /**
     * 待付款订单
     */
    private Long pendingPaymentOrderCount;

    /**
     * 待发货订单
     */
    private Long pendingDeliveryOrderCount;

    /**
     * 退货待处理订单
     */
    private Long returnOrderCount;

    /**
     * 量化率
     */
    private String quantificationRate;

    /**
     * 技术引流次数
     */
    private Integer technologyDrainageCount;

    /**
     * 平台促销金
     */
    private BigDecimal platformPromotionGold;

    /**
     * 代销抵扣金（代销帮忙买卖商品的佣金，不能提现,只能赠送给用户进行购买商品）
     */
    private BigDecimal deductionMoney;

}
