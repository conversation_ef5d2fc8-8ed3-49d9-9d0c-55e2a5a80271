package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 记录用户推荐商家的记录，并且记录是否被用来提升额度
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tz_user_recommend_shop_record")
public class TzUserRecommendShopRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 是否已经使用过：0：未兑换：1：已兑换
     */
    private String status;

}
