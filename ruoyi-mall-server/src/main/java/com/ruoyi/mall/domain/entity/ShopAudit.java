package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 商家审核记录对象 mall_shop_audit
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop_audit")
public class ShopAudit extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 审核ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID (申请人)
     */
    private Long userId;

    /**
     * 商家名称
     */
    private String name;

    /**
     * 品牌
     **/
    private String brand;

    /**
     * 商家类型（0代销 1企业）
     */
    private String type;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 企业认证省份编码
     */
    private Long provinceCode;

    /**
     * 企业认证省份
     */
    private String province;

    /**
     * 企业认证城市编码
     */
    private String cityCode;

    /**
     * 企业认证城市
     */
    private String city;

    /**
     * 企业认证区域编码
     */
    private String districtCode;

    /**
     * 企业认证区域
     */
    private String district;

    /**
     * 企业认证编码
     */
    private String townCode;

    /**
     * 企业认证街道名称
     */
    private String town;

    /**
     * 企业认证详细地址
     */
    private String address;

    /**
     * 营业时间
     */
    private String businessHours;

    /**
     * 商家简介
     */
    private String introduction;

    /**
     * 商家logo
     */
    private String logo;

    /**
     * 企业邮箱
     */
    private String email;

    /**
     * 企业法人
     */
    private String legalPerson;

    /**
     * 企业法人电话
     */
    private String legalPersonPhone;

    /**
     * 企业法人身份证号
     */
    private String legalPersonIdCard;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 营业执照号
     */
    private String businessLicenseNumber;

    /**
     * 营业执照有效期
     */
    private String businessLicenseExpireTime;

    /**
     * 其他资质证书
     */
    private String otherCertificate;

    /**
     * 身份证正面
     */
    private String idCardFront;

    /**
     * 身份证反面
     */
    private String idCardBack;

    /**
     * 手持身份证照片
     */
    private String idCardHand;

    /**
     * 审核状态（0待审核 1审核通过 2审核拒绝）
     */
    private String auditStatus;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核人ID
     */
    private Long auditorId;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
