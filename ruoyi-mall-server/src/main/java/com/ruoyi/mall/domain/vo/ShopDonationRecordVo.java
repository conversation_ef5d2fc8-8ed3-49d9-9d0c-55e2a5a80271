package com.ruoyi.mall.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家促销金赠送记录VO
 */
@Data
@ExcelIgnoreUnannotated
@HeadRowHeight(20)
@HeadFontStyle(fontHeightInPoints = 12)
public class ShopDonationRecordVo {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 商家名称
     */
    @ExcelProperty(value = "商家名称")
    @ColumnWidth(20)
    private String shopName;

    /**
     * 接收用户ID
     */
    private Long userId;

    /**
     * 接收用户手机号
     */
    @ExcelProperty(value = "接收用户手机号")
    @ColumnWidth(18)
    private String receiverPhone;

    /**
     * 接收用户昵称
     */
    @ExcelProperty(value = "接收用户昵称")
    @ColumnWidth(18)
    private String receiverNickname;

    /**
     * 赠送金
     */
    @ExcelProperty(value = "赠送金(¥)")
    @ColumnWidth(15)
    private BigDecimal amount;

    /**
     * 千六损耗金额
     */
    @ExcelProperty(value = "千六损耗金额")
    @ColumnWidth(15)
    private BigDecimal lossAmount;

    /**
     * 实际到账金额
     */
    @ExcelProperty(value = "实际到账金额")
    @ColumnWidth(15)
    private BigDecimal actualAmount;

    /**
     * 赠送状态：0-失败，1-成功
     */
    private String status;

    /**
     * 赠送状态名称
     */
    @ExcelProperty(value = "赠送状态")
    @ColumnWidth(15)
    private String statusName;

    /**
     * 赠送类型：1-商家赠送，2-代销赠送
     */
    private String donationType;

    /**
     * 赠送类型名称
     */
    @ExcelProperty(value = "赠送类型")
    @ColumnWidth(15)
    private String donationTypeName;

    /**
     * 赠送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "赠送时间")
    @ColumnWidth(22)
    private Date donationTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ColumnWidth(30)
    private String remark;
}
