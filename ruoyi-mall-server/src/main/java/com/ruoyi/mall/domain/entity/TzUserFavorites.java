package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 用户收藏夹
 */
@Data
@TableName("mall_user_favorites")
public class TzUserFavorites extends BaseEntity {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 商品id
     */
    private Long productId;
    /**
     * 收藏时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否取消（0:否，1:是）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     **/
    @TableLogic
    private String delFlag;

    /**
     * 商铺id
     */
    @TableField(exist = false)
    private Long shopId;
    /**
     * 商铺名称
     */
    @TableField(exist = false)
    private String shopName;

    /**
     * 商铺logo
     */
    @TableField(exist = false)
    private String shopLogo;

    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String productName;

    /**
     * 商品封面
     */
    @TableField(exist = false)
    private String productCover;
    /**
     * 商品价格
     */
    @TableField(exist = false)
    private String price;

}
