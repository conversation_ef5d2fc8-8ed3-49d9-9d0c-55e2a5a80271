package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("mall_user_account_status")
public class UserAccountStatus extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId; // 用户ID

    private Integer hasUnpaidOrders;       // 是否有未付款订单
    private Integer hasUndeliveredOrders;  // 是否有未发货订单
    private Integer hasUnreceivedOrders;   // 是否有未收货订单
    private Integer hasRefunds;           // 是否有退款中订单
    private Integer hasDebt;              // 是否有欠款
    private Integer hasComplaints;        // 是否有投诉

    // CB/B 注销条件字段
    private Integer hasPendingCbSettlement; // 是否有未结算的 CB 订单
    private Integer hasActiveConsignGoods;  // 是否还有上架的代销商品
    private Integer cbOrderLastSettledDays; // 最近一次 CB 订单结算距今的天数

    private Integer isDeleteable; // 是否允许注销

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
