package com.ruoyi.mall.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户平台抵扣金每日统计VO
 */
@Data
@NoArgsConstructor
public class UserDeductionDailyStatVO {

    /**
     * 更新日期
     */
    private String updateDate;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 昨日已使用平台抵扣金
     */
    private String writeOffSubsidy;

    /**
     * 累计已使用平台抵扣金
     */
    private String writeOffSubsidyTotal;

    /**
     * 未使用平台抵扣金（剩余）
     */
    private String unWriteOffSubsidy;
}
