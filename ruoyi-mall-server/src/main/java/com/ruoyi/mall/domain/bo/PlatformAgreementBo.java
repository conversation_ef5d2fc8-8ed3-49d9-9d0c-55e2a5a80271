package com.ruoyi.mall.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 平台协议配置业务对象 mall_platform_agreement
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class PlatformAgreementBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "Id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 协议名称
     */
    @NotBlank(message = "协议名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 协议内容
     */
    @NotBlank(message = "协议内容不能为空", groups = {EditGroup.class})
    private String value;


}
