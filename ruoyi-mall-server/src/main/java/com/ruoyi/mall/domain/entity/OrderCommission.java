package com.ruoyi.mall.domain.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单佣金结算
 */

@Data
@TableName("mall_order_commission")
public class OrderCommission extends BaseEntity {


    /**
     * 结算Id
     */
    private Long id;
    /**
     * 结算商家ID
     */
    private Long shopId;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 订单项ID
     */
    private Long itemId;

    /**
     * 订单实际支付金额
     */
    private BigDecimal orderAmount;

    /**
     * 佣金类型
     */
    private Integer commissionType;

    /**
     * 计算出来的佣金
     */
    private BigDecimal calcCommission;

    /**
     * 实际结算佣金
     */
    private BigDecimal actualCommission;

    /**
     * 状态 结算状态(0:未结算，1：已结算，2：退款扣回)
     */
    private String status;

    /**
     * 删除标志（0：否，1：是）
     */
    private String delFlag;


}
