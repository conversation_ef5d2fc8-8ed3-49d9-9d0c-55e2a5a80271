package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品分类对象 category
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_category")
public class Category extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 分类ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 父分类ID
     */
    @NotNull(message = "父分类ID不能为空")
    private Long parentId;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    private String name;

    /**
     * 分类编码
     */
    private String code;

    /**
     * 分类图标
     */
    private String icon;

    /**
     * 显示顺序
     */
    private Integer sort;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 是否显示（0显示 1隐藏）
     */
    private String visible;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 同iD值，用与树结构的
     */
    @TableField(exist = false)
    private Long value;

    /**
     * 子分类
     */
    @TableField(exist = false)
    private List<Category> children = new ArrayList<>();

    /**
     * 关联的商家信息
     */
    @TableField(exist = false)
    private Shop shop;
}
