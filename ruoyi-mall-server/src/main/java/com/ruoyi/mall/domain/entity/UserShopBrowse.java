package com.ruoyi.mall.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop_browse")
public class UserShopBrowse extends BaseEntity {

    /**
     * 浏览id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商铺id
     */
    private Long shopId;

    /**
     * 删除标志（0代表存在 2代表删除）
     **/
    @TableLogic
    private String delFlag;

    /**
     * 商铺名称
     */
    @TableField(exist = false)
    private String shopName;

    /**
     * 店铺logo
     */
    @TableField(exist = false)
    private String shopLogo;

    /**
     * 用户名称
     */
    @TableField(exist = false)
    private String userName;

    /**
     * 用户头像
     */
    @TableField(exist = false)
    private String userAvatar;
}
