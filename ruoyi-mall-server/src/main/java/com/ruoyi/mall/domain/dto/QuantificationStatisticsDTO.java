package com.ruoyi.mall.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 量化值统计结果DTO
 */
@Data
@HeadRowHeight(20)
@HeadFontStyle(fontHeightInPoints = 12)
public class QuantificationStatisticsDTO {

    /**
     * 统计日期
     */
    @ExcelProperty(value = "统计日期")
    @ColumnWidth(15)
    private String date;

    /**
     * 商家电话
     */
    @ExcelProperty(value = "商家电话")
    @ColumnWidth(15)
    private String phone;

    /**
     * 商家名称
     */
    @ExcelProperty(value = "商家名称")
    @ColumnWidth(20)
    private String shopName;

    /**
     * 法人代表
     */
    @ExcelProperty(value = "法人代表")
    @ColumnWidth(15)
    private String legalPerson;

    /**
     * 今日量化值进化量
     */
    @ExcelProperty(value = "今日量化值进化量")
    @ColumnWidth(15)
    private Double dayConvertAmount;
}
