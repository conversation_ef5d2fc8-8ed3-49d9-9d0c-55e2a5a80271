package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("public_param")
public class PublicParam extends BaseEntity {

    /**
     * 公共参数id
     */
    @TableId(value = "public_id")
    private Long publicId;

    /**
     * 公共参数名称
     */
    private String publicName;

    /**
     * 公共参数key
     */
    private String publicKey;

    /**
     * 公共参数值
     */
    private String publicValue;

    /**
     * 类型，0未知，1系统，2业务
     */
    private String publicType;
    /**
     * 状态，0禁用，1启用
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
