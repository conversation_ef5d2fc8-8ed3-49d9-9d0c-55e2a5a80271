package com.ruoyi.mall.domain.dto;


import com.ruoyi.mall.domain.entity.TzUserRealName;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 用户实名认证
 */
@Data
public class UserRealNameDTO extends TzUserRealName {


    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    @NotNull(message = "用户名不能为空")
    private String username;
    /**
     * 省份编码
     */
    @NotNull(message = "省份编码不能为空")
    private String provinceCode;

    /**
     * 省份
     */
    @NotNull(message = "省份不能为空")
    private String province;

    /**
     * 城市编码
     */
    @NotNull(message = "城市编码不能为空")
    private String cityCode;
    /**
     * 城市
     */
    @NotBlank(message = "城市不能为空")
    private String city;

    /**
     * 区域编码
     */
//    @NotNull(message = "区域编码不能为空")
    private String districtCode;
    /**
     * 区域
     */
//    @NotBlank(message = "区域不能为空")
    private String district;

    /**
     * 编码
     */
//    @NotNull(message = "编码不能为空")
    private String townCode;
    /**
     * 街道名称
     */
//    @NotNull(message = "街道名称不能为空")
    private String town;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    private String address;

    /**
     * 身份证号码
     */
    @NotBlank(message = "身份证不能为空")
    @Size(min = 18, max = 18, message = "身份证必须为18位")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$",
        message = "身份证格式不正确")
    private String idCard;

    /**
     * 身份证正面
     */
    @NotBlank(message = "身份证正面不能为空")
    private String idCardFront;

    /**
     * 身份证反面
     */
    @NotBlank(message = "身份证反面不能为空")
    private String idCardBack;

    /**
     * 手持身份证照片
     */
    @NotBlank(message = "手持身份证照片不能为空")
    private String idCardHand;

    /**
     * 证件类型 1：身份证
     */
    @NotBlank(message = "证件类型不能为空")
    private String cardType;
}
