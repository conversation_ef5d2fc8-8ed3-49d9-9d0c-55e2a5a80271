package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("mall_order_refund")
public class OrderRefund extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "refund_id", type = IdType.AUTO)
    private Long refundId;

    /**
     * 退款编号
     */
    private String refundSn;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 买家ID
     */
    private Long userId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单商品项id
     */
    private Long itemId;

    /**
     * 申请原因
     */
    private String buyerReason;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 联系方式
     */
    private String buyerMobile;

    /**
     * 拒绝原因
     */
    private String rejectMessage;

    /**
     * 拒绝时间
     */
    private Date rejectTime;

    /**
     * 撤销时间
     */
    private Date cancelTime;

    /**
     * 处理退款状态:(1.买家申请 2.卖家接受  3.退款成功 4.买家撤回申请 5.商家拒绝 -1.退款关闭)详情见ReturnMoneyStsType
     */
    private Integer returnMoneySts;

    /**
     * 关联的订单信息（非数据库字段）
     */
    @TableField(exist = false)
    private Order order;

    /**
     * 订单编号
     */
    @TableField(exist = false)
    private String orderNo;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

}
