package com.ruoyi.mall.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class OrderRefundDTO {

    @NotEmpty(message = "订单id不能为空")
    private String orderId;

    /**
     * 订单商品项ID
     */
    private Long itemId;

    @NotNull(message = "申请原因不能为空")
    private String buyerReason;

    @NotNull(message = "手机号码不能为空")
    private String buyerMobile;

    private String buyerDesc;

    /**
     * 退款金额（可选，默认为订单实付金额）
     */
    private BigDecimal refundAmount;
}
