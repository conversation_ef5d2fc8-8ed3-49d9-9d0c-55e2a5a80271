package com.ruoyi.mall.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 结算短信验证码校验VO对象
 */
@Data
public class SettlementVerifySmsVo {

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    private String contactPhone;

    /**
     * 验证码
     */
//    @NotBlank(message = "验证码不能为空")
    private String code;

    /**
     * 图形验证码uuid
     */
    @NotBlank(message = "uuid不能为空")
    private String randomStr;

    /**
     * 图形验证码code
     */
    @NotBlank(message = "验证码不能为空")
    private String randomCode;

    /**
     * 操作密碼
     */
    @NotBlank(message = "操作密碼不能为空")
    private String operationPassword;

    /**
     * 申请结算金额
     */
    @NotNull(message = "申请结算金额不能为空")
    private BigDecimal amount;

    /**
     * 收款账户名
     */
    @NotBlank(message = "收款账户名不能为空")
    private String accountName;

    /**
     * 收款账号
     */
    @NotBlank(message = "收款账号不能为空")
    private String accountNo;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 开户行
     */
    private String bankBranchName;
}
