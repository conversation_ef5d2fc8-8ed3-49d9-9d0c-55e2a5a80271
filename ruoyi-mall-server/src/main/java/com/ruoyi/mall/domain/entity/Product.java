package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.mall.domain.vo.EvaluateVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品对象 mall_product
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_product")
public class Product extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 商品ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    @Size(max = 100, message = "商品名称长度不能超过100个字符")
    private String name;

    /**
     * 0:普通商品,1:虚拟商品
     */
    private String type;

    /**
     * 商品封面
     */
    private String cover;

    /**
     * 商品视频
     */
    private String video;

    /**
     * 商品图片，多个图片用逗号分隔
     */
    @NotBlank(message = "商品图片不能为空")
    private String images;

    /**
     * 商品价格
     */
    @NotNull(message = "商品价格不能为空")
    @Min(value = 0, message = "商品价格不能小于0")
    private Double price;

    /**
     * 商品原价
     */
    private Double originalPrice;

    /**
     * 商品库存
     */
    @NotNull(message = "商品库存不能为空")
    @Min(value = 0, message = "商品库存不能小于0")
    private Integer stock;

    /**
     * 商品销量
     */
    private Integer sales;

    /**
     * 商品单位
     */
    private String unit;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品详情
     */
    @NotBlank(message = "商品详情不能为空")
    private String content;

    /**
     * 商品状态（0下架 1上架）
     */
    private String status;

    /**
     * 是否推荐（0否 1是）
     */
    private String isRecommend;

    /**
     * 是否新品（0否 1是）
     */
    private String isNew;

    /**
     * 是否热销（0否 1是）
     */
    private String isHot;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 减库存的方式（1：下单减库存，2付款时减库存：）
     */
    private Integer stockType;

    /**
     * 是否代销
     */
    private Integer isDaixiao;

    /**
     * 代销佣金方式（1：固定金额，2比例%）
     */
    private Integer daixiaoType;

    /**
     * 代销佣金的值
     */
    private Double daixiaoTypeValue;

    /**
     * 商家信息
     */
    @TableField(exist = false)
    private Shop shop;

    /**
     * 分类信息
     */
    @TableField(exist = false)
    private Category category;
    /**
     * 规格信息
     */
    @TableField(exist = false)
    private List<Sku> skuList;

    /**
     * 商品评价信息
     */
    @TableField(exist = false)
    private List<EvaluateVo> productEvaluateList;

    /**
     * 评价总条数
     */
    @TableField(exist = false)
    private Long totalCount;

    /**
     * 审核状态
     * 0:待审核，1审核通过，2：审核未通过
     */
    private String auditStatus;

    /**
     * 审核备注
     */
    private String auditRemake;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 代销产品链接生成时间
     */
    private Date urlCreateTime;

    /**
     * 最低价格（查询用）
     */
    @TableField(exist = false)
    private BigDecimal minPrice;

    /**
     * 最高价格（查询用）
     */
    @TableField(exist = false)
    private BigDecimal maxPrice;

    /**
     * 手续费承担方（0店铺 1用户）
     */
    private String feeBearer;

    /**
     * 代销状态
     */
    @TableField(exist = false)
    private String daiXiaoStatus;

    /**
     * 店铺名称
     */
    @TableField(exist = false)
    private String merchantName;
}
