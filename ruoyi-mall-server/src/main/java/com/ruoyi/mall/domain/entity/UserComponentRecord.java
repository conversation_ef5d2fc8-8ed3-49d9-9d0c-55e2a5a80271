package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 用户分量记录对象 mall_user_component_record
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_user_component_record")
public class UserComponentRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 获得的分量值
     */
    private BigDecimal componentValue;

    /**
     * 记录类型（1:签到, 2:购物, 3:评价, 4:活动, 5:管理员调整, 6:兑换扣减）
     */
    private Integer recordType;

    /**
     * 相关业务ID（如订单ID、评价ID等）
     */
    private Long businessId;

    /**
     * 相关业务编号（如订单编号等）
     */
    private String businessNo;

    /**
     * 记录描述
     */
    private String description;

    /**
     * 操作人员
     */
    private String operator;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 总量
     */
    @TableField(exist = false)
    private BigDecimal dayAmount;
}
