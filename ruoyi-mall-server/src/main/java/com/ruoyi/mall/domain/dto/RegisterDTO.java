package com.ruoyi.mall.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 用户注册对象
 */
@Data
public class RegisterDTO {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

//    /**
//     * 验证码
//     */
//    @NotBlank(message = "验证码不能为空")
//    @Pattern(regexp = "^\\d{6}$", message = "验证码格式不正确")
//    private String code;
//
//    /**
//     * 唯一编码
//     */
//    @NotBlank(message = "唯一编码不能为空")
//    private String uuid;

    /**
     * 图形验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String code;

    /**
     * 图形验证码随机数
     */
    @NotBlank(message = "图形验证码随机数不能为空")
    private String randomStr;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[!@#$%^&*()_+=~`{}\\[\\]|:;<>,.?/]).{6,20}$", message = "密码必须包含至少一个数字、一个字母和一个特殊符号，且长度在6-20位")
    private String password;

    /**
     * 邀请人手机号
     */
    private String invitationPhone;

}
