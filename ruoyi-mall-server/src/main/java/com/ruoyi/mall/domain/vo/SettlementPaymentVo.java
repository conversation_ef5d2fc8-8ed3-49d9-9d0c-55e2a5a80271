package com.ruoyi.mall.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 结算打款VO对象
 */
@Data
public class SettlementPaymentVo {

    /**
     * 结算单ID
     */
    @NotNull(message = "结算单ID不能为空")
    private Long id;

    /**
     * 打款凭证
     */
    @NotBlank(message = "打款凭证不能为空")
    private String payProof;

    /**
     * 打款备注
     */
    private String payRemark;
}
