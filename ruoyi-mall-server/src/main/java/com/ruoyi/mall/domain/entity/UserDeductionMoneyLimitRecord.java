package com.ruoyi.mall.domain.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

@Data
@TableName("tz_user_deduction_money_limit_record")
public class UserDeductionMoneyLimitRecord extends BaseEntity {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 扣减金额限制
     */
    private Double deductionMoneyLimit;

    /**
     * 描述
     */
    private String description;

    /**
     * 类型 (1:有效引流商家，2：无效引流商家，3：推荐奖励)
     */
    private String type;

    /**
     * 状态（0：增加，1：减少）
     */
    private String status;


}
