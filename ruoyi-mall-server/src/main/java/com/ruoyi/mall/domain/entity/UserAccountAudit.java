package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户账户注销审核记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_user_account_audit")
public class UserAccountAudit extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 注销审核ID
     */
    private Long id;

    /**
     * 用户ID (申请人)
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String username;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户类型(3消费者,2代销,1商家)
     */
    private String userType;

    /**
     * 注销原因
     */
    private String deleteReason;

    /**
     * 审核状态（0待审核 1审核通过 2审核拒绝）
     */
    private String auditStatus;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 审核人ID
     */
    private Long auditorId;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
