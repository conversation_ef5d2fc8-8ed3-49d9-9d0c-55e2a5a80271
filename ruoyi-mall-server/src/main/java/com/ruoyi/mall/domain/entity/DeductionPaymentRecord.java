package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 抵扣金支付记录对象 mall_deduction_payment_record
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_deduction_payment_record")
public class DeductionPaymentRecord extends BaseEntity {
    /**
     * 支付类型：用户抵扣金支付
     */
    public static final String PAY_TYPE_USER = "1";
    /**
     * 支付类型：商家平台兑换金支付
     */
    public static final String PAY_TYPE_SHOP = "2";
    private static final long serialVersionUID = 1L;
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 业务订单编号
     */
    private String orderNo;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 手续费
     */
    private BigDecimal feeAmount;

    /**
     * 实际扣减金额（支付金额+手续费）
     */
    private BigDecimal totalAmount;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付状态：0-成功，1-失败
     */
    private String status;

    /**
     * 支付类型：1-用户抵扣金支付，2-商家平台兑换金支付
     */
    private String payType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
