// 文件路径：com.ruoyi.mall.domain.dto.SettlementQueryDTO.java

package com.ruoyi.mall.domain.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 商家结算记录-查询参数 DTO
 */
@Data
public class SettlementRecordDTO {

    private String accountName;         // 收款账号 / 结算卡号

    private String legalPersonPhone;             // 手机号（企业法人手机号）

//    private String settlementType;    // 结算方式（1银行卡 2微信 3支付宝）

    private String status;            // 状态（0待审核 1审核通过等）


    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTime;


    /**
     * 截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
