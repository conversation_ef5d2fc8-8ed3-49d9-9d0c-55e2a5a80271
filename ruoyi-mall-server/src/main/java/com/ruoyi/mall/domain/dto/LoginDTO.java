package com.ruoyi.mall.domain.dto;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 商城用户登录对象
 *
 * <AUTHOR>
 */
@Data
public class LoginDTO {

    /**
     * 登录类型
     * 1：账户密码登录
     * 2：验证码登录
     */
    @NotNull(message = "登录类型不能为空")
    private Integer LoginType;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 密码
     */
    private String password;

    /**
     * 验证码
     */
    private String code;

    /**
     * 验证码的唯一编码
     */
    private String uuid;

    /**
     * 图片验证码随机数
     */
    private String randomStr;
}
