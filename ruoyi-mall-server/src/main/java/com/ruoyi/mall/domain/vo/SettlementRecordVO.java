// 文件路径：com.ruoyi.mall.domain.vo.SettlementListVO.java

package com.ruoyi.mall.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商家结算结算记录 VO
 */
@Data
public class SettlementRecordVO {


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("结算时间")
    private LocalDateTime settlementTime;


    @ExcelProperty("账号")
    private String accountName;

    /**
     * 手机号（企业法人手机号）
     */
    @ExcelProperty("手机号")
    private String legalPersonPhone;

    @ExcelProperty("结算单号")
    private String settlementNo;

    @ExcelProperty("结算卡号")
    private String accountNo;

    @ExcelProperty("公司名称")
    private String businessName;

   /* @ExcelProperty("结算方式")
    private String settlementType;*/

    @ExcelProperty("货款金额")
    private BigDecimal actualAmount;

    @ExcelProperty("状态")
    private String status;
}
