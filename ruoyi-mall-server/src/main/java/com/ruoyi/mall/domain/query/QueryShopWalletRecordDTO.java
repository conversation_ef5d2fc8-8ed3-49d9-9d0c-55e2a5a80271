package com.ruoyi.mall.domain.query;


import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 商家交易记录查询条件
 */

@Data
public class QueryShopWalletRecordDTO {

    /**
     * 商家ID
     */
    @NotNull(message = "商家id不能为空")
    private Long shopId;

    /**
     * 当前页数
     */
    @NotNull(message = "当前页数不能为空")
    private Integer pageNUm;

    /**
     * 页数条数
     */
    @NotNull(message = "页数条数不能为空")
    private Integer pageSize;
}
