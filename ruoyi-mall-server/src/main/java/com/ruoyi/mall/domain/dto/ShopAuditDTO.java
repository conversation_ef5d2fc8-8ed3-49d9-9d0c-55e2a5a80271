package com.ruoyi.mall.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商家审核DTO
 *
 * <AUTHOR>
 */
@Data
public class ShopAuditDTO {

    /**
     * 审核ID
     */
    @NotNull(message = "审核ID不能为空")
    private Long id;

    /**
     * 审核状态（1通过 2拒绝）
     */
    @NotBlank(message = "审核状态不能为空")
    private String auditStatus;

    /**
     * 审核备注
     */
    private String auditRemark;
}
