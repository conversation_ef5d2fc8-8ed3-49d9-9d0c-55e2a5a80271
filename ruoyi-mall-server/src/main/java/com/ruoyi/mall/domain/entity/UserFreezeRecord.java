package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 用户扣除金额记录
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_user_freeze_record")
public class UserFreezeRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 冻结记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long userId;

    /**
     * 店铺名称
     */
    private String userName;

    /**
     * 店铺电话
     */
    private String phone;

    /**
     * 类型（1：用户，2：商家）
     */
    private Integer type;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 原因
     */
    private String reason;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
