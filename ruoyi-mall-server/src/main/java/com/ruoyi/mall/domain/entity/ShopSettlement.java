package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家结算单对象 mall_shop_settlement
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop_settlement")
public class ShopSettlement extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 结算单ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 结算单号
     */
    @NotBlank(message = "结算单号不能为空")
    private String settlementNo;

    /**
     * 商家ID
     */
    @NotNull(message = "商家ID不能为空")
    private Long shopId;

    /**
     * 钱包ID
     */
    @NotNull(message = "钱包ID不能为空")
    private Long walletId;

    /**
     * 申请结算金额
     */
    @NotNull(message = "申请结算金额不能为空")
    private BigDecimal amount;

    /**
     * 实际结算金额
     */
    private BigDecimal actualAmount;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 收款账户名
     */
    @NotBlank(message = "收款账户名不能为空")
    private String accountName;

    /**
     * 收款账号
     */
    @NotBlank(message = "收款账号不能为空")
    private String accountNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 开户行
     */
    private String bankBranchName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 结算状态（0待审核 1审核通过 2审核拒绝 3已打款 4已完成，5已取消）
     */
    private String status;

    /**
     * 审核人ID
     */
    private Long auditorId;

    /**
     * 审核人名称
     */
    private String auditorName;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核意见
     */
    private String auditRemark;

    /**
     * 打款人ID
     */
    private Long payerId;

    /**
     * 打款人名称
     */
    private String payerName;

    /**
     * 打款时间
     */
    private Date payTime;

    /**
     * 打款凭证
     */
    private String payProof;

    /**
     * 打款备注
     */
    private String payRemark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
