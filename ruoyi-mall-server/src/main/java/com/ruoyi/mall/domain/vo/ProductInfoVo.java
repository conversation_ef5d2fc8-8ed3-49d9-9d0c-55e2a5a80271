package com.ruoyi.mall.domain.vo;

import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.entity.Sku;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * 产品详情Vo
 */
@Data
public class ProductInfoVo {

    /**
     * 商家ID
     */
    private Long shopId;
    /**
     * 产品丶
     */
    private Long productId;

    /**
     * 商品状态（0下架 1上架）
     */
    private String status;

    /**
     * 商品分类ID
     */
    private Long categoryId;
    /**
     * 产品名称
     */
    private String name;
    /**
     * 产品视频
     */
    private String video;
    /**
     * 产品图片
     */
    private String images;
    /**
     * 产品价格
     */
    private Double price;
    /**
     * 产品描述
     */
    private String description;
    /**
     * 产品详情
     */
    private String content;
    /**
     * 产品库存
     */
    private Integer stock;
    /**
     * 产品销量
     */
    private Integer sales;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 发货地址
     */
    private String address;
    /**
     * 商家信息
     */
    private Shop shop;
    /**
     * 规格列表
     */
    private List<Sku> skus;
    /**
     * 商品评价列表
     */
    private List<EvaluateVo> productEvaluates;
    /**
     * 评价总条数
     */
    private Integer evaluateCount;

    /**
     * 卖家秀列表
     */
    private List<EvaluateVo> customerShows;

    /**
     * 图片列表
     */
    private List<String> imagesList;

    /**
     * 手续费承担方（0店铺 1用户）
     */
    private BigDecimal feeBearer;

}
