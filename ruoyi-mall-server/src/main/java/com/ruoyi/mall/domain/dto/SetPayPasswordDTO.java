package com.ruoyi.mall.domain.dto;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;


/**
 * 设置兑换金支付密码
 */
@Data
public class SetPayPasswordDTO {


    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 6, message = "密码必须为6位")
    @Pattern(regexp = "\\d+", message = "密码必须为数字")
    private String password;


    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 验证码
     */
    private String code;

    /**
     * 唯一编码uuid
     */
    private String uuid;
}
