package com.ruoyi.mall.domain.dto;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 促销金赠送请求对象
 */
@Data
public class ShopDonationDTO {

    /**
     * 接收用户手机号
     */
    @NotBlank(message = "用户手机号不能为空")
    private String phone;

    /**
     * 赠送金
     */
    @NotNull(message = "赠送金不能为空")
    @DecimalMin(value = "0.01", message = "赠送金必须大于0")
    private BigDecimal amount;

    /**
     * 操作密码
     */
    @NotBlank(message = "操作密码不能为空")
    private String password;

    /**
     * 备注
     */
    private String remark;
}
