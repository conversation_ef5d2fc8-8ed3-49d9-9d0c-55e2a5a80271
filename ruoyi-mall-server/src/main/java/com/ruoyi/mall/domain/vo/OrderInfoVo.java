package com.ruoyi.mall.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单详情vo
 */
@Data
public class OrderInfoVo {

    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 商品项ID
     */
    private Long itemId;
    /**
     * 商家id
     */
    private Long shopId;
    /**
     * 商家名称
     */
    private String shopName;

    /**
     * 商家logo
     */
    private String shopLogo;
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品图片
     */
    private String productImage;
    /**
     * 产品数量
     */
    private Integer quantity;
    /**
     * 产品单价
     */
    private Double productPrice;
    /**
     * 总价
     */
    private Double TotalAmount;

    /**
     * 实际支付金额
     */
    private Double payAmount;
    /**
     * 补贴金金额
     */
    private BigDecimal deductionAmount;
    /**
     * 支付方式
     */
    private String payType;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 收货方式（1：快递，2自提）
     */
    private String receiveType;

    /**
     * 收货人
     */
    private String receiveName;
    /**
     * 收货人电话
     */
    private String receivePhone;
    /**
     * 收货人地址
     */
    private String receiveAddress;
    /**
     * 收货时间
     */
    private Date receiveTime;
    /**
     * 发货人
     */
    private String sendName;
    /**
     * 发货人电话
     */
    private String sendPhone;
    /**
     * 发货地址
     */
    private String sendAddress;
    /**
     * 发货时间
     */
    private Date sendTime;

    /**
     * skuId
     */
    private String skuId;
    /**
     * sku规格
     */
    private String skuSpec;

    /**
     * 售后状态
     */
    private String refundStatus;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 退款时间
     */
    private Date refundTime;
    /**
     * 退款金额
     */
    private Double refundPrice;
    /**
     * 退款理由
     */
    private String refundReason;

    /**
     * 快递公司
     */
    private String deliveryName;

    /**
     * 快递单号
     */
    private String deliveryNumber;

    /**
     * 快递备注
     */
    private String remark;
    /**
     * 订单评价状态（0未评价，1：已评价，2:评价被隐藏）
     */
    private String commentStatus;


}
