package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 购物车对象 mall_cart
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("tz_cart")
@Builder
public class Cart extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商家ID/代销ID
     */
    private Long shopId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 是否选中：0-未选中，1-已选中
     */
    private String selected;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 商品信息
     */
    @TableField(exist = false)
    private Product product;

    /**
     * SKU信息
     */
    @TableField(exist = false)
    private Sku sku;

    /**
     * 商家信息
     */
    @TableField(exist = false)
    private Shop shop;

    /**
     * 商品价格
     */
    @TableField(exist = false)
    private BigDecimal price;

    /**
     * 商品总价
     */
    @TableField(exist = false)
    private BigDecimal totalAmount;
}
