package com.ruoyi.mall.domain.query;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 购物车查询参数DTO
 */
@Data
public class CartQueryParamDTO {


    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 查询内容
     */
    private String query;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-mm")
    private String startTime;

    /**
     * 截止时间
     */
    @JsonFormat(pattern = "yyyy-mm")
    private String endTime;

    /**
     * 分类ID
     */
    private Long categoryId;

}
