package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 商家钱包对象 mall_shop_wallet
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop_wallet")
public class ShopWallet extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 钱包ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 商家ID
     */
    @NotNull(message = "商家ID不能为空")
    private Long shopId;

    /**
     * 钱包余额
     */
    @NotNull(message = "钱包余额不能为空")
    private BigDecimal balance;

    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;

    /**
     * 累计收入
     */
    private BigDecimal totalIncome;

    /**
     * 累计支出
     */
    private BigDecimal totalExpense;

    /**
     * 钱包状态（0正常 1冻结）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
