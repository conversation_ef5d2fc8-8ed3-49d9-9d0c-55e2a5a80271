package com.ruoyi.mall.domain.vo;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class EvaluateVo {

    /**
     * 评价ID
     */
    private Long id;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 用户id
     */
    private Long user_id;
    /**
     * 评价图片
     */
    private String img;
    /**
     * 评价视频
     */
    private String video;
    /**
     * 评价内容
     */
    private String content;
    /**
     * 是否匿名
     */
    private String isShow;
    /**
     * 评价等级
     */
    private Integer level;
    /**
     * 产品名称
     */
    private String name;
    /**
     * 产品封面
     */
    private String cover;
    /**
     * 产品id
     */
    private Long productId;

    /**
     * 点赞数
     */
    private Integer likeCount;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 评价时间
     */
    private String createTime;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 商品规格
     */
    private String skuSpec;

    /**
     * 商家回复
     */
    private String reply;

}
