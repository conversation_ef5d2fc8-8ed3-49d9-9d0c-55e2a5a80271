package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 抵扣金赠送记录对象 mall_deduction_record
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_deduction_record")
public class DeductionRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID（赠送方）
     */
    private Long shopId;

    /**
     * 用户ID（接收方）
     */
    private Long userId;

    /**
     * 用户手机号
     */
    private String phone;

    /**
     * 赠送金
     */
    private BigDecimal amount;

    /**
     * 赠送原因
     */
    private String reason;

    /**
     * 状态（0成功 1失败）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
