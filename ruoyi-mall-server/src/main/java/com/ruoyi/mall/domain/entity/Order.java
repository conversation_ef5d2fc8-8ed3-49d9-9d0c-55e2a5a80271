package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单对象 mall_order
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_order")
public class Order extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品名称
     */
    private String orderName;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 代销ID
     */
    private Long consignmentId;

    /**
     * 订单总数量
     */
    private Long totalQuantity;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 补贴金金额
     */
    private BigDecimal deductionAmount;

    /**
     * 实付金额
     */
    private BigDecimal payAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 运费金额
     */
    private BigDecimal freightAmount;

    /**
     * 手续费总金额
     */
    private BigDecimal feeAmount;

    /**
     * 支付方式：1-微信支付，2-支付宝支付
     */
    private String payType;

    /**
     * 订单的支付状态：PayStatusEnum
     */
    private String payStatus;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 发货时间
     */
    private Date deliveryTime;

    /**
     * 确认收货时间
     */
    private Date receiveTime;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 订单状态
     * 具体查看 OrderStatusEnum
     */
    private String status;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 发货ID
     */
    private Long shipId;

    /**
     * 收货方式（1：快递，2自提）
     */
    private String receiverType;

    /**
     * 收货人姓名
     */
    private String receiverName;

    /**
     * 收货人电话
     */
    private String receiverPhone;

    /**
     * 收货人省份
     */
    private String receiverProvince;

    /**
     * 收货人城市
     */
    private String receiverCity;

    /**
     * 收货人区域
     */
    private String receiverDistrict;

    /**
     * 收货人详细地址
     */
    private String receiverAddress;

    /**
     * 省+市+区+详细地址
     */
    private String receiverAddressAll;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 快递公司Id
     */
    private Long deliveryId;

    /**
     * 快递公司名称
     */
    private String deliveryName;

    /**
     * 快递单号
     */
    private String deliveryNumber;

    /**
     * 订单退款状态（1:申请退款 2:退款成功  3:退款失败）
     */
    private Integer refundStatus;

    /**
     * 订单商品信息
     */
    @TableField(exist = false)
    private List<OrderItem> orderItems;

    /**
     * 商家信息
     */
    @TableField(exist = false)
    private Shop shop;

    /**
     * 订单组编号（用于关联同一批次创建的多个店铺订单）
     */
    private String orderGroupNo;

    /**
     * 是否为主订单（1是，0否）
     * 一个订单组中只有一个主订单
     */
    private Integer isMainOrder;

    /**
     * 订单组内的订单数量
     * 仅在主订单中记录
     */
    private Integer orderGroupCount;

    @TableField(exist = false)
    private String userPhone;

    /**
     * 用户昵称
     */
    @TableField(exist = false)
    private String userNickName;

    /**
     * 商家名称
     */
    @TableField(exist = false)
    private String shopName;

    /**
     * 商家Logo
     */
    @TableField(exist = false)
    private String shopLogo;

    /**
     * 商家电话
     */
    @TableField(exist = false)
    private String shopPhone;
}
