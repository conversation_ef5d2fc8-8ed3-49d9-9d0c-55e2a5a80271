package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 店铺货款冻结记录对象 mall_shop_freeze_record
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop_freeze_record")
public class ShopFreezeRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 冻结记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺电话
     */
    private String shopPhone;

    /**
     * 冻结金额
     */
    private BigDecimal amount;

    /**
     * 冻结原因
     */
    private String reason;

    /**
     * 状态（frozen-已冻结 unfrozen-已解冻）
     */
    private String status;

    /**
     * 解冻时间
     */
    private Date unfreezeTime;

    /**
     * 解冻操作人
     */
    private String unfreezeBy;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 开始时间 查询条件
     */
    @TableField(exist = false)
    private Date beginTime;

    /**
     * 结束时间 查询条件
     */
    @TableField(exist = false)
    private Date endTime;
}
