package com.ruoyi.mall.domain.dto;

import com.ruoyi.mall.domain.entity.Cart;
import com.ruoyi.mall.domain.entity.Shop;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 按店铺分组的购物车列表
 */
@Data
public class CartListDTO {

    /**
     * 店铺信息
     */
    private Shop shop;

    /**
     * 该店铺的购物车商品列表
     */
    private List<Cart> cartList;

    /**
     * 店铺商品总数量
     */
    private Integer totalQuantity;

    /**
     * 店铺商品总金额
     */
    private BigDecimal totalAmount;

    /**
     * 是否全选
     */
    private Boolean allSelected;
}
