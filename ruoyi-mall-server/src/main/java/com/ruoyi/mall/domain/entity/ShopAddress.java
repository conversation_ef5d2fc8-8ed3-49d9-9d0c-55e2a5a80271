package com.ruoyi.mall.domain.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商家发货地址表
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop_address")
public class ShopAddress extends BaseEntity {

    /**
     * 商家地址ID
     */
    private Long id;
    /**
     * 商家ID
     */
    private Long shopId;
    /**
     * 发货人
     */
    private String sendName;
    /**
     * 发货人电话
     */
    private String sendPhone;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区
     */
    private String district;
    /**
     * 街道
     */
    private String town;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 是否是默认地址（):否，1：是）
     */
    private String isDefault;

    /**
     * 删除标志（0：否，1：是）
     */
    private String delFlag;
}
