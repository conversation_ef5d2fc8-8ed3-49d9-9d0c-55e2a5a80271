package com.ruoyi.mall.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 修改订单收货地址DTO
 */
@Data
public class UpdateOrderAddressDTO {

    /**
     * 订单编号
     */
    @NotBlank(message = "订单编号不能为空")
    private String orderNo;

    /**
     * 收货人姓名
     */
    @NotBlank(message = "收货人姓名不能为空")
    private String receiverName;

    /**
     * 收货人电话
     */
    @NotBlank(message = "收货人电话不能为空")
    private String receiverPhone;

    /**
     * 收货人省份
     */
    @NotBlank(message = "收货人省份不能为空")
    private String receiverProvince;

    /**
     * 收货人城市
     */
    @NotBlank(message = "收货人城市不能为空")
    private String receiverCity;

    /**
     * 收货人区域
     */
    @NotBlank(message = "收货人区域不能为空")
    private String receiverDistrict;

    /**
     * 收货人详细地址
     */
    @NotBlank(message = "收货人详细地址不能为空")
    private String receiverAddress;
}
