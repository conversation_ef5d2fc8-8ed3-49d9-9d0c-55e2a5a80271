package com.ruoyi.mall.domain.vo;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家平台促销金 请求VO
 */

@Data
@ExcelIgnoreUnannotated
public class ShopPromotionVO implements Serializable {

    private static final long serialVersionUID = 1L;

//    /** 1.1 商家ID */
//    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "商家ID")
    @ColumnWidth(15)
    private Long userId;

    /**
     * 订单单号
     */
    @ExcelProperty(value = "订单编号")
    @ColumnWidth(25)
    private String orderNo;

    //商家信息
    /**
     * 1.2 商家名称
     */
    @ExcelProperty(value = "商家名称")
    @ColumnWidth(20)
    private String name;

    /**
     * 1.3 联系电话
     */
    @ExcelProperty(value = "联系电话")
    @ColumnWidth(15)
    private String phone;

    /**
     * 1.4 商家简介
     */
    private String introduction;

    /**
     * 1.5 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    // 平台促销金情况
    /**
     * 平台抵促销总额
     */
    private BigDecimal promotionTotal;

    /**
     * 已使用平台促销金
     */
    @ExcelProperty(value = "已用金额")
    @ColumnWidth(15)
    private BigDecimal promotionUsed;

    /**
     * 2.2 平台剩余促销金
     */
    private BigDecimal platformPromotionGold;

    /**
     * 最近使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUsedTime;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "使用时间")
    @ColumnWidth(25)
    private Date payTime;

    /**
     * 使用时间开始
     */
    private String useTimeStart;

    /**
     * 使用时间结束
     */
    private String useTimeEnd;


}
