package com.ruoyi.mall.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 虚拟订单视图对象
 */
@Data
public class VirtualOrderVo {

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 订单类型（1：商家的，2：用户的）用户暂时只有开通代销
     * 默认为1
     */
    private String orderType;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 支付金额（包含手续费）
     */
    private BigDecimal amount;

    /**
     * 手续费金额（千分之六）
     */
    private BigDecimal serviceFee;

    /**
     * 订单状态（0-待支付，1-已支付，2-已取消，3-已过期，4-已完成）
     */
    private String status;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 支付方式
     */
    private String payType;

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 第三方交易号
     */
    private String transactionId;

    /**
     * 有效期（天）
     */
    private Integer validDays;

    /**
     * 生效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date effectiveTime;

    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /**
     * 商品参数（JSON格式）
     */
    private String productParams;

    /**
     * 商品描述
     */
    private String productDesc;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
