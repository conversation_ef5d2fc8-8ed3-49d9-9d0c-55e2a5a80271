package com.ruoyi.mall.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 用户分量调整DTO
 */
@Data
public class UserComponentAdjustDTO {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 分量值（正数为增加，负数为减少）
     */
    @NotNull(message = "分量值不能为空")
    private BigDecimal componentValue;

    /**
     * 备注
     */
    private String remark;
} 