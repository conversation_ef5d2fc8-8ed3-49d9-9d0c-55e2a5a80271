package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商家钱包交易记录对象 mall_shop_wallet_record
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop_wallet_record")
public class ShopWalletRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 钱包ID
     */
    @NotNull(message = "钱包ID不能为空")
    private Long walletId;

    /**
     * 商家ID
     */
    @NotNull(message = "商家ID不能为空")
    private Long shopId;

    /**
     * 交易单号
     */
    @NotBlank(message = "交易单号不能为空")
    private String tradeNo;

    /**
     * 关联订单号
     */
    private String orderNo;

    /**
     * 交易金额
     */
    @NotNull(message = "交易金额不能为空")
    private BigDecimal amount;

    /**
     * 交易前余额
     */
    private BigDecimal beforeBalance;

    /**
     * 交易后余额
     */
    private BigDecimal afterBalance;

    /**
     * 交易类型（1入账 2出账）
     */
    @NotNull(message = "交易类型不能为空")
    private Integer tradeType;

    /**
     * 交易方式（1销售收入 2提现 3退款 4其他）
     */
    @NotNull(message = "交易方式不能为空")
    private Integer tradeMethod;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 交易状态（0成功 1失败 2处理中）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 月份
     */
    @TableField(exist = false)
    private Date month;
    /**
     * 月交易总数据
     */
    @TableField(exist = false)
    private Double totalAmount;

    /**
     * 交易记录
     */
    @TableField(exist = false)
    private List<ShopWalletRecord> shopWalletRecord;
}
