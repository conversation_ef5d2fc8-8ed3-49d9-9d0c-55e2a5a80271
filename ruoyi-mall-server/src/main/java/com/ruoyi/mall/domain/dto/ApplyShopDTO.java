package com.ruoyi.mall.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 申请商家DTO
 *
 * <AUTHOR>
 */
@Data
public class ApplyShopDTO {

    /**
     * 商家名称
     */
    @NotBlank(message = "商家名称不能为空")
    private String name;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 商家类型（0代销 1企业）
     */
    @NotBlank(message = "商家类型不能为空")
    private String type;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 企业认证省份编码
     */
    @NotBlank(message = "省份编码不能为空")
    private String provinceCode;

    /**
     * 企业认证省份
     */
    @NotBlank(message = "省份不能为空")
    private String province;

    /**
     * 企业认证城市编码
     */
    @NotBlank(message = "城市编码不能为空")
    private String cityCode;
    /**
     * 企业认证城市
     */
    @NotBlank(message = "城市不能为空")
    private String city;

    /**
     * 企业认证区域编码
     */
//    @NotBlank(message = "区域编码不能为空")
    private String districtCode;
    /**
     * 企业认证区域
     */
//    @NotBlank(message = "区域不能为空")
    private String district;

    /**
     * 企业认证编码
     */
//    @NotBlank(message = "街道编码不能为空")
    private String townCode;
    /**
     * 企业认证街道名称
     */
//    @NotBlank(message = "街道名称不能为空")
    private String town;

    /**
     * 企业认证地址
     */
    @NotBlank(message = "企业认证地址")
    private String address;

    /**
     * 营业时间
     */
    private String businessHours;

    /**
     * 商家简介
     */
    private String introduction;

    /**
     * 商家logo
     */
    private String logo;

    /**
     * 企业邮箱
     */
    private String email;

    /**
     * 企业法人
     */
    @NotBlank(message = "企业法人不能为空")
    private String legalPerson;

    /**
     * 企业法人电话
     */
    @NotBlank(message = "法人电话不能为空")
    private String legalPersonPhone;

    /**
     * 企业法人身份证号
     */
    @NotBlank(message = "身份证不能为空")
    @Size(min = 18, max = 18, message = "身份证必须为18位")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$",
        message = "身份证格式不正确")
    private String legalPersonIdCard;

    /**
     * 营业执照
     */
    @NotBlank(message = "营业执照不能为空")
    private String businessLicense;

    /**
     * 营业执照号
     */
    @NotBlank(message = "营业执照号不能为空")
    @Pattern(regexp = "^([0-9A-Z]{18}|[0-9]{15}|[0-9]{13}|[0-9]{8})$", message = "营业执照格式不正确")
    private String businessLicenseNumber;

    /**
     * 统一社会信用代码
     */
    @NotBlank(message = "统一社会信用代码不能为空")
    private String socialCreditCode;

    /**
     * 营业执照有效期
     */
    private String businessLicenseExpireTime;

    /**
     * 其他资质证书
     */
    private String otherCertificate;

    /**
     * 身份证正面
     */
    @NotBlank(message = "身份证正面不能为空")
    private String idCardFront;

    /**
     * 身份证反面
     */
    @NotBlank(message = "身份证反面不能为空")
    private String idCardBack;

    /**
     * 手持身份证照片
     */
    @NotBlank(message = "手持身份证照片不能为空")
    private String idCardHand;
}
