package com.ruoyi.mall.domain.query;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class QueryOrderTypeDTO {

    /**
     * 查询条件
     */
    private String query;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-mm")
    private String startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-mm")
    private String endTime;
    /**
     * 订单类型(1:全部，2：待付款 ,3:待发货,4:待发货，5：已收货，6:已取消，7：待评价 8:售后)
     */
    @NotNull(message = "订单类型不能为空")
    private Integer type;

}
