package com.ruoyi.mall.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商家退货地址业务对象 mall_shop_return_address
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class ShopReturnAddressBo extends BaseEntity {

    /**
     * 地址ID
     */
    @NotNull(message = "地址ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 收件人姓名
     */
    @NotBlank(message = "收件人姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String receiverName;

    /**
     * 收件人电话
     */
    @NotBlank(message = "收件人电话不能为空", groups = {AddGroup.class, EditGroup.class})
    private String receiverPhone;

    /**
     * 省份编码
     */
    @NotNull(message = "省份编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long provinceCode;

    /**
     * 省份名称
     */
    @NotBlank(message = "省份名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String provinceName;

    /**
     * 城市编码
     */
    @NotNull(message = "城市编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long cityCode;

    /**
     * 城市名称
     */
    @NotBlank(message = "城市名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String cityName;

    /**
     * 区县编码
     */
    @NotNull(message = "区县编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long districtCode;

    /**
     * 区县名称
     */
    @NotBlank(message = "区县名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String districtName;

    /**
     * 街道代码
     */
    private Long townCode;

    /**
     * 街道名称
     */
    private String townName;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String detailAddress;

    /**
     * 邮政编码
     */
    private String postCode;

    /**
     * 是否默认（0否 1是）
     */
    @NotBlank(message = "是否默认（0否 1是）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String isDefault;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 备注信息
     */
    private String addressRemark;

    /**
     * 备注
     */
    private String remark;


}
