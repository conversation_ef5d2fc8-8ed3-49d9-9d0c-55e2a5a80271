package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商家银行账户对象 mall_shop_bank_account
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop_bank_account")
public class ShopBankAccount extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 账户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 对公银行账号
     */
    private String bankAccountNumber;

    /**
     * 公司开户名字
     */
    private String bankAccountName;
    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 开户支行
     */
    private String bankBranchName;

    /**
     * 支付宝账号
     */
    private String alipayAccount;

    /**
     * 支付宝实名
     */
    private String alipayRealName;

    /**
     * 微信账号
     */
    private String wechatAccount;

    /**
     * 是否默认账户（0:否,1:是）
     */
    private String isDefault;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
