package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商城用户对象 tz_user
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("tz_user")
public class TzUser extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 用户的分量
     */
    private BigDecimal component;

    /**
     * 补贴金（商家进行赠送，用户不能提现，只能用来购买东西）
     */
    private BigDecimal deductionMoney;
    /**
     * 补贴金上限额度
     */
    private Double deductionMoneyLimit;
    /**
     * 邀请码
     */
    private String InvitationCode;

    /**
     * 绑定别人的电话号码
     */
    private String bindingInvitationCode;

    /**
     * 用户类型(C消费者,CB代销,B商家)
     */
    private String userType;

    /**
     * 邀请人ID,关联user_id
     */
    private Long parentId;

    /**
     * 链类型(C,B)
     */
    private String chainType;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录地址
     */
    private String loginAddress;

    /*
     * 最后登录时间
     */
    private Date loginTime;
    /**
     * 是否第一次已经获取补贴金（0：否，1：是）
     */
    private String isFirst;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市编码
     */
    private String cityCode;
    /**
     * 城市
     */
    private String city;

    /**
     * 区域编码
     */
    private String districtCode;
    /**
     * 区域
     */
    private String district;

    /**
     * 编码
     */
    private String townCode;
    /**
     * 街道名称
     */
    private String town;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 用户是否实名 0:否，1：是
     */
    private String isRealName;
    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 身份证正面
     */
    private String idCardFront;

    /**
     * 身份证反面
     */
    private String idCardBack;

    /**
     * 手持身份证照片
     */
    private String idCardHand;

    /**
     * 证件类型 1：身份证
     */
    private String cardType;

    /**
     * 兑换金支付密码
     */
    private String payPassword;

    /**
     * 分量总量
     */
    @TableField(exist = false)
    private BigDecimal dayAmount;
}
