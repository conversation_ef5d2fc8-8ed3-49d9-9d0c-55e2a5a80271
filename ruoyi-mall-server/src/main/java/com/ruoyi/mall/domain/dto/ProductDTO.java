package com.ruoyi.mall.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.mall.domain.entity.Sku;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 商品对象
 *
 * <AUTHOR>
 */
@Data
public class ProductDTO {

    /**
     * 商品ID
     */
    private Long id;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    @Size(max = 100, message = "商品名称长度不能超过100个字符")
    private String name;

    /**
     * 0:普通商品,1:虚拟商品
     */
    private String type;

    /**
     * 商品封面
     */
    private String cover;

    /**
     * 商品视频
     */
    private String video;

    /**
     * 商品图片，多个图片用逗号分隔
     */
    @NotBlank(message = "商品图片不能为空")
    private String images;

    /**
     * 商品价格
     */
    @NotNull(message = "商品价格不能为空")
    @Min(value = 0, message = "商品价格不能小于0")
    private Double price;

    /**
     * 商品原价
     */
    private Double originalPrice;

    /**
     * 商品库存
     */
    @NotNull(message = "商品库存不能为空")
    @Min(value = 0, message = "商品库存不能小于0")
    private Integer stock;

    /**
     * 商品销量
     */
    private Integer sales;

    /**
     * 商品单位
     */
    private String unit;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品详情
     */
    @NotBlank(message = "商品详情不能为空")
    private String content;

    /**
     * 商品状态（0上架 1下架）
     */
    private String status;

    /**
     * 是否推荐（0否 1是）
     */
    private String isRecommend;

    /**
     * 是否新品（0否 1是）
     */
    private String isNew;

    /**
     * 是否热销（0否 1是）
     */
    private String isHot;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 减库存的方式（1：下单减库存，2付款时减库存：）
     */
    private Integer stockType;

    /**
     * 是否代销
     */
    private Integer isDaixiao;

    /**
     * 代销佣金方式（1：固定金额，2比例%）
     */
    private Integer daixiaoType;

    /**
     * 代销佣金的值
     */
    private Double daixiaoTypeValue;

    /**
     * 规格信息
     */
    @TableField(exist = false)
    private List<Sku> skuList;

    /**
     * 手续费承担方（0店铺 1用户）
     */
    private String feeBearer;

}
