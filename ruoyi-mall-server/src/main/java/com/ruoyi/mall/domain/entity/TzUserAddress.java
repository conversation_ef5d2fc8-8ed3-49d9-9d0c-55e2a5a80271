package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 用户收货地址对象 tz_user_address
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tz_user_address")
public class TzUserAddress extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 地址ID
     */
    @TableId(value = "address_id")
    private Long addressId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 收货人
     */
    @NotNull(message = "收货人不能为空")
    private String receiver;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /** 省份 */
    /**
     * 收货人省份编码
     */
    private Long provinceCode;

    /**
     * 收货人省份
     */
    @NotNull(message = "收货人省份不能为空")
    private String province;

    /**
     * 收货人城市编码
     */
    private String cityCode;
    /**
     * 收货人城市
     */
    @NotNull(message = "收货人城市不能为空")
    private String city;

    /**
     * 收货人区域编码
     */
    private String districtCode;
    /**
     * 收货人区域
     */
    @NotNull(message = "收货人区域不能为空")
    private String district;

    /**
     * 街道编码
     */
    private String townCode;
    /**
     * 街道名称
     */
    private String town;

    /**
     * 详细地址
     */
    @NotNull(message = "详细地址不能为空")
    private String detailAddress;

    /**
     * 是否默认地址（0否 1是）
     */
    private Integer isDefault;
}
