package com.ruoyi.mall.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 货款明细 VO
 */

@Data
public class ShopOrderDetailVO {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("时间")
    private LocalDateTime receiveTime; // 确认收货时间

    @ExcelProperty("商家名称")
    private String shopName; // 商家名称

    @ExcelProperty("手机号")
    private String phone; // 手机号

    @ExcelProperty("订单编号")
    private String orderNo; // 订单编号

    @ExcelProperty("商品名称")
    private String orderName; // 商品名称

    @ExcelProperty("交易金额")
    private BigDecimal payAmount; // 实付金额

    @ExcelProperty("当日总金额")
    private BigDecimal dayTotalAmount; // 当日总金额

}
