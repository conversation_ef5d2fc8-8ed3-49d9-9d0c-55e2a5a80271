package com.ruoyi.mall.domain.bo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class RelationChainBo {

    /**
     * 商城用户ID
     */
    private Long id;

    /**
     * 当前用户名称
     */
    private String username;

    /**
     * 用户类型(C消费者,CB代销,B商家)
     */
    private String userType;

    /**
     * 链类型(C,B)
     */
    private String chainType;

    /**
     * 状态 TzUserStatus
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 平台补贴金上限，根据分量来的
     */
    private Double deductionMoneyLimit;

    /**
     * 企业名称
     */
    private String businessName;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 粉丝数
     */
    private Integer fans;

    /**
     * 企业认证省份编码
     */
    private String provinceCode;

    /**
     * 企业认证省份
     */
    private String province;

    /**
     * 企业认证城市编码
     */
    private String cityCode;
    /**
     * 企业认证城市
     */
    private String city;

    /**
     * 企业认证区域编码
     */
    private String districtCode;
    /**
     * 企业认证区域
     */
    private String district;

    /**
     * 企业认证编码
     */
    private String townCode;
    /**
     * 企业认证街道名称
     */
    private String town;

    /**
     * 企业地址
     */
    private String address;

    /**
     * 权限管理
     * 权限1：1，权限2：2，权限3：3
     */
    private String jurisdiction;

    /**
     * 邀请人ID
     */
    private Long parentId;

    /**
     * 邀请人名称
     */
    private String parentNusername;

    /**
     * 是否达标
     */
    private Integer flag;

    /**
     * 登录地址
     */
    private String loginAddress;

    /**
     * 今日分量
     */
    private BigDecimal todayComponent;

    /**
     * 用户分量
     */
    private BigDecimal component;
}
