package com.ruoyi.mall.domain.vo;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class DaiXiaoInfoVo {

    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 商家id
     */
    private Long shopId;
    /**
     * 产品名称
     */
    private String name;

    /**
     * 商品封面
     */
    private String cover;

    /**
     * 跳转URL
     */
    private String jumpUrl;
    /**
     * 商家电话
     */
    private String phone;

    /**
     * 代销商家电话
     */
    private String consignmentPhone;

    /**
     * 赚钱的代销金
     */
    private BigDecimal price;

    /**
     * 代销人数
     */
    private Integer number;

    /**
     * 状态(0：下架，1：上架)
     */
    private String status;

    /**
     * 当前代销用户是否代销（0：否，1：是）
     */
    private String isConsignment;

    /**
     * 代销佣金方式（1：固定金额，2比例%）
     */
    private String daixiaoType;

    /**
     * 代销佣金的值
     */
    private String daixiaoTypeValue;

    /**
     * 代销状态
     */
    private String daiXiaoStatus;
}
