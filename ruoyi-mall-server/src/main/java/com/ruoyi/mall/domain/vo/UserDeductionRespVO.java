package com.ruoyi.mall.domain.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户平台抵扣金 响应VO
 */
@Data
public class UserDeductionRespVO implements Serializable {


    /**
     * 订单单号
     */
    @ExcelProperty("订单单号")
    private String orderNo;

    /**
     * 昵称(姓名)
     */
    @ExcelProperty("姓名")
    private String nickname;

    /**
     * 手机号码
     */
    @ExcelProperty("手机号")
    private String phone;

    /**
     * 已使用平台抵扣金
     */
    @ExcelProperty("已使用金额")
    private BigDecimal deductionUsed;


    /**
     * 累计使用抵扣金
     */
    @ExcelProperty("累计使用金额")
    private BigDecimal totalDeductionUsed;


    /**
     * 支付时间
     */
    @ExcelProperty("使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;


}
