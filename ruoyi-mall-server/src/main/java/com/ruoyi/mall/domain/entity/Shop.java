package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家信息对象 mall_shop
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop")
public class Shop extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 商家ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商家名称
     */
    private String name;

    /**
     * 品牌
     **/
    private String brand;

    /**
     * 商家类型（0个人 1企业）
     */
    private String type;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 企业认证省份编码
     */
    private String provinceCode;

    /**
     * 企业认证省份
     */
    private String province;

    /**
     * 企业认证城市编码
     */
    private String cityCode;
    /**
     * 企业认证城市
     */
    private String city;

    /**
     * 企业认证区域编码
     */
    private String districtCode;
    /**
     * 企业认证区域
     */
    private String district;

    /**
     * 企业认证编码
     */
    private String townCode;
    /**
     * 企业认证街道名称
     */
    private String town;

    /**
     * 企业认证详细地址
     */
    private String address;

    /**
     * 营业时间
     */
    private String businessHours;

    /**
     * 商家简介
     */
    private String introduction;

    /**
     * 商家logo
     */
    private String logo;

    /**
     * 企业邮箱
     */
    private String email;

    /**
     * 企业法人
     */
    private String legalPerson;

    /**
     * 企业法人电话
     */
    private String legalPersonPhone;

    /**
     * 企业法人身份证号
     */
    private String legalPersonIdCard;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 营业执照号
     */
    private String businessLicenseNumber;

    /**
     * 营业执照有效期
     */
    private String businessLicenseExpireTime;

    /**
     * 其他资质证书
     */
    private String otherCertificate;

    /**
     * 身份证正面
     */
    private String idCardFront;

    /**
     * 身份证反面
     */
    private String idCardBack;

    /**
     * 手持身份证照片
     */
    private String idCardHand;

    /**
     * 认证状态（0未认证 1已认证）
     */
    private String authStatus;

    /**
     * 营业状态（0营业中 1休息中）
     */
    private String businessStatus;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 技术引流次数
     */
    private Integer drainage;
    /**
     * 粉丝数
     */
    private Integer fans;

    /**
     * 代销抵扣金（代销帮忙买卖商品的佣金，不能提现,只能赠送给用户进行购买商品）
     */
    private BigDecimal deductionMoney;

    /**
     * 企业名称
     */
    private String businessName;

    /**
     * 店铺唯一分类ID，第一次创建商品时确定
     */
    private Long shopCategoryId;

    /**
     * 店铺分类名称
     */
    private String shopCategoryName;

    /**
     * 权限管理
     * 权限1：1，权限2：2，权限3：3
     */
    private String jurisdiction;

    /**
     * 权限过期时间
     */
    private Date jurisdictionExpireTime;

    /**
     * 代销权限(0:未开通，1：已开通)
     */
    private String consignmentPermission;

    /**
     * 代销权限过期时间
     */
    private Date consignmentExpireTime;

    /**
     * 代销等级 (0:基础代销, 1:代销权限一, 2:代销权限二)
     */
    private String consignmentLevel;

    /**
     * 代销等级过期时间
     */
    private Date consignmentLevelExpireTime;

    /**
     * 量化值
     */
    private Double quantificationValue;

    /**
     * 累计量化值
     */
    private Double altogetherQuantificationValue;

    /**
     * 量化
     */
    private Double userFaith;

    /**
     * 累计量化
     */
    private Double altogetherUserFaith;

    /**
     * 平台促销金
     */
    private BigDecimal platformPromotionGold;


    /**
     * 量化率（A系统推送过来的）
     */
    private Double quantificationRate;

    /**
     * 开始创建时间
     */
    @TableField(exist = false)
    private Long beginCreateTime;

    /**
     * 结束创建时间
     */
    @TableField(exist = false)
    private Long endCreateTime;

    /**
     * 查询条件 统一社会信用代码或电话号码
     */
    @TableField(exist = false)
    private String socialCreditCodeOrPhone;

    /**
     * 虚拟商品类型
     */
    @TableField(exist = false)
    private Integer productType;

    /**
     * 虚拟商品金额
     */
    @TableField(exist = false)
    private BigDecimal amount;

    /**
     * 虚拟商品支付时间
     */
    @TableField(exist = false)
    private Date payTime;

    /**
     * 商户银行信息
     */
    @TableField(exist = false)
    private ShopBankAccount shopBankAccount;


}
