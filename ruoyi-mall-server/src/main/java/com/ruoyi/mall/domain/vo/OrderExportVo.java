package com.ruoyi.mall.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.ExcelDictFormat;
import com.ruoyi.common.convert.ExcelDictConvert;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单导出对象
 */
@Data
@ExcelIgnoreUnannotated
public class OrderExportVo {

    /**
     * 用户账号
     */
    @ExcelProperty(value = "账号")
    private String userPhone;

    /**
     * 订单编号
     */
    @ExcelProperty(value = "订单号")
    private String orderNo;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=待支付,1=支付中,2=待发货,3=待收货,4=已完成,5=已取消,6=已退款")
    private String status;

    /**
     * 退款状态
     */
    @ExcelProperty(value = "售后状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=申请退款,1=退款成功,2=退款失败")
    private String refundStatus;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String orderName;

    /**
     * 订单总金额
     */
    @ExcelProperty(value = "订单金额")
    private BigDecimal totalAmount;

    /**
     * 订单总数量
     */
    @ExcelProperty(value = "购买数量")
    private Long totalQuantity;

    /**
     * 补贴金金额
     */
    @ExcelProperty(value = "补贴金")
    private BigDecimal deductionAmount;

    /**
     * 支付方式
     */
    @ExcelProperty(value = "支付类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=微信支付,2=支付宝支付,3=用户补贴金,4=支付宝PC端,5=商家平台兑换金")
    private String payType;

    /**
     * 实付金额
     */
    @ExcelProperty(value = "实付金额")
    private BigDecimal payAmount;

    /**
     * 收货人姓名
     */
    @ExcelProperty(value = "收件人")
    private String receiverName;

    /**
     * 收货人电话
     */
    @ExcelProperty(value = "收件人电话")
    private String receiverPhone;

    /**
     * 收货地址
     */
    @ExcelProperty(value = "收货地址")
    private String receiverAddressAll;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;
} 