package com.ruoyi.mall.domain.vo;

import com.ruoyi.mall.domain.entity.OrderItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 订单列表Vo
 */
@Data
public class OrderVo {

    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 商家id
     */
    private Long shopId;
    /**
     * 商家名称
     */
    private String shopName;

    /**
     * 商家logo
     */
    private String shopLogo;
    /**
     * 总价
     */
    private Double TotalAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 实际支付金额
     */
    private Double payAmount;
    /**
     * 支付方式：1-微信支付，2-支付宝支付 3；抵扣金兑换
     */
    private String payType;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 订单状态  PENDING_PAYMENT("0", "待支付"),
     * DURING_PAYMENT("1", "支付中"),
     * PENDING_SHIPMENT("2", "待发货"),
     * PENDING_RECEIVE("3", "待收货"),
     * COMPLETED("4", "已完成"),
     * CANCELLED("5", "已取消"),
     * REFUNDED("6", "退款/售后中")
     */
    private String orderStatus;

    /**
     * 收货方式（1：快递，2自提）
     */
    private String receiveType;

    /**
     * 运费金额
     */
    private BigDecimal freightAmount;

    /**
     * 收货人
     */
    private String receiveName;
    /**
     * 收货人电话
     */
    private String receivePhone;

    /**
     * 收货人省份
     */
    private String receiverProvince;

    /**
     * 收货人城市
     */
    private String receiverCity;

    /**
     * 收货人区域
     */
    private String receiverDistrict;

    /**
     * 收货人详细地址
     */
    private String receiverAddress;

    /**
     * 收货人全详细地址省+市+区+详细地址
     */
    private String receiverAddressAll;
    /**
     * 收货时间
     */
    private Date receiveTime;

    /**
     * 快递公司名称
     */
    private String deliveryName;

    /**
     * 快递单号
     */
    private String deliveryNumber;
    /**
     * 订单退款状态（1:申请退款中 2:退款成功  3:退款失败）
     */
    private String refundStatus;

    /**
     * 订单商品项ID
     */
    private List<OrderItem> items;

    /**
     * 抵扣金额
     */
    private BigDecimal deductionAmount;

    /**
     * 手续费
     */
    private BigDecimal feeAmount;

    /**
     * 第三方交易账号
     */
    private String transactionId;

}
