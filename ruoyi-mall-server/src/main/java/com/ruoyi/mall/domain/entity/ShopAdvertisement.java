package com.ruoyi.mall.domain.entity;


import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop_advertisement ")
public class ShopAdvertisement extends BaseEntity {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 广告名称
     */
    @NotBlank(message = "广告名称不能为空")
    @Size(max = 50, message = "广告名称长度不能超过50个字符")
    private String adName;

    /**
     * 广告类型（1：图片，2：视频）
     */
    @NotBlank(message = "广告类型不能为空")
    @Pattern(regexp = "^[12]$", message = "广告类型不正确，只能是图片(1)或视频(2)")
    private String adType;

    /**
     * 广告位置 0 :首页 1:分享 2:商区
     */
    private String type;

    /**
     * 广告展示内容
     */
    @NotBlank(message = "广告展示内容不能为空")
    private String adUrl;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 截止时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 状态 0:待审核，1：待支付，2：已生效 3：拒绝
     */
    private String status;

    /**
     * 点击次数
     */
    private Integer clickNumber;

    /**
     * 点击跳转链接
     */
    private String jumpUrl;

    /**
     * 删除标志（0:正常，1：删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;

}
