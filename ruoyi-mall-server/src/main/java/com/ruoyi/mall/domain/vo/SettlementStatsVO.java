package com.ruoyi.mall.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 结算统计 VO
 *
 * <AUTHOR>
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SettlementStatsVO {

    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;


    /**
     * 结算日期
     */
    @ExcelProperty("结算日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime settlementDate;


    /**
     * 商家名称
     */
    @ExcelProperty("商家名称")
    private String shopName;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    private String phone;


    /**
     * 交易金额(银行卡)
     */
    @ExcelProperty("银行卡")
    private BigDecimal bankCardActualAmount;


    /**
     * 当天累计金额
     */
    @ExcelProperty("当天累计金额")
    private BigDecimal totalAmount;


}
