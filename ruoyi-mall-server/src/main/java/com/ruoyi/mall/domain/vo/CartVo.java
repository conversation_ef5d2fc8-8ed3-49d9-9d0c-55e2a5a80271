package com.ruoyi.mall.domain.vo;


import lombok.Data;

import java.math.BigDecimal;

/**
 * 购物车商品信息
 */

@Data
public class CartVo {

    /**
     * ID
     */
    private Long cartId;
    /**
     * 商家或者代销ID
     */
    private Long shopId;

    /**
     * 店铺状态（0：正常，1：停用）
     */
    private String shopStatus;

    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品状态（0下架 1上架）
     */
    private String productStatus;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品图片
     */
    private String productImage;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 库存数量
     */
    private Integer stock;
    /**
     * sku状态（0正常 1停用）
     */
    private String skuStatus;

    /**
     * sku值
     */
    private String skuValue;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 总价格
     */
    private BigDecimal totalAmount;

    /**
     * 是否选中：0-未选中，1-已选中
     */
    private String selected;

    /**
     * 状态 0：正常，1；sku不存在，2：商品下架,3：库存不足，4：商品删除，5：店铺不存在
     */
    private String status;
    /**
     * 手续费承担方（0店铺 1用户）
     */
    private String feeBearer;

    /**
     * 手续费
     */
    private BigDecimal feeAmount;
}
