package com.ruoyi.mall.domain.dto;


import lombok.Data;


/**
 * 开通代销权限
 */
@Data
public class OpenPermissionsDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 用户名称
     */
    private String userName;

//    /**
//     * 开始时间不能为空
//     */
//    @NotNull(message = "开始时间不能为空")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private Date startTime;
//
//    /**
//     * 结束时间不能为空
//     */
//    @NotNull(message = "结束时间不能为空")
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    private  Date endTime;

}
