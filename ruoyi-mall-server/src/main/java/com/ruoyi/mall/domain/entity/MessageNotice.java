package com.ruoyi.mall.domain.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("mall_message_notice")
public class MessageNotice {

    /**
     * 消息哦对
     */
    private Long id;
    /**
     * 接收者ID
     */
    private String userId;
    /**
     * 消息类型
     */
    private String messageType;
    /**
     * 消息标题
     */
    private String title;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 是否已读(0:否，1：是)
     */
    private String isRead;
    /**
     * 删除标志(0：否，2：是)
     */
    private String delFLag;


}
