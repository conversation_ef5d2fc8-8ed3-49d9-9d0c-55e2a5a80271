package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 管理员核销记录
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user_donation_record")
public class SysUserDonationRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 核销金额
     */
    @NotNull(message = "核销金额不能为空")
    private BigDecimal amount;

    /**
     * 核销状态：0-失败，1-成功
     */
    private String status;

    /**
     * 核销时间
     */
    private Date donationTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 操作密码
     */
    @TableField(exist = false)
    @NotBlank(message = "操作密码不能为空")
    private String operationPassword;

    /**
     * 开始时间
     */
    @TableField(exist = false)
    private String beginTime;

    /**
     * 结束时间
     */
    @TableField(exist = false)
    private String endTime;

}
