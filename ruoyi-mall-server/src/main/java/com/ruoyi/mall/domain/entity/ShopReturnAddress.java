package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商家退货地址对象 mall_shop_return_address
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop_return_address")
public class ShopReturnAddress extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 地址ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 商家ID
     */
    private Long shopId;
    /**
     * 收件人姓名
     */
    private String receiverName;
    /**
     * 收件人电话
     */
    private String receiverPhone;
    /**
     * 省份编码
     */
    private Long provinceCode;
    /**
     * 省份名称
     */
    private String provinceName;
    /**
     * 城市编码
     */
    private Long cityCode;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 区县编码
     */
    private Long districtCode;
    /**
     * 区县名称
     */
    private String districtName;

    /**
     * 街道代码
     */
    private Long townCode;

    /**
     * 街道名称
     */
    private String townName;

    /**
     * 详细地址
     */
    private String detailAddress;
    /**
     * 是否默认（0否 1是）
     */
    private String isDefault;
    /**
     * 状态（0正常 1停用）
     */
    private String status;
    /**
     * 备注信息
     */
    private String addressRemark;
    /**
     * 备注
     */
    private String remark;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
