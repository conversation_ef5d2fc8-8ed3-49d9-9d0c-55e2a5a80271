package com.ruoyi.mall.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 平台兑换金请求项DTO
 */
@Data
public class ExchangeFundDTO {

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String phone;

    /**
     * 兑换金额
     */
    @NotNull(message = "兑换金额不能为空")
    @Positive(message = "兑换金额必须大于0")
    private BigDecimal amount;

    /**
     * 备注
     */
    private String remark;
} 