package com.ruoyi.mall.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class TzUserInfo {

    /**
     * C端显示用户id，B/CB显示商家id
     */
    private Long id;

    /**
     * 用户名/公司名称
     */
    private String name;

    /**
     * 用户昵称/商家店铺名称/代销用户名称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;
    /**
     * B端显示引流次数,CB显示代销产品数量(C端不显示)
     */
    private Integer number;
    /**
     * B端显示货款，C/CB显示抵扣金
     */
    private BigDecimal deductionMoney;

    /**
     * C显额度上限
     */
    private Double deductionMoneyLimit;

    /**
     * B端显示量化(C/CB不显示)
     */
    private Double credit;

    /**
     * 代付款订单数量
     */
    private Integer pendingOrderCount;

    /**
     * 待发货订单数量
     */
    private Integer payOrderCount;
    /**
     * 待收货订单数量
     */
    private Integer deliverOrderCount;
    /**
     * 待评价订单数量
     */
    private Integer receivedOrderCount;
    /**
     * 退款订单数量
     */
    private Integer refundOrderCount;

}
