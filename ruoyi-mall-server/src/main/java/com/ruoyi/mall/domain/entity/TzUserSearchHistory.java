package com.ruoyi.mall.domain.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("mall_user_search_history")
public class TzUserSearchHistory extends BaseEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 搜索关键字
     */
    private String keyword;

    /**
     * 搜索时间
     */
    private Date createTime;
}
