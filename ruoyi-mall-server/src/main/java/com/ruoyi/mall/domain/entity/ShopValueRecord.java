package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商家量化值、量化、平台促销金转换记录
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop_value_record")
public class ShopValueRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 操作类型
     * 1：量化值转量化
     * 2：量化转平台促销金
     * 3：量化值转量化损耗
     * 4：量化转平台促销金损耗
     */
    private Integer operationType;

    /**
     * 转换前量化值
     */
    private Double beforeQuantificationValue;

    /**
     * 转换后量化值
     */
    private Double afterQuantificationValue;

    /**
     * 转换量化值数量
     */
    private Double quantificationValueAmount;

    /**
     * 转换前量化
     */
    private Double beforeUserFaith;

    /**
     * 转换后量化
     */
    private Double afterUserFaith;

    /**
     * 转换量化数量
     */
    private Double userFaithAmount;

    /**
     * 转换前平台促销金
     */
    private BigDecimal beforePromotionGold;

    /**
     * 转换后平台促销金
     */
    private BigDecimal afterPromotionGold;

    /**
     * 转换平台促销金数量
     */
    private Double promotionGoldAmount;

    /**
     * 转换率（量化值到量化或量化到平台促销金的比率）
     */
    private Double conversionRate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
