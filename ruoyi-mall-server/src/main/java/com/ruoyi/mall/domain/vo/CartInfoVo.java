package com.ruoyi.mall.domain.vo;


import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 购物车列表
 */
@Data
public class CartInfoVo {

    /**
     * 商家类型，0：代销，1：商家
     */
    private String type;
    /**
     * 商家ID
     */
    private Long shopId;
    /**
     * 商家名称
     */
    private String shopName;

    /**
     * 商家logo
     */
    private String shopLogo;

    /**
     * 商品列表
     */
    private List<CartVo> cart;

    /**
     * 备注
     */
    private String remark;

    /**
     * 总手续费
     */
    private BigDecimal totalFeeAmount;
}
