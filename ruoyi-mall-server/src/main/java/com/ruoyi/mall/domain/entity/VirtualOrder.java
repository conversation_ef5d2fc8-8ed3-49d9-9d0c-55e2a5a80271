package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 虚拟订单实体对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_virtual_order")
public class VirtualOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 电话号码
     */
    private String phone;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 订单类型（1：商家的，2：用户的）用户暂时只有开通代销
     * 默认为1
     */
    private String orderType;

    /**
     * 商品类型
     */
    private Integer productType;

    /**
     * 相关的业务ID（商品类型为6时，为广告ID）
     */
    private Long orderId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 支付金额（包含手续费）
     */
    private BigDecimal amount;

    /**
     * 手续费金额（千分之六）
     */
    private BigDecimal serviceFee;

    /**
     * 订单状态 OrderVirtualStatusEnum
     */
    private String status;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 支付方式
     */
    private String payType;

    /**
     * 第三方交易号
     */
    private String transactionId;

    /**
     * 有效期（天）
     */
    private Integer validDays;

    /**
     * 生效时间
     */
    private Date effectiveTime;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 删除标志（0:代表存在 2:代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 开始时间
     */
    @TableField(exist = false)
    private String startTime;

    /**
     * 结束时间
     */
    @TableField(exist = false)
    private String endTime;

}
