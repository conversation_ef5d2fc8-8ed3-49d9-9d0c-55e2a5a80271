package com.ruoyi.mall.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 货款明细实体类
 */
@Data
public class ShopOrderDetail extends BaseEntity {

    @ExcelProperty(value = "时间", index = 0)
    private LocalDateTime receiveTime;

    @ExcelProperty(value = "商家名称", index = 1)
    private String shopName;

    @ExcelProperty(value = "手机号", index = 2)
    private String phone;

    @ExcelProperty(value = "订单信息", index = 3)
    private String orderInfo;

    @ExcelProperty(value = "交易金额", index = 4)
    private BigDecimal payAmount;

    @ExcelProperty(value = "当日总金额", index = 5)
    private BigDecimal dayTotalAmount;

    public ShopOrderDetail(LocalDateTime receiveTime, String shopName, String phone,
                           String orderNo, String orderName, BigDecimal payAmount,
                           BigDecimal dayTotalAmount) {
        this.receiveTime = receiveTime;
        this.shopName = shopName;
        this.phone = phone;
        this.orderInfo = "订单编号：" + orderNo + "\n商品名称：" + orderName;
        this.payAmount = payAmount;
        this.dayTotalAmount = dayTotalAmount;
    }

}
