package com.ruoyi.mall.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class ReceiveQuantizedBo {

    /**
     * 量化值进化量,
     */
    @NotEmpty(message = "量化值进化量不能为空")
    private String quantifyToCredit;

    /**
     * 量化进化量,
     */
    @NotEmpty(message = "量化进化量不能为空")
    private String creditToCoupon;


    /**
     * 量化率
     */
    @NotEmpty(message = "量化率不能为空")
    private String quantifyRate;

    /**
     * 日期
     */
    @NotNull(message = "日期不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date quantifyDate;
}
