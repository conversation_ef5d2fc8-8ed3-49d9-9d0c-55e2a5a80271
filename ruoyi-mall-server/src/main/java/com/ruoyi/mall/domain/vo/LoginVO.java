package com.ruoyi.mall.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 商城用户登录响应对象
 *
 * <AUTHOR>
 */
@Data
public class LoginVO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 登录令牌
     */
    private String token;

    /**
     * 分量
     **/
    private BigDecimal component;

    /**
     * 用户类型(1消费者,2商家,,3代销)
     **/
    private Integer role;

    /**
     * 是否实名 0：否，1：是
     */
    private String isRoleName;

}
