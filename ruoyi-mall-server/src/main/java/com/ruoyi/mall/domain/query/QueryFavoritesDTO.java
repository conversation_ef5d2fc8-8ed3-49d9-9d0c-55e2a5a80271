package com.ruoyi.mall.domain.query;


import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class QueryFavoritesDTO {

    /**
     * 页数
     */
    @NotNull(message = "页数不能为空")
    private Integer pageNum;

    /**
     * 每页数据
     */
    @NotNull(message = "每页数据不能为空")
    private Integer pageSize;

    /**
     * 搜索内容
     */
    private String query;
    /**
     * 根据时间排序 (1：正序，2：倒序)
     */
    private String timeSort;
    /**
     * 根据价格排序(1：正序，2：倒序)
     */
    private String priceSort;
}
