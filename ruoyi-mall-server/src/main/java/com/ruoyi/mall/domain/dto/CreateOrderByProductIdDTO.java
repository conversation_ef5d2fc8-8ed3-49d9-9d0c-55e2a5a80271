package com.ruoyi.mall.domain.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 立即购买参数
 */
@Data
public class CreateOrderByProductIdDTO {

    /**
     * 商品id
     */
    @NotNull(message = "商品ID不能为空")
    private Long productId;
    /**
     * 数量
     */
    @Min(value = 1, message = "数量不能小于1")
    @NotNull(message = "数量不能为空")
    private Integer quantity;
    /**
     * 商品skuId
     */
    @NotNull(message = "商品skuId不能为空")
    private Long skuId;
    /**
     * 商家ID
     */
    @NotNull(message = "商家ID不能为空")
    private Long shopId;

    /**
     * 收货方式（1：快递，2自提）
     */
    @NotNull(message = "收货方式不能为空")
    @Pattern(regexp = "^[1-2]$", message = "接收者类型只能是1或2")
    private String receiverType;

    /**
     * 收货人姓名
     */
    @NotEmpty(message = "收货人姓名不能为空")
    private String receiverName;

    /**
     * 收货人电话
     */
    @NotEmpty(message = "收货人电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String receiverPhone;

    /**
     * 收货人省份
     */
    @NotEmpty(message = "收货人省份不能为空")
    private String receiverProvince;

    /**
     * 收货人城市
     */
    @NotEmpty(message = "收货人城市不能为空")
    private String receiverCity;

    /**
     * 收货人区域
     */
    @NotEmpty(message = "收货人区域不能为空")
    private String receiverDistrict;

    /**
     * 收货人详细地址
     */
    @NotEmpty(message = "收货人详细地址不能为空")
    private String receiverAddress;

    /**
     * 订单备注
     */
    private String remark;

    /**
     * 支付方式 1：微信，2：支付宝 ，3：抵扣金支付
     */
    private String payType;

}
