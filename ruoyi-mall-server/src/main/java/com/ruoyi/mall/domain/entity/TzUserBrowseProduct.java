package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("mall_user_browse_product")
public class TzUserBrowseProduct extends BaseEntity {

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 产品id
     */
    private Long productId;
    /**
     * 第一次访问时间
     */
    private Date createTime;
    /**
     * 访问次数
     */
    private int number;
    /**
     * 最近一次访问时间
     */
    private Date lastTime;

    /**
     * 商铺id
     */
    @TableField(exist = false)
    private Long shopId;
    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String name;
    /**
     * 产品价格
     */
    @TableField(exist = false)
    private BigDecimal price;
    /**
     * 产品封面
     */
    @TableField(exist = false)
    private String cover;

}
