package com.ruoyi.mall.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * 订单发货
 */
@Data
public class ShipmentOderDto {

    /**
     * 订单编号
     */
    @NotBlank(message = "订单编号不能为空")
    private String orderNo;

    /**
     * 配送类型
     */
    @NotBlank(message = "未选择配送类型")
    private String receiverType;

    /**
     * 快递公司ID
     */
    private Long deliveryId;

    /**
     * 快递单号
     */
    private String deliveryNumber;

}
