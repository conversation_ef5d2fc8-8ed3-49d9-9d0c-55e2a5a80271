package com.ruoyi.mall.domain.dto;

/**
 * 账号注销状态 请求DTO
 *
 * <AUTHOR>
 */

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AccountStatusDTO {
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @NotNull(message = "CB订单状态不能为空")
    private Integer cbOrderLastSettledDays;

    @NotNull(message = "CB货款结算状态不能为空")
    private Integer hasPendingCbSettlement;

    @NotNull(message = "代销商品状态不能为空")
    private Integer hasActiveConsignGoods;

    @NotNull(message = "可注销状态不能为空")
    private Integer isDeleteable;
}
