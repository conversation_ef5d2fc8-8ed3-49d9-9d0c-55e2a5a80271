package com.ruoyi.mall.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

/**
 * 商品导出对象
 */
@Data
public class ProductExportVo {

    /**
     * 商品ID
     */
    @ExcelProperty("商品ID")
    private Long id;

    /**
     * 商品名称
     */
    @ExcelProperty("商品名称")
    private String name;

    /**
     * 商品分类
     */
    @ExcelProperty("商品分类")
    private String categoryName;

    /**
     * 商品价格
     */
    @ExcelProperty("商品价格")
    private Double price;

    /**
     * 商品库存
     */
    @ExcelProperty("商品库存")
    private Integer stock;

    /**
     * 商品销量
     */
    @ExcelProperty("商品销量")
    private Integer sales;

    /**
     * 上架状态
     */
    @ExcelProperty("上架状态")
    private String status;

    /**
     * 是否推荐
     */
    @ExcelProperty("是否推荐")
    private String isRecommend;

    /**
     * 是否新品
     */
    @ExcelProperty("是否新品")
    private String isNew;

    /**
     * 是否热销
     */
    @ExcelProperty("是否热销")
    private String isHot;

    /**
     * 创建时间
     */
    @ColumnWidth(30)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("创建时间")
    private Date createTime;
}
