package com.ruoyi.mall.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 结算审核VO对象
 */
@Data
public class SettlementAuditVo {

    /**
     * 结算单ID
     */
    @NotNull(message = "结算单ID不能为空")
    private Long id;

    /**
     * 审核状态（1通过 2拒绝）
     */
    @NotBlank(message = "审核状态不能为空")
    private String status;

    /**
     * 审核意见
     */
    private String auditRemark;
}
