package com.ruoyi.mall.domain.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;


/**
 * 商品属性配置
 */
@Data
@TableName("mall_shop_product_attribute_config")
public class ShopProductAttributeConfig extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 商品id
     */
    private Long productId;
   /**
     * 属性类型 (0:推荐，1：新品，2：热卖)
     */
    private String type;
    /**
     * 状态 0:未支付，1：已支付,3:支付失败
     */
    private String status;
    /**
     * 开始时间
     */
    private Date startTime;
   /**
     * 结束时间
     */
    private Date endTime;
}
