package com.ruoyi.mall.domain.vo;


import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家平台促销金 响应VO
 */

@Data
public class ShopPromotionRespVO implements Serializable {


    /**
     * 订单单号
     */
    @ExcelProperty("订单单号")
    private String orderNo;


    //商家信息
    /**
     * 1.2 商家名称
     */
    @ExcelProperty("商家名称")
    private String name;

    /**
     * 1.3 联系电话
     */
    @ExcelProperty("联系电话")
    private String phone;


    /**
     * 已使用平台促销金
     */
    @ExcelProperty("已用金额")
    private BigDecimal promotionUsed;


    /**
     * 累计使用促销金
     */
    @ExcelProperty("累计使用金额")
    private BigDecimal totalPromotionUsed;


    /**
     * 支付时间
     */
    @ExcelProperty("使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;


}
