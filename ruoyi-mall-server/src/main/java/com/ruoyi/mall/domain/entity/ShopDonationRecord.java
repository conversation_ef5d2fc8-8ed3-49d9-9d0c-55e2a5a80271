package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商家促销金赠送记录对象 mall_shop_donation_record
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_shop_donation_record")
public class ShopDonationRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 接收用户ID
     */
    private Long userId;

    /**
     * 接收用户手机号
     */
    private String receiverPhone;

    /**
     * 赠送金
     */
    private BigDecimal amount;

    /**
     * 千六损耗金额
     */
    private BigDecimal lossAmount;

    /**
     * 实际到账金额
     */
    private BigDecimal actualAmount;

    /**
     * 赠送状态：0-失败，1-成功
     */
    private String status;

    /**
     * 赠送类型：1-商家赠送，2-代销赠送
     */
    private String donationType;

    /**
     * 赠送时间
     */
    private Date donationTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
}
