package com.ruoyi.mall.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 商家银行账户DTO
 */
@Data
public class ShopBankAccountDTO {

    /**
     * 账户ID（新增时为空）
     */
    private Long id;

    /**
     * 对公银行账号
     */
    @NotBlank(message = "对公银行账号不能为空")
    private String bankAccountNumber;

    /**
     * 公司开户名字
     */
    @NotBlank(message = "公司开户名字不能为空")
    private String bankAccountName;

    /**
     * 银行名称
     */
    @NotBlank(message = "银行名称不能为空")
    private String bankName;

    /**
     * 开户支行
     */
    @NotBlank(message = "开户支行不能为空")
    private String bankBranchName;

    /**
     * 支付宝账号
     */
    private String alipayAccount;

    /**
     * 支付宝实名
     */
    private String alipayRealName;

    /**
     * 微信账号
     */
    private String wechatAccount;

    /**
     * 是否默认账户（0:否,1:是）
     */
    private String isDefault;

    /**
     * 备注
     */
    private String remark;
}
