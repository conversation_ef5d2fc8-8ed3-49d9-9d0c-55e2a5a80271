package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 规格对象 mall_sku
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_sku")
public class Sku extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 规格ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 规格名称
     */
    @NotBlank(message = "规格名称不能为空")
    private String name;

    /**
     * 规格值，多个规格值用逗号分隔
     */
    private String value;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 规格图片
     */
    @NotBlank(message = "规格图片不能为空")
    private String image;

    /**
     * 规格价格
     */
    @NotNull(message = "规格价格不能为空")
    @Min(value = 0, message = "商品库存不能小于0")
    private Double price;

    /**
     * 规格库存
     */
    @NotNull(message = "规格库存不能为空")
    @Min(value = 0, message = "商品库存不能小于0")
    private Double stock;

    /**
     * 规格编码
     */
    private String code;

    /**
     * 规格状态（0正常 1停用）
     */
    private String status;

    /**
     * 商品信息
     */
    @TableField(exist = false)
    private Product product;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("productId", getProductId())
            .append("name", getName())
            .append("value", getValue())
            .append("image", getImage())
            .append("price", getPrice())
            .append("stock", getStock())
            .append("code", getCode())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
