package com.ruoyi.mall.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_product_evaluate")
public class ProductEvaluate extends BaseEntity {

    /**
     * 评价id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 商品id
     */
    private Long productId;
    /**
     * 图片
     */
    private String img;
    /**
     * 视频
     */
    private String video;
    /**
     * 评价内容
     */
    @NotBlank(message = "评价内容不能为空")
    private String content;
    /**
     * 评价程度（1：一星,2:两星，3：三星，4：四星，5：五星）
     */
    @NotNull(message = "评价等级不能为空")
    @Min(value = 1, message = "评价等级至少为1")
    @Max(value = 5, message = "评价等级最多为5")
    private Integer level;
    /**
     * 是否匿名（0：否，1：是）
     */
    private String isShow;
    /**
     * 0：审核中，1:审核通过，2：不通过
     */
    private String status;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单项ID
     */
    @NotNull(message = "订单项ID不能为空")
    private Long itemId;

    /**
     * 商品skuID
     */
    private Long skuId;

    /**
     * 点赞数
     */
    private int likeCount;

    /**
     * 商家回复
     */
    private String reply;
    /**
     * 商家回复时间
     */
    private Date replyTime;

    /**
     * 评价者
     */
    @TableField(exist = false)
    private String userName;
    /**
     * 用户头像
     */
    @TableField(exist = false)
    private String avatar;

    /**
     * 商品信息
     */
    @TableField(exist = false)
    private String productName;

    /**
     * 商品图片
     */
    @TableField(exist = false)
    private String productImg;

    /**
     * 商品sku
     */
    @TableField(exist = false)
    private String sku;

}
