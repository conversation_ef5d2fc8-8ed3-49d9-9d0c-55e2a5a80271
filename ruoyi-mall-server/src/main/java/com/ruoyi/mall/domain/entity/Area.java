package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("area")
public class Area {

    /**
     * 地区编号
     */
    private Long code;

    /**
     * 地区名称
     */
    private String name;

    /**
     * 地区等级
     */
    private Integer level;

    /**
     * 父级编号
     */
    private Long parentId;

    /**
     * 首字母
     */
    private String initial;

    /**
     * 拼音
     */
    private String pinyin;


}
