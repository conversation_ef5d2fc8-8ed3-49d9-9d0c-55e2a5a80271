package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tz_user_real_name")
public class TzUserRealName extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市编码
     */
    private String cityCode;
    /**
     * 城市
     */
    private String city;

    /**
     * 区域编码
     */
    private String districtCode;
    /**
     * 区域
     */
    private String district;

    /**
     * 编码
     */
    private String townCode;
    /**
     * 街道名称
     */
    private String town;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 身份证正面
     */
    private String idCardFront;

    /**
     * 身份证反面
     */
    private String idCardBack;

    /**
     * 手持身份证照片
     */
    private String idCardHand;

    /**
     * 证件类型 1：身份证
     */
    private String cardType;

    /**
     * 状态 0：审核中，1：审核通过  2：拒绝，3：重新提交
     */
    private String status;

    /**
     * 拒绝原因
     */
    private String reason;

    /**
     * 审核人ID
     */
    private Long auditorId;

    /**
     * 审核人名称
     */
    private String auditorName;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;


}
