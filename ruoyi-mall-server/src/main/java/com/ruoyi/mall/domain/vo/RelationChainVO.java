package com.ruoyi.mall.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 关系链视图对象
 */
@Data
public class RelationChainVO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邀请人用户名
     */
    private String parentNusername;

    /**
     * 状态（0正常 1禁用 2睡眠 3失效 4无效 5封号）
     */
    private String status;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 权限（1权限一 2权限二 3权限三）
     */
    private String jurisdiction;

    /**
     * 用户名
     */
    private String username;

    /**
     * 粉丝量
     */
    private Integer fans;

    /**
     * 达标状态（0未达标 1达标1 2达标2）
     */
    private Integer flag;

    /**
     * 上限额度
     */
    private BigDecimal deductionMoneyLimit;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区/县
     */
    private String district;

    /**
     * 镇/街道
     */
    private String town;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 登录地址/定位
     */
    private String loginAddress;

    /**
     * 是否是商家（true:是 false:否）
     */
    private Boolean isMerchant;
}
