package com.ruoyi.mall.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 用户关注商铺
 */

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_user_focus")
public class TZUserFocus extends BaseEntity {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商铺ID
     */
    private Long shopId;

    /**
     * 0：关注，1：取消关注
     */
    private String status;

    /**
     * 商铺名称
     */
    @TableField(exist = false)
    private String shopName;

    /**
     * 商铺logo
     */
    @TableField(exist = false)
    private String shopLogo;
}
