package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 商家快递地址 mall_cart
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("mall_delivery")
@Builder
public class Delivery extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 快递公司名称 添加与修改是不能为空
     */
    @NotBlank(message = "快递公司名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String deliveryName;

    /**
     * 快递公司编码
     */
    @NotBlank(message = "快递公司编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String deliveryCode;

}
