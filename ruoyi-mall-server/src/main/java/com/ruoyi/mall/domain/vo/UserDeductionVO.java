package com.ruoyi.mall.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户平台抵扣金信息视图对象
 */
@Data
@ExcelIgnoreUnannotated
public class UserDeductionVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    @ColumnWidth(15)
    private Long userId;

    /**
     * 订单单号
     */
    @ExcelProperty(value = "订单编号")
    @ColumnWidth(25)
    private String orderNo;

    /**
     * 用户名
     */
    @ExcelProperty(value = "用户名")
    @ColumnWidth(20)
    private String username;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号")
    @ColumnWidth(15)
    private String phone;

    /**
     * 性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 平台抵扣金总额
     */
    private BigDecimal deductionTotal;

    /**
     * 已使用平台抵扣金
     */
    @ExcelProperty(value = "已用金额")
    @ColumnWidth(15)
    private BigDecimal deductionUsed;

    /**
     * 剩余平台抵扣金
     */
    private BigDecimal deductionBalance;

    /**
     * 最近使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUsedTime;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "使用时间")
    @ColumnWidth(25)
    private Date payTime;

    /**
     * 使用时间开始
     */
    private String useTimeStart;

    /**
     * 使用时间结束
     */
    private String useTimeEnd;
}
