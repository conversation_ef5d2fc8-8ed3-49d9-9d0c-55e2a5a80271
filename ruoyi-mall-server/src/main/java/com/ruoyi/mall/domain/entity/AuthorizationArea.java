package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("authorization_area")
public class AuthorizationArea {

    @TableId(type = IdType.AUTO)
    private Long authorizationAreaId;

    /**
     * 授权ID
     */
    private String id;

    /**
     * 授权手机号
     */
    private String phone;

    /**
     * 授权地区ID
     */
    private String areaId;

    /**
     * 授权开始时间
     */
    private Date startTime;

    /**
     * 授权结束时间
     */
    private Date endTime;

    /**
     * 授权类型
     */
    private String type;

    /**
     * 量化率
     */
    private String value;

    /**
     * 更新时间
     */
    private Date updateTime;

}
