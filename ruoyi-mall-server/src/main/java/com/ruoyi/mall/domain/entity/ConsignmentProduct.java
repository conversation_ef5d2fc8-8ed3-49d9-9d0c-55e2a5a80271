package com.ruoyi.mall.domain.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 代销产品
 */

@Data
@TableName("mall_consignment_product")
public class ConsignmentProduct {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 商家ID
     */
    private Long shopId;
    /**
     * 产品ID
     */
    private Long productId;
    /**
     * 产品url
     */
    private String productUrl;
    /**
     * 代销店铺ID
     */
    private Long consignmentShop;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 是否取消代销(0否，1：是)
     */
    private String status;

    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String productName;

    /**
     * 产品状态（0:下架，1：上架）
     */
    @TableField(exist = false)
    private String productStatus;
}
