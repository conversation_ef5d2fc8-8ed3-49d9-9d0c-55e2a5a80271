package com.ruoyi.mall.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单商品对象 mall_order_item
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mall_order_item")
public class OrderItem extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商家ID
     */
    private Long shopId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品图片
     */
    private String productImage;

    /**
     * SKU规格
     */
    private String skuSpec;

    /**
     * 商品单价
     */
    private BigDecimal productPrice;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 商品总价
     */
    private BigDecimal totalAmount;

    /**
     * 实付金额
     */
    private BigDecimal payAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 手续费金额
     */
    private BigDecimal feeAmount;

    /**
     * 手续费承担方（0店铺 1用户）
     */
    private String feeBearer;

    /**
     * 订单评价状态（0未评价，1：已评价，2:评价被隐藏）
     */
    private String commentStatus;

    /**
     * 退款状态：0-未退款，1-退款中，2-已退款
     */
    private String refundStatus;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 商品信息
     */
    @TableField(exist = false)
    private Product product;

    /**
     * SKU信息
     */
    @TableField(exist = false)
    private Sku sku;

    /**
     * 商家信息
     */
    @TableField(exist = false)
    private Shop shop;
}
