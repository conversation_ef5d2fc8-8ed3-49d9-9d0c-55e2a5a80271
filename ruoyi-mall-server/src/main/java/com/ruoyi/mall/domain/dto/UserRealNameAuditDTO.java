package com.ruoyi.mall.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户实名审核DTO
 */
@Data
public class UserRealNameAuditDTO {

    /**
     * 实名认证ID
     */
    @NotNull(message = "实名认证ID不能为空")
    private Long id;

    /**
     * 审核状态（1审核通过 2审核拒绝）
     */
    @NotBlank(message = "审核状态不能为空")
    private String status;

    /**
     * 审核意见/拒绝原因
     */
    private String reason;
}
