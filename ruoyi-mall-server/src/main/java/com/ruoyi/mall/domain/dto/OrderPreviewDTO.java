package com.ruoyi.mall.domain.dto;

import com.ruoyi.mall.domain.entity.TzUserAddress;
import com.ruoyi.mall.domain.vo.CartInfoVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单预览数据传输对象
 */
@Data
public class OrderPreviewDTO {

//    /**
//     * 按店铺分组的购物车商品列表
//     * key: 店铺ID
//     * value: 该店铺的购物车商品列表
//     */
//    private Map<Long, List<Cart>> shopCartMap;
//
//    /**
//     * 店铺信息列表
//     */
//    private List<Shop> shopList;

    /**
     * 购物车商品列表
     */
    private List<CartInfoVo> cartInfoList;

    /**
     * 商品总数量
     */
    private Integer totalQuantity;
    /**
     * 总手续费
     */
    private BigDecimal totalFeeAmount;

    /**
     * 商品总金额
     */
    private BigDecimal totalAmount;

    /**
     * 运费总金额
     */
    private BigDecimal totalFreightAmount;

    /**
     * 优惠总金额
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 实付总金额
     */
    private BigDecimal totalPayAmount;

    /**
     * 收货人信息(如果用户有默认地址则返回)
     */
    private TzUserAddress userAddress;
}
