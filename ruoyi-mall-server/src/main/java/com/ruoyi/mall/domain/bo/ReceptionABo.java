package com.ruoyi.mall.domain.bo;

import cn.hutool.json.JSONObject;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * A系统同步单业务对象
 */
@Data
public class ReceptionABo {

    /**
     * 同步数据类型 1：分量配置，2：引流配置
     */
    @NotNull(message = "同步数据类型不能为空")
    private Integer type;

    /**
     * 同步的数据
     */
    @NotNull(message = "同步的数据不能为空")
    private JSONObject jsonObject;

}
