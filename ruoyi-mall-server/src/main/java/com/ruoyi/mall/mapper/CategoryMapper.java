package com.ruoyi.mall.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.mall.domain.entity.Category;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 商品分类Mapper接口
 */
@Mapper
public interface CategoryMapper extends MPJBaseMapper<Category> {


    /**
     * 根据当前分类ID获取子分类ID包括自己
     *
     * @param categoryId 分类ID
     * @return
     */
    List<Long> getCategoryIdByCategoryId(Long categoryId);
}
