package com.ruoyi.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.mall.domain.entity.ProductEvaluate;
import com.ruoyi.mall.domain.vo.EvaluateVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 商品评价Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-20
 */
@Mapper
public interface ProductEvaluateMapper extends BaseMapper<ProductEvaluate> {

    /**
     * 查询商品评价列表
     *
     * @param productId 商品ID
     * @return 商品评价列表
     */
    Page<EvaluateVo> selProductEvaluate(@Param("productId") Long productId, @Param("type") Integer type, @Param("page") Page<EvaluateVo> page);

    /**
     * 审核评价
     *
     * @param id     评价id
     * @param status 0：审核中，1:审核通过，2：不通过
     */
    Integer ExamineEvaluate(@Param("id") Long id, @Param("status") String status);

    /**
     * 批量删除评价
     *
     * @param ids 评价ID集合
     * @return
     */
    Integer deleteProductEvaluateByIds(Long[] ids);

}
