package com.ruoyi.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.mall.domain.entity.Area;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地区Mapper接口
 */
@Mapper
public interface AreaMapper extends BaseMapper<Area> {

    /**
     * 获取所有省份列表
     *
     * @return 省份列表
     */
    List<Area> selectProvinces();

    /**
     * 获取指定省份下的城市列表
     *
     * @param parentId 父级ID（省份ID）
     * @return 城市列表
     */
    List<Area> selectCitiesByProvince(@Param("parentId") Long parentId);

    /**
     * 获取指定城市下的区县列表
     *
     * @param parentId 父级ID（城市ID）
     * @return 区县列表
     */
    List<Area> selectDistrictsByCity(@Param("parentId") Long parentId);

    /**
     * 根据编码获取地区信息
     *
     * @param code 地区编码
     * @return 地区信息
     */
    Area selectByCode(@Param("code") Long code);

    /**
     * 根据父ID查询下级地区列表
     *
     * @param parentId 父级ID
     * @return 下级地区列表
     */
    List<Area> selectByParentId(@Param("parentId") Long parentId);
}
