package com.ruoyi.mall.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.mall.domain.entity.Product;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.query.QueryConsignmentProductDTO;
import com.ruoyi.mall.domain.vo.DaiXiaoInfoVo;
import com.ruoyi.mall.domain.vo.ProductVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductMapper extends MPJBaseMapper<Product> {

    /**
     * 根据销量查询产品列表
     */
    Page<ProductVo> selectProductBySales(@Param("page") Page<ProductVo> page);

    /**
     * 查询用户浏览商铺最近一周上新的产品
     *
     * @param userId 用户id
     */
    Page<ProductVo> selectProductByBrowse(@Param("userId") Long userId, @Param("page") Page<ProductVo> page);

    /**
     * 查询用户最近浏览的产品类似产品价格低列表
     *
     * @param userId 用户id
     */
    Page<ProductVo> UserBrowseLikeProduct(@Param("userId") Long userId, @Param("page") Page<ProductVo> page);

    /**
     * 根据ID查询店铺信息
     *
     * @param shopId 店铺ID
     * @return 店铺信息
     */
    Shop selectShopById(@Param("shopId") Long shopId);

    /**
     * 更新店铺信息
     *
     * @param shop 店铺信息
     * @return 影响行数
     */
    int updateShop(Shop shop);

    /**
     * 随机查询代销产品列表
     *
     * @return
     */
    Page<ProductVo> getRandomConsignmentProductPage(@Param("page") Page<ProductVo> page);

    /**
     * 分页随机查询产品列表
     *
     * @return
     */
    Page<ProductVo> getRandomProductPage(@Param("page") Page<ProductVo> page);


    /**
     * 分页查询这个分类下所有的产品
     *
     * @param categoryIds 商品分类ids
     * @return
     */
    Page<ProductVo> getProductByCategoryIds(@Param("ids") List<Long> categoryIds, @Param("keyword") String keyword, @Param("page") Page<ProductVo> page);


    List<ProductVo> getProductByUserBuyingProduct(Integer pageNum, Integer pageSize, Long productName, Long categoryName);

    /**
     * 优化的代销商品查询 - 支持商品编号、商品名称、企业手机号码、代销状态的搜索
     *
     * @param page    分页参数
     * @param query   查询条件
     * @param shopIds 店铺ID列表
     * @return 代销商品信息列表
     */
    Page<DaiXiaoInfoVo> selectConsignmentProductsOptimized(@Param("page") Page<DaiXiaoInfoVo> page,
                                                           @Param("query") QueryConsignmentProductDTO query,
                                                           @Param("shopIds") List<Long> shopIds);

    /**
     * 根据关键字查询产品列表
     *
     * @param page
     * @param keyword
     * @return
     */
    Page<ProductVo> getProductByKeyword(@Param("page") Page<ProductVo> page, @Param("keyword") String keyword);
}
