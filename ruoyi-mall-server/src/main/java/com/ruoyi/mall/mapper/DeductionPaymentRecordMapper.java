package com.ruoyi.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.mall.domain.entity.DeductionPaymentRecord;
import com.ruoyi.mall.domain.vo.ShopPromotionRespVO;
import com.ruoyi.mall.domain.vo.ShopPromotionVO;
import com.ruoyi.mall.domain.vo.UserDeductionReqVO;
import com.ruoyi.mall.domain.vo.UserDeductionRespVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 抵扣金支付记录Mapper接口
 */
@Mapper
public interface DeductionPaymentRecordMapper extends BaseMapper<DeductionPaymentRecord> {

    /**
     * 查询用户已使用平台抵扣金总额
     *
     * @param userIds 用户ID列表
     * @return 用户ID与已使用平台抵扣金的映射
     */
    @MapKey("userId")
    List<Map<String, Object>> selectUserDeductionUsed(@Param("userIds") List<Long> userIds, @Param("payType") Integer payType);

    /**
     * 查询用户最近使用平台抵扣金时间
     *
     * @param userIds 用户ID列表
     * @return 用户ID与最近使用时间的映射
     */
    @MapKey("userId")
    List<Map<String, Object>> selectUserLastUsedTime(@Param("userIds") List<Long> userIds);


    /**
     * 查询用户抵扣金使用明细（分页）
     *
     * @param payType 用户类型
     * @return 抵扣金使用明细vo
     */
    @MapKey("userId")
    IPage<UserDeductionRespVO> selectUserDeductionDetailInfoPage(@Param("vo") UserDeductionReqVO userDeduction, @Param("payType") Integer payType, @Param("pageQuery") Page pageQuery);


    /**
     * 查询用户抵扣金使用明细（不分页）
     *
     * @param payType 用户类型
     * @return 抵扣金使用明细vo
     */
    @MapKey("userId")
    List<UserDeductionRespVO> selectUserDeductionDetailInfoList(@Param("vo") UserDeductionReqVO userDeduction, @Param("payType") Integer payType);

    /**
     * 查询用户昨日抵扣金使用统计
     *
     * @param startTime 开始时间（昨天开始）
     * @param endTime   结束时间（昨天结束）
     * @param payType   用户类型
     * @return 用户昨日抵扣金使用统计列表
     */
    List<Map<String, Object>> selectUserDeductionDailyStat(@Param("startTime") LocalDateTime startTime,
                                                           @Param("endTime") LocalDateTime endTime,
                                                           @Param("payType") Integer payType);

    /**
     * 查询商家促销金使用明细
     *
     * @param payType 用户类型
     * @return 促销金使用明细vo
     */
    @MapKey("userId")
    IPage<ShopPromotionRespVO> selectUserPromotionDetailInfoPage(@Param("vo") ShopPromotionVO shopPromotion, @Param("payType") Integer payType, @Param("pageQuery") Page pageQuery);


    /**
     * 查询商家促销金使用明细（不分页）
     *
     * @param payType 用户类型
     * @return 促销金使用明细vo
     */
    @MapKey("userId")
    List<ShopPromotionRespVO> selectUserPromotionDetailInfoList(@Param("vo") ShopPromotionVO shopPromotion, @Param("payType") Integer payType);

}
