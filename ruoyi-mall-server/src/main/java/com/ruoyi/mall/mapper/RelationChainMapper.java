package com.ruoyi.mall.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.mall.domain.dto.RelationChainQueryParam;
import com.ruoyi.mall.domain.vo.RelationChainVO;
import com.ruoyi.mall.domain.vo.UserStatusJurisdictionVO;
import org.apache.ibatis.annotations.Param;

/**
 * 关系链Mapper接口
 */
public interface RelationChainMapper {

    /**
     * 查询关系链列表
     *
     * @param queryParam 查询参数
     * @return 关系链列表
     */
    IPage<RelationChainVO> selectRelationChainList(Page pageQuery, @Param("queryParam") RelationChainQueryParam queryParam, @Param("dailyThreshold") Integer dailyThreshold, @Param("sum") Integer sum, @Param("dailyThresholdReward") Double dailyThresholdReward);

    /**
     * 检查用户是否为商家
     *
     * @param userId 用户ID（手机号）
     * @return 是否为商家
     */
    Boolean checkUserIsMerchant(@Param("userId") Long userId);

    /**
     * 更新系统用户状态（商家）
     *
     * @param userId 用户ID（手机号）
     * @param status 状态
     * @return 结果
     */
    int updateSysUserStatus(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 更新用户状态
     *
     * @param userId 用户ID（手机号）
     * @param status 状态
     * @return 结果
     */
    int updateUserStatus(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 查询用户状态权限
     *
     * @param userId
     * @return UserStatusJurisdictionVO
     * <AUTHOR>
     */

    UserStatusJurisdictionVO selectStatusAndJurisdiction(@Param("userId") Long userId);


}
