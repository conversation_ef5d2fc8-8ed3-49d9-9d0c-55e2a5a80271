package com.ruoyi.mall.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.mall.domain.entity.Shop;
import com.ruoyi.mall.domain.vo.ShopVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 商家信息Mapper接口
 */
@Mapper
public interface ShopMapper extends BaseMapper<Shop> {

    /**
     * 查询首页店铺
     */
    Page<ShopVo> selectHomeShopList(@Param("page") Page<ShopVo> page, @Param("userId") Long userId);

    /**
     * 当数据不足时，显示这个
     *
     * @param userId
     * @param page
     * @return
     */
    Page<ShopVo> selectUserShopList(@Param("page") Page<ShopVo> page, @Param("userId") Long userId);

    /**
     * 根据关键字查询店铺列表
     *
     * @param keyword 关键字
     * @param page    分页
     * @return
     */
    Page<ShopVo> selectShopListByKeyWord(@Param("userId") Long userId, @Param("keyword") String keyword, @Param("page") Page<ShopVo> page);

    /**
     * 根据手机号查询店铺
     */
    Shop selectShopByPhone(@Param("phone") String phone);

    /**
     * 根据用户ID更新删除标记吗
     *
     * @param userId 用户ID
     * @return
     */
    @Update("update mall_shop set del_flag=#{delFlag} where user_id = #{userId}")
    int updateShopDelFagByUserId(@Param("userId") Long userId, @Param("delFlag") String delFlag);


}
