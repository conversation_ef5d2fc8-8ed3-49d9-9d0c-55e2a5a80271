package com.ruoyi.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.mall.domain.entity.UserShopBrowse;
import com.ruoyi.mall.domain.vo.ShopVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Mapper
public interface UserShopBrowseMapper extends BaseMapper<UserShopBrowse>, MPJBaseMapper<UserShopBrowse> {

    /**
     * 新增用户浏览店铺
     */
    int saveUserShopBrowse(@RequestBody UserShopBrowse userShopBrowse);

    /**
     * 根据用户id和商铺id查询用户是否店铺粉丝
     *
     * @param userId 用户id
     * @param shopId 店铺id
     */
    Integer findUserShopBrowse(@Param("userId") Long userId, @Param("shopId") Long shopId);

    List<ShopVo> selectShopIds(@Param("userId") Long userId);


}
