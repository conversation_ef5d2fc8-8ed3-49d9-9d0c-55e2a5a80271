package com.ruoyi.mall.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.mall.domain.entity.QuantizationRate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 商品Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface QuantizationRateMapper extends MPJBaseMapper<QuantizationRate> {

    List<QuantizationRate> getQuantizationRate(@Param("searchMonth") Date searchMonth);

}
