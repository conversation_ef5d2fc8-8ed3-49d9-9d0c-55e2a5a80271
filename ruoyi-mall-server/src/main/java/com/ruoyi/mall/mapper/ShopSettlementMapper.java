package com.ruoyi.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.mall.domain.dto.SettlementRecordDTO;
import com.ruoyi.mall.domain.dto.SettlementStatsDTO;
import com.ruoyi.mall.domain.entity.ShopSettlement;
import com.ruoyi.mall.domain.vo.SettlementRecordVO;
import com.ruoyi.mall.domain.vo.SettlementStatsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商家结算单Mapper接口
 */
@Mapper
public interface ShopSettlementMapper extends BaseMapper<ShopSettlement> {

    Page<SettlementStatsVO> selectListSettlementStatsByPage(@Param("dto") SettlementStatsDTO dto, Page<SettlementStatsVO> page);

    List<SettlementStatsVO> selectSettlementStatsList(@Param("dto") SettlementStatsDTO dto);


    /**
     * 商家结算记录列表分页
     *
     * @param dto
     * @param page
     * @return IPage<SettlementRecordVO>
     * <AUTHOR>
     */

    IPage<SettlementRecordVO> selectSettlementRecordPage(@Param("dto") SettlementRecordDTO dto, Page<SettlementRecordVO> page);


    /**
     * 商家结算记录列表 (不分页)
     *
     * @param dto
     * @return List<SettlementRecordVO>
     * <AUTHOR>
     */

    List<SettlementRecordVO> selectSettlementRecordList(@Param("dto") SettlementRecordDTO dto);

}
