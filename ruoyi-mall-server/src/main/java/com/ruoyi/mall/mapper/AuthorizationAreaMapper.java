package com.ruoyi.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.mall.domain.entity.AuthorizationArea;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 *
 */
public interface AuthorizationAreaMapper extends BaseMapper<AuthorizationArea> {

    Page<Map<String, Object>> queryAreaAuthorizeByShop(@Param("type") String type, @Param("level") String level, Page<AuthorizationArea> build);

    Page<Map<String, Object>> queryAreaAuthorizeByUser(@Param("type") String type, @Param("level") String level, Page<AuthorizationArea> build);

}
