package com.ruoyi.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.mall.domain.entity.ShopDonationRecord;
import com.ruoyi.mall.domain.vo.ShopDonationRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 商家促销金赠送记录Mapper接口
 */
public interface ShopDonationRecordMapper extends BaseMapper<ShopDonationRecord> {

    /**
     * 查询商家赠送记录分页列表
     *
     * @param page         分页对象
     * @param shopId       商家ID
     * @param phone        用户手机号
     * @param donationType 赠送类型
     * @return 赠送记录分页列表
     */
    Page<ShopDonationRecordVo> selectDonationRecordPage(Page<ShopDonationRecord> page,
                                                        @Param("beginTime") Date beginTime,
                                                        @Param("endTime") Date endTime,
                                                        @Param("shopId") Long shopId,
                                                        @Param("phone") String phone,
                                                        @Param("donationType") Integer donationType);
}
