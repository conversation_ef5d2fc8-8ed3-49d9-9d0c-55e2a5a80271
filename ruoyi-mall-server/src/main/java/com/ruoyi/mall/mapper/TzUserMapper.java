package com.ruoyi.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.mall.domain.bo.RelationChainBo;
import com.ruoyi.mall.domain.entity.TzUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 商城用户Mapper接口
 */
@Mapper
public interface TzUserMapper extends BaseMapper<TzUser> {

    void updateDeductionMoneyLimit(Long userId, Double deductionMoneyLimit);

    /**
     * 查询商城用户
     *
     * @param userId 商城用户主键
     * @return 商城用户
     */
    TzUser selectTzUserByUserId(Long userId);

    /**
     * 查询商城用户列表
     *
     * @param tzUser 商城用户
     * @return 商城用户集合
     */
    List<TzUser> selectTzUserList(TzUser tzUser);

    /**
     * 新增商城用户
     *
     * @param tzUser 商城用户
     * @return 结果
     */
    int insertTzUser(TzUser tzUser);

    /**
     * 修改商城用户
     *
     * @param tzUser 商城用户
     * @return 结果
     */
    int updateTzUser(TzUser tzUser);

    /**
     * 删除商城用户
     *
     * @param userId 商城用户主键
     * @return 结果
     */
    int deleteTzUserByUserId(Long userId);

    /**
     * 批量删除商城用户
     *
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTzUserByUserIds(Long[] userIds);

    List<RelationChainBo> getRelationChain(@Param("dailyThresholdReward") Integer dailyThresholdReward, @Param("sum") Integer sum);

    /**
     * 根据用户ID更新删除标记
     *
     * @param userId
     * @param delFag
     * @return int
     * <AUTHOR>
     */

    @Update("update tz_user set del_flag=#{delFlag} where user_id = #{userId}")
    int updateTzUserDelFagByUserId(@Param("userId") Long userId, @Param("delFlag") String delFlag);

}
