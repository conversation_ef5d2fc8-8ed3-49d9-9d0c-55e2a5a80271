package com.ruoyi.mall.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.mall.domain.entity.ConsignmentProduct;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ConsignmentProductMapper extends BaseMapper<ConsignmentProduct>, MPJBaseMapper<ConsignmentProduct> {


    @MapKey("productId")
    List<Map<String, Object>> selectCountGroupByProductId(@Param("productIds") List<Long> productIds);

}
