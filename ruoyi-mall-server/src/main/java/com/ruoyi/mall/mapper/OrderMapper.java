package com.ruoyi.mall.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.mall.domain.entity.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 订单Mapper接口
 */
@Mapper
public interface OrderMapper extends MPJBaseMapper<Order> {

    /**
     * 统计指定店铺在指定日期范围内的成功订单数量
     *
     * @param shopId    店铺ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 订单数量
     */
    Integer countSuccessfulOrdersByShopIdAndDateRange(@Param("shopId") Long shopId,
                                                      @Param("startDate") Date startDate,
                                                      @Param("endDate") Date endDate);

    /**
     * 统计指定店铺的所有成功订单数量
     *
     * @param shopId 店铺ID
     * @return 订单数量
     */
    Integer countSuccessfulOrdersByShopId(@Param("shopId") Long shopId);

    /**
     * 统计指定店铺在指定日期范围内的所有订单数量（不论状态）
     *
     * @param shopId    店铺ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 订单数量
     */
    Integer countAllOrdersByShopIdAndDateRange(@Param("shopId") Long shopId,
                                               @Param("startDate") Date startDate,
                                               @Param("endDate") Date endDate);

    /**
     * 统计指定店铺特定状态的订单数量
     *
     * @param shopId 店铺ID
     * @param status 订单状态
     * @return 订单数量
     */
    Integer countOrdersByShopIdAndStatus(@Param("shopId") Long shopId, @Param("status") String status);

    /**
     * 统计指定店铺的退货待处理订单数量
     *
     * @param shopId 店铺ID
     * @return 订单数量
     */
    Integer countReturnOrdersByShopId(@Param("shopId") Long shopId, @Param("status") Integer status);
}
