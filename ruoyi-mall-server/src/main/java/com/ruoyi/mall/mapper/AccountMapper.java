package com.ruoyi.mall.mapper;

import com.ruoyi.mall.domain.entity.UserAccountStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户账户状态 Mapper 接口
 */

@Mapper
public interface AccountMapper {


    /**
     * 计算代销商没有取消商品的数量
     *
     * @param userId
     */
    int countNotCancelAllProducts(@Param("userId") Long userId);


    /**
     * 计算未完成订单数量
     *
     * @param userId
     * @return
     */
    Integer countUnfinishedOrder(
        @Param("userId") Long userId,
        @Param("status") String status
    );


    /**
     * 计算全部订单完成后距离现在时间的差值
     *
     * @param userId
     * @return
     */

    @Select(" SELECT ifnull(DATEDIFF(sysdate(), MAX(receive_time)), 100) as diffDays\n" +
        "        FROM mall_order\n" +
        "        WHERE user_id = #{userId}\n" +
        "          AND status = #{status}")
    Integer countOrderCompletedTimeDifference(
        @Param("userId") Long userId,
        @Param("status") String status
    );


    /**
     * 计算商家B和代销商CB未完成结算的数量
     *
     * @param userId
     * @return
     */
    int countUnsettledOrder(@Param("userId") Long userId);


    @Select("select * from mall_user_account_status where user_id = #{userId} and del_flag='0'")
    UserAccountStatus selectAccountStatusByUserId(@Param("userId") Long userId);


}
