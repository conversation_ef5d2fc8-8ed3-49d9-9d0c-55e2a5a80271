package com.ruoyi.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.mall.domain.entity.Cart;
import com.ruoyi.mall.domain.vo.CartVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 购物车Mapper接口
 */
@Mapper
public interface CartMapper extends BaseMapper<Cart>, MPJBaseMapper<Cart> {

    /**
     * 获取用户购物车有哪些商家
     *
     * @param userId
     * @return
     */
    List<CartVo> getUserCartInfo(@Param("userId") Long userId, @Param("query") String query, @Param("pageNum") Integer pageNum, @Param("pageSize") Integer pageSize);

    /**
     * 获取指定购物车id的商品消息
     *
     * @param cartIds
     * @return
     */
    List<CartVo> getCartInfoByCartIds(@Param("userId") Long userId, @Param("cartIds") List<Long> cartIds);

    /**
     * 获取用户购物车数量
     *
     * @param userId
     * @param query
     * @return
     */
    Integer getUserCartCount(@Param("userId") Long userId, @Param("query") String query);


}
