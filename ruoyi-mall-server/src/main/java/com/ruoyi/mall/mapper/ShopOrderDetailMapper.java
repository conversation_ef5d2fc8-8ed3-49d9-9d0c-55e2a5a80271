package com.ruoyi.mall.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.mall.domain.entity.ShopOrderDetail;
import com.ruoyi.mall.domain.query.ShopOrderDetailDTO;
import com.ruoyi.mall.domain.vo.ShopOrderDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 货款明细Mapper接口
 *
 * <AUTHOR>
 */

@Mapper
public interface ShopOrderDetailMapper extends BaseMapper<ShopOrderDetail> {

    IPage<ShopOrderDetailVO> selectMallShopOrderDetails(
        @Param("query") ShopOrderDetailDTO query,
        Page<ShopOrderDetailVO> page
    );

    List<ShopOrderDetailVO> selectMallShopOrderDetailsForExport(
        @Param("query") ShopOrderDetailDTO query);

}
