# ruoyi-system 系统管理模块

## 模块介绍

`ruoyi-system` 是MallB电商平台的系统基础管理模块，提供用户、角色、权限、部门、菜单等系统基础功能的管理和维护，是整个系统的基础支撑模块。

## 主要功能

- 用户管理
    - 用户信息维护
    - 密码策略设置
    - 用户状态管理
    - 用户导入导出
    - 用户关联角色
- 角色管理
    - 角色信息维护
    - 角色权限分配
    - 数据权限控制
- 菜单管理
    - 菜单树管理
    - 权限标识管理
    - 菜单显示控制
- 部门管理
    - 部门层级结构
    - 部门负责人设置
    - 组织机构维护
- 岗位管理
    - 岗位信息维护
    - 岗位与部门关联
- 字典管理
    - 数据字典维护
    - 字典类型管理
    - 字典数据管理
- 参数配置
    - 系统参数管理
    - 参数缓存控制
- 通知公告
    - 系统通知发布
    - 公告管理
- 日志管理
    - 操作日志记录
    - 登录日志记录
    - 系统异常记录

## 核心业务对象

- SysUser: 用户信息
- SysRole: 角色信息
- SysMenu: 菜单信息
- SysDept: 部门信息
- SysPost: 岗位信息
- SysDict: 字典信息
- SysConfig: 系统配置
- SysNotice: 通知公告
- SysOperLog: 操作日志
- SysLoginInfo: 登录日志

## 数据权限控制

系统支持以下数据权限控制方式：

1. 全部数据权限
2. 本部门数据权限
3. 本部门及以下数据权限
4. 仅本人数据权限
5. 自定义数据权限

## 与其他模块交互

- 被ruoyi-framework调用：提供权限检查和用户认证
- 被ruoyi-admin调用：提供系统管理界面
- 被业务模块调用：提供基础数据支撑

## 开发指南

1. 新增业务功能时，需先在菜单管理中添加对应的菜单和权限标识
2. 权限控制使用注解 `@PreAuthorize("@ss.hasPermi('system:xxx:xxx')")`
3. 数据权限控制使用注解 `@DataScope`
4. 系统敏感操作应记录操作日志 `@Log(title = "xx管理", businessType = BusinessType.XXX)` 