package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.bo.SysAgreementBo;
import com.ruoyi.system.domain.vo.SysAgreementVo;

import java.util.Collection;
import java.util.List;

/**
 * 协议Service接口
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
public interface ISysAgreementService {

    /**
     * 查询协议
     */
    SysAgreementVo queryById(Long agreementId);

    /**
     * 查询协议列表
     */
    TableDataInfo<SysAgreementVo> queryPageList(SysAgreementBo bo, PageQuery pageQuery);

    /**
     * 查询协议列表
     */
    List<SysAgreementVo> queryList(SysAgreementBo bo);

    /**
     * 新增协议
     */
    Boolean insertByBo(SysAgreementBo bo);

    /**
     * 修改协议
     */
    Boolean updateByBo(SysAgreementBo bo);

    /**
     * 校验并批量删除协议信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
