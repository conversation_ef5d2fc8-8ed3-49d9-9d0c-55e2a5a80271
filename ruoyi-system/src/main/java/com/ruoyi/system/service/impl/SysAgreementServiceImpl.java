package com.ruoyi.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.SysAgreement;
import com.ruoyi.system.domain.bo.SysAgreementBo;
import com.ruoyi.system.domain.vo.SysAgreementVo;
import com.ruoyi.system.mapper.SysAgreementMapper;
import com.ruoyi.system.service.ISysAgreementService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 协议Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@RequiredArgsConstructor
@Service
public class SysAgreementServiceImpl implements ISysAgreementService {

    private final SysAgreementMapper baseMapper;

    /**
     * 查询协议
     */
    @Override
    public SysAgreementVo queryById(Long agreementId) {
        return baseMapper.selectVoById(agreementId);
    }

    /**
     * 查询协议列表
     */
    @Override
    public TableDataInfo<SysAgreementVo> queryPageList(SysAgreementBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysAgreement> lqw = buildQueryWrapper(bo);
        Page<SysAgreementVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询协议列表
     */
    @Override
    public List<SysAgreementVo> queryList(SysAgreementBo bo) {
        LambdaQueryWrapper<SysAgreement> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysAgreement> buildQueryWrapper(SysAgreementBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysAgreement> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), SysAgreement::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), SysAgreement::getContent, bo.getContent());
        return lqw;
    }

    /**
     * 新增协议
     */
    @Override
    public Boolean insertByBo(SysAgreementBo bo) {
        SysAgreement add = BeanUtil.toBean(bo, SysAgreement.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setAgreementId(add.getAgreementId());
        }
        return flag;
    }

    /**
     * 修改协议
     */
    @Override
    public Boolean updateByBo(SysAgreementBo bo) {
        SysAgreement update = BeanUtil.toBean(bo, SysAgreement.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysAgreement entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除协议
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
