package com.ruoyi.system.domain.bo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.validate.AddGroup;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 协议业务对象 sys_agreement
 *
 * <AUTHOR>
 * @date 2025-05-08
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SysAgreementBo extends BaseEntity {

    /**
     * 协议id
     */
    @NotNull(message = "协议id不能为空", groups = {EditGroup.class})
    private Long agreementId;

    /**
     * 协议名称
     */
    @NotBlank(message = "协议名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 协议内容
     */
    @NotBlank(message = "协议内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;


}
