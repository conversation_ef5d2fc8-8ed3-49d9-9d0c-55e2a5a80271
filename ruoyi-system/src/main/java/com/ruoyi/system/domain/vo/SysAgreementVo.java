package com.ruoyi.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 协议视图对象 sys_agreement
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@ExcelIgnoreUnannotated
public class SysAgreementVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 协议id
     */
    @ExcelProperty(value = "协议id")
    private Long agreementId;

    /**
     * 协议名称
     */
    @ExcelProperty(value = "协议名称")
    private String name;

    /**
     * 协议内容
     */
    @ExcelProperty(value = "协议内容")
    private String content;


}
