package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 协议对象 sys_agreement
 *
 * <AUTHOR>
 * @date 2025-05-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_agreement")
public class SysAgreement extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 协议id
     */
    @TableId(value = "agreement_id")
    private Long agreementId;
    /**
     * 协议名称
     */
    private String name;
    /**
     * 协议内容
     */
    private String content;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
